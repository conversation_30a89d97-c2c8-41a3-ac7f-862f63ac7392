#!/usr/bin/env sh

# Exit immediately if a command exits with a non-zero status.
set -e

# Get the current branch name
branch_name=$(git symbolic-ref --short HEAD)

# Extract the ticket number from the branch name
ticket_number=$(echo $branch_name | grep -o 'VAP-[0-9A-Z]\+\|VID-[0-9A-Z]\+' | tr '[:lower:]' '[:upper:]')

# Check if a ticket number was found
#if [ -z "$ticket_number" ]; then
#    echo "No ticket number found in branch name."
#    exit 1
#fi

# Get the current commit message
commit_msg=$(cat .git/COMMIT_EDITMSG)

# Check if the commit message already contains the ticket number (case-insensitive)
if echo "$commit_msg" | grep -iq "^$ticket_number:"; then
    # Find and replace 'vap-' with 'VAP-' in the commit message
    updated_commit_msg=$(echo "$commit_msg" | sed -e "s/vap-/VAP-/i")
    echo "$updated_commit_msg" > .git/COMMIT_EDITMSG
    exit 0
fi

# Prepend the ticket number to the commit message
echo "$ticket_number: $commit_msg" > .git/COMMIT_EDITMSG

exit 0
