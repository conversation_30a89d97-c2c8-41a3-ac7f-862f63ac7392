# Link to Jira

Provide a link to the Jira ticket associated with this merge request for easy reference and tracking.

# Type of Change

- [ ] Feature
- [ ] Bug Fix

# Summary / Acceptance Criteria

- For a feature: Provide a concise summary of the feature being implemented.
- For a bug: Summarize the bug encountered concisely.

# Relevant Logs and/or Screenshots

Paste any relevant logs - use code blocks (```) to format console output, logs, and code, as it's very hard to read otherwise.

# Merge Request Checklist

- [ ] The MR is linked to the corresponding ticket.
- [ ] The code has been formatted and linted.
- [ ] The build passes.
- [ ] All new code is fully covered by tests (if applicable).
- [ ] All new features or bug fixes are documented (if applicable).