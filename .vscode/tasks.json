{"version": "2.0.0", "options": {"env": {}}, "tasks": [{"label": "Update AWS Env", "type": "shell", "command": "node devops/update-aws-env.js", "problemMatcher": []}, {"label": "Update Envs", "dependsOn": ["Update AWS Env"], "type": "shell", "command": "node devops/update-envs.js", "problemMatcher": []}, {"label": "Run All Services", "dependsOn": ["Update Envs", "Services"], "dependsOrder": "sequence", "problemMatcher": []}, {"label": "Run All Services - Without AWS", "dependsOn": ["Services"], "dependsOrder": "sequence", "problemMatcher": []}, {"label": "Services", "dependsOn": ["Auction Catalogs", "Bidding", "Sockets", "Users", "Notifications"], "problemMatcher": []}, {"label": "Auction Catalogs", "type": "shell", "command": "npx nest start auction-catalogs --watch", "isBackground": true, "problemMatcher": [], "hide": false, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}, {"label": "Bidding", "type": "shell", "command": "npx nest start bidding --watch", "isBackground": true, "problemMatcher": [], "hide": false, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}, {"label": "Sockets", "type": "shell", "command": "npx nest start sockets --watch", "isBackground": true, "problemMatcher": [], "hide": false, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}, {"label": "Users", "type": "shell", "command": "npx nest start users --watch", "isBackground": true, "problemMatcher": [], "hide": false, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}, {"label": "Notifications", "type": "shell", "command": "npx nest start notifications --watch", "isBackground": true, "problemMatcher": [], "hide": false, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}, {"label": "Install Dependencies", "type": "shell", "command": "npm install", "problemMatcher": [], "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "clear": false}}]}