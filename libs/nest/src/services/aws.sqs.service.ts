import { DeleteMessageBatchCommand, DeleteMessageCommand, Message, ReceiveMessageCommand, SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { forwardRef, Inject, Injectable, Logger, OnModuleInit, Optional } from '@nestjs/common';
import { IUrl } from '@vb/nest/interfaces/iUrl.interface';
import { Consumer } from 'sqs-consumer';
import { IConsumerHandlers } from '../interfaces/iConsumer.handlers.interface';
import { ConfigService } from 'libs/config-service/src/config.service';

@Injectable()
export class AwsSqsService implements OnModuleInit {
  queueUrls: IUrl;
  consumerHandlers: IConsumerHandlers;
  private client: SQSClient;
  private readonly logger = new Logger(AwsSqsService.name);

  constructor(
    private configService: ConfigService,
    @Inject('queueUrls') queueUrls: IUrl,
    @Optional() @Inject(forwardRef(() => 'consumerHandlers')) consumerHandlers: IConsumerHandlers,
  ) {
    this.queueUrls = queueUrls;
    this.consumerHandlers = consumerHandlers;
  }

  async onModuleInit(): Promise<void> {
    const messagesQueueUrl: string | undefined = await this.configService.getSetting('MESSAGES_QUEUE_URL');
    const region: string | undefined = await this.configService.getSetting('AWS_REGION');
    const nodeEnv: string | undefined = await this.configService.getSetting('NODE_ENV');
    const localstackEndpoint: string | undefined = await this.configService.getSetting('LOCALSTACK_ENDPOINT');

    this.client = new SQSClient({
      region: region,
      endpoint: nodeEnv === 'local' ? localstackEndpoint : undefined,
    });
  }

  async sendMessage(messageBody: string, queue: string): Promise<any> {
    try {
      const command: SendMessageCommand = new SendMessageCommand({
        QueueUrl: this.queueUrls[queue],
        DelaySeconds: 0,
        MessageAttributes: {
          Title: {
            DataType: 'String',
            StringValue: 'ViewBid',
          },
          Author: {
            DataType: 'String',
            StringValue: 'ViewBid',
          },
          WeeksOn: {
            DataType: 'Number',
            StringValue: '6',
          },
        },
        MessageBody: messageBody,
      });
      const response: any = await this.client.send(command);
      return response;
    } catch (error) {
      return error;
    }
  }

  receiveMessage(queueUrl: string): any {
    this.client.send(
      new ReceiveMessageCommand({
        MaxNumberOfMessages: 10,
        MessageAttributeNames: ['All'],
        QueueUrl: queueUrl,
        WaitTimeSeconds: 20,
        VisibilityTimeout: 60,
      }),
    );
  }

  deleteMessage(queueUrl: string, receiptHandle: string): any {
    this.client.send(
      new DeleteMessageCommand({
        ReceiptHandle: receiptHandle,
        QueueUrl: queueUrl,
      }),
    );
  }

  async receiveMessageClient(queue: string): Promise<any> {
    if (this.queueUrls[queue] === undefined || this.queueUrls[queue] === null || this.queueUrls[queue]?.length === 0) {
      this.logger.error('Message queue URL cannot be null or empty');
      process.kill(process.pid, 'SIGTERM');
      return;
    }
    
    const consumer: Consumer = Consumer.create({
      queueUrl: this.queueUrls[queue],
      sqs: this.client,
      region: process.env.AWS_REGION,
      handleMessage: async (message: Message) => {
        await this.consumerHandlers[queue].processQueueMessage(message);
        await this.deleteMessage(this.queueUrls[queue], message.ReceiptHandle!); // remove message consumed with success
      },
      visibilityTimeout: 60, // prevents another instance from seeing the same message por 60s
      waitTimeSeconds: 20, // uses long polling (better for load balancing)
    });

    consumer.on('error', (err: Error) => this.logger.log(err.message));
    consumer.on('processing_error', (err: Error) => this.logger.log(err.message));
    consumer.on('timeout_error', (err: Error) => this.logger.log(err.message));

    try {
      consumer.start();
    } catch (error) {
      this.logger.log(error);
    }
  }

  async deleteMessageClient(receiptHandle: string, queue: string): Promise<any> {
    try {
      const response: any = await this.deleteMessage(this.queueUrls[queue], receiptHandle);
      return response;
    } catch (error) {
      return error;
    }
  }

  // TODO: we may not need this service after some point
  async deleteAllMessagesClient(queue: string): Promise<any> {
    try {
      const { Messages } = await this.receiveMessage(this.queueUrls[queue]);

      if (!Messages) {
        return;
      }
      await this.client.send(
        new DeleteMessageBatchCommand({
          QueueUrl: this.queueUrls[queue],
          Entries: Messages.map((message: any) => ({
            Id: message.MessageId,
            ReceiptHandle: message.ReceiptHandle,
          })),
        }),
      );
    } catch (error) {
      return error;
    }
  }
}
