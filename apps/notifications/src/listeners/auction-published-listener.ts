import { Injectable, Logger } from '@nestjs/common';

import {
  AuctionCatalogsApi,
  AuctionLotsStatus,
  AuctionStatus,
  BiddingApi,
  BooleanResponse,
  GetAuctionById200Response,
  GetWatcherByIdResponse,
  ListResponse,
  LotStatus,
} from '@viewbid/ts-node-client';

import { InjectQueue } from '@nestjs/bullmq';
import { AuctionOpenNotificationParametersDto } from '@vb-utils/messaging/dtos/auction-open-notification-paramaters.dto';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { AccountMappingNotFoundError } from '@vb/nest/errors/users/account-mapping-not-found-error';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { RealtimeQueueListener } from '@vb/nest/interfaces/realtime-listener.interface';
import { RealtimeEventOptions, RealtimeProcessor } from '@vb/nest/realtime-worker/realtime-processor';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { ProfileService } from '@vb/nest/services/profile.service';
import { RedisService } from '@vb/redis';
import { WatcherNotFoundError } from 'apps/bidding/src/watchers/exceptions/watcher-not-found.error';
import * as axios from 'axios';
import { AxiosResponse } from 'axios';
import { Queue } from 'bullmq';
import { AuctionDto } from '../../../auction-catalogs/src/auction/dtos/auction.dto';
import { AuctionNotFoundError } from '../../../auction-catalogs/src/auction/exceptions/auction-not-found.error';
import { QUEUE_NAMES } from '../bull-workers/queue-names';
import { AuctionsCachingService } from '../../../auction-catalogs/src/services/auctions-caching.service';

interface LotNotificationResponse {
  accounts: AccountMappingCommonListDto;
  auctionOpenNotificationParameters: AuctionOpenNotificationParametersDto;
}

@Injectable()
export class AuctionPublishedListener implements IConsumerHandler, RealtimeQueueListener {
  private readonly logger = new Logger(AuctionPublishedListener.name);
  private readonly AUCTION_OPEN_NOTIFICATION: string = 'AUCTION_OPEN_NOTIFICATION';
  private queue: string;
  private baseUrl: string;
  private readonly realtimeProcessor: RealtimeProcessor = new RealtimeProcessor(this.logger);

  constructor(
    private readonly auctionCatalogsApi: AuctionCatalogsApi,
    private readonly biddingApi: BiddingApi,
    private readonly configService: ConfigService,
    private readonly sqsService: AwsSqsService,
    private readonly profileService: ProfileService,
    private readonly redisService: RedisService,
    @InjectQueue(QUEUE_NAMES.AUCTION_PUBLISHED) public realtimeQueue: Queue,
  ) {}

  /**
   * Initializes module with necessary configurations.
   */
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('AUCTION_PUBLISHED_QUEUE_URL', '');
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  /**
   * Processes a message from the queue indicating an auction is published.
   *
   * @param {any} message - The message received from the queue.
   */
  public async processQueueMessage(message: any): Promise<void> {
    const currentTime: number = Date.now();
    const body: any = JSON.parse(message.Body);
    const auction: GetAuctionById200Response = await this.getAuction(body.id);
    const fromDateTimeDate: Date = new Date(auction.data.fromDatetime);

    const processorOptions: RealtimeEventOptions = {
      eventName: 'Auction Published',
      eventDateTime: fromDateTimeDate,
      currentTime,
      eventPayload: { id: body.id, data: auction, timeToExecute: fromDateTimeDate },
      processEvent: this.processEvent.bind(this),
      queue: this.realtimeQueue,
      delayThreshold: 1000,
      jobPrefix: QUEUE_NAMES.AUCTION_PUBLISHED,
    };

    await this.realtimeProcessor.processOrQueueForLater(processorOptions);
  }

  public async processEvent(eventData: { id: string; data: any }): Promise<void> {
    try {
      const data: LotNotificationResponse = await this.constructNotificationData(eventData.id, eventData.data);
      this.logger.log(`Received auction published event for auction: ${eventData.id}`);

      await this.publishAuction(eventData.id, eventData.data);
      await this.sendAuctionOpenEventNotification(data.accounts, data.auctionOpenNotificationParameters);
    } catch (error) {
      this.logger.error('Unexpected error in AuctionPublishedListener', error);
    }
  }

  private async publishAuction(auctionId: string, auctionData: GetAuctionById200Response): Promise<void> {
    // rename to open auction
    const auctionResponse: AuctionDto = new AuctionDto(auctionData.data);
    if (auctionResponse.status === AuctionStatus.Public) {
      try {
        const activatedAuctions: AxiosResponse<BooleanResponse, any> = await this.auctionCatalogsApi.updateAuctionStatus({
          auctionIds: [auctionId],
          status: AuctionStatus.Open,
          isViewable: true,
        });
        if (activatedAuctions.data) {
          this.logger.log(`Following auctions have been activated/public: [${auctionId}]`);
          await AuctionsCachingService.deleteCache(this.redisService, auctionId);
          this.logger.log(`Invalidated cache for: [${auctionId}]`);
        }
      } catch (error) {
        this.logger.error(`Was an error while activating the auction for the following auction : [${auctionId}]`);
        throw error;
      }
      // we should not worrying/concerned about setting auctionlot or lot statuses because we are still not considering them anywhere till now
      try {
        const activatedAuctionLots: AxiosResponse<BooleanResponse, any> = await this.auctionCatalogsApi.updateAuctionLotsStatus({
          auctionIds: [auctionId],
          auctionLotsStatus: AuctionLotsStatus.Published,
          lotStatus: LotStatus.Published,
        });
        if (activatedAuctionLots.data) {
          this.logger.log(`Lots for the following auctions have been activated/public: [${auctionId}]`);
        }
      } catch (error) {
        this.logger.error(`Was an error while activating the lots for the following auction : [${auctionId}]`);
        throw error;
      }
    }
  }

  /**
   * Constructs notification data for an auction lot by aggregating essential details.
   *
   * Fetches and compiles details such as watchers, account mappings, and lot information into a single object.
   * Returns null if any required details are missing.
   *
   * @param {string} auctionId - The ID of the auction.
   * @returns {Promise<any>} A promise that resolves with the notification data or null if data is incomplete.
   */
  private async constructNotificationData(auctionId: string, auctionData: GetAuctionById200Response): Promise<LotNotificationResponse> {
    const watchersData: GetWatcherByIdResponse[] = await this.fetchWatcherDetails(auctionId);

    const uniqueAccountIds: string[] = [
      ...new Set(
        [...(watchersData ?? [])].map((detail: GetWatcherByIdResponse) => detail.accountMappingsId).filter((id: string | undefined): id is string => typeof id === 'string'),
      ),
    ];

    const fetchedAccountMappings: AccountMappingCommonDto[] | null = await this.fetchAccountMapping(uniqueAccountIds);
    const accountMappings: AccountMappingCommonDto[] = fetchedAccountMappings ? fetchedAccountMappings.filter((result: AccountMappingCommonDto) => result !== null) : [];

    const notificationResponse: LotNotificationResponse = {
      accounts: new AccountMappingCommonListDto(accountMappings),
      auctionOpenNotificationParameters: AuctionOpenNotificationParametersDto.fromAuctionLotResponse(auctionData.data, `${this.baseUrl}/auctions/${auctionData.data.id}`),
    };

    return notificationResponse;
  }

  /**
   * Fetches details related to an auction lot including watcher, lot data.
   * This function consolidates fetching of watcher details and lot details
   * for a given auction lot ID into a single method to streamline data retrieval.
   *
   * @param {string} auctionId - The ID of the auction.
   * @returns {Promise<Object>} Auction lot details or nulls if not found.
   */
  private async fetchWatcherDetails(auctionId: string): Promise<GetWatcherByIdResponse[]> {
    const watchersData: GetWatcherByIdResponse[] = await this.biddingApi
      .allWatchersByAuctionsId(auctionId, 1, -1)
      .then((response: AxiosResponse<ListResponse, any>) => response.data.data)
      .catch((error: any) => {
        this.logger.error('Error occurred during call allWatchersByAuctionsId: ' + error);
        throw new WatcherNotFoundError(auctionId);
      });

    if (!watchersData) {
      throw new WatcherNotFoundError(auctionId);
    }

    return watchersData;
  }

  private async getAuction(auctionId: string): Promise<GetAuctionById200Response> {
    const auctionResponse: axios.AxiosResponse<GetAuctionById200Response> = await this.auctionCatalogsApi.getAuctionById(auctionId).catch((error: any) => {
      this.logger.error('Error occurred during call getAuctionById: ' + error);
      throw new AuctionNotFoundError(auctionId);
    });

    if (!auctionResponse?.data) {
      throw new AuctionNotFoundError(auctionId);
    }

    return auctionResponse.data;
  }

  /**
   * Fetches the account mapping for a given account ID.
   *
   * TODO: We need to update the getAccountMappingByAccountMappingsId endpoint to include user data.
   *
   * @param {string} accountId - The ID of the account for which to fetch the mapping.
   * @returns {Promise<AccountMappingDto | null>} The account mapping details or null if not found.
   */

  private async fetchAccountMapping(accountIds: string[]): Promise<AccountMappingCommonDto[]> {
    const accountMappingCommonDtos: AccountMappingCommonDto[] = await this.profileService.getAccountMappingByIds(accountIds);

    if (!accountMappingCommonDtos) {
      throw new AccountMappingNotFoundError(accountIds[0]);
    }
    return accountMappingCommonDtos;
  }

  /**
   * Sends a auction open event notification.
   *
   * @param accounts - Account mappings.
   * @param auctionOpenNotificationParameters - Auction Open Notification parameters.
   */
  private async sendAuctionOpenEventNotification(accounts: AccountMappingCommonListDto, auctionOpenNotificationParameters: AuctionOpenNotificationParametersDto): Promise<void> {
    try {
      accounts.accountMappings.forEach((accountMapping: AccountMappingCommonDto) => {
        const message: Message = MessageFactory.build(this.AUCTION_OPEN_NOTIFICATION, new AccountMappingCommonListDto([accountMapping]), auctionOpenNotificationParameters);

        this.logger.log(`Sending Auction opening notification to ${accountMapping.email}`);
        this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
      });
    } catch (error) {
      this.logger.error(`Error sending auction opening notification: ${error}`);
    }
  }
}
