import { Injectable, Logger } from '@nestjs/common';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { AuctionCatalogsApi, Bid<PERSON>inner, <PERSON>iddingA<PERSON>, GetAuctionLotById200Response, GetWatcherByIdResponse, ListResponse } from '@viewbid/ts-node-client';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { ConfigService } from '@vb/config/src/config.service';
import { AxiosResponse } from 'axios';
import { SharedService } from 'libs/shared_services/shared.service';
import { LotClosedParametersDto } from '@vb-utils/messaging/dtos/lot-closed-parameters.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { BidParametersDto } from '@vb-utils/messaging/dtos/bid-parameters.dto';

interface NotificationParameterData {
  watcherDetails: GetWatcherByIdResponse[] | null;
  lotDetails: GetAuctionLotById200Response | null;
}

/**
 * Listener for handling lot item closed notifications for auction lots.
 */
@Injectable()
export class LotItemClosedListener implements IConsumerHandler {
  private readonly logger = new Logger(LotItemClosedListener.name);
  private readonly LOT_ITEM_CLOSED_NOTIFICATION: string = 'LOT_CLOSED_NOTIFICATION';
  private queue: string;
  private baseUrl: string;
  private readonly WINNING_BID_NOTIFICATION: string = 'WINNING_BID_NOTIFICATION';
  private cdnURL: string;

  constructor(
    private readonly biddingApi: BiddingApi,
    private readonly auctionCatalogsApi: AuctionCatalogsApi,
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly sharedService: SharedService,
  ) {}

  /**
   * Initializes module with necessary configurations.
   */
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('LOT_ITEM_CLOSED_QUEUE_URL', '');
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
    this.cdnURL = await this.configService.getSetting('CDN_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  /**
   * Processes a message from the queue indicating an auction lot is closing soon.
   *
   * @param {any} message - The message received from the queue.
   */
  public async processQueueMessage(message: any): Promise<void> {
    const body: any = JSON.parse(message.Body);
    const data: NotificationParameterData | null = await this.getNotificationData(body.id);
    this.logger.log(`Received lot item closed event for auctionLotId: ${body.id}`);

    if (data) {
      this.sendLotItemClosedEventNotification(data);
      if (data?.lotDetails) {
        this.prepareBidWinnerNotification(body.id, data.lotDetails);
      }
    } else {
      this.logger.log('No watchers or bidders to send out notification.');
    }
  }

  /**
   * Retrieves notification data for a given auction lot ID.
   *
   * @param {string} auctionLotId - The ID of the auction lot.
   * @return {Promise<NotificationParameterData | null>} A promise that resolves with the notification parameter data or null if data is missing or an error occurs.
   */
  private async getNotificationData(auctionLotId: string): Promise<NotificationParameterData | null> {
    try {
      const auctionDetails: NotificationParameterData = await this.fetchAuctionLotDetails(auctionLotId);
      if (!auctionDetails.watcherDetails || !auctionDetails.lotDetails) {
        this.logger.error(`Missing data in notification parameter object for notification construction: ${JSON.stringify(auctionDetails)}`);
        return null;
      }

      return auctionDetails;
    } catch (error) {
      this.logger.error(`Error constructing notification parameter data: ${error}`);
      return null;
    }
  }

  /**
   * Fetches details related to an auction lot including watcher, lot, bidder, and bidding data.
   * This function consolidates fetching of watcher details, lot details, bidder details, and bidding data
   * for a given auction lot ID into a single method to streamline data retrieval.
   *
   * @param {string} auctionLotId - The ID of the auction lot.
   * @returns {Promise<NotificationParameterData>} A promise that resolves with the notification parameter data.
   */
  private async fetchAuctionLotDetails(auctionLotId: string): Promise<NotificationParameterData> {
    const watcherDetailsPromise: Promise<GetWatcherByIdResponse[] | null> = this.sharedService.handleApiCall<GetWatcherByIdResponse[] | null>(() =>
      this.biddingApi.getAllWatchers(1, -1, auctionLotId).then((response: AxiosResponse<ListResponse, any>) => response.data.data ?? null),
    );
    const lotDetailsPromise: Promise<GetAuctionLotById200Response | null> = this.sharedService.handleApiCall<GetAuctionLotById200Response | null>(() =>
      this.auctionCatalogsApi.getAuctionLotsById(auctionLotId).then((response: AxiosResponse<GetAuctionLotById200Response, any>) => response.data ?? null),
    );

    return await Promise.all([watcherDetailsPromise, lotDetailsPromise]).then(
      ([watcherDetails, lotDetails]: [GetWatcherByIdResponse[] | null, GetAuctionLotById200Response | null]) => ({
        watcherDetails,
        lotDetails,
      }),
    );
  }

  /**
   * Sends a notification for a closed lot item to unique account mappings.
   *
   * @param {NotificationParameterData} data - The notification parameter data.
   * @param {string} id - The ID of the closed lot item.
   * @return {Promise<void>} A promise that resolves when the notification is sent.
   */
  private async sendLotItemClosedEventNotification(data: NotificationParameterData): Promise<void> {
    try {
      const lotClosedParameters: LotClosedParametersDto = new LotClosedParametersDto(
        data?.lotDetails?.data?.lots?.title,
        data?.lotDetails?.data?.currentBid,
        `${this.baseUrl}/auctions/${data?.lotDetails?.data?.auctionId}/lot/${data.lotDetails?.data?.lotId}`,
        data?.lotDetails?.data?.id,
        data?.lotDetails?.data?.highBidderId,
      );
      const message: Message = MessageFactory.build(this.LOT_ITEM_CLOSED_NOTIFICATION, new AccountMappingCommonListDto([]), lotClosedParameters);
      this.logger.log(`Sending lot item closed notification`);
      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending lot item closed notification: ${error}`);
    }
  }

  private async prepareBidWinnerNotification(auctionLotId: string, auctionLotsDetails: GetAuctionLotById200Response): Promise<void> {
    const bidWinners: BidWinner[] = (await this.biddingApi.getAllBidWinners(auctionLotId)).data.data as BidWinner[];

    if (!bidWinners) {
      throw new Error('no bid winners');
    }

    bidWinners.forEach((winner: BidWinner) => {
      this.sendWinningLotEventNotification(winner.accountMappingsId, winner.auctionLotsid, winner.amount, auctionLotsDetails);
    });
  }

  private async sendWinningLotEventNotification(
    currentHighBidderId: string,
    auctionLotId: string,
    amount: number,
    auctionLotsDetails: GetAuctionLotById200Response,
  ): Promise<void> {
    try {
      const message: Message = MessageFactory.build(
        this.WINNING_BID_NOTIFICATION,
        new AccountMappingCommonListDto([new AccountMappingCommonDto('', '', '', currentHighBidderId, '', '')]),
        new BidParametersDto(
          auctionLotsDetails.data.lots?.title ?? '',
          auctionLotsDetails.data.number ?? 0,
          `${this.baseUrl}/auctions/${auctionLotsDetails.data.auctionId}/lot/${auctionLotsDetails.data.lotId}`,
          amount,
          auctionLotId,
          auctionLotsDetails.data?.lots?.images?.[0]?.url ?? `${this.cdnURL}/no-image-lot.svg`,
          `${this.baseUrl}/profile/schedulePickup`, // this is a placeholder link for now because we don't have fulfillment part yet
        ),
      );
      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.log(error);
      throw error;
    }
  }
}
