import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AuctionCatalogsApi, AuctionLotsStatus, AuctionStatus, BooleanResponse, GetAuctionById200Response, ListResponse } from '@viewbid/ts-node-client';

import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';

import { ConfigService } from 'libs/config-service/src/config.service';

import { InjectQueue } from '@nestjs/bullmq';
import { RealtimeQueueListener } from '@vb/nest/interfaces/realtime-listener.interface';
import { RealtimeEventOptions, RealtimeProcessor } from '@vb/nest/realtime-worker/realtime-processor';
import { RedisService } from '@vb/redis';
import { AuctionLotDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot.dto';
import { AuctionNotFoundError } from 'apps/auction-catalogs/src/auction/exceptions/auction-not-found.error';
import * as axios from 'axios';
import { AxiosResponse } from 'axios';
import { Queue } from 'bullmq';
import { QUEUE_NAMES } from '../bull-workers/queue-names';
import { StandalonePubSubController } from '@vb/redis/controllers/standalone-pub-sub.controller';
import { AuctionDto } from '../../../auction-catalogs/src/auction/dtos/auction.dto';

@Injectable()
export class AuctionClosedListener extends StandalonePubSubController implements IConsumerHandler, RealtimeQueueListener {
  private readonly logger = new Logger(AuctionClosedListener.name);

  private queue: string;
  private realtimeProcessor: RealtimeProcessor = new RealtimeProcessor(this.logger);
  public delay: any = (ms: number) => new Promise((resolve: any) => setTimeout(resolve, ms));

  constructor(
    private auctionCatalogsApi: AuctionCatalogsApi,
    private configService: ConfigService,
    redisService: RedisService,
    @InjectQueue(QUEUE_NAMES.AUCTION_CLOSED) public realtimeQueue: Queue,
  ) {
    super(redisService, 'auctions');
  }

  /**
   * Initializes module with necessary configurations.
   */
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('AUCTION_CLOSED_QUEUE_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  /**
   * Processes a message from the queue indicating an auction is closed.
   *
   * @param {any} message - The message received from the queue.
   */
  public async processQueueMessage(message: any): Promise<void> {
    const currentTime: number = Date.now();
    const body: any = JSON.parse(message.Body);
    const auction: GetAuctionById200Response = await this.getAuction(body.id);
    const eventDateTime: Date = new Date(auction.data.toDatetime);

    const processorOptions: RealtimeEventOptions = this.generateRealtimeEventOptions(eventDateTime, currentTime, body.id, auction);

    await this.realtimeProcessor.processOrQueueForLater(processorOptions);
  }

  private generateRealtimeEventOptions(eventDateTime: Date, currentTime: number, id: any, auction: any): RealtimeEventOptions {
    return {
      eventName: 'Auction Closed',
      eventDateTime,
      currentTime,
      eventPayload: { id: id, data: auction, timeToExecute: eventDateTime },
      processEvent: this.processEvent.bind(this),
      queue: this.realtimeQueue,
      delayThreshold: 1000,
      jobPrefix: QUEUE_NAMES.AUCTION_CLOSED,
    };
  }

  async processEvent(eventData: { id: string; data: any }): Promise<void> {
    const auction: GetAuctionById200Response = await this.getAuction(eventData.id);
    const currentTime: number = Date.now();
    const differenceBetweenToDateTime: number = new Date(auction.data.toDatetime).getTime() - currentTime;
    if (differenceBetweenToDateTime > 5000) {
      this.logger.log(
        `Inserting back on to Redis Queue - Auction Closed - for auctionId: ${eventData.id}. Difference between now and toDateTime: ${differenceBetweenToDateTime} ms`,
      );
      const processorOptions: RealtimeEventOptions = this.generateRealtimeEventOptions(new Date(auction.data.toDatetime), currentTime, eventData.id, auction);
      await this.realtimeProcessor.processOrQueueForLater(processorOptions);
      return;
    } else {
      const delayAmount: number = new Date(auction.data.toDatetime).getTime() - 1000 - currentTime;
      this.logger.log(`Delaying ${delayAmount}, and then closing auction :  ${eventData.id}`);
      await this.delay(delayAmount);
    }

    try {
      this.logger.log(`Received auction close event for auction: ${eventData.id}`);
      await this.closeAuction(eventData.id);
    } catch (error) {
      this.logger.log(`Received auction close error for auction: ${error}`);
      return error;
    }

    try {
      this.logger.log(`Sending notifications for auction close event for auction: ${eventData.id}`);
      await this.constructNotificationData(eventData.id);
    } catch (error) {
      return error;
    }
  }

  private async closeAuction(auctionId: string): Promise<void> {
    const deactivatedAuctions: AxiosResponse<BooleanResponse, any> = await this.auctionCatalogsApi.updateAuctionStatus({
      auctionIds: [auctionId],
      status: AuctionStatus.Closed,
      isViewable: true,
    });
    // we should not worrying/concerned about setting auctionlot or lot statuses because we are still not considering them anywhere till now
    if (deactivatedAuctions.data) {
      this.logger.log(`The auction has been deactivated/complete: [${auctionId}]`);
      this.logger.warn(`REDIS_DEBUG: Instance ${process.env.HOSTNAME || 'unknown'} publishing auction closed event for ${auctionId} at ${new Date().toISOString()}`);
      this.notify('success', 'closed', { items: new AuctionDto({ id: auctionId }) });
      // await this.redisService.cacheDelete('auctions_' + auctionId);
      this.logger.log(`Invalidated cache for: [${auctionId}]`);
    }

    if (!deactivatedAuctions) {
      this.logger.log(`Was an error while Deactivating the auction or lots for the following auctions: [${auctionId}]`);
    }
  }

  /**
   * Constructs notification data for an auction lot by fetching bidding, watcher, accountMapping and lot details.
   *
   * @param {string} auctionId - The ID of the auction lot.
   * @returns {Promise<any>} A promise that resolves with an object containing bidding, watcher, accountMapping and lot details or null if not found.
   */
  private async constructNotificationData(auctionId: string): Promise<any> {
    try {
      const auctionLots: AxiosResponse<ListResponse, any> = await this.auctionCatalogsApi.getAllAuctionLotsByAuctionIds(1, -1, AuctionLotsStatus.Complete, {
        auctionIds: [auctionId],
        details: { Bidding: true },
      });

      if (!auctionLots) throw new NotFoundException(`lots not found`);
      const auctionLotList: string[] = [];

      auctionLots.data.data.map((auctionLot: AuctionLotDto) => auctionLotList.push(auctionLot?.id ?? ''));
    } catch (error) {
      this.logger.log(error);
      throw error;
    }
  }

  //TODO : This should be extracted out into another service layer, so can be used across all the listeners, etc
  private async getAuction(auctionId: string): Promise<GetAuctionById200Response> {
    const auctionResponse: axios.AxiosResponse<GetAuctionById200Response> = await this.auctionCatalogsApi.getAuctionById(auctionId).catch((error: any) => {
      this.logger.error('Error occurred during call getAuctionById: ' + error);
      throw new AuctionNotFoundError(auctionId);
    });

    if (!auctionResponse || !auctionResponse.data) {
      throw new AuctionNotFoundError(auctionId);
    }

    return auctionResponse.data;
  }
}
