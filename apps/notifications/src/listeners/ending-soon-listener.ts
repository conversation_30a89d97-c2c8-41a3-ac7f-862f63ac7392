import { Injectable, Logger } from '@nestjs/common';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { AuctionCatalogsApi, GetAuctionLotById200Response, SocketsApi } from '@viewbid/ts-node-client';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { Message } from '@vb-utils/messaging/dtos/message';
import { SocketMessageType } from '@vb-utils/messaging/dtos/socket-message.dto';
import { SocketMessageFactory } from '@vb-utils/messaging/factories/socket-message-factory';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { LotParametersDto } from '@vb-utils/messaging/dtos/lot-parameters.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { AxiosResponse } from 'axios';
import { SharedService } from 'libs/shared_services/shared.service';

interface AuctionLotDetails {
  lotDetails: GetAuctionLotById200Response | null;
}

interface LotNotificationResponse {
  lotParameters: LotParametersDto;
}

/**
 * Listener for handling ending soon notifications for auction lots.
 */
@Injectable()
export class EndingSoonListener implements IConsumerHandler {
  private readonly logger = new Logger(EndingSoonListener.name);
  private readonly LOT_CLOSING_SOON_NOTIFICATION: string = 'LOT_CLOSING_SOON_NOTIFICATION';
  private queue: string;
  private baseUrl: string;
  private cdnURL: string;

  constructor(
    private readonly auctionCatalogsApi: AuctionCatalogsApi,
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly socketService: SocketsApi,
    private readonly sharedService: SharedService,
  ) {}

  /**
   * Initializes module with necessary configurations.
   */
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('LOT_ENDING_SOON_QUEUE_URL', '');
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
    this.cdnURL = await this.configService.getSetting('CDN_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  /**
   * Processes a message from the queue indicating an auction lot is closing soon.
   *
   * @param {any} message - The message received from the queue.
   */
  public async processQueueMessage(message: any): Promise<void> {
    const body: any = JSON.parse(message.Body);
    const data: any = await this.constructNotificationData(body.id);
    this.logger.log(`Received ending soon event for auctionLotId: ${body.id}`);

    if (data) {
      const socketMessage: string = SocketMessageFactory.create(SocketMessageType.EndingSoon, body.id, data.lotParameters);
      this.socketService.postSocketMessage({ message: socketMessage });
      this.sendLotClosingSoonEventNotification(data.lotParameters);
    } else {
      this.logger.log('No watchers or bidders to send out notification.');
    }
  }

  /**
   * Constructs notification data for an auction lot by aggregating essential details.
   *
   * Fetches and compiles details such as bidding, watchers, account mappings, and lot information into a single object.
   * Returns null if any required details are missing.
   *
   * @param {string} auctionLotId - The ID of the auction lot.
   * @returns {Promise<any>} A promise that resolves with the notification data or null if data is incomplete.
   */
  private async constructNotificationData(auctionLotId: string): Promise<LotNotificationResponse | null> {
    try {
      const auctionDetails: AuctionLotDetails = await this.fetchAuctionLotDetails(auctionLotId);
      if (!auctionDetails.lotDetails) {
        this.logger.error(`Missing data in auction details for notification construction: ${JSON.stringify(auctionDetails)}`);
        return null;
      }
      const remainingTime: string = this.calculateRemainingTime(auctionDetails.lotDetails.data.auctions.toDatetime);

      const notificationResponse: LotNotificationResponse = {
        lotParameters: LotParametersDto.fromAuctionResponse(
          auctionDetails.lotDetails,
          auctionDetails.lotDetails.data?.lots.images.length > 0 ? auctionDetails.lotDetails.data?.lots.images[0].url : `${this.cdnURL}/no-image-lot.svg`,
          `${this.baseUrl}/auctions/${auctionDetails.lotDetails.data?.auctionId}/lot/${auctionDetails.lotDetails.data?.lotId}`,
          remainingTime,
        ),
      };

      return notificationResponse;
    } catch (error) {
      this.logger.error(`Error constructing notification data: ${error}`);
      return null;
    }
  }

  /**
   * Fetches details related to an auction lot including watcher, lot, bidder, and bidding data.
   * This function consolidates fetching of watcher details, lot details, bidder details, and bidding data
   * for a given auction lot ID into a single method to streamline data retrieval.
   *
   * @param {string} auctionLotId - The ID of the auction lot.
   * @returns {Promise<Object>} Auction lot details or nulls if not found.
   */
  private async fetchAuctionLotDetails(auctionLotId: string): Promise<AuctionLotDetails> {
    const lotDetailsPromise: Promise<GetAuctionLotById200Response | null> = this.sharedService.handleApiCall<GetAuctionLotById200Response | null>(() =>
      this.auctionCatalogsApi.getAuctionLotsById(auctionLotId).then((response: AxiosResponse<GetAuctionLotById200Response, any>) => response.data ?? null),
    );

    return await Promise.all([lotDetailsPromise]).then(([lotDetails]: [GetAuctionLotById200Response | null]) => ({ lotDetails }));
  }

  /**
   * Sends a lot closing soon event notification.
   *
   * @param accounts - Account mappings.
   * @param lotParameters - Lot parameters.
   */
  private async sendLotClosingSoonEventNotification(lotParameters: LotParametersDto): Promise<void> {
    try {
      const message: Message = MessageFactory.build(this.LOT_CLOSING_SOON_NOTIFICATION, new AccountMappingCommonListDto([]), lotParameters);

      this.logger.log(`Sending ending soon notification`);
      await this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending lot closing soon notification: ${error}`);
    }
  }

  private calculateRemainingTime(toDatetime: string): string {
    // Define the two dates
    const currentDateTime: Date = new Date();
    const toDateTime: Date = new Date(toDatetime);

    // Calculate the difference in milliseconds
    const diffInMilliseconds: number = toDateTime.valueOf() - currentDateTime.valueOf();

    const minutes: number = Math.floor(diffInMilliseconds / 60000);
    const seconds: string = ((diffInMilliseconds % 60000) / 1000).toFixed(0);

    return `${minutes}m ${seconds}s`;
  }
}
