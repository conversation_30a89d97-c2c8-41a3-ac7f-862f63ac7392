import { Controller } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { BaseController } from '@mvc/controllers/base.controller';
import { ApplicationPreferencesHandler } from '../handler/app-preferences.handler';
import { ApplicationPreferencesListResponse } from 'libs/application-preferences/src/http/responses/app-preferences-list.response';
import { ApplicationPreferencesResponse } from 'libs/application-preferences/src/http/responses/app-preferences.response';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';
import { ApplicationPreferencesRequest } from 'libs/application-preferences/src/http/requests/app-preferences.request';

@Controller('applicationPreferences')
export class ApplicationPreferencesController extends BaseController<
  ApplicationPreferencesHandler,
  ApplicationPreferencesRequest,
  ApplicationPreferencesDto,
  ApplicationPreferencesResponse,
  ApplicationPreferencesListResponse
> {
  constructor(handler: ApplicationPreferencesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ApplicationPreferencesRequest): ApplicationPreferencesDto {
    return new ApplicationPreferencesDto(request);
  }

  createResponseFromDto(dto: ApplicationPreferencesDto): ApplicationPreferencesResponse {
    return new ApplicationPreferencesResponse(dto, {});
  }
  createResponseList(list: ListResultset<ApplicationPreferencesDto>): ApplicationPreferencesListResponse {
    return new ApplicationPreferencesListResponse(list);
  }
}
