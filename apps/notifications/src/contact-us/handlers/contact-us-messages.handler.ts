import { ContactUsMessagesDbService } from '../data/contact-us-messages-db.service';
import { Injectable } from '@nestjs/common';
import { ContactUsMessageDto } from '../dtos/contact-us-message.dto';
import { PubSubHandler } from '@vb/redis/handlers/pub-sub.handler';
import { RedisService } from '@vb/redis';

@Injectable()
export class ContactUsMessagesHandler extends PubSubHandler<ContactUsMessagesDbService, ContactUsMessageDto> {
  constructor(dbService: ContactUsMessagesDbService, redisService: RedisService) {
    super(dbService, redisService, 'CONTACT_US');
  }
  async get(id: string): Promise<ContactUsMessageDto> {
    return await this.executeAndNotify(
      'get',
      async () => {
        return await this.dbService.get(id);
      },
      { message: '', id },
    );
  }
}
