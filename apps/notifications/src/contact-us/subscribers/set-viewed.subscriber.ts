import { Injectable } from '@nestjs/common';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { ContactUsMessageDto } from '../dtos/contact-us-message.dto';
import { ContactUsMessagesHandler } from '../handlers/contact-us-messages.handler';

@Injectable()
export class SetViewedSubscriber implements SubscriberInterface {
  constructor(private contactUsMessagesHandler: ContactUsMessagesHandler) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    this.contactUsMessagesHandler.update(
      eventPayload.accountMappingsId,
      new ContactUsMessageDto({
        viewedOn: new Date().toISOString(),
      }),
      eventPayload.id,
    );
  }
}
