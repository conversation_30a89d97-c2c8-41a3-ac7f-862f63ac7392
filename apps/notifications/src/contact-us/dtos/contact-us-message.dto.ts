export class ContactUsMessageDto {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  telephone?: string;
  role: string;
  subject: string;
  message: string;
  subscribe: boolean;
  promotionalConsent: boolean;
  createdOn: string;
  viewedOn: string;

  constructor(row: any) {
    this.id = row.id;
    this.firstname = row.firstname;
    this.lastname = row.lastname;
    this.email = row.email;
    this.telephone = row.telephone ?? '';
    this.role = row.role;
    this.subject = row.subject;
    this.message = row.message;
    this.subscribe = row.subscribe;
    this.promotionalConsent = row.promotionalConsent;
    this.createdOn = row.createdOn;
    this.viewedOn = row.viewedOn;
  }
}
