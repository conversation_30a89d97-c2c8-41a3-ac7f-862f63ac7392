import { ContactRole, Subject } from '@viewbid/ts-node-client';
import { IsEmail, IsOptional, IsBoolean, IsString, IsIn, Length } from 'class-validator';

export class ContactUsMessageRequest {
  @IsString()
  @Length(1, 20)
  firstname: string;

  @IsString()
  @Length(1, 20)
  lastname: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  @Length(10, 15)
  telephone: string;

  @IsString()
  @IsIn(Object.values(ContactRole))
  role: string;

  @IsString()
  @IsIn(Object.values(Subject))
  subject: string;

  @IsString()
  @Length(1, 1000)
  message: string;

  @IsBoolean()
  subscribe: boolean;

  @IsBoolean()
  promotionalConsent: boolean;

  @IsString()
  reCaptchaToken: string;
}
