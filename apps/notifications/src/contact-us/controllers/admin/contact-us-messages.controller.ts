import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ContactUsListDto } from '../../data/contact-us-list.dto';
import { ContactUsMessageDto } from '../../dtos/contact-us-message.dto';
import { ContactUsMessagesHandler } from '../../handlers/contact-us-messages.handler';
import { ContactUsMessageRequest } from '../../http/requests/contact-us-message.request';
import { AdminContactUsMessagesListResponse } from '../../http/responses/admin-contact-us-messages-list.response';
import { ContactUsMessageResponse } from '../../http/responses/contact-us-message.response';

@Controller('admin/contact-us')
@UseGuards(IsAdmin)
export class ContactUsMessagesController extends BaseController<
  ContactUsMessagesHandler,
  ContactUsMessageRequest,
  ContactUsMessageDto,
  ContactUsMessageResponse,
  AdminContactUsMessagesListResponse
> {
  constructor(handler: ContactUsMessagesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ContactUsMessageRequest): ContactUsMessageDto {
    return new ContactUsMessageDto(request);
  }

  createResponseFromDto(dto: ContactUsMessageDto): ContactUsMessageResponse {
    return new ContactUsMessageResponse(dto);
  }

  createResponseList(rows: ListResultset<ContactUsMessageDto>): AdminContactUsMessagesListResponse {
    const convertedList: Array<ContactUsListDto> = rows.list.map((item: ContactUsMessageDto) => new ContactUsListDto(item));
    const newListResultset: ListResultset<ContactUsListDto> = new ListResultset<ContactUsListDto>(convertedList, rows.page, rows.pageSize, rows.totalCount, rows.totalPages);
    return new AdminContactUsMessagesListResponse(newListResultset);
  }

  @Post('/')
  async create(@Body() body: ContactUsMessageRequest, @Req() req: VBInterceptorRequest): Promise<ContactUsMessageResponse | ErrorResponse> {
    req;
    body;
    return this.blockedEndpoint();
  }
}
