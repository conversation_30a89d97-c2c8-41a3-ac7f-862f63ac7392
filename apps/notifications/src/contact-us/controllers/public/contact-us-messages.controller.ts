import { BaseController } from '@mvc/controllers/base.controller';
import { ContactUsMessagesHandler } from '../../handlers/contact-us-messages.handler';
import { ContactUsMessageRequest } from '../../http/requests/contact-us-message.request';
import { ContactUsMessageResponse } from '../../http/responses/contact-us-message.response';
import { Body, Controller, Delete, Get, Param, Post, Query, Req } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { ContactUsMessageDto } from '../../dtos/contact-us-message.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { ContactUsMessagesListResponse } from '../../http/responses/contact-us-messages-list.response';
import { ContactUsCaptchaNotMatchingError } from '../../exceptions/contact-us-captcha-not-matching.error';
import { NewsletterSubscriberDto } from 'apps/notifications/src/newsletters/dtos/newsletter-subscriber-dto';
import { NewslettersSubscribersHandler } from '../../../newsletters/handlers/newsletters-subscribers.handler';
import { randomUUID } from 'crypto';
import { SharedService } from 'libs/shared_services/shared.service';

@Controller('contact-us')
export class ContactUsMessagesController extends BaseController<
  ContactUsMessagesHandler,
  ContactUsMessageRequest,
  ContactUsMessageDto,
  ContactUsMessageResponse,
  ContactUsMessagesListResponse
> {
  constructor(
    handler: ContactUsMessagesHandler,
    private newslettersSubscribersHandler: NewslettersSubscribersHandler,
    private sharedService: SharedService,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: ContactUsMessageRequest): ContactUsMessageDto {
    return new ContactUsMessageDto(request);
  }

  createResponseFromDto(dto: ContactUsMessageDto): ContactUsMessageResponse {
    return new ContactUsMessageResponse(dto);
  }

  createResponseList(list: ListResultset<ContactUsMessageDto>): ContactUsMessagesListResponse {
    return new ContactUsMessagesListResponse(list);
  }

  @Post('/')
  async create(@Body() body: ContactUsMessageRequest, @Req() req: VBInterceptorRequest): Promise<ContactUsMessageResponse | ErrorResponse> {
    try {
      // Extract the reCAPTCHA token from the request body
      const token: string = body.reCaptchaToken;

      // Validate the token with Google
      const isCaptchaValid: boolean = await this.sharedService.verifyReCaptcha(token);
      if (!isCaptchaValid) {
        throw new ContactUsCaptchaNotMatchingError();
      }

      // Proceed with the original logic if the reCAPTCHA is valid
      const contactUsRequest: ContactUsMessageDto = this.createDtoFromRequest(body);
      let existingEmailRecord: NewsletterSubscriberDto | undefined = undefined;
      if (contactUsRequest.subscribe && contactUsRequest.email) {
        try {
          existingEmailRecord = await this.newslettersSubscribersHandler.findByEmail(contactUsRequest.email);
        } catch (error) {
          this.logger.warn(`Error finding email ${contactUsRequest.email}, continuing with subscription creation: ${error.message}`);
        }
      }

      if (!existingEmailRecord && contactUsRequest.subscribe && contactUsRequest.email) {
        const fullName: string = `${contactUsRequest.firstname} ${contactUsRequest.lastname}`;
        const newsLettersRequest: NewsletterSubscriberDto = {
          id: randomUUID(),
          fullName: fullName,
          email: contactUsRequest.email,
        };

        try {
          await this.newslettersSubscribersHandler.create(req.accountMappingsId, newsLettersRequest);
        } catch (error) {
          this.logger.error(`Error creating new subscriber for email ${contactUsRequest.email}: ${error.message}`);
        }
      } else {
        this.logger.error(`Subscriber already exists with email: ${contactUsRequest.email}`);
      }

      const contactUsMessageDto: ContactUsMessageDto = await this.handler.create(req.accountMappingsId, contactUsRequest);
      return this.createResponseFromDto(contactUsMessageDto);
    } catch (error) {
      this.logger.error("Error caught in POST 'createContactUs': " + error);
      return new ErrorResponse(error, 'createContactUs');
    }
  }

  @Delete('/:id')
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    id;
    req;
    return this.blockedEndpoint();
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<ContactUsMessagesListResponse | ErrorResponse> {
    page;
    size;
    query;
    req;
    return this.blockedEndpoint();
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<ContactUsMessageResponse | ErrorResponse> {
    id;
    req;
    return this.blockedEndpoint();
  }
}
