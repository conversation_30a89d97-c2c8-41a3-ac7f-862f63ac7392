import { PreparedMessage } from '@vb-utils/messaging/dtos/prepared-message';
import { BaseBuildCommand } from './base-build.command';
import { MessageRequest } from '../http/requests/message.request';
import { TemplateMessageDto } from '../../templates/dtos/template-message-dto';
import Handlebars from 'handlebars';

export class HandlebarsBuildCommand extends BaseBuildCommand {
  async buildMessage(templateMessage: TemplateMessageDto, messageRequest: MessageRequest): Promise<PreparedMessage> {
    const compiledTemplate: HandlebarsTemplateDelegate = Handlebars.compile(templateMessage.html);
    const htmlContent: string = compiledTemplate(messageRequest.parameters);
    const preparedMessage: PreparedMessage = new PreparedMessage();
    preparedMessage.sms = this.merge(templateMessage.sms, messageRequest.parameters);
    preparedMessage.email = htmlContent;
    preparedMessage.ceilingBar = this.merge(templateMessage.ceilingBar, messageRequest.parameters);
    preparedMessage.subject = this.merge(templateMessage.subject, messageRequest.parameters);

    return preparedMessage;
  }
}
