import { BaseBuildCommand } from './base-build.command';
import { PreparedMessage } from '@vb-utils/messaging/dtos/prepared-message';
import { TemplateMessageDto } from '../../templates/dtos/template-message-dto';
import { MessageRequest } from '../http/requests/message.request';
import { ConfigService } from '@vb/config/src/config.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class DefaultBuildCommand extends BaseBuildCommand {
  constructor(protected configService: ConfigService) {
    super(configService);
  }

  async buildMessage(templateMessage: TemplateMessageDto, messageRequest: MessageRequest): Promise<PreparedMessage> {
    const updatedMessage: PreparedMessage = new PreparedMessage();
    updatedMessage.sms = this.merge(templateMessage.sms, messageRequest.parameters);
    updatedMessage.email = this.merge(templateMessage.html, messageRequest.parameters);
    updatedMessage.ceilingBar = this.merge(templateMessage.ceilingBar, messageRequest.parameters);
    updatedMessage.subject = this.merge(templateMessage.subject, messageRequest.parameters);

    return updatedMessage;
  }
}
