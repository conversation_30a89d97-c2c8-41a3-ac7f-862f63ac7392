import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RecipientTypes, TemplateDto } from '../../templates/dtos/template-dto';
import { TemplateMessageDto } from '../../templates/dtos/template-message-dto';
import { TemplateMessagesDbService } from '../../templates/data/template-messages-db.service';
import { RequestLogDto } from '../../request-logs/dtos/request-log-dto';
import { EmailService } from '../../messages/services/email.service';
import { S3BucketService } from '../data/s3-bucket.service';
import { PreparedMessage } from '@vb-utils/messaging/dtos/prepared-message';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { ConfigService } from 'libs/config-service/src/config.service';
import { TemplatesHandler } from '../../templates/handler/templates.handler';
import { SnsService } from '../../messages/services/sns.service';
import { RecipientDto } from '@vb-utils/messaging/dtos/recipient.dto';
import { RecipientsService } from '../services/recipients.service';
import { RecipientContactInfoDto } from '../../messages/dtos/recipient-contact-info.dto';
import { WebsocketService } from '../../messages/services/websocket.service';
import { AlarmDto } from '../dtos/alarm.dto';
import { TemplateNotFoundAlarmDto } from '../dtos/template-not-found-alarm.dto';
import { NotificationsTemplateNotFoundError } from '../exceptions/notifications-template-not-found.error';
import { RecipientListNotFoundAlarmDto } from '../dtos/recipient-list-not-found-alarm.dto';
import { TemplateMessageNotFoundAlarmDto } from '../dtos/template-message-not-found-alarm.dto';
import { TemplateMessageNotFoundError } from '../../templates/exceptions/template-message-not-found-error';
import { HtmlTemplateNotFoundAlarmDto } from '../dtos/html-template-not-found-alarm.dto';
import { AlarmPreparedMessageDto } from '../dtos/alarm-prepared-message.dto';
import { RequestLogFactory } from '../../request-logs/factories/request-log-factory';
import { RequestLogsHandler } from '../../request-logs/handler/request-logs-handler';
import { HtmlTemplateNotFoundError } from '../exceptions/html-template-not-found.error';
import { UnknownAlarmDto } from '../dtos/unknown-alarm.dto';
import { MessageRequest } from '../../messages/http/requests/message.request';
import { CommandFactory } from '../../messages/factories/command.factory';
import { BaseBuildCommand } from '../../messages/build-commands/base-build.command';
import { SubscribersHandler } from '../../subscribers/handler/subscribers.handler';
import { NoRecipientsFoundException } from '../exceptions/no-recipients-found.exception';
import { SubscriberDto } from '../../subscribers/dtos/subscriber.dto';
import { BiddingApi } from '@viewbid/ts-node-client';
import * as Sentry from '@sentry/browser';

@Injectable()
export class SqsHandler implements IConsumerHandler, OnModuleInit {
  private readonly logger: Logger = new Logger(SqsHandler.name);
  private alarmEmail: any;
  private queue: string;
  static readonly IF_ALL_ELSE_FAILS_EMAIL: string = '<EMAIL>'; //#Not-It!

  constructor(
    private readonly templatesHandler: TemplatesHandler,
    private readonly subscribersHandler: SubscribersHandler,
    private readonly templateMessagesDbService: TemplateMessagesDbService,
    private readonly websocketService: WebsocketService,
    private readonly emailService: EmailService,
    private readonly snsService: SnsService,
    private readonly s3BucketService: S3BucketService,
    private readonly configService: ConfigService,
    private readonly recipientsService: RecipientsService,
    private readonly requestLogsHandler: RequestLogsHandler,
    private readonly commandFactory: CommandFactory,
    private readonly biddingApi: BiddingApi,
  ) {}

  async onModuleInit(): Promise<void> {
    this.alarmEmail = await this.configService.getSetting('ALARM_EMAIL', SqsHandler.IF_ALL_ELSE_FAILS_EMAIL);
    this.queue = await this.configService.getSetting('MESSAGES_QUEUE_URL', '');
  }

  getQueueName(): string {
    return this.queue;
  }

  async processQueueMessage(notificationRequest: NotificationRequest): Promise<void> {
    let requestLog: RequestLogDto = new RequestLogDto();
    const request: MessageRequest = new MessageRequest(notificationRequest);
    try {
      requestLog = await this.requestLogsHandler.create('00000000-0000-0000-0000-000000000000', RequestLogFactory.createRequest(request, notificationRequest.MessageId));
      const template: TemplateDto = await this.templatesHandler.findByEventKey(request.eventKey);
      if (!template) {
        this.raiseAlarm(new TemplateNotFoundAlarmDto(request.eventKey), requestLog);
        throw new NotificationsTemplateNotFoundError(request.eventKey);
      }
      const recipients: Array<RecipientDto> = await this.getRecipients(template, request, requestLog);
      if (recipients.length == 0) {
        throw new NoRecipientsFoundException(request.eventKey, template.recipientType);
      }

      const templateMessage: TemplateMessageDto = await this.templateMessagesDbService.findByTemplateId(template.id, 'en-us');
      if (!templateMessage) {
        this.raiseAlarm(new TemplateMessageNotFoundAlarmDto(request.eventKey, template.id), requestLog);
        throw new TemplateMessageNotFoundError(request.eventKey, template.id);
      }

      const command: BaseBuildCommand = this.commandFactory.createCommand(template.buildCommand);
      //embed all the parameters into the email, sms, and ceilingbar notifications
      const preparedMessage: PreparedMessage = await command.buildMessage(templateMessage, request);

      const htmlTemplate: string = await this.s3BucketService.getEmailTemplate(template.templateUrl);
      if (!htmlTemplate) {
        this.raiseAlarm(new HtmlTemplateNotFoundAlarmDto(request.eventKey, template.id), requestLog);
        throw new HtmlTemplateNotFoundError(template.id);
      }

      command.postProcessList(request, recipients);
      const preferences: Array<RecipientContactInfoDto> = await this.recipientsService.getUserPreferences(recipients);
      const emailRecipientsList: Array<RecipientDto> = await this.recipientsService.getEmailRecipients(preferences, request.eventKey);
      const smsRecipientsList: Array<RecipientDto> = await this.recipientsService.getSmsRecipients(preferences, request.eventKey);

      if (emailRecipientsList.length > 0) {
        await this.emailService.sendMessage(preparedMessage, template, requestLog, emailRecipientsList, htmlTemplate);
      }
      if (smsRecipientsList.length > 0) {
        await this.snsService.sendMessage(preparedMessage, template, requestLog, smsRecipientsList, htmlTemplate);
      }
      await this.websocketService.sendMessage(preparedMessage, template, requestLog, recipients);
    } catch (error) {
      Sentry.captureException(error);

      this.logger.error('Unexpected error during processing of a queue message in SqsHandler:', JSON.stringify(error));
      await this.raiseAlarm(new UnknownAlarmDto(request.eventKey, error), requestLog);
    }
  }

  /**
   * because we are in an alarm state, we avoid touching the database (or any other external services) since
   * we don't know what caused the alarm. Sending a message that doesn't require any lookups.
   *
   * @param alarmMessage
   * @param requestLog
   */
  async raiseAlarm(alarmMessage: AlarmDto, requestLog: RequestLogDto): Promise<void> {
    try {
      this.logger.error(`Raising alarm for ${requestLog.eventKey}`, alarmMessage.message);
      const emailAlias: string = await this.configService.getSetting(alarmMessage.configKey, this.alarmEmail);
      const alarmPreparedMessage: AlarmPreparedMessageDto = new AlarmPreparedMessageDto(alarmMessage);
      await this.emailService.sendMessage(
        alarmPreparedMessage,
        TemplateDto.buildForAlarm(requestLog.eventKey),
        requestLog,
        RecipientDto.buildForAlarm(emailAlias),
        alarmPreparedMessage.email,
        true,
      );
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async getRecipients(template: TemplateDto, request: IMessageRequest, requestLog: RequestLogDto): Promise<Array<RecipientDto>> {
    let recipients: Array<RecipientDto> = [];
    if (template.recipientType == RecipientTypes.SUBSCRIBER) {
      const subscribers: Array<SubscriberDto> = (await this.subscribersHandler.findAllbySubscriptionKeysId(template.subscriptionKeysId)).list;
      if (subscribers.length == 0) {
        this.raiseAlarm(new RecipientListNotFoundAlarmDto(request.eventKey), requestLog);
        return [];
      }
      return subscribers.map((subscriber: SubscriberDto) => RecipientDto.buildWithAccountMappingsId(subscriber.accountMappingsId));
    } else if (template.recipientType == RecipientTypes.WATCHLIST) {
      const watchers: any = (await this.biddingApi.allWatchersByAuctionLotsId(request.parameters.auctionLotsId, 1, -1)).data;
      return watchers.data.map((subscriber: SubscriberDto) => RecipientDto.buildWithAccountMappingsId(subscriber.accountMappingsId));
    } else if (template.recipientType == RecipientTypes.INDIVIDUAL) {
      recipients = request.recipients.map((recipient: any) => RecipientDto.buildWithAccountMappingsId(recipient.accountMappingsId));
    } else {
      recipients = request.recipients;
    }

    return recipients;
  }
}
export interface NotificationRequest {
  Body: any;
  MessageId: string;
}
interface IMessageRequest {
  eventKey: string;
  parameters: Record<string, any>;
  recipients: Array<any>;
}
