import { UserPreferencesService } from './user-preferences.service';
import { Injectable } from '@nestjs/common';
import { RecipientDto } from '@vb-utils/messaging/dtos/recipient.dto';
import { UserNotificationPreferenceDto } from '../../messages/dtos/user-notification-preference.dto';
import { RecipientContactInfoDto } from '../../messages/dtos/recipient-contact-info.dto';

@Injectable()
export class RecipientsService {
  constructor(private readonly userPreferencesService: UserPreferencesService) {}

  async getUserPreferences(recipients: any): Promise<Array<RecipientContactInfoDto>> {
    const preferences: Array<UserNotificationPreferenceDto> = await this.userPreferencesService.getUserPreferencesList(
      recipients.map((recipient: RecipientDto) => recipient.accountMappingsId),
    );

    // Map each recipient to a new RecipientContactInfoDto, combining with fetched preferences
    return recipients.map((recipient: RecipientDto) => {
      // Find the matching preference for the recipient
      const matchingPreference: UserNotificationPreferenceDto | undefined = preferences.find(
        (pref: UserNotificationPreferenceDto) => pref.accountMappingsId === recipient.accountMappingsId,
      );

      // Throw an error if no matching preference is found
      if (!matchingPreference) {
        throw new Error(`No preference found for recipient with ID ${recipient.id}`);
      }

      // Create the RecipientContactInfoDto with mapped fields
      return new RecipientContactInfoDto(
        {
          email: matchingPreference.defaultEmail[0],
          mobile: matchingPreference.defaultPhone[0],
          accountMappingsId: matchingPreference.accountMappingsId,
          id: recipient.id,
          firstname: matchingPreference.firstname,
          lastname: matchingPreference.lastname,
        },
        matchingPreference,
      );
    });
  }

  async getEmailRecipients(preferences: Array<RecipientContactInfoDto>, eventKey: string): Promise<Array<RecipientDto>> {
    const list: Array<RecipientDto> = [];
    preferences.forEach((item: RecipientContactInfoDto) => {
      if (item.userNotificationPreferences.notificationPreferences && Array.isArray(item.userNotificationPreferences.notificationPreferences)) {
        const matchedPreference: any = item.userNotificationPreferences.notificationPreferences.find((preference: any) => preference.preferenceKey === eventKey);
        if (matchedPreference.channels.email) {
          list.push(item.userInfo);
        }
      }
    });

    return list;
  }
  async getSmsRecipients(preferences: Array<RecipientContactInfoDto>, eventKey: string): Promise<Array<RecipientDto>> {
    const list: Array<RecipientDto> = [];
    preferences.forEach((item: RecipientContactInfoDto) => {
      if (item.userNotificationPreferences.notificationPreferences && Array.isArray(item.userNotificationPreferences.notificationPreferences)) {
        const matchedPreference: any = item.userNotificationPreferences.notificationPreferences.find((preference: any) => preference.preferenceKey === eventKey);
        if (matchedPreference.channels.SMS) {
          list.push(item.userInfo);
        }
      }
    });

    return list;
  }
}
