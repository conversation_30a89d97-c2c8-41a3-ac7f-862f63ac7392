-- Update Account Migration Template Message to remove "Regards, Viewbid Admin" section
UPDATE public."TemplateMessages"
SET html = '<div class=''message-body''>
        <h1 class=''title''>Hi %firstName%,</h1>
        <p>This email is to let you know that we''ve migrated your account to the new Viewbid platform.</p>
        <p>We''re doing our best to keep things smooth and simple for you.</p>

        <div class=''section''>
            <h3>What you need to know:</h3>
            <p><strong>1. Your data is secure.</strong> We''ve taken the necessary precautions to ensure your data is safe during the migration process.</p>
            <p><strong>2. Your payment information remains secure.</strong> We don''t store your credit card information ourselves and will be using the same trusted payment platform as the old site.</p>
            <p><strong>3. No interruptions.</strong> Our current platform is still running for the time being until the migration is complete.</p>
            <p><strong>4. We''ll support you every step of the way.</strong> Please reach out via mail or phone if you have any questions/queries regarding the migration.</p>
        </div>

        <div class=''section''>
            <h3>What you need to do:</h3>
            <p><strong>1.</strong> Click the button below to create a new password.</p>
            <p><strong>2.</strong> Follow the on-screen instructions.</p>
            <p><strong>3.</strong> Login and enjoy the new bidding platform.</p>
        </div>

        <div class=''section''>
            <h3>The current details we have for you are:</h3>
            <table class=''user-details-table'' style=''width: 100%; border-collapse: collapse; margin: 20px 0;''>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Username</td>
                    <td style=''padding: 8px 0; color: #666;''>%username%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Email</td>
                    <td style=''padding: 8px 0; color: #666;''>%email%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>First Name</td>
                    <td style=''padding: 8px 0; color: #666;''>%firstName%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Last Name</td>
                    <td style=''padding: 8px 0; color: #666;''>%lastName%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Phone Number</td>
                    <td style=''padding: 8px 0; color: #666;''>%phoneNumber%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Address</td>
                    <td style=''padding: 8px 0; color: #666;''>%address%</td>
                </tr>
                <tr>
                    <td style=''padding: 8px 0; color: #000; width: 140px; vertical-align: top;''>Country</td>
                    <td style=''padding: 8px 0; color: #666;''>%country%</td>
                </tr>
            </table>
        </div>

        <p>Please click the button below to create a new password.</p>

        <div class=''center-aligned-text''>
            <a class=''btn-green btn-layout'' href=''%createPasswordLink%''>Create New Password</a>
        </div>
    </div>',
    "updatedOn" = NOW()
WHERE "templatesId" = (SELECT id FROM "Templates" WHERE "eventKey" = 'ACCOUNT_MIGRATION')
  AND locale = 'en-us';
