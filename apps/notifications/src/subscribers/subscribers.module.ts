import { Module } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SubscribersController } from './controller/subscribers.controller';
import { SubscribersHandler } from './handler/subscribers.handler';
import { SubscribersDbService } from './data/subscribers-db.service';
import { TemplatesModule } from '../templates/templates.module';

@Module({
  controllers: [SubscribersController],
  providers: [SubscribersHandler, PrismaService, SubscribersDbService],
  exports: [SubscribersDbService, SubscribersHandler],
  imports: [TemplatesModule],
})
export class SubscribersModule {}
