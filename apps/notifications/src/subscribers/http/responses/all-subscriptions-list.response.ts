import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { CheckedSubscriptionDto } from '../../dtos/checked-subscription.dto';

export class AllSubscriptionsListResponse extends ListResponse {
  constructor(resultSet: ListResultset<CheckedSubscriptionDto>, details: object) {
    super(resultSet, 'subscriptionList', details);
  }
}
