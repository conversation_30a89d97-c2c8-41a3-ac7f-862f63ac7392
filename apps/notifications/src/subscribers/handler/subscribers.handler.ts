import { <PERSON><PERSON><PERSON><PERSON> } from '@mvc/handlers/base.handler';
import { SubscribersDbService } from '../data/subscribers-db.service';
import { SubscriberDto } from '../dtos/subscriber.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { CheckedSubscriptionRequest } from '../http/requests/checked-subscription.request';

@Injectable()
export class SubscribersHandler extends BaseHandler<SubscribersDbService, SubscriberDto> {
  constructor(service: SubscribersDbService) {
    super(service);
  }

  async findAllbySubscriptionKeysId(subscriptionKeyId: string): Promise<ListResultset<SubscriberDto>> {
    return await this.dbService.findAllbySubscriptionKeysId(subscriptionKeyId);
  }

  async updateUserSubscriptions(userId: string, subscriptions: CheckedSubscriptionRequest[], accountMappingsId: string): Promise<void> {
    const dtos: SubscriberDto[] = subscriptions
      .filter((s: CheckedSubscriptionRequest) => s.subscribed)
      .map(
        (s: CheckedSubscriptionRequest) =>
          new SubscriberDto({
            accountMappingsId,
            subscriptionKeysId: s.subscriptionKeysId,
          }),
      );

    await this.dbService.deleteMany(userId, { accountMappingsId });

    const promises: Promise<any>[] = dtos.map((dto: SubscriberDto) => this.dbService.create(userId, dto));
    await Promise.all(promises);
  }
}
