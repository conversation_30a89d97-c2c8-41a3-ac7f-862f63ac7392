import { BaseController } from '@mvc/controllers/base.controller';
import { Templates<PERSON>and<PERSON> } from '../handler/templates.handler';
import { TemplateDto } from '../dtos/template-dto';
import { TemplateRequest } from '../http/requests/template.request';
import { TemplateResponse } from '../http/responses/template-response';
import { TemplateListResponse } from '../http/responses/template-list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { Controller } from '@nestjs/common';

@Controller('templates')
export class TemplatesController extends BaseController<TemplatesHandler, TemplateRequest, TemplateDto, TemplateResponse, TemplateListResponse> {
  constructor(handler: TemplatesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: TemplateRequest): TemplateDto {
    return new TemplateDto(request);
  }

  createResponseFromDto(dto: TemplateDto): TemplateResponse {
    return new TemplateResponse(dto);
  }

  createResponseList(list: ListResultset<TemplateDto>): TemplateListResponse {
    return new TemplateListResponse(list);
  }

  async getTemplateByEventKey(eventKey: string): Promise<TemplateDto> {
    return await this.handler.findByEventKey(eventKey);
  }
}
