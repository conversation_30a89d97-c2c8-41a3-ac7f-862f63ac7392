import { PrismaService } from '../../prisma/prisma.service';
import { TemplateMessageDto } from '../dtos/template-message-dto';
import { TemplateMessageNotFoundError } from '../exceptions/template-message-not-found-error';
import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/notifications-db-client';

@Injectable()
export class TemplateMessagesDbService extends BaseDbService<Prisma.TemplateMessagesDelegate, TemplateMessageDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.templateMessages);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TemplateMessageDto {
    return new TemplateMessageDto(model);
  }

  throw404(id: string): Error {
    return new TemplateMessageNotFoundError(id, 'en_us');
  }

  async findByTemplateId(id: string, locale: string): Promise<TemplateMessageDto> {
    const item: any = await this.prisma.templateMessages.findFirst({
      where: {
        templatesId: id,
        locale: locale,
      },
    });

    if (!item) {
      throw new TemplateMessageNotFoundError(id, locale);
    }

    return new TemplateMessageDto(item);
  }
}
