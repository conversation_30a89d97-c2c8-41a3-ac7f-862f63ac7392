import type { Config } from 'jest';

const config: Config = {
  // ...conf.default,
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testEnvironment: 'node',
  testRegex: '.e2e-spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['<rootDir>/src/**/*'],
  coveragePathIgnorePatterns: ['<rootDir>/src/prisma/'],
  coverageDirectory: '<rootDir>/../../coverage',
  coverageReporters: ['html', 'cobertura', 'text', 'text-summary'],
  reporters: ['default', 'jest-junit'],
  moduleNameMapper: {
    '^apps/jobs/src/(.*)': '<rootDir>/src/$1',
    '@vb/account-mappings/(.*)': '<rootDir>/../../libs/account-mappings/src/$1',
    '@vb/account-mappings': '<rootDir>/../../libs/account-mappings/src',
    '@vb-utils/messaging/(.*)': '<rootDir>/../../libs/messaging/src/$1',
    '@vb-utils/messaging': '<rootDir>/../../libs/messaging/src',
    '@mvc/(.*)': '<rootDir>/../../libs/mvc/src/$1',
    '@mvc': '<rootDir>/../../libs/mvc/src',
    '@vb/dtos/(.*)': '<rootDir>/../../libs/dtos/src/$1',
    '@vb/dtos': '<rootDir>/../../libs/dtos/src',
    '@vb/authorization/(.*)': '<rootDir>/../../libs/authorization/src/$1',
    '@vb/authorization': '<rootDir>/../../libs/authorization/src',
    '@vb/nest/(.*)': '<rootDir>/../../libs/nest/src/$1',
    '@vb/nest': '<rootDir>/../../libs/nest/src',
    '@vb/otel/(.*)': '<rootDir>/../../libs/otel/src/$1',
    '@vb/otel': '<rootDir>/../../libs/otel/src',
    '@vb/health/(.*)': '<rootDir>/../../libs/health/src/$1',
    '@vb/health': '<rootDir>/../../libs/health/src',
    '@vb/redis/(.*)': '<rootDir>/../../libs/redis/src/$1',
    '@vb/redis': '<rootDir>/../../libs/redis/src',
    '@vb/config/src/config.service': '<rootDir>/../../libs/config-service/src/config.service',
    '@vb/config': '<rootDir>/../../libs/config-service/src',
    '@vb/caching': '<rootDir>/../../libs/caching/src',
    '@vb/utils/src/data/mask-string': '<rootDir>/../../libs/utils/src/data/mask-string',
    'libs/ssl/src/(.*)': '<rootDir>/../../libs/ssl/src/$1',
    'libs/ssl/src': '<rootDir>/../../libs/ssl/src',
    'apps/auction-catalogs/src/auction/(.*)': '<rootDir>/../../../apps/auction-catalogs/src/auction/$1',
    'apps/auction-catalogs/src/auction': '<rootDir>/../../../apps/auction-catalogs/src/auction',
    'libs/config-service/src/(.*)': '<rootDir>/../../libs/config-service/src/$1',
    'libs/shared_services/(.*)': '<rootDir>/../../libs/shared_services/$1',
    'libs/application-preferences/src/(.*)': '<rootDir>/../../libs/application-preferences/src/$1',
  },
};

export default config;
