import { <PERSON><PERSON><PERSON><PERSON> } from '@mvc/handlers/base.handler';
import { ItemMappingsDbService } from '../data/item-mappings-db.service';
import { ItemMappingDto } from '../dtos/item-mapping.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ItemMappingsHandler extends BaseHandler<ItemMappingsDbService, ItemMappingDto> {
  constructor(dbService: ItemMappingsDbService) {
    super(dbService);
  }

  async getMappingByProxyId(id: string): Promise<ItemMappingDto> {
    return await this.dbService.getCustom([{ proxyId: id }], ItemMappingDto);
  }

  async getOrCreateMappingByProxyId(itemIds: Array<string>, type: string): Promise<Map<string, string>> {
    return await this.dbService.getOrCreateMappingByProxyId(itemIds, type);
  }
}
