import { MappingType, Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ItemMappingDto } from '../dtos/item-mapping.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { ItemMappingNotFoundError } from '../exceptions/item-mapping-not-found-error';
import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';

/**
 * it's important to note that the lookups for item mappings are generally the proxyId and
 * not the id column. This is because items can come from the Lots table or the ecommerce
 * area of the website (Viewbid's developer <PERSON> is working on this separately) so we
 * have this mapping table as a proxy for both of those item tables.
 */
@Injectable()
export class ItemMappingsDbService extends BaseDbService<Prisma.ItemMappingsDelegate, ItemMappingDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.itemMappings, false, false, false);
  }

  createFilter(params: any[]): any {
    return {
      id: {
        in: params,
      },
    };
  }

  mapToDto(model: any): ItemMappingDto {
    return new ItemMappingDto(model);
  }

  throw404(id: string): Error {
    return new ItemMappingNotFoundError(id);
  }

  async getOrCreateMappingByProxyId(itemIds: Array<string>, type: string): Promise<Map<string, string>> {
    // Step 1: Batch Fetch Existing ItemMappings
    const existingMappings: Array<{ proxyId: string; id: string }> = await this.table.findMany({
      where: {
        proxyId: { in: itemIds },
        mappingType: type as MappingType, // Use the provided type dynamically
      },
    });

    // Extract existing proxyIds
    const existingProxyIds: Set<string> = new Set(existingMappings.map((mapping: { proxyId: string }) => mapping.proxyId));

    // Step 2: Identify Missing ItemMappings
    const missingLotIds: Array<string> = itemIds.filter((lotId: string) => !existingProxyIds.has(lotId));

    // Step 3: Batch Insert Missing ItemMappings
    if (missingLotIds.length > 0) {
      const createItemMappings: Array<{ proxyId: string; mappingType: MappingType }> = missingLotIds.map((lotId: string) => ({
        proxyId: lotId,
        mappingType: type as MappingType,
      }));

      await this.table.createMany({
        data: createItemMappings,
      });
    }

    // Step 4: Fetch all ItemMappings after creation
    const allMappings: Array<{ proxyId: string; id: string }> = await this.table.findMany({
      where: {
        proxyId: { in: itemIds },
        mappingType: type as MappingType, // Use the provided type dynamically
      },
    });

    // Create a lookup map for itemMappingsId
    return new Map<string, string>(allMappings.map((mapping: { proxyId: string; id: string }) => [mapping.proxyId, mapping.id]));
  }
}
