import { IsEnum, IsString, <PERSON><PERSON><PERSON><PERSON>, ValidateIf } from 'class-validator';

enum MappingType {
  Lots = 'Lots',
  POItems = 'POItems',
  Products = 'Products',
  LineItems = 'LineItems',
}

export class ItemMappingDto {
  @IsUUID()
  id: string;

  @IsEnum(MappingType)
  mappingType: MappingType;

  @IsUUID()
  proxyId: string;

  @IsString()
  @ValidateIf((object: any, value: any) => value !== null)
  barcode: string | null;

  constructor(row: any) {
    this.id = row.id;
    this.mappingType = row.mappingType;
    this.proxyId = row.proxyId;
    this.barcode = row.barcode;
  }
}
