import { Injectable } from '@nestjs/common';
import { ItemMappingsHandler } from '../handler/item-mappings.handler';
import { ItemMappingDto } from '../dtos/item-mapping.dto';
import { LotDto } from '../../lots/dtos/lot.dto';
import { MappingType } from '../../prisma/generated/auction-catalogs-db-client';
import { ProductDto } from '../../products/dtos/product.dto';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { ProductsHandler } from '../../products/handler/products.handler';

@Injectable()
export class ItemMappingsFactory {
  constructor(
    private itemMappingsHandler: ItemMappingsHandler,
    private lotsHandler: LotsHandler,
    private productsHandler: ProductsHandler,
  ) {}

  async getItemMapping(dto: LotDto): Promise<ItemMappingDto> {
    let itemMapping: ItemMappingDto | undefined = undefined;

    try {
      itemMapping = await this.itemMappingsHandler.getMappingByProxyId(dto.id);
    } catch (error) {}
    if (itemMapping) {
      return itemMapping;
    }
    itemMapping = await this.itemMappingsHandler.create(
      '00000000-0000-0000-0000-000000000000',
      new ItemMappingDto({
        mappingType: dto instanceof LotDto ? MappingType.Lots : MappingType.Products,
        proxyId: dto.id,
        barcode: '',
      }),
    );

    return itemMapping;
  }

  async getMappedItem(itemMapping: ItemMappingDto): Promise<LotDto | ProductDto> {
    switch (itemMapping.mappingType) {
      case MappingType.Lots:
        return this.lotsHandler.get(itemMapping.proxyId);
      case MappingType.Products:
        return this.productsHandler.get(itemMapping.proxyId);
      default:
        throw new Error(`Unsupported mapping type: ${itemMapping.mappingType}`);
    }
  }
}
