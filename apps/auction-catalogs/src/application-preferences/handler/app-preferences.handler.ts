import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { ApplicationPreferencesDbService } from '../data/app-preferences-db.service';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';

@Injectable()
export class ApplicationPreferencesHandler extends BaseHandler<ApplicationPreferencesDbService, ApplicationPreferencesDto> {
  constructor(dbService: ApplicationPreferencesDbService) {
    super(dbService);
  }

  async getByKey(key: string): Promise<ApplicationPreferencesDto> {
    return await this.dbService.getByKey(key);
  }
}
