import { Injectable } from '@nestjs/common';
import { DefaultArgs } from 'prisma/prisma-client/runtime/library';
import { BaseDbService } from '@mvc/data/base-db.service';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';
import { ApplicationPreferencesNotFoundWithKeyError } from 'libs/application-preferences/src/exceptions/app-preferences-not-found-with-key-error';
import { ApplicationPreferencesNotFoundError } from 'libs/application-preferences/src/exceptions/app-preferences-not-found-error';

@Injectable()
export class ApplicationPreferencesDbService extends BaseDbService<Prisma.ApplicationPreferencesDelegate<DefaultArgs>, ApplicationPreferencesDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.applicationPreferences);
  }

  mapToDto(model: any): ApplicationPreferencesDto {
    return new ApplicationPreferencesDto(model);
  }

  async getByKey(key: string): Promise<ApplicationPreferencesDto> {
    const item: any = await this.prismaService.applicationPreferences.findFirst({
      where: {
        preferenceKey: key,
      },
    });
    if (!item) {
      throw new ApplicationPreferencesNotFoundWithKeyError(key);
    }
    return this.mapToDto(item);
  }

  createFilter(params: any[]): any {
    params;
  }

  throw404(id: string): Error {
    throw new ApplicationPreferencesNotFoundError(id);
  }
}
