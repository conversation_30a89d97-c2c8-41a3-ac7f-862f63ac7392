import { Module } from '@nestjs/common';
import { AppPreferencesController } from './controller/app-preferences.controller';
import { ApplicationPreferencesDbService } from './data/app-preferences-db.service';
import { ApplicationPreferencesHandler } from './handler/app-preferences.handler';

@Module({
  controllers: [AppPreferencesController],
  providers: [ApplicationPreferencesHandler, ApplicationPreferencesDbService],
  exports: [ApplicationPreferencesHandler],
})
export class ApplicationPreferencesModule {}
