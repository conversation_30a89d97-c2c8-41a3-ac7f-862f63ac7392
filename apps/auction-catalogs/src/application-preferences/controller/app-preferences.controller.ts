import { Controller } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { BaseController } from '@mvc/controllers/base.controller';
import { ApplicationPreferencesRequest } from 'libs/application-preferences/src/http/requests/app-preferences.request';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';
import { ApplicationPreferencesHandler } from '../handler/app-preferences.handler';
import { ApplicationPreferencesResponse } from 'libs/application-preferences/src/http/responses/app-preferences.response';
import { ApplicationPreferencesListResponse } from 'libs/application-preferences/src/http/responses/app-preferences-list.response';

@Controller('applicationPreferences')
export class AppPreferencesController extends BaseController<
  ApplicationPreferencesHandler,
  ApplicationPreferencesRequest,
  ApplicationPreferencesDto,
  ApplicationPreferencesResponse,
  ApplicationPreferencesListResponse
> {
  constructor(handler: ApplicationPreferencesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ApplicationPreferencesRequest): ApplicationPreferencesDto {
    return new ApplicationPreferencesDto(request);
  }

  createResponseFromDto(dto: ApplicationPreferencesDto): ApplicationPreferencesResponse {
    return new ApplicationPreferencesResponse(dto, {});
  }
  createResponseList(list: ListResultset<ApplicationPreferencesDto>): ApplicationPreferencesListResponse {
    return new ApplicationPreferencesListResponse(list);
  }
}
