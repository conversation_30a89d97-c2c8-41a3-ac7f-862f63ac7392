import { BaseController } from '@mvc/controllers/base.controller';
import { Templates<PERSON>andler } from '../handlers/templates.handler';
import { TemplateRequest } from '../http/requests/template.request';
import { TemplateDto } from '../dtos/template.dto';
import { TemplateResponse } from '../http/responses/template.response';
import { TemplatesListResponse } from '../http/responses/templates-list.response';
import { ListResultset } from '@mvc/data/list-resultset';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { ItemMappingsHandler } from '../../item-mappings/handler/item-mappings.handler';
import { ItemMappingDto } from '../../item-mappings/dtos/item-mapping.dto';
import { MappingType } from '../../prisma/generated/auction-catalogs-db-client';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { LotDto } from '../../lots/dtos/lot.dto';
import { Transactional } from '@transactional/core';
import { ProductsHandler } from '../../products/handler/products.handler';
import { ProductDto } from '../../products/dtos/product.dto';
import { TemplateMissingIdError } from '../exceptions/template-missing-id.error';
import { LotTemplatesHandler } from '../handlers/lot-templates.handler';
import { LotTemplateDto } from '../dtos/lot-template.dto';
import { LotTemplateRequest } from '../http/requests/lot-template.request';
import { Body, Controller, Get, Param, Post, Query, Req, Patch } from '@nestjs/common';
import { ListTemplateDto } from '../dtos/list-template.dto';
import { LotDetailedAdminResponse } from '../../lots/http/responses/lot-detailed-admin.response';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { LotAttributeDto } from '../../lot-attributes/dtos/lot-attribute.dto';
import { LotAttributeViewDto } from '../../lot-attributes/dtos/lot-attribute-view.dto';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { UploadsController } from '../../uploads/controller/uploads.controller';
import { LotAttributesHandler } from '../../lot-attributes/handler/lot-attributes.handler';
import { SharedService } from '../../shared/shared.service';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { ConfigService } from 'libs/config-service/src/config.service';

@Controller('admin/lot-templates')
export class TemplatesController extends BaseController<TemplatesHandler, TemplateRequest, TemplateDto, TemplateResponse, TemplatesListResponse> {
  private bucketName: string = '';

  constructor(
    handler: TemplatesHandler,
    private lotTemplatesHandler: LotTemplatesHandler,
    private itemMappingsHandler: ItemMappingsHandler,
    private lotsHandler: LotsHandler,
    private productsHandler: ProductsHandler,
    private auctionsHandler: AuctionsHandler,
    private auctionLotsHandler: AuctionLotsHandler,
    private uploadsController: UploadsController,
    private lotAttributesHandler: LotAttributesHandler,
    private sharedService: SharedService,
    private configService: ConfigService,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: TemplateRequest): TemplateDto {
    return new TemplateDto(request);
  }

  createResponseFromDto(dto: TemplateDto): TemplateResponse {
    return new TemplateResponse(dto);
  }

  createResponseList(list: ListResultset<TemplateDto>): TemplatesListResponse {
    return new TemplatesListResponse(list);
  }

  async onModuleInit(): Promise<void> {
    const templateBucketName: string | undefined = await this.configService.getSetting('TEMPLATE_BUCKET_NAME');

    if (!templateBucketName) {
      this.logger.error('Auction Catalog Service was not provided with the required env variable: TEMPLATE_BUCKET_NAME');
      process.kill(process.pid, 'SIGTERM');
    } else {
      this.bucketName = templateBucketName ?? '';
    }
  }

  @Patch('/link-to-lot')
  async linkLotToTemplate(@Body() body: LotTemplateRequest, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;

      await this.lotsHandler.get(body.lotsId);
      await this.handler.get(body.templatesId);

      const dto: LotTemplateDto = new LotTemplateDto(body);

      await this.lotTemplatesHandler.deleteCustom(userId, [{ lotsId: body.lotsId }]);
      await this.lotTemplatesHandler.create(userId, dto);
    } catch (error) {
      return new ErrorResponse(error, 'link');
    }
  }

  @Get('/lot/:lotsId')
  async getByLotsId(@Param('lotsId') lotsId: string, @Req() req: VBInterceptorRequest): Promise<TemplateResponse | ErrorResponse> {
    req;
    try {
      const lot: LotDto = await this.lotsHandler.get(lotsId);

      const template: TemplateDto = await this.handler.getByLotsId(lot.id);

      return new TemplateResponse(template);
    } catch (error) {
      return new ErrorResponse(error, 'getByLotsId');
    }
  }

  @Get('/barcode/:barcode/lot')
  async getLotTemplateByBarcode(@Param('barcode') barcode: string, @Req() req: VBInterceptorRequest): Promise<TemplateResponse | ErrorResponse> {
    req;
    try {
      const template: TemplateDto = await this.handler.getLotTemplateByBarcode(barcode);

      return this.createResponseFromDto(template);
    } catch (error) {
      return new ErrorResponse(error, 'getByBarcode');
    }
  }

  @Post('/')
  async create(@Body() body: TemplateRequest, @Req() req: VBInterceptorRequest): Promise<TemplateResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      let template: TemplateDto = this.createDtoFromRequest(body);
      const dto: LotDto | ProductDto = await this.getTemplate(body);

      let itemMapping: ItemMappingDto | undefined;
      try {
        itemMapping = await this.itemMappingsHandler.getMappingByProxyId(dto.id);
      } catch (error) {}
      template = await this.saveTemplate(userId, template, dto, itemMapping);

      return this.createResponseFromDto(template);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  protected async getTemplate(body: TemplateRequest): Promise<LotDto | ProductDto> {
    if (body.lotsId) {
      return await this.lotsHandler.get(body.lotsId);
    } else if (body.productsId) {
      return await this.productsHandler.get(body.productsId);
    } else {
      throw new TemplateMissingIdError();
    }
  }

  @Transactional()
  private async saveTemplate(userId: string, template: TemplateDto, dto: LotDto | ProductDto, itemMapping?: ItemMappingDto): Promise<TemplateDto> {
    if (!itemMapping) {
      const barcode: string | null = this.getBarcodeValue(template);
      itemMapping = await this.itemMappingsHandler.create(
        userId,
        new ItemMappingDto({
          mappingType: dto instanceof LotDto ? MappingType.Lots : MappingType.Products,
          proxyId: dto.id,
          barcode: barcode,
        }),
      );
    }
    template.itemMappingsId = itemMapping.id;

    return await this.handler.create(userId, template);
  }

  private getBarcodeValue(dto: any): string | null {
    // Check if dto.body is an object and contains the barcode property
    return typeof dto.body === 'object' && dto.body !== null && typeof dto.body.barcode === 'string' ? dto.body.barcode : null;
  }

  protected convertQueryToArray(uri: string, additionalParams?: { [key: string]: any }): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    searchParams.delete('page');
    searchParams.delete('size');

    const conditions: string[] = [];
    let query: string;

    if (searchParams.has('query') && (query = searchParams.get('query')!.toString()) !== '') {
      // Add conditions for searching within JSONB fields
      const jsonFields: Array<string> = ['tags', 'title', 'subtitle', 'consignorNumber', 'category', 'description', 'pickupLocation', 'suffix'];

      jsonFields.forEach((field: string) => {
        // Embed the query directly in the SQL condition
        conditions.push(`body ->> '${field}' ILIKE '%${query}%'`);
      });
      conditions.push(`name ILIKE '%${query}%'`);
    }

    additionalParams; //later

    return [conditions]; // Return the conditions array wrapped in an array as per the original return type
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<TemplatesListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const items: ListResultset<ListTemplateDto> = await this.handler.getAllAdmin(page, size, params);

      return new TemplatesListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Post('clone/:auctionId/templates/:templateId')
  @Transactional()
  async clone(@Param('auctionId') auctionId: string, @Param('templateId') templateId: string, @Req() req: VBInterceptorRequest): Promise<LotDetailedAdminResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const template: TemplateDto = await this.handler.get(templateId);
      const userId: string = req.accountMappingsId;

      const clonedLot: LotDto = await this.lotsHandler.cloneFromTemplate(userId, auction.id, template.id);
      const templateAuctionLot: AuctionLotDto = AuctionLotDto.buildFromTemplate(template, auction, clonedLot);
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.createFromTemplate(userId, auction, templateAuctionLot);
      //clone the images and documents
      this.uploadsController.clone(userId, template.getValueByKey('lotsId'), clonedLot.id);
      const lotAttributes: ListResultset<LotAttributeDto> = await this.lotAttributesHandler.clone(userId, template.getValueByKey('lotsId'), clonedLot.id);
      const { clonedLotImages, clonedLotDocs } = await this.sharedService.handleImageCloning(clonedLot.id, clonedLot.id, this.bucketName, this.logger);

      return new LotDetailedAdminResponse(
        clonedLot,
        auctionLot,
        clonedLotImages,
        clonedLotDocs,
        lotAttributes.list.map((item: LotAttributeDto) => new LotAttributeViewDto(item)),
      );
    } catch (error) {
      return new ErrorResponse(error, 'cloneAuctionLot');
    }
  }
}
