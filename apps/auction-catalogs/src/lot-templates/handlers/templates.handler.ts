import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { TemplatesDbService } from '../data/templates-db.service';
import { TemplateDto } from '../dtos/template.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { ListTemplateDto } from '../dtos/list-template.dto';

@Injectable()
export class TemplatesHand<PERSON> extends BaseHandler<TemplatesDbService, TemplateDto> {
  constructor(dbService: TemplatesDbService) {
    super(dbService);
  }

  async getLotTemplateByBarcode(barcode: string): Promise<TemplateDto> {
    return await this.dbService.getLotTemplateByBarcode(barcode);
  }

  async getByLotsId(lotsId: string): Promise<TemplateDto> {
    return await this.dbService.getByLotsId(lotsId);
  }

  async getAllAdmin(page: number, size: number, params: any[]): Promise<ListResultset<ListTemplateDto>> {
    return await this.dbService.getAllAdmin(page, size, params);
  }
}
