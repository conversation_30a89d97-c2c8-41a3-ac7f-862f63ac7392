import { BaseHandler } from '@mvc/handlers/base.handler';
import { LotTemplatesDbService } from '../data/lot-templates-db.service';
import { LotTemplateDto } from '../dtos/lot-template.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotTemplatesHandler extends BaseHandler<LotTemplatesDbService, LotTemplateDto> {
  constructor(dbService: LotTemplatesDbService) {
    super(dbService);
  }
}
