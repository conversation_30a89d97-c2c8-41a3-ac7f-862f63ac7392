import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TemplateDto } from '../dtos/template.dto';
import { Injectable } from '@nestjs/common';
import { TemplateNotFoundError } from '../exceptions/template-not-found.error';
import { PrismaService } from '../../prisma/prisma.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { ListTemplateDto } from '../dtos/list-template.dto';
import { LotTemplateNotFoundForLotError } from '../exceptions/lot-template-not-found-for-lot.error';
import { TemplateBarcodeNotFoundError } from '../exceptions/template-barcode-not-found.error';

@Injectable()
export class TemplatesDbService extends BaseDbService<Prisma.TemplatesDelegate, TemplateDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.templates);
  }

  mapToDto(model: any): TemplateDto {
    return new TemplateDto(model);
  }

  protected getOrderBy(): Record<string, 'asc' | 'desc'> {
    return { name: 'asc' };
  }

  async getLotTemplateByBarcode(barcode: string): Promise<TemplateDto> {
    const sql: Prisma.Sql = Prisma.sql`
    SELECT templates.*
    FROM public."Templates" templates
    INNER JOIN public."ItemMappings" mappings
    ON templates."itemMappingsId" = mappings.id
    WHERE mappings."mappingType" = 'Lots'
    AND templates.body->>'barcode' = ${barcode}
    LIMIT 1`;

    const dbResult: any[] = await this.prisma.$queryRaw<any[]>(sql);

    if (dbResult.length === 0) {
      throw new TemplateBarcodeNotFoundError(barcode);
    }

    return this.mapToDto(dbResult[0]);
  }

  async getByLotsId(lotsId: string): Promise<TemplateDto> {
    const sql: Prisma.Sql = Prisma.sql`
    SELECT templates.*
    FROM public."Templates" templates
    INNER JOIN public."LotTemplates" lot_templates
    ON templates.id = lot_templates."templatesId"
    WHERE lot_templates."lotsId" = ${lotsId}
    AND lot_templates."deletedOn" IS NULL
    AND templates."deletedOn" IS NULL`;

    const dbResult: any[] = await this.prisma.$queryRaw<any[]>(sql);

    if (dbResult.length === 0) {
      throw new LotTemplateNotFoundForLotError(lotsId);
    }

    return this.mapToDto(dbResult[0]);
  }

  throw404(id: string): Error {
    return new TemplateNotFoundError(id);
  }

  async getAllAdmin(page: number, size: number, params: any[]): Promise<ListResultset<ListTemplateDto>> {
    const conditions: any = params[0];
    const where: string = conditions.length > 0 ? ` WHERE ${conditions.join(' OR ')}` : '';
    const sqlQuery: string = `
      select t.id, t.name, im.barcode from public."Templates" t
      inner join public."ItemMappings" im on im.id = t."itemMappingsId"
      ${where}
      ORDER BY "id" ASC
      LIMIT ${size} OFFSET ${(page - 1) * size};
    `;

    const countQuery: string = `
    SELECT COUNT(*) FROM public."Templates"
    ${where};
  `;

    const [results, countResult] = await Promise.all<any[]>([this.prisma.$queryRawUnsafe(sqlQuery), this.prisma.$queryRawUnsafe(countQuery)]);
    const count: number = Number(countResult[0].count);

    return new ListResultset(
      results.map((row: any) => new ListTemplateDto(row)),
      page,
      size,
      count,
      Math.ceil(count / size),
    );
  }
}
