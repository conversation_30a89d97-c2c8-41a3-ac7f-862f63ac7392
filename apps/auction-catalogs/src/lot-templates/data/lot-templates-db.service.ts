import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { LotTemplateDto } from '../dtos/lot-template.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { LotTemplateNotFoundError } from '../exceptions/lot-template-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotTemplatesDbService extends BaseDbService<Prisma.LotTemplatesDelegate, LotTemplateDto> {
  constructor(prisma: PrismaService) {
    super(prisma.lotTemplates, true, false, true);
  }

  createFilter(params: any[]): any {
    const lotsId: string = params.find((r: any) => r.hasOwnProperty('lotsId'))?.lotsId;
    const templatesId: string = params.find((r: any) => r.hasOwnProperty('templatesId'))?.templatesId;

    return { lotsId, templatesId };
  }

  mapToDto(model: any): LotTemplateDto {
    return new LotTemplateDto(model);
  }

  getOrderBy(): any {}

  throw404(id: string): Error {
    throw new LotTemplateNotFoundError(id);
  }
}
