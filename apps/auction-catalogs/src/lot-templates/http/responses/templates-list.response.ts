import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { TemplateDto } from '../../dtos/template.dto';
import { ListTemplateDto } from '../../dtos/list-template.dto';

export class TemplatesListResponse extends ListResponse {
  constructor(resultSet: ListResultset<TemplateDto | ListTemplateDto>) {
    super(resultSet, 'templates', {});
  }
}
