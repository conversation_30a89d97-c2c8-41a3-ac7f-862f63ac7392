export class TemplateDto {
  id: string;
  itemMappingsId: string;
  name: string;
  body: any;

  constructor(row: any) {
    this.id = row.id;
    this.itemMappingsId = row.itemMappingsId;
    this.name = row.name;
    this.body = row.body;
  }

  getValueByKey(key: string): any {
    // Ensure that body is an object and the key exists in the body
    if (this.body && typeof this.body === 'object' && key in this.body) {
      return this.body[key]; // Return the value for the given key
    }
    return null; // Return null if the key doesn't exist
  }
}
