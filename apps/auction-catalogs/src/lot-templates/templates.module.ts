import { Module } from '@nestjs/common';
import { TemplatesController } from './controllers/templates.controller';
import { TemplatesDbService } from './data/templates-db.service';
import { TemplatesHandler } from './handlers/templates.handler';
import { ItemMappingsDbService } from '../item-mappings/data/item-mappings-db.service';
import { ItemMappingsHandler } from '../item-mappings/handler/item-mappings.handler';
import { LotsDbService } from '../lots/data/lots-db.service';
import { LotsHandler } from '../lots/handlers/lots.handler';
import { ProductsModule } from '../products/products.module';
import { LotTemplatesDbService } from './data/lot-templates-db.service';
import { LotTemplatesHandler } from './handlers/lot-templates.handler';
import { AuctionsDbService } from '../auction/data/auctions-db.service';
import { AuctionsHandler } from '../auction/handlers/auctions.handler';
import { AuctionLotsDbService } from '../auction-lots/data/auction-lots-db.service';
import { AuctionLotsHandler } from '../auction-lots/handlers/auction-lots.handler';
import { UploadsController } from '../uploads/controller/uploads.controller';
import { LotAttributesDbService } from '../lot-attributes/data/lot-attributes-db.service';
import { LotAttributesHandler } from '../lot-attributes/handler/lot-attributes.handler';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import { SharedService } from '../shared/shared.service';
import { BidIncrementsHandler } from '../bid-increments/handler/bid-increments.handler';
import { ApplicationPreferencesHandler } from '../application-preferences/handler/app-preferences.handler';
import { ApplicationPreferencesDbService } from '../application-preferences/data/app-preferences-db.service';
import { BidIncrementsDbService } from '../bid-increments/data/bid-increments-db.service';
import { UploadsService } from '../uploads/service/uploads.service';
import { UploadItemsService } from '../upload-items/service/upload-items.service';
import { S3Service } from '../aws/s3/data/s3.service';
import { ResourcesController } from '../resources/controller/resources.controller';
import { ConfigService } from '@nestjs/config';
import { ResourcesHandler } from '../resources/handler/resources.handler';
import { ResourcesDBService } from '../resources/service/resources-db.service';
import { ResourcesModule } from '../resources/resources.module';

@Module({
  controllers: [TemplatesController],
  providers: [
    ApplicationPreferencesDbService,
    ApplicationPreferencesHandler,
    AuctionsDbService,
    AuctionsHandler,
    AuctionLotsDbService,
    AuctionLotsHandler,
    BidIncrementFactory,
    BidIncrementsDbService,
    BidIncrementsHandler,
    ConfigService,
    ItemMappingsDbService,
    ItemMappingsHandler,
    LotAttributesDbService,
    LotAttributesHandler,
    LotsDbService,
    LotsHandler,
    ResourcesDBService,
    ResourcesController,
    ResourcesHandler,
    TemplatesDbService,
    TemplatesHandler,
    LotTemplatesDbService,
    LotTemplatesHandler,
    SharedService,
    S3Service,
    UploadItemsService,
    UploadsController,
    UploadsService,
  ],
  imports: [ProductsModule, ResourcesModule],
  exports: [TemplatesHandler],
})
export class TemplatesModule {}
