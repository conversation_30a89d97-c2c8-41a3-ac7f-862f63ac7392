import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@vb/config/src/config.service';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { AuctionLotsStatus } from '@viewbid/ts-node-client';
import { ListResultset } from '@mvc/data/list-resultset';
import { LotItemUserNotesHandler } from '../handler/lot-item-user-notes.handler';
import { AuctionLotListHighlightedDto } from '../../auction-lots/dto/auction-lot-list-highlighted.dto';

@Injectable()
export class DeleteLotItemUserNotesListener implements IConsumerHandler {
  private readonly logger = new Logger(DeleteLotItemUserNotesListener.name);
  private queue: string;
  constructor(
    private configService: ConfigService,
    private auctionLotsHandler: AuctionLotsHandler,
    private lotItemUserNotesHandler: LotItemUserNotesHandler,
  ) {}
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('LOT_ITEM_USER_NOTES_TO_DELETE_QUEUE_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  public async processQueueMessage(message: any): Promise<void> {
    try {
      const body: any = JSON.parse(message.Body);
      this.logger.log(`Deleting lot item uer notes for auction id: ${body.id}`);
      const auctionLotsList: ListResultset<AuctionLotListHighlightedDto> = await this.auctionLotsHandler.getAllByAuctionId(body.id, 1, -1, AuctionLotsStatus.Complete);
      if (auctionLotsList) {
        const lotIdList: string[] = auctionLotsList.list.map((auctionLot: AuctionLotListHighlightedDto) => {
          return auctionLot.lotId;
        });
        if (lotIdList.length > 0) {
          this.lotItemUserNotesHandler.deleteAllNotesByLotsIds(lotIdList);
        }
      }
    } catch (error) {
      this.logger.error(`Unable to delete lot item user notes : ${error}`);
    }
  }
}
