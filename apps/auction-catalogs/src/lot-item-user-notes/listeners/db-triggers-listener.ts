import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@vb/config/src/config.service';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { Client } from 'pg';

@Injectable()
export class DBTriggersListener implements OnModuleInit {
  private readonly logger = new Logger(DBTriggersListener.name);
  private client: Client;
  private targetArn: string;
  private deleteNotesDelay: string;

  constructor(
    private configService: ConfigService,
    private eventBridgeService: EventBridgeService,
  ) {}
  async onModuleInit(): Promise<void> {
    this.deleteNotesDelay = await this.configService.getSetting('DELETE_NOTES_DELAY', '30');
    this.targetArn = await this.configService.getSetting('LOT_ITEM_USER_NOTES_TO_DELETE_QUEUE_ARN');
    this.client = new Client({
      connectionString: await this.configService.getSetting('DATABASE_URL'),
    });
    this.client.connect();
    await this.listenToNotifications();
  }

  async listenToNotifications(): Promise<void> {
    await this.client.query('LISTEN auction_complete_channel'); // listening to triggers notifications channel not a db query

    this.client.on('notification', async (msg: any) => {
      try {
        if (msg.channel === 'auction_complete_channel' && msg.payload) {
          const auctionId: string = msg.payload;
          if (auctionId) {
            const scheduleDate: Date = new Date();
            scheduleDate.setDate(scheduleDate.getDate() + Number(this.deleteNotesDelay));
            this.eventBridgeService.manageScheduler(`${auctionId}_DELETE_LOT_ITEM_USER_NOTES`, auctionId, scheduleDate, this.targetArn);
          }
        }
      } catch (error) {
        this.logger.error(`Unable to schedule delete lot item user notes schedular: ${error}`);
      }
    });
  }
}
