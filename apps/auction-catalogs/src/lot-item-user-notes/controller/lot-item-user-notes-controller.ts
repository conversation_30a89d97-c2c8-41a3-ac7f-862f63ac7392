import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Controller, Get, Param, Patch, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { LotItemUserNoteDto } from '../dtos/lot-item-user-note.dto';
import { LotItemUserNotesHandler } from '../handler/lot-item-user-notes.handler';
import { LotItemUserNotesRequest } from '../http/requests/lot-item-user-notes.request';
import { LotItemUserNotesListResponse } from '../http/responses/lot-item-user-notes-list.response';
import { LotItemUserNotesResponse } from '../http/responses/lot-item-user-notes.response';

@Controller('lotItemUserNotes')
export class LotItemUserNotesController extends BaseController<
  LotItemUserNotesHandler,
  LotItemUserNotesRequest,
  LotItemUserNoteDto,
  LotItemUserNotesResponse,
  LotItemUserNotesListResponse
> {
  createDtoFromRequest(request: LotItemUserNotesRequest): LotItemUserNoteDto {
    return new LotItemUserNoteDto(request);
  }
  createResponseFromDto(dto: LotItemUserNoteDto): LotItemUserNotesResponse {
    return new LotItemUserNotesResponse(dto);
  }
  createResponseList(list: ListResultset<LotItemUserNoteDto>): LotItemUserNotesListResponse {
    return new LotItemUserNotesListResponse(list);
  }
  constructor(handler: LotItemUserNotesHandler) {
    super(handler);
  }

  @Get('getLotItemUserNotesByLotsId')
  async getLotItemUserNotesByLotsId(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('lotsId') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<LotItemUserNotesListResponse | ErrorResponse> {
    try {
      const params: Record<string, any>[] = this.buildFilterParams(req.accountMappingsId, query);
      const notes: ListResultset<LotItemUserNoteDto> = await this.handler.getLotItemUserNotesByLotsId(page, size, params);
      return this.createResponseList(notes);
    } catch (error) {
      return new ErrorResponse(error, 'getLotItemUserNotesByLotsId');
    }
  }

  buildFilterParams(accountMappingsId: string, lotsId: string): Record<string, any>[] {
    const params: Record<string, any>[] = [];
    params.push({ key: 'lotsId', value: lotsId });
    params.push({ key: 'accountMappingsId', value: accountMappingsId });
    params.push({ key: 'deletedBy', value: null });
    return params;
  }

  @Patch('restoreLotItemUserNote/:id')
  async restoreLotItemUserNoteById(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotItemUserNotesResponse | ErrorResponse> {
    try {
      return super.restore(id, req);
    } catch (error) {
      return new ErrorResponse(error, 'restoreLotItemUserNote');
    }
  }
}
