import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { LotItemUserNoteDto } from '../dtos/lot-item-user-note.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { LotItemUserNoteNotFoundError } from '../exceptions/lot-item-user-note-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotItemUserNoteDbService extends BaseDbService<Prisma.LotItemUserNotesDelegate, LotItemUserNoteDto> {
  createFilter(params: any[]): any {
    const result: any = params.reduce((obj: any, item: any) => {
      obj[item.key] = item.value;
      return obj;
    }, {});
    return result;
  }
  mapToDto(model: any): LotItemUserNoteDto {
    return new LotItemUserNoteDto(model);
  }
  throw404(id: string): Error {
    throw new LotItemUserNoteNotFoundError(id);
  }

  constructor(prisma: PrismaService) {
    super(prisma.lotItemUserNotes);
  }

  async deleteAllNotesByLotsIds(lotsIds: string[]): Promise<void> {
    await this.table.deleteMany({
      where: {
        lotsId: {
          in: lotsIds,
        },
      },
    });
  }
}
