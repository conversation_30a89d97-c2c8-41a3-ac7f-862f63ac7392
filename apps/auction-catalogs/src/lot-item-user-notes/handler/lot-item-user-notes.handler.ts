import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { LotItemUserNoteDbService } from '../data/lot-item-user-notes-db.service';
import { LotItemUserNoteDto } from '../dtos/lot-item-user-note.dto';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class LotItemUserNotesHandler extends BaseHandler<LotItemUserNoteDbService, LotItemUserNoteDto> {
  constructor(lotItemUserNoteDbService: LotItemUserNoteDbService) {
    super(lotItemUserNoteDbService);
  }

  async getLotItemUserNotesByLotsId(page: number, size: number, params: Record<string, any>[]): Promise<ListResultset<LotItemUserNoteDto>> {
    const result: ListResultset<typeof LotItemUserNoteDto> = await this.getAllCustom(page, size, params, LotItemUserNoteDto);
    return result as unknown as ListResultset<LotItemUserNoteDto>;
  }

  async deleteAllNotesByLotsIds(lotsIds: string[]): Promise<void> {
    await this.dbService.deleteAllNotesByLotsIds(lotsIds);
  }
}
