import { Modu<PERSON> } from '@nestjs/common';
import { LotItemUserNotesController } from './controller/lot-item-user-notes-controller';
import { LotItemUserNoteDbService } from './data/lot-item-user-notes-db.service';
import { LotItemUserNotesHandler } from './handler/lot-item-user-notes.handler';
import { SharedModule } from '../shared/shared.module';
import { SharedServicesModule } from 'libs/shared_services/shared.services.module';

@Module({
  controllers: [LotItemUserNotesController],
  providers: [LotItemUserNoteDbService, LotItemUserNotesHandler],
  imports: [SharedModule, SharedServicesModule],
})
export class LotItemUserNotesModule {}
