import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { Injectable, Inject, OnModuleInit } from '@nestjs/common';
import { ConfigService } from 'libs/config-service/src/config.service';
import { IUrl } from '@vb/nest/interfaces/iUrl.interface';

@Injectable()
export class AwsSqsService implements OnModuleInit {
  private SQS_QUEUE_URL: any;
  private client: SQSClient;
  queueUrls: IUrl;

  constructor(
    private configService: ConfigService,
    @Inject('queueUrls') queueUrls: IUrl,
  ) {
    this.queueUrls = queueUrls;
  }

  async onModuleInit(): Promise<void> {
    const messagesQueueUrl: string | undefined = await this.configService.getSetting('MESSAGES_QUEUE_URL');
    const region: string | undefined = await this.configService.getSetting('AWS_REGION');
    const nodeEnv: string | undefined = await this.configService.getSetting('NODE_ENV');
    const localstackEndpoint: string | undefined = await this.configService.getSetting('LOCALSTACK_ENDPOINT');

    this.SQS_QUEUE_URL = messagesQueueUrl;

    this.client = new SQSClient({
      region: region,
      endpoint: nodeEnv === 'local' ? localstackEndpoint : undefined,
    });
  }

  async sendMessage(messageBody: string, queue: string): Promise<any> {
    try {
      const command: SendMessageCommand = new SendMessageCommand({
        QueueUrl: this.queueUrls[queue],
        DelaySeconds: 0,
        MessageAttributes: {
          Title: {
            DataType: 'String',
            StringValue: 'ViewBid',
          },
          Author: {
            DataType: 'String',
            StringValue: 'ViewBid',
          },
          WeeksOn: {
            DataType: 'Number',
            StringValue: '6',
          },
        },
        MessageBody: messageBody,
      });
      const response: any = await this.client.send(command);
      return response;
    } catch (error) {
      return error;
    }
  }
}
