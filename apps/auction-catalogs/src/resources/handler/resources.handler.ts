import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { Injectable, Param } from '@nestjs/common';
import { ResourcesDBService } from '../service/resources-db.service';
import { ResourcesDto } from '../dto/resources.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import * as _ from 'lodash';

@Injectable()
export class ResourcesHandler extends BaseHandler<ResourcesDBService, ResourcesDto> {
  constructor(dbService: ResourcesDBService) {
    super(dbService);
  }

  async findByResourceType(resourceType: string): Promise<ResourcesDto> {
    return this.dbService.findByResourceType(resourceType);
  }

  async findByIds(@Param('ids') ids: string[]): Promise<ListResultset<ResourcesDto>> {
    const map: any = _.map(ids, (id: string) => {
      return { id: id.trim() };
    });
    const result: ListResultset<ResourcesDto> = await this.dbService.getAllCustom(-1, -1, map);
    //Don't like this - but it seems this is the only way to typecast from 'typeof ResourceDto' to 'ResourceDto' that I could find
    return result as unknown as ListResultset<ResourcesDto>;
  }
}
