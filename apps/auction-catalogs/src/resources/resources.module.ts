import { Module } from '@nestjs/common';
import { ResourcesController } from './controller/resources.controller';
import { ResourcesDBService } from './service/resources-db.service';
import { ResourcesHandler } from './handler/resources.handler';

@Module({
  controllers: [ResourcesController],
  providers: [ResourcesDBService, ResourcesController, ResourcesHandler],
  exports: [ResourcesDBService, ResourcesController],
})
export class ResourcesModule {}
