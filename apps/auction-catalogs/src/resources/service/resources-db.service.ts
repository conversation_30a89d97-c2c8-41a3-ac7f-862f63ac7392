import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ResourcesDto } from '../dto/resources.dto';
import { ResourcesNotFoundError } from '../exceptions/resources-not-found-error';
import * as _ from 'lodash';

@Injectable()
export class ResourcesDBService extends BaseDbService<Prisma.ResourcesDelegate, ResourcesDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.resources);
  }

  mapToDto(model: any): ResourcesDto {
    return new ResourcesDto(model);
  }

  createFilter(params: any[]): any {
    const queryParam: string[] = _.map(params, 'id');
    if (queryParam.length > 0) {
      return {
        AND: [
          {
            id: {
              in: queryParam,
            },
          },
        ],
      };
    }
    return {};
  }

  throw404(id: string): Error {
    return new ResourcesNotFoundError(id);
  }

  async findByResourceType(resourceType: string): Promise<ResourcesDto> {
    const resources: ResourcesDto | null = await this.prismaService.resources.findUnique({
      where: {
        resourceType: resourceType,
      },
    });

    if (resources == null || resources == undefined) {
      throw this.throw404(resourceType);
    }

    return this.mapToDto(resources);
  }
}
