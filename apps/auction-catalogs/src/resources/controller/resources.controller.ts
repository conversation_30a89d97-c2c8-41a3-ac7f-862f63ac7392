import { Controller, Get, Param } from '@nestjs/common';
import { BaseController } from '@mvc/controllers/base.controller';
import { ResourcesRequest } from '../http/requests/resources.request';
import { ResourcesDto } from '../dto/resources.dto';
import { ResourcesResponse } from '../http/responses/resources.response';
import { ResourcesListResponse } from '../http/responses/resources-list.response';
import { ResourcesHandler } from '../handler/resources.handler';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';

@Controller('resources')
export class ResourcesController extends BaseController<ResourcesHandler, ResourcesRequest, ResourcesDto, ResourcesResponse, ResourcesListResponse> {
  constructor(handler: ResourcesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ResourcesRequest): ResourcesDto {
    return new ResourcesDto(request);
  }

  createResponseFromDto(dto: ResourcesDto): ResourcesResponse {
    return new ResourcesResponse(dto);
  }

  createResponseList(list: ListResultset<ResourcesDto>): ResourcesListResponse {
    return new ResourcesListResponse(list);
  }

  @Get('byResourceType/:resourceType')
  async findByResourceType(@Param('resourceType') resourceType: string): Promise<ResourcesResponse> {
    try {
      const resources: ResourcesDto = await this.handler.findByResourceType(resourceType);
      return this.createResponseFromDto(resources);
    } catch (error: any) {
      return new ErrorResponse(error, 'resources', resourceType);
    }
  }

  @Get('/byIds/:ids')
  async findByIds(@Param('ids') ids: string): Promise<ResourcesListResponse> {
    const result: ListResultset<ResourcesDto> = await this.handler.findByIds(ids.split(','));
    return new ResourcesListResponse(result);
  }
}
