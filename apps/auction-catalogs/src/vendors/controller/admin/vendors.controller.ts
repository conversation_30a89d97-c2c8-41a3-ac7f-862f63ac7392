import { Controller } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { BaseController } from '@mvc/controllers/base.controller';
import { VendorDto } from '../../dtos/vendor.dto';
import { Vendors<PERSON>andler } from '../../handler/vendors.handler';
import { VendorResponse } from '../../http/responses/vendor.response';
import { VendorRequest } from '../../http/requests/vendor.request';
import { VendorListResponse } from '../../http/responses/vendor-list.response';

@Controller('admin/vendors')
export class VendorsController extends BaseController<VendorsHandler, VendorRequest, VendorDto, VendorResponse, VendorListResponse> {
  constructor(handler: VendorsHandler) {
    super(handler);
  }

  createDtoFromRequest(request: VendorRequest): VendorDto {
    return new VendorDto(request);
  }

  createResponseFromDto(dto: VendorDto): VendorResponse {
    return new VendorResponse(dto);
  }

  createResponseList(list: ListResultset<VendorDto>): VendorListResponse {
    return new VendorListResponse(list);
  }
}
