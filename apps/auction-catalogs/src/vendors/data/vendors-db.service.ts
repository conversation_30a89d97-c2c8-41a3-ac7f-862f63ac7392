import { Injectable } from '@nestjs/common';
import { DefaultArgs } from 'prisma/prisma-client/runtime/library';
import { BaseDbService } from '@mvc/data/base-db.service';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { VendorDto } from '../dtos/vendor.dto';
import { VendorNotFoundError } from '../exceptions/vendor-not-found.error';

@Injectable()
export class VendorsDbService extends BaseDbService<Prisma.VendorsDelegate<DefaultArgs>, VendorDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.vendors);
  }

  mapToDto(model: any): VendorDto {
    return new VendorDto(model);
  }

  createFilter(params: any[]): any {
    const query: string = params.find((r: any) => r.hasOwnProperty('query'))?.query ?? '';
    const ids: string[] = params.find((r: any) => r.hasOwnProperty('ids'))?.ids;

    return {
      name: {
        contains: query,
        mode: 'insensitive',
      },
      ...(ids
        ? {
            id: {
              in: ids,
            },
          }
        : {}),
    };
  }

  throw404(id: string): Error {
    throw new VendorNotFoundError(id);
  }
}
