import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { PubSubHandler } from '@vb/redis/handlers/pub-sub.handler';
import { AuctionLotsStatus } from '@viewbid/ts-node-client';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { BidIncrementDto } from '../../bid-increments/dtos/bid-increment.dto';
import { BidIncrementFactory } from '../../bid-increments/factory/bid-increment-factory';
import { SharedService } from '../../shared/shared.service';
import { ResourceType } from '../../uploads/entities/uploads.interfaces';
import { AuctionLotsDbService } from '../data/auction-lots-db.service';
import { AuctionLotFlattenedWatchlistDto } from '../dto/auction-lot-flattened-watchlist.dto';
import { AuctionLotFlattenedDto } from '../dto/auction-lot-flattened.dto';
import { AuctionLotListHighlightedDto } from '../dto/auction-lot-list-highlighted.dto';
import { AuctionLotDto } from '../dto/auction-lot.dto';
import { LotNumberDto } from '../dto/lot-number.dto';

@Injectable()
export class AuctionLotsHandler extends PubSubHandler<AuctionLotsDbService, AuctionLotDto> {
  constructor(
    dbService: AuctionLotsDbService,
    redisService: RedisService,
    private readonly bidIncrementFactory: BidIncrementFactory,
    private readonly sharedService: SharedService,
  ) {
    super(dbService, redisService, 'auctionLots');
  }

  async create(userId: string, request: AuctionLotDto): Promise<AuctionLotDto> {
    return await this.dbService.create(userId, request);
  }

  async getByAuctionIdAndNumber(auctionId: string, number: number): Promise<AuctionLotDto> {
    return await this.dbService.getByAuctionIdAndNumber(auctionId, number);
  }

  async getByLotId(lotId: string): Promise<AuctionLotDto> {
    return await this.dbService.getByLotId(lotId);
  }

  async getByLotIdAndAuctionId(lotId: string, auctionId: string): Promise<AuctionLotDto> {
    return await this.dbService.getByLotIdAndAuctionId(lotId, auctionId);
  }

  async count(auctionId: string): Promise<number> {
    return await this.dbService.count(auctionId);
  }

  async updateToDateTime(auctionLotId: string, toDateTime: Date, userId: string): Promise<AuctionLotDto> {
    return await this.dbService.updateToDateTime(auctionLotId, toDateTime, userId);
  }

  async getAllByAuctionId(
    auctionId: string,
    page: number,
    size: number,
    auctionLotsId?: string,
    params?: Record<string, any>,
  ): Promise<ListResultset<AuctionLotListHighlightedDto>> {
    return await this.dbService.getAllByAuctionId(auctionId, page, size, auctionLotsId, params);
  }

  async getAllWithLotsByAuctionIds(auctionId: string[], page: number, size: number, status: string): Promise<ListResultset<AuctionLotDto>> {
    return await this.dbService.getAllWithLotsByAuctionIds(auctionId, page ?? 0, size ?? -1, status);
  }

  async getWithLotAndAuction(id: string): Promise<AuctionLotDto> {
    return await this.dbService.getWithLotAndAuction(id);
  }

  async getAllByAuctionIds(auctionIds: string[], page?: number, size?: number): Promise<ListResultset<AuctionLotDto>> {
    return await this.dbService.getAllByAuctionIds(auctionIds, page ?? 0, size ?? -1);
  }

  async updateStatuses(auctionIds: string[], auctionLotsStatus: AuctionLotsStatus): Promise<number> {
    return await this.dbService.updateStatuses(auctionIds, auctionLotsStatus);
  }

  async updateStatus(auctionLotId: string, auctionLotsStatus: AuctionLotsStatus): Promise<number> {
    return await this.dbService.updateStatus(auctionLotId, auctionLotsStatus);
  }

  async getLotItemsGroupedByAuction(auctionIds: Array<string>): Promise<ListResultset<AuctionLotFlattenedDto>> {
    return await this.dbService.getLotItemsGroupedByAuction(auctionIds);
  }
  async updateBidDetails(auctionLot: AuctionLotDto, userId: string): Promise<AuctionLotDto> {
    return await this.dbService.updateBidDetails(auctionLot, userId);
  }
  async getNextAvailableLotNumber(auctionId: string): Promise<LotNumberDto> {
    return await this.dbService.getNextAvailableLotNumber(auctionId);
  }

  async ungroupAuctionLots(accountMappingsId: string, auctionId: string, groupId: string): Promise<void> {
    await this.dbService.ungroupAuctionLots(accountMappingsId, auctionId, groupId);
  }

  async ungroupAllLotsInAuction(accountMappingsId: string, auctionId: string): Promise<void> {
    await this.dbService.ungroupAllLotsInAuction(accountMappingsId, auctionId);
  }

  async getAuctionLotsByGroup(groupId: string, page: number, size: number, query?: string): Promise<ListResultset<AuctionLotFlattenedDto>> {
    return await this.dbService.getAuctionLotsByGroup(groupId, page, size, query);
  }

  async extendClosingTimeByGroup(auctionLotId: string, extensionTime: number): Promise<void> {
    await this.dbService.extendClosingTimeByGroup(auctionLotId, extensionTime);
  }

  async getAuctionLotByLotNumber(param: Record<string, any>[]): Promise<AuctionLotDto> {
    return await this.dbService.getAuctionLotByLotNumber(param);
  }

  async listAuctionLotsFlattenedByIds(
    page: number,
    size: number,
    auctionLotIds: Array<string>,
    additionalFilters?: Record<string, any>,
    currentUserId?: string,
  ): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    const auctionLots: ListResultset<AuctionLotFlattenedWatchlistDto> = await this.dbService.listAuctionLotsFlattenedByIds(
      page,
      size,
      auctionLotIds,
      additionalFilters,
      currentUserId,
    );

    await Promise.all(
      auctionLots.list.map(async (auctionLot: AuctionLotFlattenedWatchlistDto): Promise<void> => {
        const isCurrentBidZero: boolean = Number(auctionLot.currentBid) === 0;
        const validStartAmount: boolean = auctionLot.startAmount > 0;

        const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, auctionLot.currentBid);
        auctionLot.minimumBidAmount = isCurrentBidZero && validStartAmount ? auctionLot.startAmount : Number(auctionLot.currentBid) + bidIncrement.increment;
      }),
    );

    return auctionLots;
  }

  async getAllWatchedItems(page: number, size: number, auctionLotIds: Array<string>): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    return await this.dbService.getAllWatchedItems(page, size, auctionLotIds);
  }

  async getFlattenedById(auctionLotId: string): Promise<AuctionLotFlattenedDto> {
    const auctionLotDetails: AuctionLotFlattenedDto = await this.dbService.getFlattenedById(auctionLotId);
    const images: { [key: string]: any; id?: string | undefined }[] = await this.sharedService.getResourceURLById(auctionLotDetails.lotId, ResourceType.LOT_ITEM_IMAGES);
    auctionLotDetails.images = images
      ? images.map((image: { [key: string]: any; id?: string | undefined }) => ({
          url: image.url,
          name: image.name,
          type: image.type,
        }))
      : [];
    return auctionLotDetails;
  }

  async getFlattenedByIds(auctionLotIds: string[]): Promise<ListResultset<AuctionLotFlattenedDto>> {
    return await this.dbService.getFlattenedByIds(auctionLotIds);
  }

  async getAllAuctionLots(page: number, size: number, params: Record<string, any>, orderBy?: any): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    const auctionLots: ListResultset<AuctionLotFlattenedWatchlistDto> = await this.dbService.getAllWithLotsByIds(page, size, params, orderBy);
    await Promise.all(
      auctionLots.list.map(async (auctionLot: AuctionLotFlattenedWatchlistDto): Promise<void> => {
        const images: { [key: string]: any; id?: string | undefined }[] = await this.sharedService.getResourceURLById(auctionLot.lotId, ResourceType.LOT_ITEM_IMAGES);
        auctionLot.imageURL = images.length > 0 ? images[0].url : '';

        // Calculate minimumBidAmount
        const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, auctionLot.currentBid);
        auctionLot.minimumBidAmount = this.sharedService.calculateMinimumBidAmount(Number(auctionLot.currentBid), bidIncrement.increment, undefined, undefined);
      }),
    );
    return auctionLots;
  }

  async updateAuctionLotsToDateTimes(auctionId: string, delayInSeconds: number): Promise<void> {
    await this.dbService.updateAuctionLotsToDateTimes(auctionId, delayInSeconds);
  }

  async clone(userId: string, auctionId: string, lotId: string, clonedLotId: string): Promise<AuctionLotDto> {
    const lotNumber: LotNumberDto = await this.dbService.getNextAvailableLotNumber(auctionId);
    return await this.dbService.clone(userId, auctionId, lotId, clonedLotId, lotNumber);
  }

  async createFromTemplate(userId: string, auction: AuctionDto, templateAuctionLot: AuctionLotDto): Promise<AuctionLotDto> {
    const lotNumber: LotNumberDto = await this.dbService.getNextAvailableLotNumber(auction.id);
    templateAuctionLot.number = Number.parseInt(lotNumber.number);

    return await this.dbService.create(userId, templateAuctionLot);
  }

  async checkLastItemClosed(auctionId: string): Promise<AuctionLotDto> {
    return await this.dbService.checkLastItemClosed(auctionId);
  }
}
