import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { AuctionLotFlattenedWatchlistDto } from '../../dto/auction-lot-flattened-watchlist.dto';

export class AuctionLotsFlattenedListResponse extends ListResponse {
  constructor(resultSet: ListResultset<AuctionLotFlattenedWatchlistDto>) {
    super(resultSet, 'auctionLots');
  }
}
