import { IsDefined, IsNotEmpty, IsString } from 'class-validator';

export class AuctionLotsRequest {
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  auctionId: string;
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  lotId: string;
  number?: number;
  displayOrder?: number;
  status?: string;
  toDatetime?: string;
  lots?: any;
  buyNowPrice?: number;
  suffix: string;
  nextBidMeetsReserve: boolean;
}
