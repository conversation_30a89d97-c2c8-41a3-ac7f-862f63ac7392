import { AuctionLotsStatus, LotStatus } from '@viewbid/ts-node-client';
import { IsArray, IsDefined, IsEnum, IsNotEmpty } from 'class-validator';

export class AuctionLotsUpdateRequest {
  @IsDefined()
  @IsNotEmpty()
  @IsArray()
  auctionIds: Array<string>;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(AuctionLotsStatus)
  auctionLotsStatus: AuctionLotsStatus;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(LotStatus)
  lotStatus: LotStatus;
}
