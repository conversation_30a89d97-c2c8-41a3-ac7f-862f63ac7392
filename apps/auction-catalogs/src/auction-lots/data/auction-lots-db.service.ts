import { BaseDbService } from '@mvc/data/base-db.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { AuctionLotsStatus } from '@viewbid/ts-node-client';
import { ConfigService } from 'libs/config-service/src/config.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { PrismaService } from '../../prisma/prisma.service';
import { ResourceType } from '../../uploads/entities/uploads.interfaces';
import { AuctionLotFlattenedWatchlistDto } from '../dto/auction-lot-flattened-watchlist.dto';
import { AuctionLotFlattenedDto } from '../dto/auction-lot-flattened.dto';
import { AuctionLotListHighlightedDto } from '../dto/auction-lot-list-highlighted.dto';
import { AuctionLotDto } from '../dto/auction-lot.dto';
import { LotNumberDto } from '../dto/lot-number.dto';
import { InvalidAuctionLotError } from '../exceptions/invalid-auction-lot.error';
import { NoOpenAuctionLotsFoundError } from '../exceptions/no-open-auction-lots-found.error';
import sql = Prisma.sql;
import Sql = Prisma.Sql;

@Injectable()
export class AuctionLotsDbService extends BaseDbService<Prisma.AuctionLotsDelegate<DefaultArgs>, AuctionLotDto> implements OnModuleInit {
  public closedAuctionWindowDays: number;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
  ) {
    super(prismaService.auctionLots);
  }

  async onModuleInit(): Promise<void> {
    this.closedAuctionWindowDays = Number(await this.configService.getSetting('CLOSED_AUCTION_WINDOW_DAYS', '3'));
  }

  protected getExcludedFields(): (keyof AuctionLotDto)[] {
    return ['lots', 'auctions', 'taxPercentage'];
  }

  createFilter(params: any[]): any {
    if (params.length === 0) {
      return {};
    }

    return params.reduce((acc: Record<string, any>, param: any) => {
      Object.keys(param).forEach((key: string) => {
        const value: any = param[key];

        if (key === 'AND' || key === 'OR') {
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key] = acc[key].concat(value);
        } else if (key.includes('.')) {
          const keys: string[] = key.split('.');
          let current: Record<string, any> = acc;

          keys.forEach((k: string, index: number) => {
            if (index === keys.length - 1) {
              current[k] = value;
            } else {
              current[k] = current[k] || {};
              current = current[k];
            }
          });
        } else {
          acc[key] = value;
        }
      });

      return acc;
    }, {});
  }

  mapToDto(model: any): AuctionLotDto {
    return new AuctionLotDto(model);
  }
  mapToDetailedDto(model: any): AuctionLotFlattenedWatchlistDto {
    return new AuctionLotFlattenedWatchlistDto(model);
  }

  throw404(id?: string): Error {
    throw new InvalidAuctionLotError(id);
  }

  async create(userId: string, request: AuctionLotDto): Promise<AuctionLotDto> {
    const result: any = await this.prismaService.$queryRaw`
      SELECT COALESCE(MAX("displayOrder"), -1) + 1 as "nextDisplayOrder"
      FROM "AuctionLots"
      WHERE "auctionId" = ${request.auctionId}
      LIMIT 1
    `;
    request.displayOrder = result[0].nextDisplayOrder ?? 1;
    const filteredFields: any = this.removeExcludedFields(request);
    const data: any = {
      ...filteredFields,
      ...(this.hasUpdatedBy
        ? {
            createdBy: userId,
            createdOn: new Date().toISOString(),
          }
        : {}),
    };

    const createdItem: any = await this.table.create({ data });
    const dto: AuctionLotDto = this.mapToDto(createdItem);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getByAuctionIdAndNumber(auctionId: string, number: number): Promise<AuctionLotDto> {
    const item: any = await this.table.findFirst({
      where: {
        auctionId: auctionId,
        number: number,
        deletedOn: null,
      },
    });
    if (!item || item == null) {
      this.throw404(auctionId);
    }
    const dto: AuctionLotDto = this.mapToDto(item);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getByLotId(lotId: string): Promise<AuctionLotDto> {
    const item: any = await this.table.findFirst({
      where: {
        lotId: lotId,
        deletedOn: null,
        OR: [
          {
            toDatetime: {
              gt: new Date(),
            },
          },
          {
            toDatetime: {
              gte: new Date(new Date().setDate(new Date().getDate() - this.closedAuctionWindowDays)).toISOString(),
            },
            auctions: {
              isDisplaySoldItems: true,
            },
          },
        ],
      },
      include: {
        auctions: true,
      },
    });
    if (!item || item == null) {
      this.throw404(lotId);
    }

    const dto: AuctionLotDto = this.mapToDto(item);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getByLotIdAndAuctionId(lotId: string, auctionId: string): Promise<AuctionLotDto> {
    const item: any = await this.table.findFirst({
      where: {
        lotId: lotId,
        auctionId: auctionId,
        deletedOn: null,
      },
    });
    if (!item || item == null) {
      this.throw404(lotId);
    }

    const dto: AuctionLotDto = this.mapToDto(item);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getAllByAuctionId(
    auctionId: string,
    page: number,
    size: number,
    auctionLotsId?: string,
    params?: Record<string, any>,
  ): Promise<ListResultset<AuctionLotListHighlightedDto>> {
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    // Determine the structure of whereParams based on the input params
    let whereParams: Record<string, any>[];
    if (params !== undefined) {
      whereParams = Array.isArray(params[0]) ? params[0] : [params];
    } else {
      // If params is undefined, initialize with an empty array
      whereParams = [];
    }

    const { whereClause, whereValues } = this.buildWhereClause(whereParams);

    const query: string = `
      WITH OrderedLots AS (
        SELECT DISTINCT ON (al.id)
        al.id,
        al."number",
        al.suffix,
        al.status,
        al."auctionId",
        al."displayOrder",
        al."toDatetime",
        al."currentBid",
        al."buyNowPrice",
        l.id as "lotId",
        l.title,
        l.subtitle,
        l."reserveAmount",
        l.condition,
        l.region,
        l."thumbnailId",
        l.category,
        l."startAmount",
        l."isAcceptOffers",
        a."bidSnipeWindow",
        a."isReserveMinBid",
        ROW_NUMBER() OVER (ORDER BY al."number") AS row_num
      FROM
        public."AuctionLots" al
        LEFT JOIN public."Lots" l ON al."lotId" = l.id
        JOIN public."Auctions" a on a.id = al."auctionId"
      WHERE
        al."auctionId" = '${auctionId}'
        AND al."deletedOn" IS NULL
        AND l."deletedOn" IS NULL
        ${whereClause ? `AND ${whereClause}` : ''}
        AND (
          al."toDatetime" > NOW()
          OR (
            al."toDatetime" <= NOW()
            AND al."toDatetime" > (NOW() - (${this.closedAuctionWindowDays} || ' days')::interval)
            AND a."isDisplaySoldItems" = true
          )
        )
        ),
        TargetRow AS (
      SELECT
        COALESCE(
        (SELECT row_num FROM OrderedLots WHERE id ${auctionLotsId ? ` = '${auctionLotsId}'` : 'IS NOT NULL'}),
        1
        ) AS target_row
        ),
        PaginatedLots AS (
      SELECT
        ol.*,
        CASE
        WHEN ${auctionLotsId ? `'${auctionLotsId}'` : `NULL`} IS NOT NULL
          AND ol.id = ${auctionLotsId ? `'${auctionLotsId}'` : `NULL`}
          THEN true
          ELSE false
          END AS highlight
      FROM
        OrderedLots ol,
        TargetRow tr
        )
      SELECT *
      FROM
        PaginatedLots
      ORDER BY
        "number"
      LIMIT ${take} OFFSET ${skip};
    `;

    const countQuery: string = `
      WITH OrderedLots AS (
        SELECT DISTINCT ON (al.id)
          al.id,
          ROW_NUMBER() OVER (ORDER BY al."number") AS row_num
        FROM
          public."AuctionLots" al
          LEFT JOIN public."Lots" l ON al."lotId" = l.id
          JOIN public."Auctions" a ON a.id = al."auctionId"
        WHERE
          al."auctionId" = '${auctionId}'
          AND al."deletedOn" IS NULL
          AND l."deletedOn" IS NULL
          ${whereClause ? `AND ${whereClause}` : ''}
          AND (
            al."toDatetime" > NOW()
            OR (
              al."toDatetime" <= NOW()
              AND al."toDatetime" > (NOW() - (${this.closedAuctionWindowDays} || ' days')::interval)
              AND a."isDisplaySoldItems" = true
            )
          )
      )
      SELECT COUNT(*) AS "totalCount"
      FROM OrderedLots;
      `;
    const [items, count] = await Promise.all([
      this.prismaService.$queryRawUnsafe<any[]>(query, ...whereValues),
      this.prismaService.$queryRawUnsafe<{ totalCount: number }[]>(countQuery, ...whereValues),
    ]);
    const totalCount: number = Number(count[0].totalCount);
    const resultSet: ListResultset<AuctionLotListHighlightedDto> = new ListResultset(
      items.map((item: any) => new AuctionLotListHighlightedDto(item)),
      page,
      size,
      totalCount,
      Math.ceil(totalCount / size),
    );

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async getAllWithLotsByAuctionIds(auctionIds: string[], page: number, size: number, status: string): Promise<ListResultset<AuctionLotDto>> {
    const orderBy: any = this.getOrderBy() ?? {};
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    const [items, totalCount] = await Promise.all([
      this.table.findMany({
        where: {
          auctionId: {
            in: auctionIds,
          },
          status: status,
          deletedOn: null,
        },
        include: {
          lots: true,
        },
        orderBy: orderBy,
        take: take,
        skip: skip,
      }),
      this.table.count({
        where: {
          auctionId: {
            in: auctionIds,
          },
          deletedOn: null,
        },
      }),
    ]);

    const resultSet: ListResultset<AuctionLotDto> = new ListResultset(items.map(this.mapToDto), page, size, totalCount, Math.ceil(totalCount / size));

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async getAllByAuctionIds(auctionIds: string[], page: number, size: number): Promise<ListResultset<AuctionLotDto>> {
    const orderBy: any = this.getOrderBy() ?? {};
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    const [items, totalCount] = await Promise.all([
      this.table.findMany({
        where: {
          auctionId: {
            in: auctionIds,
          },
          deletedOn: null,
        },
        orderBy: orderBy,
        take: take,
        skip: skip,
      }),
      this.table.count({
        where: {
          auctionId: {
            in: auctionIds,
          },
          deletedOn: null,
        },
      }),
    ]);

    const resultSet: ListResultset<AuctionLotDto> = new ListResultset(items.map(this.mapToDto), page, size, totalCount, Math.ceil(totalCount / size));

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async addTaxesToAuctionLots(auctionLots: any[]): Promise<void> {
    if (!auctionLots || auctionLots.length === 0) {
      return;
    }

    const lotIds: string[] = auctionLots.map((item: any) => item.lotId);

    const taxTypeInfo: any[] = await this.prismaService.$queryRaw`
      SELECT l_t."lotsId", tt.percent
      FROM public."LotTaxes" l_t
      JOIN public."TaxTypes" tt ON tt.id = l_t."taxTypesId"
      WHERE l_t."lotsId" IN (${Prisma.join(lotIds)});
    `;
    const taxMap: { [key: string]: number } = taxTypeInfo.reduce((prev: any, cur: any) => {
      prev[cur.lotsId] = cur.percent;
      return prev;
    }, {});

    auctionLots.forEach((al: any) => {
      al.taxPercentage = taxMap[al.lotId];
    });
  }

  async updateStatus(auctionLotId: string, auctionLotsStatus: AuctionLotsStatus): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        id: auctionLotId,
      },
      data: {
        status: auctionLotsStatus,
      },
    });

    return item.count;
  }

  async updateStatuses(auctionIds: string[], auctionLotsStatus: AuctionLotsStatus): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        auctionId: {
          in: auctionIds,
        },
      },
      data: {
        status: auctionLotsStatus,
      },
    });

    return item.count;
  }

  async getWithLotAndAuction(id: string): Promise<AuctionLotDto> {
    const item: any = await this.table.findFirst({
      where: {
        id: id,
        deletedOn: null,
      },
      include: {
        lots: true,
        auctions: true,
      },
    });

    if (!item || item == null) {
      this.throw404(id);
    }

    const dto: AuctionLotDto = this.mapToDto(item);

    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async update(userId: string, request: AuctionLotDto, id: string): Promise<AuctionLotDto> {
    const dto: AuctionLotDto = await super.update(userId, request, id);

    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async get(id: string): Promise<AuctionLotDto> {
    const dto: AuctionLotDto = await super.get(id);

    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async count(auctionId: string): Promise<number> {
    const numberOfItmes: number = await this.table.count({
      where: {
        auctionId: auctionId,
        deletedOn: null,
      },
    });
    return numberOfItmes;
  }

  async updateBidDetails(auctionLot: AuctionLotDto, userId: string): Promise<AuctionLotDto> {
    const item: any = await this.table.update({
      where: {
        id: auctionLot.id,
      },
      data: {
        currentBid: auctionLot.currentBid,
        currentHighBidderId: userId,
      },
    });
    if (!item || item == null) {
      this.throw404(auctionLot.auctionId);
    }

    const dto: AuctionLotDto = this.mapToDto(item);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async updateToDateTime(auctionLotId: string, toDateTime: Date, userId: string): Promise<AuctionLotDto> {
    const item: any = await this.table.update({
      where: {
        id: auctionLotId,
      },
      data: {
        toDatetime: toDateTime,
        updatedBy: userId,
      },
    });
    if (!item || item == null) {
      this.throw404(auctionLotId);
    }

    const dto: AuctionLotDto = this.mapToDto(item);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getLotItemsGroupedByAuction(auctionIds: Array<string>): Promise<ListResultset<AuctionLotFlattenedDto>> {
    const auctionIdsString: string = auctionIds.map((id: string) => `'${id}'`).join(', '); // Prepare auction IDs for SQL

    const query: string = `WITH RankedAuctionLots AS (
      SELECT
        al.id AS id,
        al."auctionId",
        al."lotId",
        al.number,
        al."displayOrder",
        al.status,
        al."toDatetime",
        al."currentBid",
        al."currentHighBidderId",
        l.title,
        l.description,
        l.code,
        l."reserveAmount",
        l.tags,
        l.condition,
        l.category,
        l.region,
        l."pickupLocation",
        l."thumbnailId",
        ROW_NUMBER() OVER (PARTITION BY al."auctionId" ORDER BY al.number) AS rn
      FROM
        "public"."AuctionLots" al
          JOIN "public"."Lots" l ON al."lotId" = l.id
      WHERE
        al."deletedOn" IS NULL
        AND al."auctionId" IN (${auctionIdsString})
        AND (
          (SELECT status FROM "public"."Auctions" WHERE id = al."auctionId") != 'CLOSED'
          OR al."toDatetime" > (NOW() - (${this.closedAuctionWindowDays} || ' days')::interval)
        )

    ),
                                AuctionLotsWithImages AS (
                                  SELECT
                                    ral.id,
                                    ral."auctionId",
                                    ral."lotId",
                                    ral.number,
                                    ral."displayOrder",
                                    ral.status,
                                    ral."toDatetime",
                                    ral."currentBid",
                                    ral."currentHighBidderId",
                                    ral.title,
                                    ral.description,
                                    ral.code,
                                    ral."reserveAmount",
                                    ral.tags,
                                    ral.condition,
                                    ral.category,
                                    ral.region,
                                    ral."pickupLocation",
                                    ral.rn, -- Include the rn column from RankedAuctionLots
                                    concat(resources."cdnPath", 'thumbnails/', ral."thumbnailId") as "uploadFilename",
                                    ROW_NUMBER() OVER (PARTITION BY ral.id ORDER BY ui."uploadFilename") AS img_rn
                                  FROM
                                    RankedAuctionLots ral
                                      LEFT JOIN "public"."Upload" u ON ral."lotId" = u."proxyId"
                                      LEFT JOIN "public"."UploadItems" ui ON u.id = ui."uploadsId"::text
                                      LEFT JOIN "public"."Resources" resources ON resources.id = u."resourceId"::text and resources."resourceType" = '${ResourceType.LOT_ITEM_IMAGES}'
                                  ORDER BY "auctionId", ral."number"
                                )
                           SELECT
                             id AS id,
                             "auctionId",
                             "lotId",
                             "number",
                             "displayOrder",
                             status,
                             "toDatetime",
                             title,
                             description,
                             code,
                             "reserveAmount",
                             tags,
                             "condition",
                             category,
                             region,
                             "pickupLocation",
                             "uploadFilename" AS "images",
                             "currentBid",
                             "currentHighBidderId"
                           FROM
                             AuctionLotsWithImages
                           WHERE
                             rn <= 10
                             AND img_rn = 1
                           ORDER BY "auctionId", "number";
    `;
    const items: any = await this.prismaService.$queryRawUnsafe(query);

    const resultSet: ListResultset<AuctionLotFlattenedDto> = new ListResultset(
      items.map((row: any) => {
        return new AuctionLotFlattenedDto(row);
      }),
      1,
      items.size,
      items.size,
      1,
    );

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async getNextAvailableLotNumber(auctionId: string): Promise<LotNumberDto> {
    const result: any = await this.prismaService.$queryRaw`
    SELECT COALESCE(MAX("number")+1, 1)  as "nextLotNumber" FROM "AuctionLots"
    WHERE "auctionId" = ${auctionId}
    `;

    return new LotNumberDto(auctionId, result[0].nextLotNumber);
  }

  async getAuctionLotsByGroup(groupId: string, page: number, size: number, _query?: string): Promise<ListResultset<AuctionLotFlattenedDto>> {
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size > 0 ? size : undefined;

    // Base query
    const baseQuery: Sql = sql`
      SELECT
        al.id AS "id",
        al."auctionId",
        al."lotId",
        al.number,
        al."toDatetime",
        al."displayOrder",
        al.status,
        l.title,
        l."reserveAmount",
        l.condition,
        l.category,
        l.region,
        l."thumbnailId"
      FROM public."AuctionLots" al
      JOIN public."Lots" l ON l.id = al."lotId"
      WHERE al."groupsId" = ${groupId}::uuid and al."deletedBy" IS NULL
      ORDER BY al."displayOrder" ASC
      LIMIT ${take} OFFSET ${skip}
    `;

    // Query for counting the total number of records
    const queryCount: Sql = sql`
      SELECT COUNT(al.id) AS count
      FROM public."AuctionLots" al
      JOIN public."Lots" l ON l.id = al."lotId"
      WHERE al."groupsId" = ${groupId}::uuid and al."deletedBy" IS NULL
    `;

    const [items, countResult] = await Promise.all([
      this.prismaService.$queryRaw<AuctionLotFlattenedDto[]>(baseQuery),
      this.prismaService.$queryRaw<{ count: number }[]>(queryCount),
    ]);

    // Convert totalCount from BigInt to Number
    const totalCount: number = Number(countResult[0]?.count ?? 0n);

    const resultSet: ListResultset<AuctionLotFlattenedDto> = new ListResultset(
      items.map((row: AuctionLotFlattenedDto) => new AuctionLotFlattenedDto(row)),
      page,
      size,
      totalCount,
      Math.ceil(totalCount / size),
    );

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async ungroupAuctionLots(userId: string, auctionId: string, groupId: string): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        auctionId: auctionId,
        groupsId: groupId,
      },
      data: {
        updatedBy: userId,
        updatedOn: new Date().toISOString(),
        groupsId: null,
      },
    });

    return item.count;
  }

  async ungroupAllLotsInAuction(userId: string, auctionId: string): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        auctionId: auctionId,
      },
      data: {
        updatedBy: userId,
        updatedOn: new Date().toISOString(),
        groupsId: null,
      },
    });

    return item.count;
  }

  async extendClosingTimeByGroup(auctionLotId: any, newClosingTime: any): Promise<void> {
    const query: string = `
        UPDATE public."AuctionLots"
        SET "toDatetime" = "toDatetime" + ($2 || ' milliseconds')::interval
        WHERE "auctionId" = (
            SELECT "auctionId"
            FROM public."AuctionLots"
            WHERE id = $1
            )
          AND (
            "groupsId" = (
            SELECT "groupsId"
            FROM public."AuctionLots"
            WHERE id = $1
            )
           OR (
            "groupsId" IS NULL AND
            (SELECT "groupsId" FROM public."AuctionLots" WHERE id = $1) IS NULL)
            )
          AND id <> $1
          AND (
            "number" > (SELECT "number" FROM public."AuctionLots" WHERE id = $1)
           OR (
            "number" = (SELECT "number" FROM public."AuctionLots" WHERE id = $1)
          AND "suffix" > (SELECT "suffix" FROM public."AuctionLots" WHERE id = $1)
            )
            );

    `;

    await this.prismaService.$executeRawUnsafe(query, auctionLotId, newClosingTime);
  }

  async getAuctionLotByLotNumber(param: Record<string, any>[]): Promise<AuctionLotDto> {
    const { lotNumber, auctionId } = param[0];

    // Ensure the lotNumber is properly concatenated and lowercased
    const formattedLotNumber: string = `${lotNumber}`.toLowerCase();

    const result: any = await this.prismaService.$queryRaw`
      SELECT *
      FROM "AuctionLots"
      WHERE "auctionId" = ${auctionId} AND LOWER("number" || "suffix") = ${formattedLotNumber}
      LIMIT 1
    `;

    // Handle null or undefined result
    if (!result || result.length === 0) {
      this.throw404(`${auctionId}::${lotNumber}`);
    }

    const dto: AuctionLotDto = this.mapToDto(result[0]);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getAllWatchedItems(page: number, size: number, auctionLotIds: Array<string>): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    const result: any = await this.prismaService.$queryRaw`
    WITH first_upload_item AS (
      SELECT DISTINCT ON (l.id)
        l.id AS lotId,
        r."cdnPath" || r.id as "imageURL"
      FROM
        public."Lots" l
        LEFT JOIN public."Upload" u ON u."proxyId"::uuid = l.id::uuid
        LEFT JOIN public."UploadItems" ui ON ui."uploadsId" = u.id::uuid
        left join public."Resources" r on r.id::uuid = u."resourceId"::uuid
      ORDER BY
        l.id, ui."createdOn" DESC -- Assuming "createdOn" determines the order
    )
    SELECT
      a.id AS "auctionId",
      a."fromDatetime",
      a."toDatetime" AS "auctionToDatetime",
      a."pickupLocation",
      a."isDisplayFinalPrice",
      a.status AS "auctionStatus",
      a."buyerPremium",
      al."currentBid",
      al."toDatetime" AS "lotToDatetime",
      al."currentHighBidderId",
      al."number" || al.suffix AS "lotNumber",
      l.id as "lotId",
      l.title,
      l.status AS "lotStatus",
      l.condition,
      l."pickupLocation" AS "lotPickupLocation",
      l."reserveAmount",
      fi."imageURL"
    FROM
      public."Auctions" a
      JOIN public."AuctionLots" al ON al."auctionId"::uuid = a.id::uuid
      JOIN public."Lots" l ON l.id::uuid = al."lotId"::uuid
      LEFT JOIN first_upload_item fi ON l.id::uuid = fi.lotId::uuid
    WHERE
      al.id::uuid = ANY (ARRAY[${Prisma.join(auctionLotIds)}]::uuid[])
    ORDER BY al."toDatetime";
  `;

    const resultSet: ListResultset<AuctionLotFlattenedWatchlistDto> = new ListResultset<AuctionLotFlattenedWatchlistDto>(result, page, result.length, result.length, 1);

    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async listAuctionLotsFlattenedByIds(
    page: number,
    size: number,
    auctionLotIds: Array<string>,
    additionalFilters?: Record<string, any>,
    currentUserId?: string,
  ): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    const offset: number = (page - 1) * size;
    if (typeof auctionLotIds === 'string') {
      auctionLotIds = [auctionLotIds];
    }
    const whereClause: string = this.parseWatchlistFilters(additionalFilters ?? {}, currentUserId ?? '');

    const result: any = await this.prismaService.$queryRaw`
      WITH first_upload_item AS (
        SELECT DISTINCT ON (l.id)
            l.id AS lotId,
            r."cdnPath" || l."thumbnailId" AS "imageURL"
        FROM
            public."Lots" l
            LEFT JOIN public."Upload" u ON u."proxyId"::uuid = l.id::uuid
            LEFT JOIN public."UploadItems" ui ON ui."uploadsId" = u.id::uuid
            LEFT JOIN public."Resources" r ON r.id::uuid = u."resourceId"::uuid
        ORDER BY
            l.id, ui."createdOn" DESC
        ),
        lot_notes AS (
            SELECT
                l.id AS lotId,
                json_agg(json_build_object(
                    'id', ln.id,
                    'createdOn', ln."createdOn",
                    'createdBy', ln."createdBy",
                    'updatedOn', ln."updatedOn",
                    'updatedBy', ln."updatedBy",
                    'lotsId', ln."lotsId",
                    'accountMappingsId', ln."accountMappingsId",
                    'note', ln."note"
                )) AS notes
            FROM
                public."LotItemUserNotes" ln
                JOIN public."Lots" l ON ln."lotsId" = l.id AND ln."accountMappingsId" = ${currentUserId}
            WHERE
                ln."deletedOn" IS NULL
            GROUP BY
                l.id
        )
        SELECT
            a.id AS "auctionId",
            a."fromDatetime",
            a."toDatetime" AS "auctionToDatetime",
            a."pickupLocation",
            a."isDisplayFinalPrice",
            a."isDisplaySoldItems",
            a.status AS "auctionStatus",
            a."buyerPremium",
            a.name AS "auctionName",
            a."isProxyBid",
            a."isReserveMinBid",
            a."bidSnipeWindow",
            al.id AS "auctionLotId",
            al."currentBid",
            al."toDatetime" AS "lotToDatetime",
            al."currentHighBidderId",
            al."number" || al.suffix AS "lotNumber",
            l.id AS "lotId",
            l.title,
            l.status AS "lotStatus",
            l.condition,
            l."pickupLocation" AS "lotPickupLocation",
            l."reserveAmount",
            l."startAmount",
            l."isAcceptOffers",
            fi."imageURL",
            ln.notes AS "lotItemUserNotes",
            COUNT(*) OVER()::int AS "totalCount"
        FROM
            public."Auctions" a
            JOIN public."AuctionLots" al ON al."auctionId"::uuid = a.id::uuid
            JOIN public."Lots" l ON l.id::uuid = al."lotId"::uuid
            LEFT JOIN first_upload_item fi ON l.id::uuid = fi.lotId::uuid
            LEFT JOIN lot_notes ln ON l.id = ln.lotId
        WHERE al.id::uuid = ANY (ARRAY[${Prisma.join(auctionLotIds)}]::uuid[])
        AND (
            al."toDatetime" > NOW()
            OR (
                al."toDatetime" <= NOW()
                AND al."toDatetime" > (NOW() - (${this.closedAuctionWindowDays} || ' days')::interval)
                AND a."isDisplaySoldItems" = true
            )
        )
        ${Prisma.raw(`${whereClause}`)}
        ORDER BY
            al."toDatetime"
        LIMIT ${size} OFFSET ${offset};
        `;

    const totalCount: number = result.length > 0 ? Number(result[0].totalCount) : 0;

    const resultSet: ListResultset<AuctionLotFlattenedWatchlistDto> = new ListResultset<AuctionLotFlattenedWatchlistDto>(
      result,
      page,
      size,
      Number(totalCount),
      Math.ceil(totalCount / size),
    );
    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async getFlattenedById(auctionLotId: string): Promise<AuctionLotFlattenedDto> {
    const result: AuctionLotFlattenedDto[] = await this.prismaService.$queryRaw<AuctionLotFlattenedDto[]>`
      SELECT al.id as "id", al."auctionId", al."lotId", al."number", al.suffix, al."displayOrder",
             al."status", al."toDatetime", al."nextBidMeetsReserve", l.title, l.description, l.code, l."reserveAmount", l.tags,
            l.condition, l.category, l.region, l."pickupLocation", al."currentBid",
             al."currentHighBidderId", l."startAmount", l."isAcceptOffers"
      FROM public."AuctionLots" al
             JOIN public."Lots" l ON l.id = al."lotId"
      WHERE al.id = ${auctionLotId}
      LIMIT 1
    `;

    if (!result || result.length === 0) {
      this.throw404(auctionLotId);
    }

    const dto: AuctionLotFlattenedDto = new AuctionLotFlattenedDto(result[0]);
    await this.addTaxesToAuctionLots([dto]);

    return dto;
  }

  async getFlattenedByIds(auctionLotIds: string[]): Promise<ListResultset<AuctionLotFlattenedDto>> {
    const result: AuctionLotFlattenedDto[] = await this.prismaService.$queryRaw<AuctionLotFlattenedDto[]>`
        SELECT al.id as "id",
               al."auctionId",
               al."lotId",
               al."number",
               al.suffix,
               al."displayOrder",
               al."status",
               al."toDatetime",
               al."nextBidMeetsReserve",
               l.title,
               l.description,
               l.code,
               l."reserveAmount",
               l.tags,
               l.condition,
               l.category,
               l.region,
               l."pickupLocation",
               al."currentBid",
               al."currentHighBidderId",
               l."startAmount",
               l."isAcceptOffers"
        FROM public."AuctionLots" al
               JOIN public."Lots" l ON l.id = al."lotId"
        WHERE al.id IN  (${Prisma.join(auctionLotIds)})
      `;

    const resultSet: ListResultset<AuctionLotFlattenedDto> = new ListResultset<AuctionLotFlattenedDto>(result, 1, result.length, result.length, 1);
    await this.addTaxesToAuctionLots(resultSet.list);

    return resultSet;
  }

  async getAllWithLotsByIds(page: number, size: number, additionalFilters: Record<string, any>, orderBy: any = {}): Promise<ListResultset<AuctionLotFlattenedWatchlistDto>> {
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    const [items, totalCount] = await Promise.all([
      this.table.findMany({
        where: additionalFilters,
        include: {
          lots: true,
          auctions: true,
        },
        orderBy: orderBy,
        take: take,
        skip: skip,
      }),
      this.table.count({
        where: additionalFilters,
      }),
    ]);
    const resultSet: ListResultset<AuctionLotFlattenedWatchlistDto> = new ListResultset(items.map(this.mapToDetailedDto), page, size, totalCount, Math.ceil(totalCount / size));
    await this.addTaxesToAuctionLots(resultSet.list);
    return resultSet;
  }

  async updateAuctionLotsToDateTimes(auctionId: string, delayInSeconds: number = 60): Promise<void> {
    await this.prismaService.$executeRaw`
    WITH RankedAuctionLots AS (
        SELECT
            al."id",
            a."toDatetime",
            ROW_NUMBER() OVER (PARTITION BY al."auctionId", al."groupsId" ORDER BY al."number", al."suffix") AS rn
        FROM
            public."AuctionLots" al
        JOIN
            public."Auctions" a ON al."auctionId" = a."id"
        WHERE
            al."auctionId" = ${auctionId}
    )
    UPDATE public."AuctionLots" alt
    SET "toDatetime" = (
        SELECT RankedAuctionLots."toDatetime" + INTERVAL '1 second' * (RankedAuctionLots.rn - 1) * ${delayInSeconds}
        FROM RankedAuctionLots
        WHERE RankedAuctionLots.id = alt."id"
    )
    WHERE EXISTS (
        SELECT 1
        FROM RankedAuctionLots
        WHERE RankedAuctionLots.id = alt."id"
    );
    `;
  }

  // added this method because this is being used for raw query for custom filters
  private parseWatchlistFilters(additionalFilters: Record<string, any>, currentUserId: string): string {
    const bidConditions: string[] = [];
    const notesConditions: string[] = [];

    if (additionalFilters?.winning) {
      bidConditions.push(`al."currentHighBidderId" = '${currentUserId}' AND al."toDatetime" > NOW()`);
    }

    if (additionalFilters?.losing) {
      bidConditions.push(`al."currentHighBidderId" != '${currentUserId}' AND al."toDatetime" > NOW()`);
    }

    if (additionalFilters?.withNotes) {
      notesConditions.push(`ln.notes IS NOT NULL`);
    }

    if (additionalFilters?.withoutNotes) {
      notesConditions.push(`ln.notes IS NULL`);
    }

    const conditions: string[] = [];

    // Add bid conditions if they exist
    if (bidConditions.length > 0) {
      conditions.push(`(${bidConditions.join(' OR ')})`);
    }

    // Add notes conditions if they exist
    if (notesConditions.length > 0) {
      conditions.push(`(${notesConditions.join(' OR ')})`);
    }

    return conditions.length > 0 ? `AND ${conditions.join(' AND ')}` : '';
  }

  async clone(userId: string, auctionId: string, lotId: string, clonedLotId: string, lotNumber: LotNumberDto): Promise<AuctionLotDto> {
    const auctionLot: any = await this.prismaService.$queryRaw<AuctionLotDto>`
      INSERT INTO "AuctionLots" (
        "id", "createdOn", "createdBy",
        "auctionId", "lotId", "number", "suffix",
        "displayOrder", "status", "toDatetime",
        "currentBid", "currentHighBidderId", "buyNowPrice", "groupsId"
      )
        SELECT
          uuid_generate_v4(),
          NOW(),
          ${userId},
          ${auctionId},
          ${clonedLotId},
          ${lotNumber.number},
          '',
          "displayOrder",
          COALESCE("status", 'Open'),
          "toDatetime",
          0.00,
          '',
          "buyNowPrice",
          NULL
        FROM "AuctionLots"
        WHERE "lotId" = ${lotId} AND "auctionId" = ${auctionId} AND "deletedOn" IS NULL
          RETURNING *;
    `;

    const dto: AuctionLotDto = auctionLot[0];
    await this.addTaxesToAuctionLots([dto]);
    return dto;
  }

  async checkLastItemClosed(auctionId: string): Promise<AuctionLotDto> {
    const result: any = await this.table.findFirst({
      where: {
        auctionId: auctionId,
        toDatetime: {
          gt: new Date(),
        },
      },
      orderBy: {
        toDatetime: 'desc', // Order by toDatetime last one in descending order
      },
    });

    if (!result) {
      throw new NoOpenAuctionLotsFoundError(auctionId);
    }

    const dto: AuctionLotDto = this.mapToDto(result);
    await this.addTaxesToAuctionLots([dto]);
    return dto;
  }

  protected buildWhereClause(params: Record<string, any>[]): { whereClause: string; whereValues: any[] } {
    let whereClause: string = '';
    const whereValues: any[] = [];
    let paramIndex: number = 1;
    if (params == undefined || params.length < 1) return { whereClause, whereValues };

    // Loop through each condition provided in the params
    params.forEach((condition: Record<string, any>) => {
      if (condition.OR && Array.isArray(condition.OR)) {
        // Handle OR conditions
        let orConditions: string = '';
        condition.OR.forEach((orCondition: any) => {
          const [key, value]: [string, any] = Object.entries(orCondition)[0];
          if (key == 'al."groupsId"') {
            orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}::uuid`;
            whereValues.push(value.contains);
          } else if (isNaN(Number(value.contains))) {
            // String comparison, use ILIKE with wildcards
            orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE $${paramIndex}`;
            whereValues.push(`%${value.contains}%`);
          } else {
            // Numeric comparison, use '=' with type casting
            orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}::DECIMAL(10,2)`;
            whereValues.push(value.contains);
          }
          paramIndex++;
        });

        if (orConditions) {
          whereClause += `${whereClause ? ' AND ' : ''}(${orConditions})`;
        }
      } else {
        // Handle other single conditions if needed
        const [key, value]: [string, any] = Object.entries(condition)[0];
        if (key == 'l."status"') {
          if (value.not != undefined) {
            whereClause += `${whereClause ? ' AND ' : ''}LOWER(${key}) NOT LIKE LOWER($${paramIndex})`;
            whereValues.push(value.not);
          } else {
            whereClause += `${whereClause ? ' AND ' : ''}LOWER(${key}) = LOWER($${paramIndex})`;
            whereValues.push(value);
          }
        } else if (key.toLowerCase() == 'auctionlotsid') {
          // Do nothing.
        } else {
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        }

        paramIndex++;
      }
    });

    return { whereClause, whereValues };
  }
}
