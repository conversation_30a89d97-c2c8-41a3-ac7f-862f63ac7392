import { Injectable } from '@nestjs/common';
// import { CreateAuctionLotDto } from './dto/create-auction-lot.dto';
// import { UpdateAuctionLotDto } from './dto/update-auction-lot.dto';

@Injectable()
export class AuctionLotsService {
  create(/*createAuctionLotDto: CreateAuctionLotDto*/): any {
    return 'This action adds a new auctionLot';
  }

  findAll(): any {
    return `This action returns all auctionLots`;
  }

  findOne(id: number): any {
    return `This action returns a #${id} auctionLot`;
  }

  update(id: number /*updateAuctionLotDto: UpdateAuctionLotDto*/): any {
    return `This action updates a #${id} auctionLot`;
  }

  remove(id: number): any {
    return `This action removes a #${id} auctionLot`;
  }
}
