import { AuctionLotsErrorCodes } from '@mvc/exceptions/viewbid-error';
import { NotFoundViewbidError } from '@mvc/exceptions/not-found-viewbid.error';
export class AccountMappingNotFoundError extends NotFoundViewbidError {
  constructor(id?: string) {
    super('Account mappings were not found ' + id, AuctionLotsErrorCodes.AUCTION_LOTS_ACCOUNT_MAPPINGS_NOT_FOUND, id, 'account-mappings#not-found');
  }
}
