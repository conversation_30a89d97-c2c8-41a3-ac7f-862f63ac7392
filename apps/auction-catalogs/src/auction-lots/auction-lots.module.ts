import { <PERSON>du<PERSON>, forwardRef, Logger } from '@nestjs/common';
import { AuctionLotsController } from './controller/auction-lots.controller';
import { UploadsModule } from '../uploads/uploads.module';
import { AuctionLotsDbService } from './data/auction-lots-db.service';
import { AuctionLotsHandler } from './handlers/auction-lots.handler';
import { RedisService } from '@vb/redis';
import { ApplicationPreferencesModule } from '../application-preferences/app-preferences.module';
import { AuctionModule } from '../auction/auction.module';
import { LotsModule } from '../lots/lots.module';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import { BidIncrementsHandler } from '../bid-increments/handler/bid-increments.handler';
import { BidIncrementsDbService } from '../bid-increments/data/bid-increments-db.service';
import { SharedService } from '../shared/shared.service';
import { GroupsModule } from '../groups/groups.module';
import { GroupsDbService } from '../groups/data/groups-db.service';
import { GroupsHandler } from '../groups/handlers/groups.handler';
import { LotItemUserNoteDbService } from '../lot-item-user-notes/data/lot-item-user-notes-db.service';
import { LotItemUserNotesHandler } from '../lot-item-user-notes/handler/lot-item-user-notes.handler';
import { LotAttributesHandler } from '../lot-attributes/handler/lot-attributes.handler';
import { LotAttributesDbService } from '../lot-attributes/data/lot-attributes-db.service';
import { ApplicationPreferencesHandler } from '../application-preferences/handler/app-preferences.handler';
import { ApplicationPreferencesDbService } from '../application-preferences/data/app-preferences-db.service';
import { S3Module } from '../aws/s3/s3.module';
import { ResourcesModule } from '../resources/resources.module';
import { SharedModule } from '../shared/shared.module';

@Module({
  controllers: [AuctionLotsController],
  exports: [AuctionLotsController, AuctionLotsDbService, AuctionLotsHandler],
  providers: [
    ApplicationPreferencesDbService,
    ApplicationPreferencesHandler,
    AuctionLotsController,
    AuctionLotsDbService,
    AuctionLotsHandler,
    BidIncrementFactory,
    BidIncrementsHandler,
    BidIncrementsDbService,
    GroupsDbService,
    GroupsHandler,
    Logger,
    LotAttributesDbService,
    LotAttributesHandler,
    LotItemUserNotesHandler,
    LotItemUserNoteDbService,
    RedisService,
    SharedService,
  ],
  imports: [
    forwardRef(() => UploadsModule),
    ApplicationPreferencesModule,
    forwardRef(() => AuctionModule),
    forwardRef(() => LotsModule),
    forwardRef(() => GroupsModule),
    forwardRef(() => SharedModule),
    S3Module,
    ResourcesModule,
  ],
})
export class AuctionLotsModule {}
