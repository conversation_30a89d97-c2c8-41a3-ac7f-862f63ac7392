import { GetScheduleCommandOutput } from '@aws-sdk/client-scheduler';
import { ListResultset } from '@mvc/data/list-resultset';
import { EmptyListResponse } from '@mvc/http/responses/empty-list.response';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { SuccessResponse } from '@mvc/http/responses/success.response';
import { Body, Controller, Delete, Get, OnModuleInit, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { Transactional } from '@transactional/core';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { AuctionLotsPostRequest, AuctionLotsUpdateRequest, BiddingApi, LotBiddingData, LotStatus, Response as ViewbidResponse } from '@viewbid/ts-node-client';
import * as axios from 'axios';
import { ConfigService } from 'libs/config-service/src/config.service';
import _ from 'lodash';
import { WatcherDto } from '../../../../bidding/src/watchers/dtos/watcher.dto';
import { Constants as AuctionConstants } from '../../auction/config/constants';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { AuctionsListWithAuctionLotsResponse } from '../../auction/http/response/auctions-list-with-auction-lots.response';
import { BidIncrementDto } from '../../bid-increments/dtos/bid-increment.dto';
import { BidIncrementFactory } from '../../bid-increments/factory/bid-increment-factory';
import { GroupDto } from '../../groups/dtos/group.dto';
import { GroupNotFoundError } from '../../groups/exceptions/group-not-found.error';
import { GroupsHandler } from '../../groups/handlers/groups.handler';
import { LotAttributeViewDto } from '../../lot-attributes/dtos/lot-attribute-view.dto';
import { LotAttributeDto } from '../../lot-attributes/dtos/lot-attribute.dto';
import { LotAttributesHandler } from '../../lot-attributes/handler/lot-attributes.handler';
import { LotBiddingDataDto } from '../../lots/dtos/lot-bidding-data.dto';
import { LotDto } from '../../lots/dtos/lot.dto';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { LotDetailedAdminResponse } from '../../lots/http/responses/lot-detailed-admin.response';
import { SharedService } from '../../shared/shared.service';
import { UploadsController } from '../../uploads/controller/uploads.controller';
import { ResourceFile, ResourceType, UploadsByResourceResponse } from '../../uploads/entities/uploads.interfaces';
import { AuctionLotDetailedHighlightedDto } from '../dto/auction-lot-detailed-highlighted.dto';
import { AuctionLotDetailedDto } from '../dto/auction-lot-detailed.dto';
import { AuctionLotFlattenedWatchlistDto } from '../dto/auction-lot-flattened-watchlist.dto';
import { AuctionLotFlattenedDto } from '../dto/auction-lot-flattened.dto';
import { AuctionLotImagesDto } from '../dto/auction-lot-images.dto';
import { AuctionLotListHighlightedDto } from '../dto/auction-lot-list-highlighted.dto';
import { AuctionLotDto } from '../dto/auction-lot.dto';
import { LotNumberDto } from '../dto/lot-number.dto';
import { AuctionLotBiddingDataNotFoundError } from '../exceptions/auction-lot-bidding-data-not-found.error';
import { InvalidAuctionLotError } from '../exceptions/invalid-auction-lot.error';
import { AuctionLotsHandler } from '../handlers/auction-lots.handler';
import { AddToGroupRequest } from '../http/requests/add-to-group.request';
import { AuctionLotsRequest } from '../http/requests/auction-lots-request';
import { FilteredAuctionLotsRequest } from '../http/requests/filtered-auction-lots-request';
import { UpdateGroupRequest } from '../http/requests/update-group.request';
import { AuctionLotResponse } from '../http/responses/auction-lot-response';
import { AuctionLotsDetailedResponse } from '../http/responses/auction-lots-detailed-response';
import { AuctionLotsFlattenedListResponse } from '../http/responses/auction-lots-flattened-list.response';
import { AuctionLotsListResponse } from '../http/responses/auction-lots-list-response';
import { AuctionLotsPatchToDateTimeResponse } from '../http/responses/auction-lots-patch-to-date-time-response';
import { AuctionLotsUpdateResponse } from '../http/responses/auction-lots-update-response';
import { LotNumberResponse } from '../http/responses/lot-number.response';
import { Cacheable } from '@vb/caching/decorators/cacheable.decorator';
import { AuctionLotsCachingService } from '../../services/auction-lots-caching.service';

@Controller('auction-lots')
export class AuctionLotsController
  extends PubSubController<AuctionLotsHandler, AuctionLotsRequest, AuctionLotDto, AuctionLotResponse, AuctionLotsListResponse>
  implements OnModuleInit
{
  private snipeBuffer: number;
  private snipeWindow: number;
  private delayInMinutes: number;
  private targetArn: string;
  private lotItemClosedARN: string;
  private isLastLotClosedARN: string;
  private bucketName: string = '';
  private checkLastClosingLotDelay: number;
  private lotClosedDelayInMinutes: number;
  private readonly lotClosingStaggeredBetweenLotSeconds: number;
  public closedAuctionWindowDays: number;

  constructor(
    handler: AuctionLotsHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly lotsHandler: LotsHandler,
    private readonly configService: ConfigService,
    private readonly biddingApi: BiddingApi,
    private readonly uploadsController: UploadsController,
    redisService: RedisService,
    private readonly eventBridgeService: EventBridgeService,
    private readonly bidIncrementFactory: BidIncrementFactory,
    private readonly sharedService: SharedService,
    private readonly groupsHandler: GroupsHandler,
    private readonly lotAttributesHandler: LotAttributesHandler,
  ) {
    super(handler, redisService, 'AUCTIONLOTS');
  }

  async onModuleInit(): Promise<void> {
    this.delayInMinutes = Number(await this.configService.getSetting('LOT_ENDING_SOON_DELAY_IN_MINUTES', '-15'));
    this.lotClosedDelayInMinutes = Number(await this.configService.getSetting('LOT_CLOSED_DELAY_IN_MINUTES', '10'));
    this.targetArn = await this.configService.getSetting('LOT_ENDING_SOON_QUEUE_ARN');
    this.lotItemClosedARN = await this.configService.getSetting('LOT_ITEM_CLOSED_QUEUE_ARN');
    this.isLastLotClosedARN = await this.configService.getSetting('IS_LAST_LOT_CLOSED_QUEUE_ARN');
    const templateBucketName: string | undefined = await this.configService.getSetting('TEMPLATE_BUCKET_NAME');
    this.checkLastClosingLotDelay = Number(await this.configService.getSetting('CHECK_LAST_CLOSING_LOT_DELAY'));
    this.snipeBuffer = Number(await this.configService.getSetting('SNIPE_BUFFER'));
    this.snipeWindow = Number(await this.configService.getSetting('SNIPE_WINDOW', '60000'));
    this.closedAuctionWindowDays = Number(await this.configService.getSetting('CLOSED_AUCTION_WINDOW_DAYS', '3'));

    if (!templateBucketName) {
      this.logger.error('Auction Catalog Service was not provided with the required env variable: TEMPLATE_BUCKET_NAME');
      process.kill(process.pid, 'SIGTERM');
    } else {
      this.bucketName = templateBucketName ?? '';
    }
  }

  createDtoFromRequest(request: AuctionLotsRequest): AuctionLotDto {
    return new AuctionLotDto(request);
  }

  createResponseFromDto(dto: AuctionLotDto): AuctionLotResponse {
    return new AuctionLotResponse(dto);
  }
  createResponseList(list: ListResultset<AuctionLotDto>): AuctionLotsListResponse {
    return new AuctionLotsListResponse(list);
  }

  @Post('/')
  async create(@Body() body: AuctionLotsRequest, @Req() req: VBInterceptorRequest): Promise<AuctionLotResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const dto: AuctionLotDto = this.createDtoFromRequest(body);
      const item: AuctionLotDto = await this.handler.create(userId, dto);
      return this.createResponseFromDto(item);
    } catch (error) {
      this.logger.error("Error caught in POST 'create': " + error);

      return new ErrorResponse(error, 'createAuctionLot');
    }
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: AuctionLotsRequest, @Req() req: VBInterceptorRequest): Promise<AuctionLotResponse | ErrorResponse> {
    this.logger.log('attempting to update auction lot');
    return this.executeAndNotify(
      'update',
      async () => {
        const userId: string = req.accountMappingsId;
        const dto: AuctionLotDto = this.createDtoFromRequest(body);
        if (dto.nextBidMeetsReserve) {
          this.lotsHandler.update(userId, new LotDto({ reserveAmount: dto.currentBid, reason: 'admin override reserve' }), dto.lotId);
          dto.nextBidMeetsReserve = !dto.nextBidMeetsReserve;
        }
        const item: AuctionLotDto = await this.handler.update(userId, dto, id);
        return this.createResponseFromDto(item);
      },
      body,
    );
  }

  //Relocated endpoints
  @Patch('updateAuctionLotsToDateTime')
  @Transactional()
  async updateAuctionLotsToDateTime(
    @Query('auctionLotsId') auctionLotsId: string,
    @Query('bidTime') bidTime: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionLotResponse | ErrorResponse> {
    try {
      let auctionLot: AuctionLotDto = await this.handler.get(auctionLotsId);
      const bidTimeDate: Date = new Date(bidTime);
      auctionLot = await this.handler.updateToDateTime(auctionLot.id, bidTimeDate, req.accountMappingsId);

      try {
        //Update Schedule because times have changed.
        const lotClosedSchedule: GetScheduleCommandOutput | null = await this.eventBridgeService.getScheduleDetails(`${auctionLot.id}_LOT_CLOSED`);

        if (lotClosedSchedule?.ScheduleExpression) {
          const scheduledTime: string = this.getEventScheduledTimeValue(lotClosedSchedule.ScheduleExpression); // added this step to get the scheduled time because we receive it as at(yyyy-MM-dd HH:mm:ssZ)
          if (scheduledTime && bidTimeDate.getTime() >= new Date(`${scheduledTime}Z`).getTime()) {
            await this.eventBridgeService.manageScheduler(`${auctionLot.id}_LOT_CLOSED`, auctionLot.id, bidTimeDate, this.lotItemClosedARN, this.lotClosedDelayInMinutes);
          }
        }
      } catch (error) {
        this.logger.error("Error caught in PATCH 'updateAuctionLotsToDateTime': " + error);
        this.logger.error(`Could not update the schedule for LOT_CLOSED ${auctionLot.id}`);
      }

      return new AuctionLotResponse(new AuctionLotsPatchToDateTimeResponse(auctionLot.id, auctionLot.toDatetime ?? ''));
    } catch (error) {
      this.logger.error("Error caught in PATCH 'updateAuctionLotsToDateTime': " + error);
      return new ErrorResponse(error, 'updateAuctionLotsToDateTime');
    }
  }

  // TODO MAJOR - ONLY DOING THIS BECAUSE WE DON'T HAVE TIME TO FIGURE OUT ISSUE WITH LOT STATUS SPEC ISSUES.
  // See KENO / KULWINDER / ALLAN for answer for this.
  @Anonymous()
  @Get('/m/getLotsByAuctionId/:id')
  async getLotsByAuctionIdMobile(
    @Param('id') auctionId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('status') status: string,
    @Query('query') query: string,
    @Query('auctionLotsId') auctionLotsId: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionLotsListResponse | ErrorResponse> {
    return await this.getLotsByAuctionId(auctionId, page, size, status, query, auctionLotsId, req);
  }
  @Cacheable()
  @Anonymous()
  @Get('getLotsByAuctionId/:id')
  async getLotsByAuctionId(
    @Param('id') auctionId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('status') _status: string,
    @Query('query') _query: string,
    @Query('auctionLotsId') auctionLotsId: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionLotsListResponse | ErrorResponse> {
    try {
      const filterParams: any[] = this.convertQueryToArray(req.url);
      const auctionLots: ListResultset<AuctionLotListHighlightedDto> = await this.handler.getAllByAuctionId(auctionId, page, size, auctionLotsId, { 0: filterParams });

      const lotIds: string[] = this.getLotIdsFromList(auctionLots);
      const auctionLotIds: string[] = this.getAuctionLotIdsFromList(auctionLots);

      const biddingDataList: LotBiddingData[] = await this.getBiddingDataForAuctionLot(auctionLotIds.toString());
      const lotBiddingDataMap: Map<string, LotBiddingData | undefined> = new Map<string, LotBiddingData | undefined>();
      biddingDataList.forEach((lot: LotBiddingData) => {
        if (lot.auctionLotId) {
          lotBiddingDataMap.set(lot.auctionLotId, lot);
        }
      });

      // Initialize auctionLotList with an empty ListResultset
      const auctionLotDetailedList: ListResultset<AuctionLotDetailedHighlightedDto> = await this.convertToDetailedHighlightedDto(auctionLots, lotBiddingDataMap);
      let auctionLotList: ListResultset<AuctionLotDetailedDto | AuctionLotDetailedHighlightedDto> = new ListResultset([], 0, 0, 0, 0);

      if (auctionLotDetailedList) {
        auctionLotList = await this.sharedService.getImagesAndDocumentByLotId(lotIds, auctionLotDetailedList);
      }

      //If the req.accountMappingsId is null/undefined it's because it's being called from a different service which doesn't pass the related account mapping id
      return new AuctionLotsListResponse(await this.mapWatchedItems(auctionLotList, req.accountMappingsId ?? ''));
    } catch (error) {
      this.logger.error("Error caught in GET 'getLotsByAuctionId': " + error);

      return new ErrorResponse(error, 'getLotsByAuctionId');
    }
  }

  private async mapWatchedItems(auctionLots: ListResultset<AuctionLotDetailedDto>, accountMappingsId: string): Promise<ListResultset<AuctionLotDetailedDto>> {
    if (accountMappingsId === '') {
      return auctionLots;
    }
    const watchedItems: Array<WatcherDto> = await this.getWatchedItems(accountMappingsId);

    const watchedItemsMap: Record<string, string> = watchedItems.reduce(
      (acc: Record<string, string>, watcher: WatcherDto) => {
        // For each watcher, add an entry in the accumulator map
        // The key is the auctionLotsid and the value is the id of the watcher
        acc[watcher.auctionLotsid] = watcher.id;
        return acc; // Return the updated accumulator map
      },
      {} as Record<string, string>, // Initialize the accumulator as an empty object of type Record<string, string>
    );

    // Map watcherId to the corresponding auctionLot
    const updatedAuctionLotsList: Array<AuctionLotDetailedDto> = auctionLots.list.map((auctionLot: AuctionLotDetailedDto) => {
      const watcherId: string = watchedItemsMap[auctionLot.id!];
      return {
        ...auctionLot,
        watcherId: watcherId || null, // Add watcherId if exists, otherwise null
      };
    });

    // Return the updated ListResultset with the amended auctionLots
    return new ListResultset<AuctionLotDetailedDto>(updatedAuctionLotsList, auctionLots.page, auctionLots.pageSize, auctionLots.totalCount, auctionLots.totalPages);
  }

  private async getWatchedItems(accountMappingsId: string): Promise<Array<WatcherDto> | []> {
    const watchedItems: any = await this.biddingApi.watchedItemsByUser(accountMappingsId, 1, -1);
    if (!watchedItems.data.data) {
      //nothing to watch
      return [];
    }

    return watchedItems.data.data;
  }

  @Anonymous()
  @Get('byLotByAuctionId/:auctionId/lot/:lotId')
  async getLotByAuctionId(@Param('auctionId') auctionId: string, @Param('lotId') lotId: string): Promise<AuctionLotsDetailedResponse | ErrorResponse> {
    try {
      const auctionLot: AuctionLotDto = await this.handler.getByLotIdAndAuctionId(lotId, auctionId);

      return new AuctionLotsDetailedResponse(auctionLot);
    } catch (error) {
      this.logger.error("Error caught in GET 'getLotByAuctionId': " + error);

      return new ErrorResponse(error, 'getLotByAuctionId');
    }
  }

  @Anonymous()
  @Get('getAuctionLotsById/:id')
  async getAuctionLotsById(@Param('id') id: string): Promise<AuctionLotsDetailedResponse | ErrorResponse> {
    try {
      const auctionLot: AuctionLotDto = await this.handler.getWithLotAndAuction(id);
      if (!auctionLot.lots || !auctionLot.auctions) {
        throw new InvalidAuctionLotError(id);
      }

      return Promise.all([
        this.sharedService.getResourceURLById(auctionLot.lotId, ResourceType.LOT_ITEM_IMAGES),
        this.sharedService.getResourceURLById(auctionLot.auctionId, ResourceType.AUCTION_IMAGES),
        this.getBiddingDataForAuctionLot(auctionLot.id),
      ]).then(async (value: any[]) => {
        const lotUpload: Array<{ url: string; name: string; type: string; id?: string }> = value[0];
        const auctionUpload: Array<{ url: string; name: string; type: string; id?: string }> = value[1];
        const biddingDataList: LotBiddingData[] = value[2];

        if (auctionLot.lots && auctionLot.auctions) {
          const lotBiddingDataDto: LotBiddingDataDto = await this.sharedService.createLotsBiddingDataDto(auctionLot.lots, biddingDataList, auctionLot, auctionLot.auctions);

          auctionLot.minBidAmount = lotBiddingDataDto.minBidAmount;
          auctionLot.highBidderId = lotBiddingDataDto.highBidderId;

          auctionLot.currentBid = biddingDataList.length >= 1 ? (biddingDataList[0].currentPrice ?? 0) : 0;
          auctionLot.auctions.images = auctionUpload;
          auctionLot.lots.images = lotUpload;
        }

        return new AuctionLotsDetailedResponse(auctionLot);
      });
    } catch (error) {
      this.logger.error("Error caught in GET 'getAuctionLotsById': " + error);
      return new ErrorResponse(error, 'getAuctionLotsById');
    }
  }

  @Post('/getAllAuctionLotsByAuctionIds')
  async getAllAuctionLotsByAuctionIds(
    @Body() body: AuctionLotsPostRequest,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('status') status: string,
  ): Promise<AuctionLotsListResponse | ErrorResponse> {
    try {
      const auctionLots: ListResultset<AuctionLotDto> = await this.handler.getAllWithLotsByAuctionIds(body.auctionIds, page, size, status);
      const lotIds: string[] = this.getLotIdsFromList(auctionLots);
      const auctionLotIds: string[] = this.getAuctionLotIdsFromList(auctionLots);

      let biddingDataMap: Map<string | undefined, LotBiddingData | undefined> = new Map<string | undefined, LotBiddingData | undefined>();

      if (body.details.Bidding || body.details.All) {
        biddingDataMap = await this.fetchBiddingDataMap(auctionLotIds);
      }

      let uploadsPerAuction: Map<string, ResourceFile[]> | undefined;
      if (body.details.Images || body.details.All) {
        uploadsPerAuction = await this.fetchUploadsPerAuction(lotIds);
      }

      if (body.details.Bidding) {
        const biddingListDto: ListResultset<AuctionLotDetailedDto> = await this.convertToDetailedDto(auctionLots, biddingDataMap);
        return new AuctionLotsListResponse(biddingListDto);
      }

      if (body.details.Images) {
        const imagesListResponse: ListResultset<AuctionLotImagesDto> = this.buildImagesResponse(auctionLots, uploadsPerAuction);
        return new AuctionLotsListResponse(imagesListResponse);
      }

      if (body.details.All) {
        const responseListDto: ListResultset<AuctionLotDetailedDto> = await this.convertToDetailedDto(auctionLots, biddingDataMap);
        this.addImagesToResponse(responseListDto, uploadsPerAuction);
        return new AuctionLotsListResponse(responseListDto);
      }

      return new AuctionLotsListResponse(auctionLots);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAllAuctionLotsByAuctionIds': " + error);
      return new ErrorResponse(error, 'getAllAuctionLotsByAuctionIds');
    }
  }
  async getBiddingDataForAuctionLot(id: string): Promise<LotBiddingData[]> {
    const biddingResponse: axios.AxiosResponse<ViewbidResponse> = await this.biddingApi.getLotBiddingDataByAuctionLotIds(id);
    if (!biddingResponse) {
      throw new AuctionLotBiddingDataNotFoundError();
    }

    return biddingResponse.data.data as LotBiddingData[];
  }

  private getIdsFromList(objects: [], propertyPath: string): string[] {
    const ids: string[] = [];
    objects.forEach((object: any) => {
      ids.push(_.get(object, propertyPath));
    });
    return ids;
  }

  private getLotIdsFromList(auctionLots: ListResultset<AuctionLotDto | AuctionLotListHighlightedDto>): string[] {
    const lotIdsList: string[] = [];
    auctionLots.list.forEach((auctionLot: AuctionLotDto) => {
      lotIdsList.push(auctionLot?.lotId ?? '');
    });
    return lotIdsList;
  }

  private getAuctionLotIdsFromList(auctionLots: ListResultset<AuctionLotDto | AuctionLotListHighlightedDto>): string[] {
    const lotIdsList: string[] = [];
    auctionLots.list.forEach((auctionLot: AuctionLotDto) => {
      lotIdsList.push(auctionLot?.id ?? '');
    });
    return lotIdsList;
  }

  @Patch('updateAuctionLotsStatus')
  public async updateAuctionLotsStatus(@Body() body: AuctionLotsUpdateRequest): Promise<AuctionLotsUpdateResponse> {
    return this.executeAndNotify(
      'update',
      async () => {
        const auctionLots: ListResultset<AuctionLotDto> = await this.handler.getAllByAuctionIds(body.auctionIds);
        const numberOfUpdatedAuctionLots: number = await this.handler.updateStatuses(body.auctionIds, body.auctionLotsStatus);
        const lotIds: string[] = this.getLotIdsFromList(auctionLots);
        const numberOfUpdatedLots: number = await this.lotsHandler.updateStatuses(lotIds, body.lotStatus);

        if (body.lotStatus === LotStatus.Published) {
          auctionLots.list.forEach((auctionLot: AuctionLotDto) => {
            if (auctionLot.toDatetime) {
              this.eventBridgeService.manageScheduler(`${auctionLot.id}_LOT_ENDING_SOON`, auctionLot.id, new Date(auctionLot.toDatetime), this.targetArn, this.delayInMinutes);
              this.eventBridgeService.manageScheduler(
                `${auctionLot.id}_LOT_CLOSED`,
                auctionLot.id,
                new Date(auctionLot.toDatetime),
                this.lotItemClosedARN,
                this.lotClosedDelayInMinutes,
              );
              this.logger.log(`Eventbridge scheduler has been created for auction lot ${auctionLot.id}`);
            }
          });
        }

        const auctionLotsUpdated: boolean = numberOfUpdatedAuctionLots > 0 && numberOfUpdatedLots > 0;

        return new AuctionLotsUpdateResponse(auctionLotsUpdated);
      },
      {},
    );
  }

  private isBidTimeUnderSnipeThreshold(toDateTime: Date, bidTime: Date, auctionDto: AuctionDto): boolean {
    const toDateTimeDate: Date = new Date(toDateTime.toUTCString());
    const bidTimeDate: Date = new Date(bidTime.toUTCString());

    const diffMilSec: number = toDateTimeDate.getTime() - bidTimeDate.getTime();
    const bidWindow: number = auctionDto.bidSnipeWindow !== 0 ? auctionDto.bidSnipeWindow * 1000 : this.snipeWindow;

    if (diffMilSec <= bidWindow) {
      return true;
    }

    return false;
  }

  private async convertToDetailedHighlightedDto(
    auctionLots: ListResultset<AuctionLotListHighlightedDto>,
    lotBiddingDataMap: Map<string | undefined, LotBiddingData | undefined>,
  ): Promise<ListResultset<AuctionLotDetailedHighlightedDto>> {
    const list: AuctionLotDetailedHighlightedDto[] = await Promise.all(
      auctionLots.list.map(async (auctionLot: AuctionLotListHighlightedDto) => {
        const lotBiddingItem: LotBiddingData | undefined = lotBiddingDataMap.get(auctionLot.id) ?? {};
        lotBiddingItem.currentPrice = lotBiddingItem?.currentPrice ?? 0;

        const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, lotBiddingItem.currentPrice);
        const minBidAmount: number = this.sharedService.calculateHighlightedMinimumBidAmount(lotBiddingItem.currentPrice, bidIncrement.increment, auctionLot);
        return new AuctionLotDetailedHighlightedDto(auctionLot, lotBiddingItem, undefined, undefined, bidIncrement.increment, minBidAmount);
      }),
    );

    return new ListResultset<AuctionLotDetailedHighlightedDto>(list, auctionLots.page, auctionLots.pageSize, auctionLots.totalCount, auctionLots.totalPages);
  }
  private async convertToDetailedDto(
    auctionLots: ListResultset<AuctionLotDto>,
    lotBiddingDataMap: Map<string | undefined, LotBiddingData | undefined>,
  ): Promise<ListResultset<AuctionLotDetailedDto>> {
    const list: AuctionLotDetailedDto[] = await Promise.all(
      auctionLots.list.map(async (auctionLot: AuctionLotDto) => {
        const lotBiddingItem: LotBiddingData | undefined = lotBiddingDataMap.get(auctionLot.id) ?? {};
        lotBiddingItem.currentPrice = lotBiddingItem?.currentPrice ?? 0;

        const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, lotBiddingItem.currentPrice);
        const minBidAmount: number = this.sharedService.calculateMinimumBidAmount(lotBiddingItem.currentPrice, bidIncrement.increment, auctionLot.lots, auctionLot.auctions);
        return new AuctionLotDetailedDto(auctionLot, lotBiddingItem, undefined, undefined, bidIncrement.increment, minBidAmount);
      }),
    );

    return new ListResultset<AuctionLotDetailedDto>(list, auctionLots.page, auctionLots.pageSize, auctionLots.totalCount, auctionLots.totalPages);
  }

  private async fetchBiddingDataMap(auctionLotIds: string[]): Promise<Map<string | undefined, LotBiddingData | undefined>> {
    const biddingDataList: LotBiddingData[] = await this.getBiddingDataForAuctionLot(auctionLotIds.toString());
    return new Map(biddingDataList.map((lot: LotBiddingData) => [lot.auctionLotId, lot]));
  }

  private async fetchUploadsPerAuction(lotIds: string[]): Promise<Map<string, ResourceFile[]>> {
    const resources: UploadsByResourceResponse[] | ErrorResponse = await this.uploadsController.findUploadsForResource(lotIds.toString());
    if (Array.isArray(resources)) {
      return new Map(resources.map((resource: UploadsByResourceResponse) => [resource.proxyId, resource.files]));
    }
    return new Map();
  }

  private buildImagesResponse(auctionLots: ListResultset<AuctionLotDto>, uploadsPerAuction?: Map<string, ResourceFile[]>): ListResultset<AuctionLotImagesDto> {
    const list: AuctionLotImagesDto[] = [];
    auctionLots.list.forEach((auctionLot: AuctionLotDto) => {
      const lotFiles: ResourceFile[] | undefined = uploadsPerAuction?.get(auctionLot.lotId);
      if (lotFiles) {
        lotFiles.forEach((file: ResourceFile) => {
          list.push(new AuctionLotImagesDto(auctionLot, [{ url: file.fileLink, name: file.filename, type: file.fileType }]));
        });
      }
    });
    return new ListResultset(list, auctionLots.page, auctionLots.pageSize, auctionLots.totalCount, auctionLots.totalPages);
  }

  private addImagesToResponse(responseListDto: ListResultset<AuctionLotDetailedDto>, uploadsPerAuction?: Map<string, ResourceFile[]>): void {
    responseListDto.list.forEach((elem: AuctionLotDetailedDto) => {
      elem.images = [];
      const lotFiles: ResourceFile[] | undefined = uploadsPerAuction?.get(elem.lotId);
      if (lotFiles) {
        lotFiles.forEach((file: ResourceFile) => {
          elem.images?.push({ url: file.fileLink, name: file.filename, type: file.fileType });
        });
      }
    });
  }

  @Get(':auctionId/grouped/:groupId')
  async getAllByGroup(
    @Param('auctionId') auctionId: string,
    @Param('groupId') groupId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
  ): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    try {
      await this.auctionsHandler.get(auctionId);
      const items: ListResultset<AuctionLotFlattenedDto> = await this.handler.getAuctionLotsByGroup(groupId, page, size, query);

      const lotIdsList: string[] = [];
      items.list.forEach((auctionLot: AuctionLotFlattenedDto) => {
        lotIdsList.push(auctionLot?.lotId ?? '');
      });

      const resources: UploadsByResourceResponse[] | ErrorResponse = await this.uploadsController.findUploadsForResource(lotIdsList.toString());
      if (Array.isArray(resources)) {
        const resourceMap: Map<string, ResourceFile[]> = new Map(resources.map((resource: UploadsByResourceResponse) => [resource.proxyId, resource.files]));
        items.list.forEach((auctionLot: AuctionLotFlattenedDto) => {
          const resourceFile: ResourceFile[] | undefined = resourceMap.get(auctionLot.lotId);
          if (resourceFile) {
            auctionLot.images = [];
            resourceFile.forEach((file: ResourceFile) => {
              auctionLot.images.push({
                url: file.fileLink,
                name: file.filename,
                type: file.fileType,
              });
            });
          }
        });
      }
      return new AuctionsListWithAuctionLotsResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAllByGroup');
    }
  }

  @Patch(':auctionId/grouped/:groupId')
  async addToGroup(
    @Param('auctionId') auctionId: string,
    @Param('groupId') groupId: string,
    @Body() body: UpdateGroupRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const group: GroupDto = await this.groupsHandler.get(groupId);

      if (group.auctionsId !== auction.id) {
        throw new GroupNotFoundError(groupId);
      }

      const groupDto: GroupDto = new GroupDto({
        id: groupId,
        auctionId: auctionId,
        name: group.name,
      });
      this.groupsHandler.update(req.accountMappingsId, groupDto, groupId);

      if (body.auctionLotIdsToAdd.length > 0) {
        await this.handler.updateMany(req.accountMappingsId, { groupsId: group.id }, body.auctionLotIdsToAdd);
      }

      if (body.auctionLotIdsToRemove.length > 0) {
        await this.handler.updateMany(req.accountMappingsId, { groupsId: null }, body.auctionLotIdsToRemove);
      }

      const items: ListResultset<AuctionLotFlattenedDto> = await this.handler.getAuctionLotsByGroup(groupId, 1, 10);
      await this.handler.updateAuctionLotsToDateTimes(auctionId, this.lotClosingStaggeredBetweenLotSeconds);

      return new AuctionsListWithAuctionLotsResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'addToGroup');
    }
  }

  @Delete(':auctionId/grouped/:groupId')
  async ungroupAuctionLots(@Param('auctionId') auctionId: string, @Param('groupId') groupId: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      await this.auctionsHandler.get(auctionId);

      await this.groupsHandler.get(groupId);

      await this.handler.ungroupAuctionLots(req.accountMappingsId, auctionId, groupId);
      await this.handler.updateAuctionLotsToDateTimes(auctionId, this.lotClosingStaggeredBetweenLotSeconds);
    } catch (error) {
      return new ErrorResponse(error, 'ungroupAuctionLots');
    }
  }

  @Delete(':auctionId/grouped')
  async deleteAllGroupsInAuction(@Param('auctionId') auctionId: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);

      await this.handler.ungroupAllLotsInAuction(req.accountMappingsId, auction.id);

      await this.groupsHandler.deleteAllGroupsInAuction(auction.id);
      await this.handler.updateAuctionLotsToDateTimes(auctionId, this.lotClosingStaggeredBetweenLotSeconds);
    } catch (error) {
      return new ErrorResponse(error, 'deleteAllGroupsInAuction');
    }
  }

  @Post(':auctionId/grouped')
  async createGroupAndAddLots(
    @Param('auctionId') auctionId: string,
    @Body() body: AddToGroupRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);

      const group: GroupDto = await this.groupsHandler.create(req.accountMappingsId, new GroupDto({ name: body.name, auctionsId: auction.id }));
      await this.handler.updateMany(req.accountMappingsId, { groupsId: group.id }, body.auctionLotIds);
      await this.handler.updateAuctionLotsToDateTimes(auctionId, this.lotClosingStaggeredBetweenLotSeconds);

      const items: ListResultset<AuctionLotFlattenedDto> = await this.handler.getAuctionLotsByGroup(group.id, 1, 10);

      return new AuctionsListWithAuctionLotsResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'createGroupAndAddLots');
    }
  }

  @Get('getNextAvailableLotNumber/:auctionId')
  async getNextAvailableLotNumber(@Param('auctionId') auctionId: string): Promise<LotNumberResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const lotNumber: LotNumberDto = await this.handler.getNextAvailableLotNumber(auction.id);
      return new LotNumberResponse(lotNumber);
    } catch (error) {
      return new ErrorResponse(error, 'getNextAvailableLotNumber');
    }
  }

  @Get('getExistingLotNumber/:auctionId/lotNumber/:number')
  async getExistingLotNumber(@Param('auctionId') auctionId: string, @Param('number') number: string): Promise<SuccessResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const param: Record<string, any>[] = [{ lotNumber: number, auctionId: auction.id }];

      const auctionLot: AuctionLotDto = await this.handler.getAuctionLotByLotNumber(param);

      return new SuccessResponse({ lotId: auctionLot.lotId }, 'getExistingLotNumber', undefined, { exists: true });
    } catch (error) {
      return new ErrorResponse(error, 'getExistingLotNumber', auctionId + '::' + number, { exists: false });
    }
  }

  @Get('listAuctionLotsFlattenedByIds')
  async listAuctionLotsFlattenedByIds(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('id') ids: string[],
    @Query('filters') filters?: string,
    @Req() req?: VBInterceptorRequest,
  ): Promise<any> {
    if (ids.length === 0) {
      return new EmptyListResponse('listAuctionLotsFlattenedByIds');
    }
    try {
      if (filters) {
        const auctionItems: ListResultset<AuctionLotFlattenedWatchlistDto> = await this.handler.listAuctionLotsFlattenedByIds(
          page,
          size,
          ids,
          this.deserializeWatchlistFilters(filters),
          req?.accountMappingsId,
        );
        return new AuctionLotsFlattenedListResponse(auctionItems);
      } else {
        const auctionItems: ListResultset<AuctionLotFlattenedWatchlistDto> = await this.handler.listAuctionLotsFlattenedByIds(page, size, ids, undefined, req?.accountMappingsId);
        return new AuctionLotsFlattenedListResponse(auctionItems);
      }
    } catch (error) {
      return new ErrorResponse(error, 'listAuctionLotsFlattenedByIds');
    }
  }

  @Post('getAuctionLotsByIds')
  async getAuctionLotsByIds(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Query('status') status: string | undefined,
    @Query('orderBy') orderBy: string | undefined,
    @Query('filter') filter: string | undefined,
    @Body() request: FilteredAuctionLotsRequest,
  ): Promise<any> {
    if (request.auctionLotIds.length === 0) {
      return new EmptyListResponse('getAuctionLotsByIds');
    }
    try {
      const filterParams: Record<string, any> = this.buildFilterParams(query, status, filter?.split(','));
      filterParams.AND = [
        ...(filterParams.AND ?? []),
        {
          id: { in: request.auctionLotIds },
        },
      ];
      const auctionItems: ListResultset<AuctionLotFlattenedWatchlistDto> = await this.handler.getAllAuctionLots(
        page,
        size,
        filterParams,
        orderBy ? JSON.parse(orderBy) : { updatedOn: 'desc' },
      );
      return new AuctionLotsFlattenedListResponse(auctionItems);
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionLotsByIds');
    }
  }

  @Cacheable()
  @Get('getBasicAuctionLot/:id')
  async getBasicAuctionLot(@Param('id') id: string): Promise<AuctionLotFlattenedDto | ErrorResponse> {
    try {
      return new AuctionLotFlattenedDto(await this.handler.getFlattenedById(id));
    } catch (error) {
      return new ErrorResponse(error, 'getBasicAUctionLot');
    }
  }

  /**
   * deal with all the possible varieties of params passed
   *
   * example usage:
   * /auction-lots/getLotsByAuctionId/:id?page=1&size=15&status=DRAFT&query=cars
   *
   * @param query
   * @param status
   * @param filter
   * @private
   */
  private buildFilterParams(query: string | undefined, status: string | undefined, filter: string[] | undefined): Record<string, any> {
    const today: string = new Date().toISOString();
    const filterObject: Record<string, any> = {};

    // Add the status filter if provided
    if (status) {
      filterObject.status = status;
    } else {
      // Ensure we exclude completed auctions
      filterObject.status = { not: '' };
    }

    // Normalize filter to an array if it's not already one
    let filterArray: Array<string>;
    if (Array.isArray(filter)) {
      filterArray = filter;
    } else if (filter) {
      filterArray = [filter];
    } else {
      filterArray = [];
    }
    // Process the filter array to add boolean filters dynamically
    filterArray.forEach((filterParam: string) => {
      if (filterParam.startsWith('is')) {
        const key: string = filterParam;
        const value: number = 1; // Prisma treats boolean-like numeric values as 1 (true)
        filterObject[key] = value;
      }
    });

    if (query) {
      // Determine the value for groupsId based on the query
      let groupsIdValue: string | null | undefined;
      if (RegExp(/^[0-9a-fA-F-]{36}$/).exec(query)) {
        groupsIdValue = query;
      } else if (query === 'null') {
        groupsIdValue = null;
      } else {
        groupsIdValue = undefined; // Explicitly undefined if neither condition is met
      }

      filterObject.AND = [
        {
          OR: [
            { lots: { tags: { contains: query, mode: 'insensitive' } } },
            { lots: { title: { contains: query, mode: 'insensitive' } } },
            { lots: { subtitle: { contains: query, mode: 'insensitive' } } },
            { lots: { consignorNumber: { contains: query, mode: 'insensitive' } } },
            { lots: { category: { contains: query, mode: 'insensitive' } } },
            { lots: { description: { contains: query, mode: 'insensitive' } } },
            { lots: { pickupLocation: { contains: query, mode: 'insensitive' } } },
            { suffix: { contains: query, mode: 'insensitive' } },
            { auctions: { name: { contains: query, mode: 'insensitive' } } },
            { groupsId: groupsIdValue }, // Use the determined value
          ],
        },
      ];
    }

    // Handle date-based filtering
    if (!filter?.includes('allDates')) {
      if (filter?.includes('expired')) {
        // For expired items, ensure they're within the closed auction window
        filterObject.AND = [
          ...(filterObject.AND ?? []),
          {
            auctions: { isDisplaySoldItems: true },
            toDatetime: {
              lte: today,
              gt: new Date(new Date().setDate(new Date().getDate() - this.closedAuctionWindowDays)).toISOString(),
            },
          },
        ];
      } else if (filter?.includes('open')) {
        // For open items, ensure they haven't ended yet
        filterObject.AND = [
          ...(filterObject.AND ?? []),
          {
            toDatetime: { gt: today },
          },
        ];
      }
    }

    return filterObject;
  }

  @Get('minimal/:id')
  async getMinimal(@Param('id') _id: string): Promise<any> {
    //placeholder for getting minimal amount of info for auctionlots for bidding api
  }

  private deserializeWatchlistFilters(filtersString: string): Record<string, boolean> {
    // Parse the query string into key-value pairs
    const params: URLSearchParams = new URLSearchParams(filtersString);
    const filterKeys: Array<string> = ['winning', 'losing', 'withNotes', 'withoutNotes', 'watching'];
    const filters: Record<string, boolean> = {};

    filterKeys.forEach((key: string) => {
      filters[key] = params.get(key) === 'true';
    });

    return filters;
  }

  @Post('clone/:auctionId/lots/:lotId')
  @Transactional()
  async clone(@Param('auctionId') auctionId: string, @Param('lotId') lotId: string, @Req() req: VBInterceptorRequest): Promise<LotDetailedAdminResponse> {
    return await this.executeAndNotify(
      'clone',
      async () => {
        try {
          const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
          const lot: LotDto = await this.lotsHandler.get(lotId);
          const userId: string = req.accountMappingsId;

          const clonedLot: LotDto = await this.lotsHandler.clone(userId, auction.id, lotId);
          const auctionLot: AuctionLotDto = await this.handler.clone(userId, auction.id, lotId, clonedLot.id);

          //clone the images and documents
          const uploadClones: any[] = await this.uploadsController.clone(userId, lot.id, clonedLot.id);
          if (uploadClones && uploadClones.length > 0) {
            //We except a single duplicate, so getting the first one
            await this.lotsHandler.updateThumbnail(uploadClones[0].uploadsId);
          }

          const lotAttributes: ListResultset<LotAttributeDto> = await this.lotAttributesHandler.clone(userId, lot.id, clonedLot.id);
          const { clonedLotImages, clonedLotDocs } = await this.sharedService.handleImageCloning(clonedLot.id, lot.id, this.bucketName, this.logger);

          await AuctionLotsCachingService.deleteKeysByPrefix(this.redisService, 'GET-/auction-lots*');

          return new LotDetailedAdminResponse(
            clonedLot,
            auctionLot,
            clonedLotImages,
            clonedLotDocs,
            lotAttributes.list.map((item: LotAttributeDto) => new LotAttributeViewDto(item)),
          );
        } catch (error) {
          return new ErrorResponse(error, 'cloneAuctionLot');
        }
      },
      {},
    );
  }

  @Get(':auctionId/checkLastItemClosed')
  async checkLastItemClosed(@Param('auctionId') auctionId: string): Promise<AuctionLotResponse | ErrorResponse | undefined> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const auctionLot: AuctionLotDto = await this.handler.checkLastItemClosed(auction.id);

      this.eventBridgeService.manageScheduler(
        `${auctionId}_IS_LAST_LOT_CLOSED`,
        auctionId,
        new Date(auctionLot.toDatetime),
        this.isLastLotClosedARN,
        this.checkLastClosingLotDelay,
      );
      this.logger.log(`Eventbridge scheduler has been created to check last closing lot for auction id ${auctionId}`);

      return new AuctionLotResponse(auctionLot);
    } catch (error) {
      if (error.code === AuctionConstants.AUCTION_NOT_FOUND) {
        return new ErrorResponse(error, 'checkLastItemClosed', auctionId);
      }

      this.notify('success', 'closed', auctionId);
    }
  }

  private getEventScheduledTimeValue(scheduledTime: string): string {
    if (scheduledTime.startsWith('at(') && scheduledTime.endsWith(')')) {
      const dateTime: string = scheduledTime.slice(3, -1);
      return dateTime;
    } else {
      this.logger.error(`Invalid ScheduleExpression format ${scheduledTime}`);
      return '';
    }
  }

  protected convertQueryToArray(uri: string, additionalParams?: { [key: string]: any }): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    // Extract 'page' and 'size' parameters as they're not used for searching
    searchParams.delete('page');
    searchParams.delete('size');

    // Extract the statusType parameter for filtering
    const statusType: string | null = searchParams.get('status');
    if (statusType) {
      searchParams.delete('status');
    }

    const auctionsId: string | null = searchParams.get('auctionsId');
    if (auctionsId) {
      searchParams.delete('auctionsId');
    }

    // If additionalParams is provided, add or override the parameters in the searchParams
    if (additionalParams) {
      for (const [key, value] of Object.entries(additionalParams)) {
        searchParams.set(key, value.toString()); // Use .set to add or override a parameter
      }
    }

    const array: any[] = [];
    // Extract the query parameter for searching
    const query: string | null = searchParams.get('query');
    searchParams.delete('query');
    if (query) {
      if (isNaN(Number(query))) {
        if (RegExp(/^[0-9a-fA-F-]{36}$/).exec(query)) {
          array.push({ OR: [{ 'al."groupsId"': { contains: query } }] });
        } else {
          // Prepare the list of conditions for string search
          const orConditions: Record<string, any>[] = [
            { 'l."tags"': { contains: query, mode: 'insensitive' } },
            { 'l."title"': { contains: query, mode: 'insensitive' } },
            { 'l."subtitle"': { contains: query, mode: 'insensitive' } },
            { 'l."consignorNumber"': { contains: query, mode: 'insensitive' } },
            { 'l."category"': { contains: query, mode: 'insensitive' } },
            { 'l."description"': { contains: query, mode: 'insensitive' } },
            { 'l."pickupLocation"': { contains: query, mode: 'insensitive' } },
            { 'al."suffix"': { contains: query, mode: 'insensitive' } },
            { 'a."name"': { contains: query, mode: 'insensitive' } },
          ];

          array.push({ OR: orConditions });
        }
      }
    }

    // Convert remaining search parameters to array
    searchParams.forEach((value: string, key: string) => {
      array.push({ [key]: value });
    });

    // Handle statusType separately and add only if it matches a valid enum value
    const statuses: string[] = Object.keys(LotStatus).map((lotStatus: string) => lotStatus.toUpperCase());
    if (statusType && statuses.includes(statusType.replace(/\s/g, '').toUpperCase())) {
      const t: string = statusType
        .split(' ')
        .map((s: string) => s.charAt(0).toUpperCase() + s.substring(1).toLowerCase())
        .join('');

      // Convert the enum key to its corresponding value
      const statusTypeValue: string = LotStatus[t as keyof typeof LotStatus];
      array.push({ 'l."status"': statusTypeValue });
    } else if (statusType === null) {
      // If no status was provided, exclude archived lots
      const archivedKey: string = LotStatus.Archived;
      const statusTypeValue: string = LotStatus[archivedKey as keyof typeof LotStatus];
      array.push({ 'l."status"': { not: statusTypeValue } });
    }

    return array;
  }
}
