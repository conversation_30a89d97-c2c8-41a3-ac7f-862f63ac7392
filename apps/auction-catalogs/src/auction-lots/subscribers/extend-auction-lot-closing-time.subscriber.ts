import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AuctionLotsHandler } from '../handlers/auction-lots.handler';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ExtendAuctionLotClosingTimeSubscriber implements SubscriberInterface {
  logger = new Logger(ExtendAuctionLotClosingTimeSubscriber.name);

  constructor(private auctionLotsHandler: AuctionLotsHandler) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const details: Details = eventPayload.items.details;

    try {
      await this.auctionLotsHandler.extendClosingTimeByGroup(details.auctionLotId, details.extensionTime);

      // Additional logging or notifications about the extension can be added here
      this.logger.log(`Auction lots in same group as ${details.auctionLotId} closing time extended by ${details.extensionTime} milliseconds`);
    } catch (error) {
      this.logger.error(`Error extending auction lot closing time: ${error.message}`, error.stack);
    }
  }
}
interface Details {
  auctionLotId: string;
  extensionTime: number;
}
