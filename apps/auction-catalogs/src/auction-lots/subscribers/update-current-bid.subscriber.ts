import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AuctionLotsHandler } from '../handlers/auction-lots.handler';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class UpdateCurrentBidSubscriber implements SubscriberInterface {
  private auctionLotsHandler: AuctionLotsHandler;
  private readonly logger = new Logger(UpdateCurrentBidSubscriber.name);

  constructor(auctionLotsHandler: AuctionLotsHandler) {
    this.auctionLotsHandler = auctionLotsHandler;
  }

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const { auctionLot, newBid, previousBid, accountMappingsId } = eventPayload.items.details;
    this.logger.log(`Updating current bid for auction lot ${auctionLot.lotNumber} with new bid amount ${newBid.amount}`);
    auctionLot.currentBid = newBid.amount;
    auctionLot.currentHighBidderId = accountMappingsId;
    try {
      await this.auctionLotsHandler.updateBidDetails(auctionLot, accountMappingsId);
    } catch (error) {
      this.logger.error((error as Error).stack);
    }
    previousBid;
  }
}
