import { LotItemUserNoteDto } from '../../lot-item-user-notes/dtos/lot-item-user-note.dto';

export class AuctionLotFlattenedWatchlistDto {
  auctionLotId: string;
  auctionId: string;
  auctionToDatetime: string;
  auctionStatus: string;
  buyerPremium: number;
  currentBid: number;
  currentHighBidderId: string;
  fromDatetime: string;
  isDisplayFinalPrice: boolean;
  isDisplaySoldItems: boolean;
  lotId: string;
  lotNumber: string;
  lotStatus: string;
  lotToDatetime: string;
  minimumBidAmount: number;
  pickupLocation: string;
  condition: string;
  lotPickupLocation: string;
  reserveAmount: number;
  title: string;
  imageURL: string | null;
  auctionName: string;
  userBid: number | undefined;
  isProxy: number | undefined;
  lotItemUserNotes: LotItemUserNoteDto[];
  isProxyBid: boolean;
  isReserveMinBid: boolean;
  startAmount: number;
  bidSnipeWindow: number;
  taxPercentage: number;

  constructor(row: any) {
    this.auctionLotId = row.id;
    this.taxPercentage = row.taxPercentage;
    this.auctionId = row.auctions.id;
    this.auctionToDatetime = row.auctions.toDatetime;
    this.auctionStatus = row.auctions.status;
    this.buyerPremium = row.auctions.buyerPremium;
    this.currentBid = row.currentBid;
    this.currentHighBidderId = row.currentHighBidderId;
    this.fromDatetime = row.fromDatetime;
    this.isDisplayFinalPrice = Boolean(row.auctions.isDisplayFinalPrice);
    this.isDisplaySoldItems = Boolean(row.auctions.isDisplaySoldItems);
    this.lotId = row.lots.id;
    this.lotNumber = `${row.number}${row.suffix}`;
    this.lotStatus = row.lots.status;
    this.lotToDatetime = row.toDatetime;
    this.minimumBidAmount = row.minimumBidAmount;
    this.pickupLocation = row.auctions.pickupLocation;
    this.condition = row.lots.condition;
    this.lotPickupLocation = row.lots.pickupLocation;
    this.reserveAmount = row.lots.reserveAmount;
    this.title = row.lots.title;
    this.imageURL = '';
    this.auctionName = row.auctions.name;
    this.isProxyBid = row.isProxyBid;
    this.isReserveMinBid = row.isReserveMinBid;
    this.startAmount = row.startAmount;
    this.bidSnipeWindow = row.bidSnipeWindow;
  }
}

export interface AuctionLotsResponseDto {
  result: AuctionLotFlattenedWatchlistDto[];
}
