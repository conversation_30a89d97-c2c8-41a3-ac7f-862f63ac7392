import { Asset } from '../../uploads/entities/uploads.interfaces';
import { LotBiddingData } from '@viewbid/ts-node-client';
import { AuctionLotListHighlightedDto } from './auction-lot-list-highlighted.dto';

export class AuctionLotDetailedHighlightedDto {
  id?: string;
  title?: string;
  description?: string;
  status?: string;
  code?: string;
  reserveAmount?: number;
  tags?: string;
  condition?: string;
  category: string;
  region: string;
  thumbnailId: string;
  auctionId: string;
  lotId: string;
  toDatetime: string;
  auctionLotsId: string;
  minBidAmount?: number;
  number?: number;
  images: Asset[];
  bidCount?: number;
  numberOfWatchers?: number;
  currentPrice?: number;
  highBidder?: string;
  highBidderId?: string;
  bidIncrement?: number;
  buyNowPrice?: number;
  documents: Asset[];
  suffix?: string;
  highlight: boolean;
  isReserveMinBid: boolean;
  isAcceptOffers: boolean;
  bidSnipeWindow: number;
  taxPercentage: number;

  constructor(auctionLot: AuctionLotListHighlightedDto, biddingData?: LotBiddingData, images?: Asset[], documents?: Asset[], bidIncrement?: number, minBidAmount?: number) {
    this.id = auctionLot.id;
    this.taxPercentage = auctionLot.taxPercentage;
    this.title = auctionLot.title;
    this.status = auctionLot.status;
    this.highlight = auctionLot.highlight;
    this.reserveAmount = auctionLot.reserveAmount ?? biddingData?.currentPrice;
    this.condition = auctionLot.condition;
    this.category = auctionLot.category;
    this.region = auctionLot.region;
    this.thumbnailId = auctionLot.thumbnailId;
    this.auctionId = auctionLot.auctionId;
    this.lotId = auctionLot.lotId;
    this.toDatetime = auctionLot.toDatetime ?? '';
    this.auctionLotsId = auctionLot.id;
    this.minBidAmount = minBidAmount;
    this.number = auctionLot.number;
    this.images = images || [];
    this.documents = documents || [];
    this.bidCount = biddingData?.biddingData?.bidCount;
    this.numberOfWatchers = biddingData?.biddingData?.numberOfWatchers;
    this.currentPrice = biddingData?.currentPrice;
    this.highBidder = biddingData?.biddingData?.highBidder;
    this.highBidderId = biddingData?.biddingData?.highBidder;
    this.bidIncrement = bidIncrement;
    this.buyNowPrice = auctionLot.buyNowPrice;
    this.suffix = auctionLot.suffix;
    this.isReserveMinBid = auctionLot.isReserveMinBid;
    this.isAcceptOffers = auctionLot.isAcceptOffers;
    this.bidSnipeWindow = auctionLot.bidSnipeWindow;
  }
}
