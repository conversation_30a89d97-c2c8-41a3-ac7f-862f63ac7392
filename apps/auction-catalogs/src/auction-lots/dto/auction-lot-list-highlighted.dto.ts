export class AuctionLotListHighlightedDto {
  id: string;
  number: number;
  suffix: string;
  status: string;
  auctionId: string;
  displayOrder: number;
  toDatetime: string;
  currentBid: number;
  buyNowPrice: number;
  title: string;
  subtitle: string;
  reserveAmount: number;
  condition: string;
  region: string;
  thumbnailId: string;
  highlight: boolean;
  lotId: string;
  category: string;
  isReserveMinBid: boolean;
  startAmount: number;
  isAcceptOffers: boolean;
  bidSnipeWindow: number;
  taxPercentage: number;

  constructor(row: any) {
    this.id = row.id;
    this.taxPercentage = row.taxPercentage;
    this.number = row.number;
    this.suffix = row.suffix;
    this.status = row.status;
    this.auctionId = row.auctionId;
    this.displayOrder = row.displayOrder;
    this.toDatetime = row.toDatetime;
    this.currentBid = parseFloat(row.currentBid);
    this.buyNowPrice = parseFloat(row.buyNowPrice);
    this.title = row.title;
    this.subtitle = row.subtitle;
    this.reserveAmount = parseFloat(row.reserveAmount);
    this.condition = row.condition;
    this.region = row.region;
    this.thumbnailId = row.thumbnailId;
    this.highlight = row.highlight;
    this.lotId = row.lotId;
    this.category = row.category;
    this.isReserveMinBid = row.isReserveMinBid;
    this.startAmount = row.startAmount;
    this.isAcceptOffers = row.isAcceptOffers;
    this.bidSnipeWindow = row.bidSnipeWindow;
  }
}
