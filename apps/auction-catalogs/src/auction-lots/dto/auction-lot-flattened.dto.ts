import { Asset } from '../../uploads/entities/uploads.interfaces';

export class AuctionLotFlattenedDto {
  id: string; // From AuctionLots table
  auctionId: string; // From AuctionLots table
  lotId: string; // From AuctionLots table
  number: number; // From AuctionLots table
  suffix: string;
  displayOrder: number; // From AuctionLots table
  status: string; // From AuctionLots table
  toDatetime: Date; // From AuctionLots table
  title: string; // From Lots table
  description: string; // From Lots table
  code: string; // From Lots table
  reserveAmount: number; // From Lots table
  tags: string; // From Lots table
  condition: string; // From Lots table
  category: string; // From Lots table
  region: string; // From Lots table
  pickupLocation: string; // From Lots table
  images: Asset[]; //from UploadItems table
  documents: Asset[]; //from UploadItems table
  currentBid: number; //from AuctionLots
  currentHighBidderId: string; //from AuctionLots
  thumbnailId: string;
  startAmount: number; //from Lots
  isAcceptOffers: boolean; //from Lots
  nextBidMeetsReserve: boolean;
  taxPercentage: number;

  constructor(row: any) {
    this.id = row.id;
    this.taxPercentage = row.taxPercentage;
    this.auctionId = row.auctionId;
    this.lotId = row.lotId;
    this.number = row.number;
    this.suffix = row.suffix;
    this.displayOrder = row.displayOrder;
    this.status = row.status;
    this.toDatetime = row.toDatetime;
    this.title = row.title;
    this.description = row.description;
    this.code = row.code;
    this.reserveAmount = row.reserveAmount;
    this.tags = row.tags;
    this.condition = row.condition;
    this.category = row.category;
    this.region = row.region;
    this.pickupLocation = row.pickupLocation;
    this.images = row.images;
    this.documents = row.documents;
    this.currentBid = row.currentBid;
    this.currentHighBidderId = row.currentHighBidderId;
    this.thumbnailId = row.thumbnailId;
    this.startAmount = row.startAmount;
    this.isAcceptOffers = row.isAcceptOffers;
    this.nextBidMeetsReserve = row.nextBidMeetsReserve;
  }
}
