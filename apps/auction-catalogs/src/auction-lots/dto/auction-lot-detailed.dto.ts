import { LotBiddingData } from '@viewbid/ts-node-client';
import { AuctionLotDto } from './auction-lot.dto';
import { Asset } from '../../uploads/entities/uploads.interfaces';

export class AuctionLotDetailedDto {
  id?: string;
  title?: string;
  description?: string;
  status?: string;
  code?: string;
  reserveAmount?: number;
  tags?: string;
  condition?: string;
  category?: string;
  region?: string;
  thumbnailId?: string;
  auctionId: string;
  lotId: string;
  toDatetime: string;
  auctionLotsId: string;
  minBidAmount?: number;
  number?: number;
  images: Asset[];
  bidCount?: number;
  numberOfWatchers?: number;
  currentPrice?: number;
  highBidder?: string;
  highBidderId?: string;
  bidIncrement?: number;
  buyNowPrice?: number;
  documents: Asset[];
  suffix?: string;
  isAcceptOffers?: boolean;
  taxPercentage: number;

  constructor(auctionLot: AuctionLotDto, biddingData?: LotBiddingData, images?: Asset[], documents?: Asset[], bidIncrement?: number, minBidAmount?: number) {
    this.id = auctionLot?.id;
    this.taxPercentage = auctionLot.taxPercentage;
    this.title = auctionLot.lots?.title;
    this.description = auctionLot.lots?.description;
    this.status = auctionLot.lots?.status;
    this.code = auctionLot.lots?.code;
    this.reserveAmount = auctionLot.lots?.reserveAmount ?? biddingData?.currentPrice;
    this.tags = auctionLot.lots?.tags;
    this.condition = auctionLot.lots?.condition;
    this.category = auctionLot.lots?.category;
    this.region = auctionLot.lots?.region;
    this.thumbnailId = auctionLot.lots?.thumbnailId;
    this.auctionId = auctionLot.auctionId;
    this.lotId = auctionLot.lotId;
    this.toDatetime = auctionLot.toDatetime;
    this.auctionLotsId = auctionLot.id;
    this.minBidAmount = minBidAmount;
    this.number = auctionLot.number;
    this.images = images || [];
    this.documents = documents || [];
    this.bidCount = biddingData?.biddingData?.bidCount;
    this.numberOfWatchers = biddingData?.biddingData?.numberOfWatchers;
    this.currentPrice = biddingData?.currentPrice;
    this.highBidder = biddingData?.biddingData?.highBidder;
    this.highBidderId = biddingData?.biddingData?.highBidder;
    this.bidIncrement = bidIncrement;
    this.buyNowPrice = auctionLot.buyNowPrice;
    this.suffix = auctionLot.suffix;
    this.isAcceptOffers = auctionLot.lots?.isAcceptOffers;
  }
}
