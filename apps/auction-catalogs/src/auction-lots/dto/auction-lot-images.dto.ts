import { Asset } from '../../uploads/entities/uploads.interfaces';
import { AuctionLotDto } from './auction-lot.dto';

export class AuctionLotImagesDto {
  id?: string;
  title?: string;
  description?: string;
  status?: string;
  code?: string;
  reserveAmount?: number;
  tags?: string;
  condition?: string;
  category?: string;
  region?: string;
  thumbnailId?: string;
  auctionId: string;
  toDatetime: string;
  auctionLotsId: string;
  minBidAmount?: number;
  number?: number;
  images: Asset[];
  bidCount?: number;
  numberOfWatchers?: number;
  currentPrice?: number;
  highBidder?: string;
  bidIncrement?: number;
  taxPercentage: number;

  constructor(auctionLot: AuctionLotDto, images?: Asset[]) {
    this.id = auctionLot.lots?.id;
    this.taxPercentage = auctionLot.taxPercentage;
    this.title = auctionLot.lots?.title;
    this.description = auctionLot.lots?.description;
    this.status = auctionLot.lots?.status;
    this.code = auctionLot.lots?.code;
    this.tags = auctionLot.lots?.tags;
    this.condition = auctionLot.lots?.condition;
    this.category = auctionLot.lots?.category;
    this.region = auctionLot.lots?.region;
    this.thumbnailId = auctionLot.lots?.thumbnailId;
    this.auctionId = auctionLot.auctionId;
    this.toDatetime = auctionLot.toDatetime;
    this.auctionLotsId = auctionLot.id;
    this.number = auctionLot.number;
    this.images = images || [];
  }
}
