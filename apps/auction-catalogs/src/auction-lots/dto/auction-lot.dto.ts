import { AuctionDto } from '../../auction/dtos/auction.dto';
import { GroupDto } from '../../groups/dtos/group.dto';
import { TemplateDto } from '../../lot-templates/dtos/template.dto';
import { LotDto } from '../../lots/dtos/lot.dto';

export class AuctionLotDto {
  id: string;
  auctionId: string;
  createdBy: string;
  lotId: string;
  number?: number;
  suffix: string;
  displayOrder?: number;
  status?: string;
  toDatetime: string;
  lots?: LotDto;
  auctions?: AuctionDto;
  currentBid: number;
  currentHighBidderId: string;
  minBidAmount?: number;
  highBidderId?: string;
  buyNowPrice?: number;
  groupsId: string;
  nextBidMeetsReserve: boolean;
  taxPercentage: number;

  constructor(row: any) {
    this.id = row.id;
    this.taxPercentage = row.taxPercentage;
    this.auctionId = row.auctionId;
    this.createdBy = row.createdBy;
    this.lotId = row.lotId;
    this.number = row.number;
    this.suffix = row.suffix;
    this.displayOrder = row.displayOrder;
    this.status = row.status;
    this.toDatetime = row.toDatetime;
    this.lots = row.lots;
    this.auctions = row.auctions;
    this.currentBid = row.currentBid;
    this.currentHighBidderId = row.currentHighBidderId;
    this.minBidAmount = row.minBidAmount;
    this.highBidderId = row.highBidderId;
    this.buyNowPrice = row.buyNowPrice;
    this.nextBidMeetsReserve = row.nextBidMeetsReserve;
    this.groupsId = row.groupsId;
  }

  static buildFromLot(item: any, lot: LotDto, auction?: AuctionDto, group?: GroupDto): AuctionLotDto {
    return new AuctionLotDto({
      id: item.id,
      createdBy: lot.createdBy ?? item.createdBy,
      auctionId: auction?.id ?? item.auctionId,
      lotId: lot.id ?? item.lotId,
      number: item.number,
      displayOrder: item.displayOrder,
      status: lot.status ?? item.status,
      toDatetime: auction?.toDatetime ? new Date(auction.toDatetime).toISOString() : item.toDatetime,
      lots: item.lots ? new LotDto(item.lots) : undefined,
      auctions: item.auctions ? new AuctionDto(item.auctions) : undefined,
      currentBid: item.currentBid,
      currentHighBidderId: item.currentHighBidderId,
      buyNowPrice: item.buyNowPrice,
      suffix: item.suffix,
      nextBidMeetsReserve: item.nextBidMeetsReserve,
      groupsId: group ? group.id : undefined,
      taxPercentage: lot.taxPercentage,
    });
  }

  static buildFromTemplate(template: TemplateDto, auction: AuctionDto, clonedLot: LotDto): AuctionLotDto {
    const { createdBy, number, displayOrder, status, currentBid, currentHighBidderId, buyNowPrice, suffix } = template.body; // Extract values from the JSON body

    return new AuctionLotDto({
      createdBy: createdBy,
      auctionId: auction.id,
      lotId: clonedLot.id,
      number: number,
      displayOrder: displayOrder,
      status: status,
      toDatetime: undefined,
      currentBid: currentBid,
      currentHighBidderId: currentHighBidderId,
      buyNowPrice: buyNowPrice,
      suffix: suffix,
      taxPercentage: clonedLot.taxPercentage,
    });
  }
}
