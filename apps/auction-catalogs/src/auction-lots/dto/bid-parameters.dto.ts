import { ParametersDto } from '@vb-utils/messaging/dtos/parameters.dto';

export class BidParametersDto implements ParametersDto {
  readonly firstname: string;
  readonly lastname: string;
  readonly lotName: string;
  readonly lotNumber: number;
  readonly lotItemLink: string;
  readonly winningPrice: number;
  readonly auctionLotsId: string;

  constructor(firstname: string, lastname: string, lotName: string, lotNumber: number, lotItemLink: string, winningPrice: number, auctionLotsId: string) {
    this.firstname = firstname;
    this.lastname = lastname;
    this.lotName = lotName;
    this.lotNumber = lotNumber;
    this.lotItemLink = lotItemLink;
    this.winningPrice = winningPrice;
    this.auctionLotsId = auctionLotsId;
  }
}
