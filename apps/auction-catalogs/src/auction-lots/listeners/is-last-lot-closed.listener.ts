import { Injectable, Logger } from '@nestjs/common';
import { IConsumerHandler } from '@vb/nest/interfaces/iConsumer.handler.interface';
import { ConfigService } from 'libs/config-service/src/config.service';
import { AuctionLotsController } from '../controller/auction-lots.controller';

@Injectable()
export class IsLastLotClosedListener implements IConsumerHandler {
  private readonly logger = new Logger(IsLastLotClosedListener.name);

  private queue: string;

  constructor(
    private auctionLotsController: AuctionLotsController,
    private configService: ConfigService,
  ) {}

  /**
   * Initializes module with necessary configurations.
   */
  async onModuleInit(): Promise<void> {
    this.queue = await this.configService.getSetting('IS_LAST_LOT_CLOSED_QUEUE_URL', '');
  }

  /**
   * Retrieves the name of the queue.
   *
   * @returns {string} The queue name.
   */
  public getQueueName(): string {
    return this.queue;
  }

  /**
   * Processes a message from the queue indicating an auction is closed.
   *
   * @param {any} message - The message received from the queue.
   */
  public async processQueueMessage(message: any): Promise<void> {
    const body: any = JSON.parse(message.Body);
    try {
      await this.auctionLotsController.checkLastItemClosed(body.id);
    } catch (error) {
      return error;
    }
  }
}
