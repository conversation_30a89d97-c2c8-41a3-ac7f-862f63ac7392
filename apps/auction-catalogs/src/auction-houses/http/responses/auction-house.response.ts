import { Response } from '@mvc/http/responses/response';
import { AuctionHouseDto } from '../../dtos/auction-house.dto';
import { AuctionHousePreferencesDto } from '../../../../../../libs/auction-houses/dtos/auction-house-preferences.dto';

export class AuctionHouseResponse extends Response {
  constructor(auctionHouse: AuctionHouseDto, preferences: Array<AuctionHousePreferencesDto>) {
    super(
      {
        auctionHouse: auctionHouse,
        auctionHousePreferences: preferences,
      },
      'auctionHouse',
      auctionHouse.id,
      {},
    );
  }
}
