import { BaseHandler } from '@mvc/handlers/base.handler';
import { AuctionHousesDbService } from '../data/auction-houses-db.service';
import { AuctionHouseDto } from '../dtos/auction-house.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AuctionHousesHandler extends BaseHandler<AuctionHousesDbService, AuctionHouseDto> {
  constructor(dbService: AuctionHousesDbService) {
    super(dbService);
  }
}
