import { <PERSON>Hand<PERSON> } from '@mvc/handlers/base.handler';
import { AuctionHousePreferencesDbService } from '../data/auction-house-preferences-db.service';
import { AuctionHousePreferencesDto } from '../../../../../libs/auction-houses/dtos/auction-house-preferences.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AuctionHousePreferencesHandler extends BaseHandler<AuctionHousePreferencesDbService, AuctionHousePreferencesDto> {
  constructor(dbService: AuctionHousePreferencesDbService) {
    super(dbService);
  }
}
