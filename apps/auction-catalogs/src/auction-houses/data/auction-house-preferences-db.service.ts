import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { AuctionHousePreferencesDto } from '../../../../../libs/auction-houses/dtos/auction-house-preferences.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { AuctionHousePreferencesNotFoundError } from '../exceptions/auction-house-preferences-not-found.error';

@Injectable()
export class AuctionHousePreferencesDbService extends BaseDbService<Prisma.AuctionHousePreferencesDelegate, AuctionHousePreferencesDto> {
  constructor(prisma: PrismaService) {
    super(prisma.auctionHousePreferences);
  }

  mapToDto(model: any): AuctionHousePreferencesDto {
    return new AuctionHousePreferencesDto(model);
  }

  /**
   * could not find an preferences by AuctionHouse id
   *
   * @param id
   */
  throw404(id: string): Error {
    throw new AuctionHousePreferencesNotFoundError(id);
  }
}
