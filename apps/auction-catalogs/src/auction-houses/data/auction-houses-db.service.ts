import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { AuctionHouseDto } from '../dtos/auction-house.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { AuctionHouseNotFoundError } from '../exceptions/auction-house-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AuctionHousesDbService extends BaseDbService<Prisma.AuctionHousesDelegate, AuctionHouseDto> {
  constructor(prisma: PrismaService) {
    super(prisma.auctionHouses);
  }

  mapToDto(model: any): AuctionHouseDto {
    return new AuctionHouseDto(model);
  }

  throw404(id: string): Error {
    throw new AuctionHouseNotFoundError(id);
  }
}
