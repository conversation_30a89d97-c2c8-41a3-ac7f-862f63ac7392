import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { AuctionHousePreferencesDto } from '../../../../../../libs/auction-houses/dtos/auction-house-preferences.dto';
import { AuctionHouseDto } from '../../dtos/auction-house.dto';
import { AuctionHousePreferencesHandler } from '../../handlers/auction-house-preferences.handler';
import { AuctionHousesHandler } from '../../handlers/auction-houses.handler';
import { AuctionHouseRequest } from '../../http/requests/auction-house.request';
import { AuctionHouseListResponse } from '../../http/responses/auction-house-list.response';
import { AuctionHouseResponse } from '../../http/responses/auction-house.response';

@Controller('admin/auction-houses')
@UseGuards(IsAdmin)
export class AuctionHousesController extends BaseController<AuctionHousesHandler, AuctionHouseRequest, AuctionHouseDto, AuctionHouseResponse, AuctionHouseListResponse> {
  constructor(
    handler: AuctionHousesHandler,
    private auctionHousePreferencesHandler: AuctionHousePreferencesHandler,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: AuctionHouseRequest): AuctionHouseDto {
    return new AuctionHouseDto(request.auctionHouse);
  }

  createResponseFromDto(dto: AuctionHouseDto): AuctionHouseResponse {
    return new AuctionHouseResponse(dto, [new AuctionHousePreferencesDto({})]);
  }

  createResponseList(list: ListResultset<AuctionHouseDto>): AuctionHouseListResponse {
    return new AuctionHouseListResponse(list);
  }

  @Post('/')
  async create(@Body() body: AuctionHouseRequest, @Req() req: VBInterceptorRequest): Promise<AuctionHouseResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const dto: AuctionHouseDto = this.createDtoFromRequest(body);

      const item: AuctionHouseDto = await this.handler.create(userId, dto);
      const auctionHousePreferences: AuctionHousePreferencesDto[] = this.createAuctionHousePreferencesFromRequest(body, item);

      // Assuming there will be a method to save preferences
      // await this.handler.createOrReplaceBulk( auctionHousePreferences,true,);

      return new AuctionHouseResponse(item, auctionHousePreferences);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Get('/getAllAuctionHouses')
  async getAllAuctionHouses(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionHouseListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const items: ListResultset<AuctionHouseDto> = await this.handler.getAll(page, size, params);

      return this.createResponseList(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }
  private createAuctionHousePreferencesFromRequest(request: AuctionHouseRequest, item: AuctionHouseDto): AuctionHousePreferencesDto[] {
    const preferences: any = request.auctionHousePreferences;

    return [
      AuctionHousePreferencesDto.buildFromRequest('logo', preferences.logo, item.id),
      AuctionHousePreferencesDto.buildFromRequest('defaultPickupLocation', preferences.defaultPickupLocation, item.id),
      AuctionHousePreferencesDto.buildFromRequest('defaultTermsConditions', preferences.defaultTermsConditions, item.id),
      AuctionHousePreferencesDto.buildFromRequest('defaultInvoiceTemplateId', preferences.defaultInvoiceTemplateId, item.id),
    ];
  }
}
