import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AuctionHousesController } from './controllers/admin/auction-houses.controller';
import { AuctionHousesDbService } from './data/auction-houses-db.service';
import { AuctionHousesHandler } from './handlers/auction-houses.handler';
import { AuctionHousePreferencesHandler } from './handlers/auction-house-preferences.handler';
import { AuctionHousePreferencesDbService } from './data/auction-house-preferences-db.service';

@Module({
  controllers: [AuctionHousesController],
  exports: [],
  providers: [AuctionHousesDbService, AuctionHousesHandler, AuctionHousePreferencesDbService, AuctionHousePreferencesHandler, Logger],
  imports: [],
})
export class AuctionHousesModule {}
