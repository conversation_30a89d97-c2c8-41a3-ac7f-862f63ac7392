import { NotFoundViewbidError } from '@mvc/exceptions/not-found-viewbid.error';
import { Constants } from '../config/constants';

export class AuctionHousePreferencesNotFoundError extends NotFoundViewbidError {
  constructor(id: string) {
    super(`No auction house preferences found for auction house ${id}`, Constants.AUCTION_HOUSE_PREFERENCES_NOT_FOUND, id, 'auction-houses#preferences-not-found');
  }
}
