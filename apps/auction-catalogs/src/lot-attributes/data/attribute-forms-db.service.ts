import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { AttributeFormDto } from '../dtos/attribute-form.dto';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AttributeFormNotFoundError } from '../exceptions/attribute-form-not-found.error';
import { RawAttributeFormQuestionDto } from '../dtos/raw-attribute-form-question.dto';

@Injectable()
export class AttributeFormsDbService extends BaseDbService<Prisma.AttributeFormsDelegate, AttributeFormDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.attributeForms);
  }

  mapToDto(model: any): AttributeFormDto {
    return new AttributeFormDto(model);
  }

  throw404(id: string): Error {
    throw new AttributeFormNotFoundError(id);
  }

  async drawForm(category: string): Promise<RawAttributeFormQuestionDto> {
    const query: string = `SELECT 
            afq.label,
            afq."groupName",
            afq."attributeKey",
            json_agg(
                json_build_object(
                    'basicListId', bl.id,
                    'value', bl.value,
                    'basicListDisplayOrder', bl."displayOrder",
                    'basicListGroupName', bl."groupName",
                    'basicKey', bl."basicKey"
                ) ORDER BY bl."basicKey", bl."displayOrder"
            ) AS "basicLists"
        FROM 
            "AttributeForms" af
        JOIN 
            "AttributeFormQuestions" afq ON af.id = afq."attributeFormsId"
        LEFT JOIN 
            "BasicLists" bl ON afq."groupName" = bl."groupName"
        WHERE 
            af.category = '${category}'
        GROUP BY 
            afq.label, afq."groupName", afq."attributeKey", afq."displayOrder"
        ORDER BY 
            afq."displayOrder";`;

    const result: any = await this.prismaService.$queryRawUnsafe(query);

    return result as RawAttributeFormQuestionDto;
  }
}
