import { BaseDbService } from '@mvc/data/base-db.service';
import { AttributeFormQuestionDto } from '../dtos/attribute-form-question.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AttributeFormQuestionNotFoundError } from '../exceptions/attribute-form-question-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class AttributeFormQuestionsDbService extends BaseDbService<Prisma.AttributeFormQuestionsDelegate, AttributeFormQuestionDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.attributeFormQuestions);
  }

  mapToDto(model: any): AttributeFormQuestionDto {
    return new AttributeFormQuestionDto(model);
  }

  throw404(id: string): Error {
    return new AttributeFormQuestionNotFoundError(id);
  }

  async getAllQuestionsByCategory(page: number, size: number, param: Record<string, any>): Promise<ListResultset<AttributeFormQuestionDto>> {
    const category: string = param.category;

    const items: any = await this.table.findMany({
      where: {
        attributeForm: {
          category: category,
        },
      },
      select: {
        id: true,
        label: true,
        groupName: true,
        displayOrder: true,
        attributeKey: true,
        attributeForm: {
          select: {
            category: true, // Include this if you want to confirm the category; otherwise, it can be omitted
          },
        },
      },
    });

    return new ListResultset(items.map(this.mapToDto), page, items.size, items.size, 1);
  }
}
