import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { LotAttributeDto } from '../dtos/lot-attribute.dto';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { LotAttributeNotFoundError } from '../exceptions/lot-attribute-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class LotAttributesDbService extends BaseDbService<Prisma.LotAttributesDelegate, LotAttributeDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.lotAttributes);
  }

  mapToDto(model: any): LotAttributeDto {
    return new LotAttributeDto(model);
  }

  throw404(id: string): Error {
    throw new LotAttributeNotFoundError(id);
  }

  async clone(userId: string, originalLotId: string, clonedLotId: string): Promise<ListResultset<LotAttributeDto>> {
    const sql: Prisma.Sql = Prisma.sql`
        INSERT INTO public."LotAttributes" (id, "createdOn", "createdBy", "updatedOn", "updatedBy", "lotId", name, "attributeKey", value)
        SELECT 
          uuid_generate_v4(),
          now(),
          ${userId}::uuid,
          now(),
          ${userId}::uuid,
          ${clonedLotId},
          name,
          "attributeKey",
          value
        FROM public."LotAttributes"
        WHERE "lotId" = ${originalLotId};
      `;

    await this.prisma.$executeRaw(sql);

    return await this.getAll(1, -1, [{ lotId: clonedLotId }]);
  }
}
