import { Modu<PERSON> } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { LotAttributesController } from './controller/lot-attributes.controller';
import { LotAttributesDbService } from './data/lot-attributes-db.service';
import { LotAttributesHandler } from './handler/lot-attributes.handler';
import { LotsDbService } from '../lots/data/lots-db.service';
import { LotsHandler } from '../lots/handlers/lots.handler';
import { AttributeFormsController } from './controller/attribute-forms.controller';
import { AttributeFormsDbService } from './data/attribute-forms-db.service';
import { AttributeFormsHandler } from './handler/attribute-forms.handler';
import { AttributeFormQuestionsDbService } from './data/attribute-form-questions-db.service';
import { AttributeFormQuestionsHandler } from './handler/attribute-form-questions.handler';
import { AttributeFormQuestionsController } from './controller/attribute-form-questions.controller';

@Module({
  controllers: [AttributeFormsController, AttributeFormQuestionsController, LotAttributesController],
  providers: [
    AttributeFormsDbService,
    AttributeFormsHandler,
    AttributeFormQuestionsDbService,
    AttributeFormQuestionsHandler,
    LotAttributesDbService,
    LotAttributesHandler,
    LotsDbService,
    LotsHandler,
    RedisService,
  ],
  imports: [],
  exports: [LotAttributesDbService],
})
export class LotAttributesModule {}
