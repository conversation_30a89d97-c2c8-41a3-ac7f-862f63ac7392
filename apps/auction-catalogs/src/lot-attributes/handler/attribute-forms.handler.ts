import { <PERSON>achi<PERSON><PERSON><PERSON><PERSON> } from '@vb/caching/handlers/caching-handler';
import { AttributeFormsDbService } from '../data/attribute-forms-db.service';
import { AttributeFormDto } from '../dtos/attribute-form.dto';
import { RedisService } from '@vb/redis';
import { Injectable } from '@nestjs/common';
import { RawAttributeFormQuestionDto } from '../dtos/raw-attribute-form-question.dto';
import { SECONDS_IN_A_WEEK } from '@vb/nest/constants/time.constants';

@Injectable()
export class AttributeFormsHandler extends CachingHandler<AttributeFormsDbService, AttributeFormDto> {
  constructor(dbService: AttributeFormsDbService, redisService: RedisService) {
    super(dbService, redisService, SECONDS_IN_A_WEEK, 'attributeforms');
  }
  async drawForm(category: string): Promise<RawAttributeFormQuestionDto> {
    return await this.getFromCache(category, this._drawFormFromDb.bind(this));
  }

  async _drawFormFromDb(category: string): Promise<RawAttributeFormQuestionDto> {
    return await this.dbService.drawForm(category);
  }
}
