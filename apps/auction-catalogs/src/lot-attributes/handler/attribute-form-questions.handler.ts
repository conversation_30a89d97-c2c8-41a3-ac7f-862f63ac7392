import { <PERSON>achi<PERSON><PERSON><PERSON><PERSON> } from '@vb/caching/handlers/caching-handler';
import { AttributeFormQuestionsDbService } from '../data/attribute-form-questions-db.service';
import { RedisService } from '@vb/redis';
import { AttributeFormQuestionDto } from '../dtos/attribute-form-question.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { SECONDS_IN_A_WEEK } from '@vb/nest/constants/time.constants';

@Injectable()
export class AttributeFormQuestionsHandler extends CachingHandler<AttributeFormQuestionsDbService, AttributeFormQuestionDto> {
  constructor(dbService: AttributeFormQuestionsDbService, redisService: RedisService) {
    super(dbService, redisService, SECONDS_IN_A_WEEK, 'attributeformquestion');
  }

  async getAllQuestionsByCategory(page: number, size: number, param: Record<string, any>): Promise<ListResultset<AttributeFormQuestionDto>> {
    return await this.dbService.getAllQuestionsByCategory(page, size, param);
  }
}
