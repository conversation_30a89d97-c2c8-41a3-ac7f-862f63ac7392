import { PubSubHand<PERSON> } from '@vb/redis/handlers/pub-sub.handler';
import { LotAttributesDbService } from '../data/lot-attributes-db.service';
import { LotAttributeDto } from '../dtos/lot-attribute.dto';
import { RedisService } from '@vb/redis';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class LotAttributesHandler extends PubSubHandler<LotAttributesDbService, LotAttributeDto> {
  constructor(dbService: LotAttributesDbService, redisService: RedisService) {
    super(dbService, redisService, 'lotattributes');
  }

  async getAllByLotId(id: string): Promise<ListResultset<LotAttributeDto>> {
    const result: any = await this.dbService.getAllCustom(0, -1, [{ lotId: id }], LotAttributeDto);
    return result as unknown as ListResultset<LotAttributeDto>;
  }

  async clone(userId: string, originalLotId: string, clonedLotId: string): Promise<ListResultset<LotAttributeDto>> {
    return await this.dbService.clone(userId, originalLotId, clonedLotId);
  }
}
