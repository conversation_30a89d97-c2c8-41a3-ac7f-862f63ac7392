import { BaseController } from '@mvc/controllers/base.controller';
import { LotAttributesHandler } from '../handler/lot-attributes.handler';
import { LotAttributeRequest } from '../http/requests/lot-attribute.request';
import { LotAttributeDto } from '../dtos/lot-attribute.dto';
import { LotAttributeResponse } from '../http/responses/lot-attribute.response';
import { LotAttributesListResponse } from '../http/responses/lot-attributes-list.response';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { LotDto } from '../../lots/dtos/lot.dto';

/**
 * this controller is for saving the response from the 'create lot' form.
 * this is where the attributes for (for example) a car to save the
 * VIN, Make, Model, etc.. for viewing/searching that lot item later
 */
@Controller('admin/lot-attributes/:lotId')
export class LotAttributesController extends BaseController<LotAttributesHandler, LotAttributeRequest, LotAttributeDto, LotAttributeResponse, LotAttributesListResponse> {
  constructor(
    handler: LotAttributesHandler,
    private lotsHandler: LotsHandler,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: LotAttributeRequest): LotAttributeDto {
    return new LotAttributeDto(request);
  }

  createResponseFromDto(dto: LotAttributeDto): LotAttributeResponse {
    return new LotAttributeResponse(dto);
  }

  createResponseList(list: ListResultset<LotAttributeDto>): LotAttributesListResponse {
    return new LotAttributesListResponse(list);
  }

  @Post('/attributes')
  async createAttributesList(@Param('lotId') lotId: string, @Body() body: LotAttributeRequest, @Req() req: VBInterceptorRequest): Promise<LotAttributeResponse | ErrorResponse> {
    try {
      const lot: LotDto = await this.lotsHandler.get(lotId);
      const userId: string = req.accountMappingsId;
      const dtos: Array<LotAttributeDto> = this.createDtoArray(body.lotAttributes, LotAttributeDto, { lotId: lot.id, createdBy: userId });

      const items: ListResultset<LotAttributeDto> = await this.handler.createOrReplaceBulk(dtos, { lotId: lot.id });

      return this.createResponseList(items);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Patch('/attributes/:id')
  async updateAttribute(
    @Param('lotId') lotId: string,
    @Param('id') id: string,
    @Body() body: LotAttributeRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<LotAttributeResponse | ErrorResponse> {
    try {
      const lot: LotDto = await this.lotsHandler.get(lotId);
      await this.handler.get(id);
      const userId: string = req.accountMappingsId;
      const dto: LotAttributeDto = this.createDtoFromRequest(body);
      dto.lotId = lot.id;
      const item: LotAttributeDto = await this.handler.create(userId, dto);

      return this.createResponseFromDto(item);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Delete('attributes/:id')
  async deleteAttribute(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      await this.handler.delete(userId, id);

      return;
    } catch (error) {
      return new ErrorResponse(error, 'delete');
    }
  }

  @Get('attributes/:id')
  async getattributes(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotAttributeResponse | ErrorResponse> {
    req;
    try {
      const item: LotAttributeDto = await this.handler.get(id);
      const response: LotAttributeResponse = this.createResponseFromDto(item);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  @Get('attributes')
  async getAllAttributes(@Param('lotId') lotId: string, @Query('page') page: number, @Query('size') size: number): Promise<LotAttributesListResponse | ErrorResponse> {
    try {
      const lot: LotDto = await this.lotsHandler.get(lotId);
      const params: Record<string, any>[] = [{ lotId: lot.id, deletedOn: null }];
      const items: ListResultset<LotAttributeDto> = await this.handler.getAll(page, size, params);
      const response: LotAttributesListResponse = this.createResponseList(items);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Patch('attributes/restore/:id')
  async restoreAttribute(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotAttributeResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const restoredItem: LotAttributeDto = await this.handler.restore(userId, id);
      const response: LotAttributeResponse = this.createResponseFromDto(restoredItem);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'restore');
    }
  }
}
