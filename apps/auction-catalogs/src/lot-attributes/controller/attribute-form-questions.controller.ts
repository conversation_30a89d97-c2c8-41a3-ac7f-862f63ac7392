import { BaseController } from '@mvc/controllers/base.controller';
import { AttributeFormQuestionsHandler } from '../handler/attribute-form-questions.handler';
import { AttributeFormQuestionRequest } from '../http/requests/attribute-form-question.request';
import { AttributeFormQuestionDto } from '../dtos/attribute-form-question.dto';
import { AttributeFormQuestionResponse } from '../http/responses/attribute-form-question.response';
import { AttributeFormQuestionsListResponse } from '../http/responses/attribute-form-questions-list.response';
import { Controller, Get, Param } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';

@Controller('admin/attribute-form-questions')
export class AttributeFormQuestionsController extends BaseController<
  AttributeFormQuestionsHandler,
  AttributeFormQuestionRequest,
  AttributeFormQuestionDto,
  AttributeFormQuestionResponse,
  AttributeFormQuestionsListResponse
> {
  constructor(handler: AttributeFormQuestionsHandler) {
    super(handler);
  }

  createDtoFromRequest(request: AttributeFormQuestionRequest): AttributeFormQuestionDto {
    return new AttributeFormQuestionDto(request);
  }

  createResponseFromDto(dto: AttributeFormQuestionDto): AttributeFormQuestionResponse {
    return new AttributeFormQuestionResponse(dto);
  }

  createResponseList(list: ListResultset<AttributeFormQuestionDto>): AttributeFormQuestionsListResponse {
    return new AttributeFormQuestionsListResponse(list);
  }

  @Get('category/:category')
  async listAllQuestionsByCategory(@Param('category') category: string): Promise<AttributeFormQuestionsListResponse> {
    const param: Record<string, any> = { category: category };

    const items: ListResultset<AttributeFormQuestionDto> = await this.handler.getAllQuestionsByCategory(1, -1, param);

    return this.createResponseList(items);
  }
}
