import { BaseController } from '@mvc/controllers/base.controller';
import { AttributeFormsHandler } from '../handler/attribute-forms.handler';
import { AttributeFormRequest } from '../http/requests/attribute-form.request';
import { AttributeFormDto } from '../dtos/attribute-form.dto';
import { AttributeFormResponse } from '../http/responses/attribute-form.response';
import { AttributeFormsListResponse } from '../http/responses/attribute-forms-list.response';
import { Controller, Get, Param } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { RawAttributeFormQuestionResponse } from '../http/responses/raw-attribute-form-question.response';

@Controller('admin/attribute-forms')
export class AttributeFormsController extends BaseController<AttributeFormsHandler, AttributeFormRequest, AttributeFormDto, AttributeFormResponse, AttributeFormsListResponse> {
  constructor(handler: AttributeFormsHandler) {
    super(handler);
  }
  createDtoFromRequest(request: AttributeFormRequest): AttributeFormDto {
    return new AttributeFormDto(request);
  }

  createResponseFromDto(dto: AttributeFormDto): AttributeFormResponse {
    return new AttributeFormResponse(dto);
  }

  createResponseList(list: ListResultset<AttributeFormDto>): AttributeFormsListResponse {
    return new AttributeFormsListResponse(list);
  }

  @Get('/category/:category')
  async getAllByCategory(@Param('category') category: string): Promise<AttributeFormsListResponse> {
    const params: Record<string, any>[] = [{ category: category }];
    const list: any = await this.handler.getAllCustom(1, -1, params, AttributeFormDto);

    return this.createResponseList(list);
  }

  @Get('drawForm/:category')
  async drawForm(@Param('category') category: string): Promise<RawAttributeFormQuestionResponse> {
    return new RawAttributeFormQuestionResponse(await this.handler.drawForm(category));
  }
}
