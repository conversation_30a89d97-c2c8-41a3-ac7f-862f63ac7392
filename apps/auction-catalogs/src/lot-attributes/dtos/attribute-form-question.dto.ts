export class AttributeFormQuestionDto {
  readonly id: string;
  readonly attributeFormsId: string;
  readonly label: string;
  readonly basicKey: string;
  readonly displayOrder: number;
  readonly attributeKey: string;

  constructor(row: any) {
    this.id = row.id;
    this.attributeFormsId = row.attributeFormsId;
    this.label = row.label;
    this.basicKey = row.basicKey;
    this.displayOrder = row.displayOrder;
    this.attributeKey = row.attributeKey;
  }
}
