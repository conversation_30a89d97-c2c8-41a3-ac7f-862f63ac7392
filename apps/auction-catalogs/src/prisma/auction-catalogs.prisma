datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider        = "prisma-client-js"
  output          = "./generated/auction-catalogs-db-client"
  seed            = "./seed/seed.ts"
  previewFeatures = ["tracing"]
  binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
}

model Resources {
  id           String    @id @unique @default(uuid())
  resourceType String    @unique @db.VarChar(45)
  location     String    @db.VarChar(200)
  cdnPath      String    @db.VarChar(255)
  createdOn    DateTime  @default(now())
  createdBy    String    @default("SYSTEM") @db.VarChar(36)
  updatedOn    DateTime  @default(now()) @updatedAt
  updatedBy    String    @default("SYSTEM") @db.VarChar(36)
  deletedOn    DateTime?
  deletedBy    String?   @db.VarChar(36)
}

model Upload {
  id         String @id @unique @default(uuid())
  resourceId String @db.Uuid
  metadata   String @db.Var<PERSON>har(100)
  proxyId    String @db.VarChar(36)

  createdOn DateTime  @default(now())
  createdBy String    @default("SYSTEM") @db.VarChar(36)
  updatedOn DateTime  @updatedAt
  updatedBy String    @default("SYSTEM") @db.VarChar(36)
  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  @@unique([resourceId, proxyId])
}

model UploadItems {
  id             String  @id @unique @default(uuid())
  uploadsId      String  @db.Uuid
  uploadFilename String? @db.VarChar(100)
  fileType       String? @db.VarChar(30)
  size           Int?    @default(0)

  createdOn DateTime  @default(now())
  createdBy String    @default("SYSTEM") @db.VarChar(36)
  updatedOn DateTime  @updatedAt
  updatedBy String    @default("SYSTEM") @db.VarChar(36)
  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)
}

model Auctions {
  id        String   @id @default(uuid())
  createdOn DateTime @default(now())
  createdBy String   @default("SYSTEM") @db.VarChar(36)

  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)

  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  name                  String                  @db.VarChar(300)
  description           String
  fromDatetime          DateTime
  toDatetime            DateTime
  isViewable            Int                     @default(value: 0)
  status                String
  numLots               Int                     @default(value: 0)
  isShip                Int
  isProxyBid            Int
  isReservable          Int
  isBuyNow              Int
  isMakeOffer           Int
  isQuickBid            Int
  isInlineBidding       Int
  isLargeBidConfirm     Int
  isReserveMinBid       Int                     @default(value: 0)
  isDisplaySoldItems    Boolean                 @default(value: false)
  isDisplayFinalPrice   Boolean                 @default(value: false)
  isSchedulingRequired  Boolean                 @default(value: false)
  isPreviewLots         Boolean                 @default(value: false)
  isLiveAuction         Boolean                 @default(value: false)
  invoicesApproved      Boolean                 @default(false)
  tags                  String                  @db.VarChar(250)
  code                  String                  @db.VarChar(2)
  invoiceTemplatesId    String
  buyerPremium          Decimal                 @db.Decimal(10, 2)
  featureDisplayOrder   Int
  paymentTypes          String[]                @db.Text
  thumbnailId           String?
  auctionLots           AuctionLots[]
  pickupLocation        String                  @default(value: "") @db.VarChar(45)
  termsConditions       String                  @default(value: "")
  invoiceText           String                  @default(value: "")
  bidIncrements         BidIncrements[]
  isCharity             Int                     @default(value: 0)
  auctionPurchaseOrders AuctionPurchaseOrders[]
  auctionType           String                  @default(value: "") @db.VarChar(50)
  Groups                Groups[]
  snipingExtension      Int                     @default(value: 0)
  bidSnipeWindow        Int                     @default(value: 0)
  AuctionTaxes          AuctionTaxes?

  @@index([id])
}

model AuctionTaxes {
  id String @id @default(uuid()) @db.Uuid

  auctionsId String   @unique
  auction    Auctions @relation(fields: [auctionsId], references: [id])
  taxTypesId Int
  taxType    TaxTypes @relation(fields: [taxTypesId], references: [id])

  @@index([auctionsId])
  @@index([taxTypesId])
}

model BidIncrements {
  id         String    @id @default(uuid())
  auction    Auctions? @relation(fields: [auctionId], references: [id])
  auctionId  String    @db.VarChar(36)
  priceAbove Decimal   @db.Decimal(10, 2)
  increment  Decimal   @db.Decimal(10, 2)
}

model Lots {
  id        String   @id @default(uuid())
  createdOn DateTime @default(now())
  createdBy String   @default("SYSTEM") @db.VarChar(36)

  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)

  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  title         String  @db.VarChar(300)
  description   String
  status        String
  code          String  @db.VarChar(15)
  reserveAmount Decimal @db.Decimal(10, 2)
  startAmount   Decimal @default(0.00) @db.Decimal(10, 2)
  tags          String  @db.VarChar(250)
  condition     String
  category      String  @db.VarChar(50)
  region        String  @db.VarChar(25)
  thumbnailId   String?

  auctionLots      AuctionLots?
  pickupLocation   String       @default(value: "") @db.VarChar(45)
  subtitle         String       @default(value: "") @db.VarChar(300)
  consignorNumber  String       @default(value: "") @db.VarChar(36)
  excludedRegions  String[]     @default(value: [""]) @db.VarChar(500)
  isRegionSpecific Int          @default(value: 0)
  inputDevice      String       @default(value: "Web") @db.VarChar(50)
  inputMethod      String       @default(value: "Barcode") @db.VarChar(50)
  isAcceptOffers   Boolean      @default(value: false)

  lotPurchaseOrderItems LotPurchaseOrderItems[]
  editHistories         LotEditHistory[]
  lotItemUserNotes      LotItemUserNotes[]
  lotTemplates          LotTemplates?
  LotTaxes              LotTaxes?

  @@index([id])
}

model LotTaxes {
  id String @id @default(uuid()) @db.Uuid

  lotsId     String   @unique
  lot        Lots     @relation(fields: [lotsId], references: [id])
  taxTypesId Int
  taxType    TaxTypes @relation(fields: [taxTypesId], references: [id])

  @@index([lotsId])
  @@index([taxTypesId])
}

model AuctionLots {
  id        String   @id @default(uuid())
  createdOn DateTime @default(now())
  createdBy String   @default("SYSTEM") @db.VarChar(36)

  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)

  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  auctions            Auctions? @relation(fields: [auctionId], references: [id], onDelete: Cascade)
  auctionId           String
  lots                Lots?     @relation(fields: [lotId], references: [id], onDelete: Cascade)
  lotId               String    @unique
  number              Int?
  suffix              String    @default(value: "") @db.VarChar(5)
  displayOrder        Int?
  status              String?
  toDatetime          DateTime?
  currentBid          Decimal   @default(0.00) @db.Decimal(10, 2)
  currentHighBidderId String    @default("") @db.VarChar(36)
  buyNowPrice         Decimal   @default(0.00) @db.Decimal(10, 2)

  groupsId String? @db.Uuid
  group    Groups? @relation(fields: [groupsId], references: [id])

  nextBidMeetsReserve Boolean @default(false)

  @@index([id])
  @@index([lotId])
}

model InvoiceTemplates {
  id        String    @id @default(uuid())
  createdOn DateTime  @default(now())
  createdBy String    @default("SYSTEM") @db.VarChar(36)
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)
  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  filename    String @db.VarChar(100)
  name        String @db.VarChar(50)
  description String @db.VarChar(500)
  thumbnail   String @db.VarChar(45)
  html        String @default("") @db.Text
}

model BasicLists {
  id        String    @id @default(uuid())
  createdOn DateTime  @default(now())
  createdBy String    @default("SYSTEM") @db.VarChar(36)
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)
  deletedOn DateTime?
  deletedBy String?   @db.VarChar(36)

  displayOrder Int    @default(0)
  groupName    String @db.VarChar(45)
  value        String @db.VarChar(100)
  basicKey     String @default(value: "") @db.VarChar(50)
}

model ApplicationPreferences {
  id            String    @id @default(uuid()) @db.Uuid
  createdOn     DateTime  @default(now())
  createdBy     String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn     DateTime? @updatedAt
  updatedBy     String?   @db.Uuid
  deletedOn     DateTime?
  deletedBy     String?   @db.Uuid
  preferenceKey String    @unique
  value         String
  description   String

  @@map("ApplicationPreferences")
}

model ItemMappings {
  id          String      @id @default(uuid()) @db.Uuid
  mappingType MappingType
  proxyId     String      @db.Uuid
  barcode     String?     @db.VarChar(50)

  TransactionLineItems TransactionLineItems[]
  Templates            Templates[]
  InvoiceItems         InvoiceItems[]
}

enum MappingType {
  Lots
  POItems
  Products
  LineItems
}

model TransactionRequests {
  id                String          @id @default(uuid()) @db.Uuid
  createdOn         DateTime        @default(now())
  createdBy         String          @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn         DateTime?       @updatedAt
  updatedBy         String?         @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?         @db.Uuid
  transactionType   TransactionType
  amount            Decimal         @db.Decimal(10, 2)
  invoicesId        String          @db.Uuid
  description       String          @db.VarChar(45)
  invoiceNumber     String          @db.VarChar(45)
  invoicePaymentsId String?         @db.Uuid

  TransactionShipping    TransactionShipping[]
  TransactionAddresses   TransactionAddresses[]
  TransactionCreditCards TransactionCreditCards[]
}

model TransactionLineItems {
  id                    String       @id @default(uuid()) @db.Uuid
  transactionRequestsId String       @db.Uuid
  itemMappingsId        String       @db.Uuid
  name                  String       @db.VarChar(200)
  description           String       @db.VarChar(200)
  quantity              Int
  unitPrice             Decimal      @db.Decimal(10, 2)
  itemMappings          ItemMappings @relation(fields: [itemMappingsId], references: [id])
}

enum TransactionType {
  CreditCard
  PayPal
}

model TransactionShipping {
  id                    String              @id @default(uuid()) @db.Uuid
  transactionRequestsId String              @db.Uuid
  name                  String              @db.VarChar(45)
  amount                Decimal             @db.Decimal(10, 2)
  description           String              @db.VarChar(200)
  transactionRequests   TransactionRequests @relation(fields: [transactionRequestsId], references: [id])
}

model TransactionAddresses {
  id                    String              @id @default(uuid()) @db.Uuid
  transactionRequestsId String              @db.Uuid
  addressType           AddressType
  firstname             String              @db.VarChar(45)
  lastname              String              @db.VarChar(45)
  companyName           String              @db.VarChar(100)
  address1              String              @db.VarChar(45)
  address2              String              @db.VarChar(45)
  city                  String              @db.VarChar(45)
  state                 String              @db.VarChar(10)
  zip                   String              @db.VarChar(10)
  country               String              @db.VarChar(3)
  transactionRequests   TransactionRequests @relation(fields: [transactionRequestsId], references: [id])
}

enum AddressType {
  Billing
  Shipping
}

model TransactionCreditCards {
  id                    String              @id @default(uuid()) @db.Uuid
  transactionRequestsId String              @db.Uuid
  last4                 String              @db.Char(4)
  expirationDate        String              @db.Char(4)
  cardType              CardType
  transactionRequests   TransactionRequests @relation(fields: [transactionRequestsId], references: [id])
}

enum CardType {
  Visa
  MasterCard
  Amex
}

model TransactionResponses {
  id                          String                        @id @default(uuid()) @db.Uuid
  createdOn                   DateTime                      @default(now())
  createdBy                   String                        @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn                   DateTime?                     @updatedAt
  updatedBy                   String?                       @db.Uuid
  deletedOn                   DateTime?
  deletedBy                   String?                       @db.Uuid
  responseCode                String?                       @db.VarChar(45)
  authCode                    String                        @db.VarChar(6)
  avsResultCode               String                        @db.Char(1)
  cvvResultCode               String                        @db.Char(1)
  cavvResultCode              String                        @db.Char(1)
  transId                     String                        @db.VarChar(12)
  accountNumber               String                        @db.VarChar(8)
  accountType                 String                        @db.VarChar(20)
  transHashSha2               String                        @db.VarChar(128)
  networkTransId              String                        @db.VarChar(25)
  transactionRequestsId       String                        @db.VarChar(36)
  TransactionResponseMessages TransactionResponseMessages[]

  @@index([transactionRequestsId], name: "fk_TransactionResponses_1_idx")
}

model TransactionResponseMessages {
  id                     String               @id @default(uuid()) @db.Uuid
  transactionResponsesId String               @db.Uuid
  code                   String               @db.VarChar(6)
  description            String               @db.VarChar(200)
  transactionResponses   TransactionResponses @relation(fields: [transactionResponsesId], references: [id])

  @@index([transactionResponsesId], name: "fk_TransactionResponseMessages_1_idx")
}

model Vendors {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  name           String           @unique @db.VarChar(45)
  phone          String           @unique @db.VarChar(12)
  email          String           @unique @db.VarChar(100)
  url            String           @unique @db.VarChar(100)
  purchaseOrders PurchaseOrders[]
  vendorProducts VendorProducts[]

  @@map("Vendors")
}

enum ShippingMethod {
  Standard
  Priority
  Express
}

model PurchaseOrders {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  shippingMethod ShippingMethod
  shippingTerms  String         @db.VarChar(45)
  deliveryDate   DateTime
  subTotal       Decimal?       @db.Decimal(10, 2)
  tax            Decimal?       @db.Decimal(10, 2)
  other          Decimal?       @db.Decimal(10, 2)
  total          Decimal?       @db.Decimal(10, 2)
  comments       String         @db.Text

  vendorsId             String                  @db.Uuid
  vendor                Vendors                 @relation(fields: [vendorsId], references: [id])
  auctionPurchaseOrders AuctionPurchaseOrders[]
  purchaseOrderItems    PurchaseOrderItems[]

  @@map("PurchaseOrders")
}

model AuctionPurchaseOrders {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  auctionsId String   @db.VarChar(36)
  auction    Auctions @relation(fields: [auctionsId], references: [id])

  purchaseOrdersId String         @db.Uuid
  purchaseOrder    PurchaseOrders @relation(fields: [purchaseOrdersId], references: [id], onDelete: Cascade)

  @@map("AuctionPurchaseOrders")
}

model Products {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  title       String @db.VarChar(300)
  description String

  tags        String  @db.VarChar(250)
  category    String  @db.VarChar(250)
  thumbnailId String?

  unitCost           Decimal?             @db.Decimal(10, 2)
  retailPrice        Decimal?             @db.Decimal(10, 2)
  productInventories ProductInventories[]
  productCodes       ProductCodes[]
  vendorProducts     VendorProducts[]
  purchaseOrderItems PurchaseOrderItems[]

  @@map("Products")
}

model ProductCodes {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  code       String   @db.VarChar(25)
  codeType   String   @db.VarChar(25)
  productsId String   @db.Uuid
  product    Products @relation(fields: [productsId], references: [id])

  @@map("ProductCodes")
}

model ProductInventories {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  productsId      String   @db.Uuid
  condition       String
  quantityInStock Int
  product         Products @relation(fields: [productsId], references: [id])

  @@map("ProductInventories")
}

model VendorProducts {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  productsId String   @db.Uuid
  vendorsId  String   @db.Uuid
  product    Products @relation(fields: [productsId], references: [id])
  vendor     Vendors  @relation(fields: [vendorsId], references: [id])

  @@map("VendorProducts")
}

model PurchaseOrderItems {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  quantityOrdered   Int?
  quantityDelivered Int?
  comments          String         @db.Text
  productsId        String         @db.Uuid
  purchaseOrdersId  String         @db.Uuid
  barcode           String         @db.VarChar(30)
  product           Products       @relation(fields: [productsId], references: [id])
  purchaseOrder     PurchaseOrders @relation(fields: [purchaseOrdersId], references: [id])

  lotPurchaseOrderItems LotPurchaseOrderItems[]

  @@map("PurchaseOrderItems")
}

model LotPurchaseOrderItems {
  id        String    @id @default(uuid())
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  purchaseOrderItemsId String             @db.Uuid
  lotsId               String             @db.VarChar(36)
  purchaseOrderItem    PurchaseOrderItems @relation(fields: [purchaseOrderItemsId], references: [id])
  lot                  Lots               @relation(fields: [lotsId], references: [id])

  @@map("LotPurchaseOrderItems")
}

model LotAttributes {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  lotId        String
  name         String @db.VarChar(100)
  attributeKey String @db.VarChar(50)
  value        String @db.VarChar(100)

  @@index([lotId])
  @@index([attributeKey])
  @@index([value])
}

model AttributeFormQuestions {
  id               String    @id @default(uuid()) @db.Uuid
  createdOn        DateTime  @default(now())
  createdBy        String    @db.Uuid
  updatedOn        DateTime?
  updatedBy        String?   @db.Uuid
  deletedOn        DateTime?
  deletedBy        String?   @db.Uuid
  attributeFormsId String    @db.Uuid
  label            String    @db.VarChar(45)
  groupName        String    @default("") @db.VarChar(45)
  displayOrder     Int
  attributeKey     String    @db.VarChar(50)

  // Relations
  attributeForm AttributeForms @relation(fields: [attributeFormsId], references: [id])

  @@index([attributeFormsId], name: "fk_AttributeFormQuestions_1_idx")
  @@map("AttributeFormQuestions")
}

model AttributeForms {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @db.Uuid
  updatedOn DateTime?
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid
  category  String    @db.VarChar(25)

  // Relations
  questions AttributeFormQuestions[]

  @@map("AttributeForms")
}

model Groups {
  id String @id @default(uuid()) @db.Uuid

  auctionsId String   @db.VarChar(36)
  auction    Auctions @relation(fields: [auctionsId], references: [id])

  name        String        @db.VarChar(300)
  AuctionLots AuctionLots[]
}

model LotEditHistory {
  id       String   @id @default(uuid())
  lotId    String
  editedOn DateTime @default(now())
  editedBy String   @db.VarChar(36)
  changes  Json
  reason   String
  lot      Lots     @relation(fields: [lotId], references: [id], onDelete: Cascade)
}

model LotItemUserNotes {
  id        String   @id @default(uuid())
  createdOn DateTime @default(now())
  createdBy String   @default("SYSTEM") @db.VarChar(36)

  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.VarChar(36)

  deletedOn         DateTime?
  deletedBy         String?   @db.VarChar(36)
  lotsId            String    @db.VarChar(36)
  lots              Lots?     @relation(fields: [lotsId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  accountMappingsId String    @db.VarChar(36)
  note              String
}

model Templates {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn DateTime? @updatedAt
  updatedBy String?   @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  itemMappingsId String @db.Uuid
  name           String @db.VarChar(100)
  body           Json   @map("body")

  // Relation to ItemMapping
  itemMapping ItemMappings   @relation(fields: [itemMappingsId], references: [id])
  lotTemplate LotTemplates[]

  @@index([itemMappingsId], name: "fk_Templates_1_idx")
  @@map("Templates")
}

model LotTemplates {
  id        String    @id @default(uuid()) @db.Uuid
  createdOn DateTime  @default(now())
  createdBy String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  deletedOn DateTime?
  deletedBy String?   @db.Uuid

  lotsId      String @unique
  templatesId String @db.Uuid

  lot      Lots      @relation(fields: [lotsId], references: [id])
  template Templates @relation(fields: [templatesId], references: [id])
}

model AuctionHouseMappings {
  id              String                  @id @default(uuid()) @db.Uuid
  auctionHousesId String                  @db.Uuid
  mappingType     AuctionHouseMappingType
  proxyId         String                  @db.Uuid

  AuctionHouses AuctionHouses @relation(fields: [auctionHousesId], references: [id])

  @@index([auctionHousesId], name: "fk_AuctionHouseMappings_1_idx")
  @@index([auctionHousesId], name: "AuctionHouseMappings_id_idx")
  @@index([proxyId], name: "AuctionHouseMappings_proxy_idx")
}

model AuctionHouses {
  id                   String                    @id @default(uuid()) @db.Uuid
  createdOn            DateTime                  @default(now())
  createdBy            String                    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn            DateTime?                 @updatedAt
  updatedBy            String?                   @db.Uuid
  deletedOn            DateTime?
  deletedBy            String?                   @db.Uuid
  name                 String                    @db.VarChar(45)
  ownerId              String                    @db.VarChar(36)
  // other fields for AuctionHouses model
  auctionHouseMappings AuctionHouseMappings[]
  preferences          AuctionHousePreferences[]
}

model AuctionHousePreferences {
  id              String          @id @default(uuid()) @db.Uuid
  createdOn       DateTime        @default(now())
  createdBy       String          @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn       DateTime?       @updatedAt
  updatedBy       String?         @db.Uuid
  deletedOn       DateTime?
  deletedBy       String?         @db.Uuid
  auctionHousesId String          @db.Uuid
  preferenceKey   String          @db.VarChar(45)
  value           String          @db.Text
  description     String          @db.VarChar(255)
  name            String          @db.VarChar(100)
  preferenceGroup PreferenceGroup
  auctionHouse    AuctionHouses   @relation(fields: [auctionHousesId], references: [id])

  @@index([auctionHousesId], name: "fk_AuctionHousePreferences_1_idx")
}

enum AuctionHouseMappingType {
  AUCTION
  LOT
}

enum PreferenceGroup {
  Users
  Emails
  Invoices
  Auctions
}

model Invoices {
  id                  String       @id @default(uuid()) @db.Uuid
  createdOn           DateTime     @default(now())
  createdBy           String       @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn           DateTime?    @updatedAt
  updatedBy           String?      @db.Uuid
  deletedOn           DateTime?
  deletedBy           String?      @db.Uuid
  mode                Mode
  auctionsId          String       @db.VarChar(36)
  number              Int          @unique @default(autoincrement())
  bidderId            String       @db.VarChar(36)
  notes               Json
  specialInstructions String       @db.VarChar(1000)
  poNumber            String       @db.VarChar(8)
  dueDate             DateTime
  isShipping          Boolean
  invoiceTemplatesId  String       @db.Uuid
  currencyType        CurrencyType
  itemsSubtotal       Decimal      @db.Decimal(10, 2)
  shippingSubtotal    Decimal      @db.Decimal(10, 2)
  taxesSubtotal       Decimal      @db.Decimal(10, 2)
  buyerPremiumTotal   Decimal      @default(0) @db.Decimal(10, 2)
  totalAmount         Decimal      @db.Decimal(10, 2)
  amountDue           Decimal      @db.Decimal(10, 2)
  invoicesApproved    Boolean      @default(false)
  isPaidInFull        Boolean      @default(false)

  InvoiceStatuses          InvoiceStatuses[]
  InvoiceShippingAddresses InvoiceShippingAddresses[]
  InvoiceBuyers            InvoiceBuyers[]
  InvoicePayments          InvoicePayments[]
  InvoiceItems             InvoiceItems[]
  InvoiceShipping          InvoiceShipping[]
  InvoiceNotes             InvoiceNotes[]
  InvoicePullers           InvoicePullers[]
  InvoicePickupBins        InvoicePickupBins[]
  invoiceSplits            InvoiceSplits[]            @relation("InvoiceSplits_invoiceId")
  newInvoiceSplits         InvoiceSplits[]            @relation("InvoiceSplits_newInvoiceId")

  @@index([invoiceTemplatesId], name: "fk_nvcs_nvctmplts_idx")
}

model InvoiceStatuses {
  id         String            @id @default(uuid()) @db.Uuid
  createdOn  DateTime          @default(now())
  createdBy  String            @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn  DateTime?         @updatedAt
  updatedBy  String?           @db.Uuid
  deletedOn  DateTime?
  deletedBy  String?           @db.Uuid
  invoicesId String            @db.Uuid
  invoices   Invoices          @relation(fields: [invoicesId], references: [id])
  statusType InvoiceStatusType

  @@index([invoicesId], name: "fk_nvcsstts_nvcs_idx")
}

model InvoiceShippingAddresses {
  id                 String    @id @default(uuid()) @db.Uuid
  createdOn          DateTime  @default(now())
  createdBy          String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn          DateTime? @updatedAt
  updatedBy          String?   @db.Uuid
  deletedOn          DateTime?
  deletedBy          String?   @db.Uuid
  address1           String    @db.VarChar(45)
  address2           String    @db.VarChar(45)
  city               String    @db.VarChar(45)
  province           String    @db.VarChar(45)
  country            String    @db.VarChar(45)
  postalCode         String    @db.VarChar(7)
  buzzer             String    @db.VarChar(45)
  accountAddressesId String    @db.VarChar(36)
  invoicesId         String    @db.Uuid
  invoices           Invoices  @relation(fields: [invoicesId], references: [id])

  @@index([invoicesId], name: "fk_nvcshppng_nvcs_idx")
}

model InvoiceBuyers {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  firstname         String    @db.VarChar(50)
  lastname          String    @db.VarChar(50)
  email             String    @db.VarChar(200)
  telephone         String    @db.VarChar(45)
  companyName       String    @db.VarChar(50)
  accountMappingsId String    @db.VarChar(36)
  invoicesId        String?   @db.Uuid
  invoices          Invoices? @relation(fields: [invoicesId], references: [id])

  @@index([invoicesId], name: "fk_nvcbyrs_nvcs_idx")
}

model InvoicePayments {
  id              String      @id @default(uuid()) @db.Uuid
  createdOn       DateTime    @default(now())
  createdBy       String      @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn       DateTime?   @updatedAt
  updatedBy       String?     @db.Uuid
  deletedOn       DateTime?
  deletedBy       String?     @db.Uuid
  invoicesId      String      @db.Uuid
  paymentTypes    PaymentType
  isPartial       Boolean
  amountPaid      Decimal     @db.Decimal(10, 2)
  currentOwing    Decimal     @default(-1) @db.Decimal(10, 2)
  transactionCode String?     @db.VarChar(25)
  paymentDate     DateTime    @default(now())
  invoices        Invoices    @relation(fields: [invoicesId], references: [id])
  location        String      @default("Online") @db.VarChar(100)

  @@index([invoicesId], name: "fk_nvcpmnts_nvcs_idx")
}

model InvoiceItems {
  id                 String        @id @default(uuid()) @db.Uuid
  createdOn          DateTime      @default(now())
  createdBy          String        @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn          DateTime?     @updatedAt
  updatedBy          String?       @db.Uuid
  deletedOn          DateTime?
  deletedBy          String?       @db.Uuid
  invoicesId         String        @db.Uuid
  itemMappingsId     String?       @db.Uuid
  quantity           Int
  price              Decimal       @db.Decimal(10, 2)
  name               String        @db.VarChar(200)
  buyerPremiumAmount Decimal       @db.Decimal(10, 2)
  invoices           Invoices      @relation(fields: [invoicesId], references: [id])
  ItemMappings       ItemMappings? @relation(fields: [itemMappingsId], references: [id])

  invoiceItemTaxes InvoiceItemTaxes[]

  @@index([invoicesId], name: "fk_nvctms_nvcs_idx")
  @@index([itemMappingsId], name: "fk_invitems_itemmappings_idx")
}

model ShippingVendors {
  id              Int               @id @default(autoincrement())
  name            String            @db.VarChar(255)
  trackUrl        String            @db.VarChar(255)
  logoUrl         String            @db.VarChar(255)
  methods         String[]          @default(["Ground"]) @db.VarChar(100)
  invoiceShipping InvoiceShipping[]
  trackingNumbers TrackingNumbers[]

  @@map("ShippingVendors") // Map the model to the correct table name in the database if needed
}

model InvoiceShipping {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  invoicesId        String    @map("invoicesId") @db.Uuid
  weight            Float
  shippingVendorsId Int       @map("shippingVendorsId")
  subtotal          Decimal   @db.Decimal(10, 2)
  numPieces         Int       @map("numPieces")
  isApproved        Boolean   @default(false) @map("isApproved")
  isBuyerApproved   Boolean   @default(false) @map("isBuyerApproved")
  method            String    @default("Ground") @db.VarChar(10)

  invoices        Invoices        @relation(fields: [invoicesId], references: [id])
  shippingVendors ShippingVendors @relation(fields: [shippingVendorsId], references: [id])

  trackingNumbers      TrackingNumbers[]
  invoiceShippingTaxes InvoiceShippingTaxes[]

  @@index([invoicesId], name: "fk_nvcsshppng_nvcs_idx")
  @@index([shippingVendorsId], name: "fk_nvcsshppng_shppngvndrs_idx")
}

model TrackingNumbers {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  number            String    @db.VarChar(45)
  shippingVendorsId Int
  invoiceShippingId String    @db.Uuid

  shippingVendors ShippingVendors @relation(fields: [shippingVendorsId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  invoiceShipping InvoiceShipping @relation(fields: [invoiceShippingId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([shippingVendorsId], name: "fk_trckngnmbrs_shppngvndrs_idx")
  @@index([invoiceShippingId], name: "fk_trckngnmbrs_nvcshppng_idx")
}

model InvoiceShippingTaxes {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  invoiceShippingId String    @db.Uuid
  taxTypesId        Int
  taxAmount         Decimal   @db.Decimal(10, 2)

  invoiceShipping InvoiceShipping @relation(fields: [invoiceShippingId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  taxTypes        TaxTypes        @relation(fields: [taxTypesId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([invoiceShippingId], name: "fk_nvcshppngtxs_nvcshppng_idx")
  @@index([taxTypesId], name: "fk_nvcshppngtxs_txtps_idx")
}

model InvoiceItemTaxes {
  id             String    @id @default(uuid()) @db.Uuid
  createdOn      DateTime  @default(now())
  createdBy      String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn      DateTime? @updatedAt
  updatedBy      String?   @db.Uuid
  deletedOn      DateTime?
  deletedBy      String?   @db.Uuid
  invoiceItemsId String    @db.Uuid
  taxTypesId     Int
  taxAmount      Decimal   @db.Decimal(10, 2)

  invoiceItems InvoiceItems @relation(fields: [invoiceItemsId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  taxTypes     TaxTypes     @relation(fields: [taxTypesId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([invoiceItemsId], name: "fk_nvctmtxs_nvctms_idx")
  @@index([taxTypesId], name: "fk_nvctmtxs_txtps_idx")
}

model InvoicePullers {
  id         String    @id @default(uuid()) @db.Uuid
  createdOn  DateTime  @default(now())
  createdBy  String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn  DateTime? @updatedAt
  updatedBy  String?   @db.Uuid
  deletedOn  DateTime?
  deletedBy  String?   @db.Uuid
  assignerId String    @db.Uuid
  pullerId   String    @db.Uuid
  startTime  DateTime?
  endTime    DateTime?
  invoicesId String    @db.Uuid

  // Foreign Key
  invoices Invoices @relation(fields: [invoicesId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([invoicesId], name: "fk_InvoicePullers_1_idx")
}

model InvoicePickupBins {
  id         String    @id @default(uuid()) @db.Uuid
  createdOn  DateTime  @default(now())
  createdBy  String    @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  updatedOn  DateTime? @updatedAt
  updatedBy  String?   @db.Uuid
  deletedOn  DateTime?
  deletedBy  String?   @db.Uuid
  invoicesId String    @db.Uuid
  pickupBin  String    @db.VarChar(50)
  pickedUpOn DateTime?

  invoices Invoices @relation(fields: [invoicesId], references: [id])
}

model TaxTypes {
  id      Int    @id @default(autoincrement())
  name    String @db.VarChar(255)
  percent Float

  invoiceShippingTaxes InvoiceShippingTaxes[]
  invoiceItemTaxes     InvoiceItemTaxes[]
  AuctionTaxes         AuctionTaxes[]
  LotTaxes             LotTaxes[]
}

model InvoiceNotes {
  id          String    @id @default(uuid()) @db.Uuid
  createdOn   DateTime  @default(now())
  createdBy   String    @db.Uuid
  updatedOn   DateTime? @updatedAt
  updatedBy   String?   @db.Uuid
  deletedOn   DateTime?
  deletedBy   String?   @db.Uuid
  instruction String    @db.VarChar(250)
  isAdmin     Boolean   @db.Boolean
  authorName  String    @db.VarChar(45)
  invoicesId  String    @db.Uuid

  invoices Invoices @relation(fields: [invoicesId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([invoicesId], name: "fk_InvoiceNotes_1_idx")
  @@map("InvoiceNotes")
}

model Timeslots {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  pickupTime        String    @db.VarChar(18)
  accountMappingsId String    @db.Uuid
  auctionsId        String    @db.Uuid
  staffId           String?   @db.Uuid
  status            String
  invoicesId        String    @db.Uuid
  index             Int
  proxyName         String    @db.VarChar(50)
  proxyEmail        String    @db.VarChar(50)
  isProxyPickup     Boolean   @default(false)

  @@index([auctionsId], name: "fk_Timeslots_1_idx")
}

// Enums

enum Mode {
  Draft
  Published
  Complete
}

enum CurrencyType {
  CAD
  USD
}

enum InvoiceStatusType {
  PENDING_ADMIN_REVIEW                @map("pending admin review")
  PENDING_BUYER_REVIEW                @map("pending buyer review")
  REQUIRES_SHIPPING_QUOTE             @map("requires shipping quote")
  PENDING_SHIPPING_QUOTE_REVIEW       @map("pending shipping quote review")
  PENDING_BUYER_SHIPPING_QUOTE_REVIEW @map("pending buyer shipping quote review")
  AWAITING_ASSEMBLY                   @map("awaiting assembly")
  PENDING_ASSEMBLY_REVIEW             @map("awaiting assembly review")
  AWAITING_PICKUP                     @map("awaiting pickup")
  COMPLETED                           @map("completed")
}

enum PaymentType {
  ETransfer    @map("e-transfer")
  Cash         @map("cash")
  InPersonCard @map("inPersonCard")
  OnlineCard   @map("onlineCard")
  Paypal       @map("paypal")
}

model PaymentProfiles {
  id                       String    @id @default(uuid()) @db.Uuid
  createdOn                DateTime  @default(now())
  createdBy                String    @db.Uuid
  deletedOn                DateTime?
  deletedBy                String?   @db.Uuid
  accountMappingsId        String    @db.Uuid
  merchantCustomerId       String    @db.VarChar(20)
  customerProfileId        String    @db.VarChar(20)
  customerPaymentProfileId String    @db.VarChar(20)
  originalNetworkTransId   String?   @db.VarChar(100)
  billingAddressId         String?   @db.Uuid
}

model PurchaseOffers {
  id                String              @id @default(uuid()) @db.Uuid
  createdOn         DateTime            @default(now())
  createdBy         String              @db.Uuid
  updatedOn         DateTime?           @updatedAt
  updatedBy         String?             @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?             @db.Uuid
  accountMappingsId String              @db.VarChar(36)
  itemMappingsId    String              @db.VarChar(36)
  quantity          Int                 @db.Integer
  price             Decimal             @db.Decimal(10, 2)
  status            PurchaseOfferStatus
  message           String              @db.VarChar(500)
  resolvedOn        DateTime?
  isAdmin           Boolean             @db.Boolean

  @@map("PurchaseOffers")
}

enum PurchaseOfferStatus {
  Pending
  Rejected
  Accepted
}

model RequestAudit {
  id                String   @id @default(uuid()) @db.Uuid
  createdOn         DateTime @default(now())
  createdBy         String   @default(dbgenerated("'********-0000-0000-0000-********0000'::uuid")) @db.Uuid
  accountMappingsId String?  @db.VarChar(36)
  path              String   @db.Text
  method            String   @db.VarChar(10)
  body              Json?
}

model InvoiceSplits {
  id           String   @id @default(uuid()) @db.Uuid
  invoiceId    String   @db.Uuid
  newInvoiceId String   @db.Uuid
  createdOn    DateTime @default(now())
  createdBy    String   @db.Uuid

  invoice    Invoices @relation(fields: [invoiceId], references: [id], name: "InvoiceSplits_invoiceId")
  newInvoice Invoices @relation(fields: [newInvoiceId], references: [id], name: "InvoiceSplits_newInvoiceId")
}
