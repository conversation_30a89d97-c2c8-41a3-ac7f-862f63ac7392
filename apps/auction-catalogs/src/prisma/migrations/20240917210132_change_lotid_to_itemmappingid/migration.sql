/*
  Warnings:

  - You are about to drop the column `lotsId` on the `InvoiceItems` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[number]` on the table `Invoices` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `itemMappingsId` to the `InvoiceItems` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "InvoiceItems" DROP COLUMN "lotsId",
ADD COLUMN     "itemMappingsId" UUID NOT NULL;

-- CreateIndex
CREATE INDEX "fk_invitems_itemmappings_idx" ON "InvoiceItems"("itemMappingsId");

-- CreateIndex
CREATE UNIQUE INDEX "Invoices_number_key" ON "Invoices"("number");

-- AddForeign<PERSON>ey
ALTER TABLE "InvoiceItems" ADD CONSTRAINT "InvoiceItems_itemMappingsId_fkey" FOREIGN KEY ("itemMappingsId") REFERENCES "ItemMappings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
