-- DropForeignKey
ALTER TABLE "AuctionLots" DROP CONSTRAINT "AuctionLots_auctionId_fkey";

-- DropForeignKey
ALTER TABLE "AuctionLots" DROP CONSTRAINT "AuctionLots_lotId_fkey";

-- AlterTable
ALTER TABLE "AuctionLots" ALTER COLUMN "auctionId" SET DATA TYPE TEXT,
ALTER COLUMN "lotId" SET DATA TYPE TEXT;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_auctionId_fkey" FOREIGN KEY ("auctionId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_lotId_fkey" FOREIGN KEY ("lotId") REFERENCES "Lots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
