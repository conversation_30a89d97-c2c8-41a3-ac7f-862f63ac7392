BEGIN;

ALTER TABLE "Invoices" ADD COLUMN "number_new" SERIAL; -- Using SERIAL creates an integer column with auto-increment by default
UPDATE "Invoices" SET "number_new" = "number"::integer;
ALTER TABLE public."Invoices" DROP COLUMN "number";
ALTER TABLE "Invoices" RENAME COLUMN "number_new" TO "number"; 

CREATE UNIQUE INDEX "Invoices_number_key" ON "Invoices"("number"); -- Auto-generated from Prisma.

COMMIT;
