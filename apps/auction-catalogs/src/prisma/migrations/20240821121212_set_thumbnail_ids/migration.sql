UPDATE public."Lots" l
SET "thumbnailId" = subquery."id"
    FROM (
             SELECT u."proxyId" as "lotId", ui."id"
             FROM public."Upload" u
                      JOIN public."UploadItems" ui ON ui."uploadsId" = u.id::uuid
             WHERE ui."deletedOn" IS NULL
             ORDER BY ui."createdOn" ASC
         ) AS subquery
WHERE l.id = subquery."lotId";


INSERT INTO "ApplicationPreferences" ("id", "preferenceKey", "value", "description")
VALUES
    ( gen_random_uuid(), 'THUMBNAIL_IMAGE_HEIGHT', '187', 'The height of a thumbnail'),
    ( gen_random_uuid(), 'THUMBNAIL_IMAGE_WIDTH', '182', 'The width of a thumbnail'),
    ( gen_random_uuid(), 'LOT_ITEM_IMAGE_HEIGHT', '700', 'The height of a display image'),
    ( gen_random_uuid(), 'LOT_ITEM_IMAGE_WIDTH', '700', 'The width of a display image');