-- CreateTable
CREATE TABLE "LotEditHistory" (
    "id" TEXT NOT NULL,
    "lotId" TEXT NOT NULL,
    "editedOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "editedBy" VARCHAR(36) NOT NULL,
    "changes" JSONB NOT NULL,
    "reason" TEXT NOT NULL,

    CONSTRAINT "LotEditHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LotEditHistory" ADD CONSTRAINT "LotEditHistory_lotId_fkey" FOREIGN KEY ("lotId") REFERENCES "Lots"("id") ON DELETE CASCADE ON UPDATE CASCADE;
