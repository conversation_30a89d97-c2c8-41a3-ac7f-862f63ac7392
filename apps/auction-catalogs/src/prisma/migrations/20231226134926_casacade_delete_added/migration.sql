-- DropForeign<PERSON>ey
ALTER TABLE "AuctionLots" DROP CONSTRAINT "AuctionLots_auctionId_fkey";

-- DropForeignKey
ALTER TABLE "AuctionLots" DROP CONSTRAINT "AuctionLots_lotId_fkey";

-- AlterTable
ALTER TABLE "Auctions" ALTER COLUMN "thumbnailId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Lots" ALTER COLUMN "thumbnailId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_auctionId_fkey" FOREIGN KEY ("auctionId") REFERENCES "Auctions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_lotId_fkey" FOREIGN KEY ("lotId") REFERENCES "Lots"("id") ON DELETE CASCADE ON UPDATE CASCADE;
