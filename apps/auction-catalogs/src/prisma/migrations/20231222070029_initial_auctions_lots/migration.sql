-- CreateTable
CREATE TABLE "Auctions" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "name" VARCHAR(300) NOT NULL,
    "description" VARCHAR(600) NOT NULL,
    "fromDatetime" TIMESTAMP(3) NOT NULL,
    "toDatetime" TIMESTAMP(3) NOT NULL,
    "isActive" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "numLots" INTEGER NOT NULL,
    "isShip" INTEGER NOT NULL,
    "isProxyBid" INTEGER NOT NULL,
    "isReservable" INTEGER NOT NULL,
    "isBuyNow" INTEGER NOT NULL,
    "isMakeOffer" INTEGER NOT NULL,
    "isQuickBid" INTEGER NOT NULL,
    "isInlineBidding" INTEGER NOT NULL,
    "isLargeBidConfirm" INTEGER NOT NULL,
    "tags" VARCHAR(250) NOT NULL,
    "code" VARCHAR(2) NOT NULL,
    "invoiceTemplatesId" VARCHAR(36) NOT NULL,
    "buyerPremium" DOUBLE PRECISION NOT NULL,
    "featureDisplayOrder" INTEGER NOT NULL,
    "paymentTypes" TEXT[],
    "thumbnailId" VARCHAR(36) NOT NULL,

    CONSTRAINT "Auctions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BidIncrements" (
    "id" TEXT NOT NULL,
    "auctionId" VARCHAR(36) NOT NULL,
    "priceAbove" DOUBLE PRECISION NOT NULL,
    "increment" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "BidIncrements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Lots" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "title" VARCHAR(300) NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "code" VARCHAR(15) NOT NULL,
    "reserveAmount" DOUBLE PRECISION NOT NULL,
    "tags" VARCHAR(250) NOT NULL,
    "isTaxable" INTEGER NOT NULL,
    "condition" TEXT NOT NULL,
    "category" VARCHAR(25) NOT NULL,
    "region" VARCHAR(25) NOT NULL,
    "thumbnailId" VARCHAR(25) NOT NULL,

    CONSTRAINT "Lots_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuctionLots" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "auctionId" VARCHAR(36) NOT NULL,
    "lotId" VARCHAR(36) NOT NULL,
    "number" INTEGER NOT NULL,
    "displayOrder" INTEGER NOT NULL,
    "status" TEXT NOT NULL,

    CONSTRAINT "AuctionLots_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BidIncrements_auctionId_key" ON "BidIncrements"("auctionId");

-- CreateIndex
CREATE UNIQUE INDEX "AuctionLots_lotId_key" ON "AuctionLots"("lotId");

-- AddForeignKey
ALTER TABLE "BidIncrements" ADD CONSTRAINT "BidIncrements_auctionId_fkey" FOREIGN KEY ("auctionId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_auctionId_fkey" FOREIGN KEY ("auctionId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_lotId_fkey" FOREIGN KEY ("lotId") REFERENCES "Lots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
