/*
  Warnings:

  - The values [PO] on the enum `MappingType` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[barcode]` on the table `ItemMappings` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "MappingType_new" AS ENUM ('Lots', 'POItems', 'Products');
ALTER TABLE "ItemMappings" ALTER COLUMN "mappingType" TYPE "MappingType_new" USING ("mappingType"::text::"MappingType_new");
ALTER TYPE "MappingType" RENAME TO "MappingType_old";
ALTER TYPE "MappingType_new" RENAME TO "MappingType";
DROP TYPE "MappingType_old";
COMMIT;

-- AlterTable
ALTER TABLE "ItemMappings" ADD COLUMN     "barcode" VARCHAR(16);

-- CreateIndex
CREATE UNIQUE INDEX "ItemMappings_barcode_key" ON "ItemMappings"("barcode");
