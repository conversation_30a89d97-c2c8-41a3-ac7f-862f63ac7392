/*
  Warnings:

  - You are about to drop the column `acceptedOffer` on the `PurchaseOffers` table. All the data in the column will be lost.
  - You are about to drop the column `initialOffer` on the `PurchaseOffers` table. All the data in the column will be lost.
  - You are about to drop the column `username` on the `PurchaseOffers` table. All the data in the column will be lost.
  - You are about to drop the `PurchaseOfferDiscussions` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `isAdmin` to the `PurchaseOffers` table without a default value. This is not possible if the table is not empty.
  - Added the required column `message` to the `PurchaseOffers` table without a default value. This is not possible if the table is not empty.
  - Added the required column `price` to the `PurchaseOffers` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "PurchaseOfferDiscussions" DROP CONSTRAINT "PurchaseOfferDiscussions_purchaseOffersId_fkey";

-- AlterTable
ALTER TABLE "PurchaseOffers" DROP COLUMN "acceptedOffer",
DROP COLUMN "initialOffer",
DROP COLUMN "username",
ADD COLUMN     "isAdmin" BOOLEAN NOT NULL,
ADD COLUMN     "message" VARCHAR(500) NOT NULL,
ADD COLUMN     "price" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "resolvedOn" TIMESTAMP(3);

-- DropTable
DROP TABLE "PurchaseOfferDiscussions";
