-- CreateTable
CREATE TABLE "ShippingVendors" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "trackUrl" VARCHAR(255) NOT NULL,
    "logoUrl" VARCHAR(255) NOT NULL,

    CONSTRAINT "ShippingVendors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceShipping" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoicesId" UUID NOT NULL,
    "weight" DOUBLE PRECISION NOT NULL,
    "shippingVendorsId" INTEGER NOT NULL,
    "subtotal" DECIMAL(10,2) NOT NULL,
    "numPieces" INTEGER NOT NULL,

    CONSTRAINT "InvoiceShipping_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrackingNumbers" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "number" VARCHAR(45) NOT NULL,
    "shippingVendorsId" INTEGER NOT NULL,
    "invoiceShippingId" UUID NOT NULL,

    CONSTRAINT "TrackingNumbers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceShippingTaxes" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoiceShippingId" UUID NOT NULL,
    "taxTypesId" INTEGER NOT NULL,
    "taxAmount" DECIMAL(10,2) NOT NULL,

    CONSTRAINT "InvoiceShippingTaxes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceItemTaxes" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoiceItemsId" UUID NOT NULL,
    "taxTypesId" INTEGER NOT NULL,
    "taxAmount" DECIMAL(10,2) NOT NULL,

    CONSTRAINT "InvoiceItemTaxes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TaxTypes" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "percent" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "TaxTypes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_nvcsshppng_nvcs_idx" ON "InvoiceShipping"("invoicesId");

-- CreateIndex
CREATE INDEX "fk_nvcsshppng_shppngvndrs_idx" ON "InvoiceShipping"("shippingVendorsId");

-- CreateIndex
CREATE INDEX "fk_trckngnmbrs_shppngvndrs_idx" ON "TrackingNumbers"("shippingVendorsId");

-- CreateIndex
CREATE INDEX "fk_trckngnmbrs_nvcshppng_idx" ON "TrackingNumbers"("invoiceShippingId");

-- CreateIndex
CREATE INDEX "fk_nvcshppngtxs_nvcshppng_idx" ON "InvoiceShippingTaxes"("invoiceShippingId");

-- CreateIndex
CREATE INDEX "fk_nvcshppngtxs_txtps_idx" ON "InvoiceShippingTaxes"("taxTypesId");

-- CreateIndex
CREATE INDEX "fk_nvctmtxs_nvctms_idx" ON "InvoiceItemTaxes"("invoiceItemsId");

-- CreateIndex
CREATE INDEX "fk_nvctmtxs_txtps_idx" ON "InvoiceItemTaxes"("taxTypesId");

-- AddForeignKey
ALTER TABLE "InvoiceShipping" ADD CONSTRAINT "InvoiceShipping_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "InvoiceShipping" ADD CONSTRAINT "InvoiceShipping_shippingVendorsId_fkey" FOREIGN KEY ("shippingVendorsId") REFERENCES "ShippingVendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "TrackingNumbers" ADD CONSTRAINT "TrackingNumbers_shippingVendorsId_fkey" FOREIGN KEY ("shippingVendorsId") REFERENCES "ShippingVendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "TrackingNumbers" ADD CONSTRAINT "TrackingNumbers_invoiceShippingId_fkey" FOREIGN KEY ("invoiceShippingId") REFERENCES "InvoiceShipping"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "InvoiceShippingTaxes" ADD CONSTRAINT "InvoiceShippingTaxes_invoiceShippingId_fkey" FOREIGN KEY ("invoiceShippingId") REFERENCES "InvoiceShipping"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "InvoiceShippingTaxes" ADD CONSTRAINT "InvoiceShippingTaxes_taxTypesId_fkey" FOREIGN KEY ("taxTypesId") REFERENCES "TaxTypes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "InvoiceItemTaxes" ADD CONSTRAINT "InvoiceItemTaxes_invoiceItemsId_fkey" FOREIGN KEY ("invoiceItemsId") REFERENCES "InvoiceItems"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "InvoiceItemTaxes" ADD CONSTRAINT "InvoiceItemTaxes_taxTypesId_fkey" FOREIGN KEY ("taxTypesId") REFERENCES "TaxTypes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
