-- CreateTable
CREATE TABLE "InvoiceNotes" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "instruction" VARCHAR(250) NOT NULL,
    "isAdmin" BOOLEAN NOT NULL,
    "authorName" VARCHAR(45) NOT NULL,
    "invoicesId" UUID NOT NULL,

    CONSTRAINT "InvoiceNotes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_InvoiceNotes_1_idx" ON "InvoiceNotes"("invoicesId");

-- Add<PERSON>ore<PERSON><PERSON>ey
ALTER TABLE "InvoiceNotes" ADD CONSTRAINT "InvoiceNotes_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
