-- Add unique constraint to prevent duplicate invoices for the same auction and bidder
-- This prevents race conditions in distributed environments where multiple instances
-- might try to create invoices for the same auction/bidder combination

CREATE UNIQUE INDEX "Invoices_auctionsId_bidderId_unique" 
ON "Invoices"("auctionsId", "bidderId") 
WHERE "deletedOn" IS NULL;

-- This partial unique index only applies to non-deleted invoices
-- allowing the same auction/bidder combination if previous invoices were soft-deleted
