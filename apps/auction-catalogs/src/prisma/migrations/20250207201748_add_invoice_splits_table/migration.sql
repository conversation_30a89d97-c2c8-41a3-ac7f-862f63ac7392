-- CreateTable
CREATE TABLE "InvoiceSplits" (
    "id" UUID NOT NULL,
    "invoiceId" UUID NOT NULL,
    "newInvoiceId" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,

    CONSTRAINT "InvoiceSplits_pkey" PRIMARY KEY ("id")
);

-- AddFore<PERSON>Key
ALTER TABLE "InvoiceSplits" ADD CONSTRAINT "InvoiceSplits_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceSplits" ADD CONSTRAINT "InvoiceSplits_newInvoiceId_fkey" FOREIGN KEY ("newInvoiceId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
