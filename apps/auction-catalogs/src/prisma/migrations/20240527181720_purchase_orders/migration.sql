-- CreateEnum
CREATE TYPE "ShippingMethod" AS ENUM ('Standard', 'Priority', 'Express');

-- CreateTable
CREATE TABLE "PurchaseOrders" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "shippingMethod" "ShippingMethod" NOT NULL,
    "shippingTerms" VARCHAR(45) NOT NULL,
    "deliveryDate" TIMESTAMP(3) NOT NULL,
    "subTotal" DECIMAL(10,2),
    "tax" DECIMAL(10,2),
    "other" DECIMAL(10,2),
    "total" DECIMAL(10,2),
    "comments" TEXT NOT NULL,
    "vendorsId" UUID NOT NULL,

    CONSTRAINT "PurchaseOrders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuctionPurchaseOrders" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "auctionsId" VARCHAR(36) NOT NULL,
    "purchaseOrdersId" UUID NOT NULL,

    CONSTRAINT "AuctionPurchaseOrders_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AuctionPurchaseOrders_auctionsId_purchaseOrdersId_key" ON "AuctionPurchaseOrders"("auctionsId", "purchaseOrdersId") WHERE "deletedOn" IS NULL;

-- AddForeignKey
ALTER TABLE "PurchaseOrders" ADD CONSTRAINT "PurchaseOrders_vendorsId_fkey" FOREIGN KEY ("vendorsId") REFERENCES "Vendors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionPurchaseOrders" ADD CONSTRAINT "AuctionPurchaseOrders_auctionsId_fkey" FOREIGN KEY ("auctionsId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionPurchaseOrders" ADD CONSTRAINT "AuctionPurchaseOrders_purchaseOrdersId_fkey" FOREIGN KEY ("purchaseOrdersId") REFERENCES "PurchaseOrders"("id") ON DELETE CASCADE ON UPDATE CASCADE;
