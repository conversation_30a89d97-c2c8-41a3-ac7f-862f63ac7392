
-- Create<PERSON>num
CREATE TYPE "MappingType" AS ENUM ('Lots', 'PO');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TransactionType" AS ENUM ('CreditCard', 'PayPal');

-- CreateEnum
CREATE TYPE "AddressType" AS ENUM ('Billing', 'Shipping');

-- CreateEnum
CREATE TYPE "CardType" AS ENUM ('Visa', 'MasterCard', 'Amex');

-- CreateTable
CREATE TABLE "ItemMappings" (
    "id" UUID NOT NULL,
    "mappingType" "MappingType" NOT NULL,
    "proxyId" UUID NOT NULL,

    CONSTRAINT "ItemMappings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionRequests" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "transactionType" "TransactionType" NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "invoicesId" UUID NOT NULL,
    "description" VARCHAR(45) NOT NULL,
    "invoiceNumber" VARCHAR(45) NOT NULL,

    CONSTRAINT "TransactionRequests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionLineItems" (
    "id" UUID NOT NULL,
    "transactionRequestsId" UUID NOT NULL,
    "itemMappingsId" UUID NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "description" VARCHAR(200) NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DECIMAL(10,2) NOT NULL,

    CONSTRAINT "TransactionLineItems_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionShipping" (
    "id" UUID NOT NULL,
    "transactionRequestsId" UUID NOT NULL,
    "name" VARCHAR(45) NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "description" VARCHAR(200) NOT NULL,

    CONSTRAINT "TransactionShipping_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionAddresses" (
    "id" UUID NOT NULL,
    "transactionRequestsId" UUID NOT NULL,
    "addressType" "AddressType" NOT NULL,
    "firstname" VARCHAR(45) NOT NULL,
    "lastname" VARCHAR(45) NOT NULL,
    "companyName" VARCHAR(100) NOT NULL,
    "address1" VARCHAR(45) NOT NULL,
    "address2" VARCHAR(45) NOT NULL,
    "city" VARCHAR(45) NOT NULL,
    "state" VARCHAR(3) NOT NULL,
    "zip" VARCHAR(10) NOT NULL,
    "country" VARCHAR(3) NOT NULL,

    CONSTRAINT "TransactionAddresses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionCreditCards" (
    "id" UUID NOT NULL,
    "transactionRequestsId" UUID NOT NULL,
    "last4" CHAR(4) NOT NULL,
    "expirationDate" CHAR(4) NOT NULL,
    "cardType" "CardType" NOT NULL,

    CONSTRAINT "TransactionCreditCards_pkey" PRIMARY KEY ("id")
);


-- AddForeignKey
ALTER TABLE "TransactionLineItems" ADD CONSTRAINT "TransactionLineItems_itemMappingsId_fkey" FOREIGN KEY ("itemMappingsId") REFERENCES "ItemMappings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransactionShipping" ADD CONSTRAINT "TransactionShipping_transactionRequestsId_fkey" FOREIGN KEY ("transactionRequestsId") REFERENCES "TransactionRequests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransactionAddresses" ADD CONSTRAINT "TransactionAddresses_transactionRequestsId_fkey" FOREIGN KEY ("transactionRequestsId") REFERENCES "TransactionRequests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransactionCreditCards" ADD CONSTRAINT "TransactionCreditCards_transactionRequestsId_fkey" FOREIGN KEY ("transactionRequestsId") REFERENCES "TransactionRequests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
