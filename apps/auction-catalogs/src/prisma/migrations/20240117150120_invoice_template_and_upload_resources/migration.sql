/*
  Warnings:

  - You are about to drop the `Resource` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[id]` on the table `Upload` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[proxyId]` on the table `Upload` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[id]` on the table `UploadItems` will be added. If there are existing duplicate values, this will fail.

*/
CREATE EXTENSION "uuid-ossp";

-- AlterTable
ALTER TABLE "AuctionLots" ALTER COLUMN "number" DROP NOT NULL,
ALTER COLUMN "displayOrder" DROP NOT NULL,
ALTER COLUMN "status" DROP NOT NULL;

-- AlterTable
ALTER TABLE "UploadItems" ALTER COLUMN "uploadFilename" DROP NOT NULL,
ALTER COLUMN "fileType" DROP NOT NULL,
ALTER COLUMN "fileType" SET DATA TYPE VARCHAR(30),
ALTER COLUMN "size" DROP NOT NULL;

-- DropTable
DROP TABLE "Resource";

-- CreateTable
CREATE TABLE "Resources" (
    "id" TEXT NOT NULL,
    "resourceType" VARCHAR(45) NOT NULL,
    "location" VARCHAR(200) NOT NULL,
    "cdnPath" VARCHAR(255) NOT NULL,

    CONSTRAINT "Resources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceTemplates" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "filename" VARCHAR(100) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" VARCHAR(500) NOT NULL,
    "thumbnail" VARCHAR(45) NOT NULL,

    CONSTRAINT "InvoiceTemplates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Resources_id_key" ON "Resources"("id");

-- CreateIndex
CREATE UNIQUE INDEX "Resources_resourceType_key" ON "Resources"("resourceType");

-- CreateIndex
CREATE UNIQUE INDEX "Upload_id_key" ON "Upload"("id");

-- CreateIndex
CREATE UNIQUE INDEX "Upload_proxyId_key" ON "Upload"("proxyId");

-- CreateIndex
CREATE UNIQUE INDEX "UploadItems_id_key" ON "UploadItems"("id");


INSERT INTO "Resources" (id, "resourceType", location, "cdnPath") VALUES (uuid_generate_v4(), 'AUCTION_IMAGES', 'app_data/auction_images/', 'https://d3myu8ay48rd2g.cloudfront.net/auction_images/');
INSERT INTO "Resources" (id, "resourceType", location, "cdnPath") VALUES (uuid_generate_v4(), 'LOT_ITEM_IMAGES', 'app_data/lot_item_images/', 'https://d3myu8ay48rd2g.cloudfront.net/lot_item_images/');

INSERT INTO "InvoiceTemplates" (id, filename, name, description, thumbnail)
VALUES (uuid_generate_v4(), 'InvoiceTemplate.html', 'Default Template', 'This is the default template', 'invoiceTemplate.png');