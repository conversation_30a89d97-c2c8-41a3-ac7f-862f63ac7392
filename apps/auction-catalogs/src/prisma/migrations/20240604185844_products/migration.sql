-- CreateTable
CREATE TABLE "Products" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "title" VARCHAR(300) NOT NULL,
    "description" TEXT NOT NULL,
    "tags" VARCHAR(250) NOT NULL,
    "category" VARCHAR(250) NOT NULL,
    "thumbnailId" TEXT,
    "unitCost" DECIMAL(10,2),
    "retailPrice" DECIMAL(10,2),

    CONSTRAINT "Products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductCodes" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "code" VARCHAR(25) NOT NULL,
    "codeType" VARCHAR(25) NOT NULL,
    "productsId" UUID NOT NULL,

    CONSTRAINT "ProductCodes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductInventories" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "productsId" UUID NOT NULL,
    "condition" TEXT NOT NULL,
    "quantityInStock" INTEGER NOT NULL,

    CONSTRAINT "ProductInventories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VendorProducts" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "productsId" UUID NOT NULL,
    "vendorsId" UUID NOT NULL,

    CONSTRAINT "VendorProducts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Products_title_key" ON "Products"("title") WHERE "deletedOn" IS NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ProductCodes_code_codeType_key" ON "ProductCodes"("code", "codeType") WHERE "deletedOn" IS NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ProductInventories_productsId_condition_key" ON "ProductInventories"("productsId", "condition") WHERE "deletedOn" IS NULL;

-- CreateIndex
CREATE UNIQUE INDEX "VendorProducts_productsId_vendorsId_key" ON "VendorProducts"("productsId", "vendorsId") WHERE "deletedOn" IS NULL;

-- AddForeignKey
ALTER TABLE "ProductCodes" ADD CONSTRAINT "ProductCodes_productsId_fkey" FOREIGN KEY ("productsId") REFERENCES "Products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductInventories" ADD CONSTRAINT "ProductInventories_productsId_fkey" FOREIGN KEY ("productsId") REFERENCES "Products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VendorProducts" ADD CONSTRAINT "VendorProducts_productsId_fkey" FOREIGN KEY ("productsId") REFERENCES "Products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VendorProducts" ADD CONSTRAINT "VendorProducts_vendorsId_fkey" FOREIGN KEY ("vendorsId") REFERENCES "Vendors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
