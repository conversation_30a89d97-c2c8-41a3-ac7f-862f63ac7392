-- CreateEnum
CREATE TYPE "Mode" AS ENUM ('Draft', 'Published');

-- CreateEnum
CREATE TYPE "CurrencyType" AS ENUM ('CAD', 'USD');

-- CreateEnum
CREATE TYPE "InvoiceStatusType" AS ENUM ('requires shipping quote', 'ready for review', 'awaiting payment', 'pay on pickup', 'awaiting assembly', 'awaiting pickup', 'completed');

-- CreateEnum
CREATE TYPE "PaymentType" AS ENUM ('E-Transfer', 'In-Person Cash', 'In-Person Card', 'Online Card');

-- CreateTable
CREATE TABLE "Invoices" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "mode" "Mode" NOT NULL,
    "auctionsId" VARCHAR(36) NOT NULL,
    "number" VARCHAR(8) NOT NULL,
    "bidderId" VARCHAR(36) NOT NULL,
    "notes" JSONB NOT NULL,
    "specialInstructions" VARCHAR(1000) NOT NULL,
    "poNumber" VARCHAR(8) NOT NULL,
    "dueDate" TIMESTAMP(3) NOT NULL,
    "isShipping" BOOLEAN NOT NULL,
    "invoiceTemplatesId" UUID NOT NULL,
    "currencyType" "CurrencyType" NOT NULL,
    "itemsSubtotal" DECIMAL(10,2) NOT NULL,
    "shippingSubtotal" DECIMAL(10,2) NOT NULL,
    "taxesSubtotal" DECIMAL(10,2) NOT NULL,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "amountDue" DECIMAL(10,2) NOT NULL,

    CONSTRAINT "Invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceStatuses" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoicesId" UUID NOT NULL,
    "statusType" "InvoiceStatusType" NOT NULL,

    CONSTRAINT "InvoiceStatuses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceShippingAddresses" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "address1" VARCHAR(45) NOT NULL,
    "address2" VARCHAR(45) NOT NULL,
    "city" VARCHAR(45) NOT NULL,
    "province" VARCHAR(45) NOT NULL,
    "country" VARCHAR(45) NOT NULL,
    "postalCode" VARCHAR(7) NOT NULL,
    "buzzer" VARCHAR(45) NOT NULL,
    "accountAddressesId" VARCHAR(36) NOT NULL,
    "invoicesId" UUID NOT NULL,

    CONSTRAINT "InvoiceShippingAddresses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceBuyers" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "firstname" VARCHAR(45) NOT NULL,
    "lastname" VARCHAR(45) NOT NULL,
    "email" VARCHAR(45) NOT NULL,
    "telephone" VARCHAR(45) NOT NULL,
    "companyName" VARCHAR(45) NOT NULL,
    "accountMappingsId" VARCHAR(36) NOT NULL,
    "invoicesId" UUID,

    CONSTRAINT "InvoiceBuyers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoicePayments" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoicesId" UUID NOT NULL,
    "paymentTypes" "PaymentType" NOT NULL,
    "isPartial" BOOLEAN NOT NULL,
    "amountPaid" DECIMAL(10,2) NOT NULL,
    "transactionCode" VARCHAR(25) NOT NULL,

    CONSTRAINT "InvoicePayments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceItems" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoicesId" UUID NOT NULL,
    "lotsId" VARCHAR(36) NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "buyerPremiumAmount" DECIMAL(10,2) NOT NULL,
    "isTaxable" BOOLEAN NOT NULL,

    CONSTRAINT "InvoiceItems_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_nvcs_nvctmplts_idx" ON "Invoices"("invoiceTemplatesId");

-- CreateIndex
CREATE INDEX "fk_nvcsstts_nvcs_idx" ON "InvoiceStatuses"("invoicesId");

-- CreateIndex
CREATE INDEX "fk_nvcshppng_nvcs_idx" ON "InvoiceShippingAddresses"("invoicesId");

-- CreateIndex
CREATE INDEX "fk_nvcbyrs_nvcs_idx" ON "InvoiceBuyers"("invoicesId");

-- CreateIndex
CREATE INDEX "fk_nvcpmnts_nvcs_idx" ON "InvoicePayments"("invoicesId");

-- CreateIndex
CREATE INDEX "fk_nvctms_nvcs_idx" ON "InvoiceItems"("invoicesId");

-- AddForeignKey
ALTER TABLE "InvoiceStatuses" ADD CONSTRAINT "InvoiceStatuses_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceShippingAddresses" ADD CONSTRAINT "InvoiceShippingAddresses_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceBuyers" ADD CONSTRAINT "InvoiceBuyers_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoicePayments" ADD CONSTRAINT "InvoicePayments_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceItems" ADD CONSTRAINT "InvoiceItems_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;



ALTER TABLE public."Invoices" DROP COLUMN "number";
-- Add the 'number' column for auto-increment
ALTER TABLE "Invoices"
    ADD COLUMN "number" SERIAL; -- Using SERIAL creates an integer column with auto-increment by default
