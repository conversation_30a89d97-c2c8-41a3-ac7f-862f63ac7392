-- Insert data for Acura
INSERT INTO "BasicLists" (id, "createdOn", "createdBy", "updatedOn", "updatedBy", "displayOrder", "groupName", "value", "basicKey")
VALUES
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MAKES', 'Acura', 'ACURA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'ILX', 'ACURA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'MDX', 'ACURA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'RDX', 'ACURA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'TLX', 'ACURA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'NSX', 'ACURA'),
-- Insert data for Alfa Romeo
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MAKES', 'Alfa Romeo', 'ALFA_ROMEO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'Giulia', 'ALFA_ROMEO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Stelvio', 'ALFA_ROMEO'),
-- Insert data for Aston Martin
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MAKES', 'Aston Martin', 'ASTON_MARTIN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'DB11', 'ASTON_MARTIN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Vantage', 'ASTON_MARTIN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'DBS', 'ASTON_MARTIN'),
-- Audi
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MAKES', 'Audi', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'A3', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'A4', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'A5', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'A6', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'A7', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'A8', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Q3', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Q5', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Q7', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Q8', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'TT', 'AUDI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'R8', 'AUDI'),

    -- BMW Models
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MAKES', 'BMW', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '1 Series', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', '2 Series Coupe', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', '2 Series Gran Coupe', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'X1', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'X2', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'X3', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'X4', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'X5', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'X6', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'X7', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'M2', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 12, 'VEHICLE_MODELS', 'M3', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 13, 'VEHICLE_MODELS', 'M4', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 14, 'VEHICLE_MODELS', 'M5', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 15, 'VEHICLE_MODELS', 'M8', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 16, 'VEHICLE_MODELS', 'iX M60', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 17, 'VEHICLE_MODELS', 'i7 M70', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 18, 'VEHICLE_MODELS', 'i5 M60 xDrive', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 19, 'VEHICLE_MODELS', 'i4 M50 xDrive Gran Coupé', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 20, 'VEHICLE_MODELS', 'XM', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 21, 'VEHICLE_MODELS', 'X7 M60i xDrive', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 22, 'VEHICLE_MODELS', 'X6 M', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 23, 'VEHICLE_MODELS', 'X5 M', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 24, 'VEHICLE_MODELS', 'X3 M50 xDrive', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 25, 'VEHICLE_MODELS', 'X4 M', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 26, 'VEHICLE_MODELS', 'X2 M35i xDrive', 'BMW'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 27, 'VEHICLE_MODELS', 'X1 M35i xDrive', 'BMW'),
    -- BUICK
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MAKES', 'Buick', 'BUICK'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Enclave Avenir', 'BUICK'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Envista', 'BUICK'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Encore GX', 'BUICK'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Envision', 'BUICK'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Cadillac', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'XT4', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'XT5', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'XT6', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Escalade', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'CT4', 'CADILLAC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'CT5', 'CADILLAC'),
-- Chevrolet
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MAKES', 'Chevrolet', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Bolt EV', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Camaro', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Colorado', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Corvette', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Equinox', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Malibu', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Silverado', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Suburban', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Tahoe', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Traverse', 'CHEVROLET'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'Trax', 'CHEVROLET'),
-- Chrysler Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MAKES', 'Chrysler', 'CHRYSLER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '300', 'CHRYSLER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Pacifica', 'CHRYSLER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Voyager', 'CHRYSLER'),
-- Dodge
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MAKES', 'Dodge', 'DODGE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Challenger', 'DODGE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Charger', 'DODGE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Durango', 'DODGE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Journey', 'DODGE'),
-- Ferrari Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MAKES', 'Ferrari', 'FERRARI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '488', 'FERRARI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Portofino', 'FERRARI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'F8', 'FERRARI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'SF90 Stradale', 'FERRARI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MAKES', 'Fiat', 'FIAT'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', '500', 'FIAT'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', '500X', 'FIAT'),
-- Ford Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MAKES', 'Ford', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Bronco', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Edge', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Escape', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Expedition', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Explorer', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'F-150', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'F-250', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'F-350', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Fusion', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Mustang', 'FORD'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'Ranger', 'FORD'),
-- Genesis Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 12, 'VEHICLE_MAKES', 'Genesis', 'GENESIS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'G70', 'GENESIS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'G80', 'GENESIS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'G90', 'GENESIS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'GV80', 'GENESIS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MAKES', 'GMC', 'GMC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Acadia', 'GMC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Canyon', 'GMC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Sierra', 'GMC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Terrain', 'GMC'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Yukon', 'GMC'),
-- Honda Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 13, 'VEHICLE_MAKES', 'Honda', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Accord', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Civic', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'CR-V', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'HR-V', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Odyssey', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Passport', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Pilot', 'HONDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Ridgeline', 'HONDA'),
-- Hyundai Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 14, 'VEHICLE_MAKES', 'Hyundai', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Elantra', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Kona', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Palisade', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Santa Fe', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Sonata', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Tucson', 'HYUNDAI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Veloster', 'HYUNDAI'),

-- Infiniti Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 15, 'VEHICLE_MAKES', 'Infiniti', 'INFINITI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Q50', 'INFINITI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Q60', 'INFINITI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'QX50', 'INFINITI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'QX60', 'INFINITI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'QX80', 'INFINITI'),
-- Jaguar Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 16, 'VEHICLE_MAKES', 'Jaguar', 'JAGUAR'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'F-PACE', 'JAGUAR'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'F-TYPE', 'JAGUAR'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'XE', 'JAGUAR'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'XF', 'JAGUAR'),
-- Jeep Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 17, 'VEHICLE_MAKES', 'Jeep', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Cherokee', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Compass', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Gladiator', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Grand Cherokee', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Renegade', 'JEEP'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Wrangler', 'JEEP'),
-- Kia Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 18, 'VEHICLE_MAKES', 'Kia', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Forte', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'K5', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Niro', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Rio', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Seltos', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Sorento', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Soul', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Sportage', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Stinger', 'KIA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Telluride', 'KIA'),
-- Land Rover Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 19, 'VEHICLE_MAKES', 'Land Rover', 'LAND_ROVER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Discovery', 'LAND_ROVER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Range Rover', 'LAND_ROVER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Range Rover Evoque', 'LAND_ROVER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Range Rover Sport', 'LAND_ROVER'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Range Rover Velar', 'LAND_ROVER'),
-- Lexus Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 20, 'VEHICLE_MAKES', 'Lexus', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'ES', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'GX', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'IS', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'LS', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'LX', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'NX', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'RC', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'RX', 'LEXUS'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'UX', 'LEXUS'),

-- Lincoln Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 21, 'VEHICLE_MAKES', 'Lincoln', 'LINCOLN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Aviator', 'LINCOLN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Corsair', 'LINCOLN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Nautilus', 'LINCOLN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Navigator', 'LINCOLN'),
-- Maserati Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 22, 'VEHICLE_MAKES', 'Maserati', 'MASERATI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Ghibli', 'MASERATI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Levante', 'MASERATI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Quattroporte', 'MASERATI'),
-- Mazda Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 23, 'VEHICLE_MAKES', 'Mazda', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'CX-3', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'CX-30', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'CX-5', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'CX-9', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Mazda3', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Mazda6', 'MAZDA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'MX-5 Miata', 'MAZDA'),

-- Mercedes-Benz Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 24, 'VEHICLE_MAKES', 'Mercedes-Benz', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'A-Class', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'C-Class', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'E-Class', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'G-Class', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'GLA', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'GLB', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'GLC', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'GLE', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'GLS', 'MERCEDES-BENZ'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'S-Class', 'MERCEDES-BENZ'),
-- Mini Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 25, 'VEHICLE_MAKES', 'Mini', 'MINI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Clubman', 'MINI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Countryman', 'MINI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Hardtop', 'MINI'),
-- Mitsubishi Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 26, 'VEHICLE_MAKES', 'Mitsubishi', 'MITSUBISHI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Eclipse Cross', 'MITSUBISHI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Mirage', 'MITSUBISHI'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Outlander', 'MITSUBISHI'),
-- Nissan Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 27, 'VEHICLE_MAKES', 'Nissan', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Altima', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Armada', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Frontier', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Kicks', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Leaf', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Maxima', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Murano', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'Pathfinder', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Rogue', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Sentra', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'Titan', 'NISSAN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 12, 'VEHICLE_MODELS', 'Versa', 'NISSAN'),
-- Porsche Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Porsche', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '718 Boxster', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', '718 Cayman', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', '911', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Cayenne', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Macan', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Panamera', 'PORSCHE'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Taycan', 'PORSCHE'),
-- Ram Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Ram', 'RAM'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '1500', 'RAM'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', '2500', 'RAM'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', '3500', 'RAM'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'ProMaster', 'RAM'),
-- Subaru Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Subaru', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Ascent', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'BRZ', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Crosstrek', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Forester', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Impreza', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Legacy', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Outback', 'SUBARU'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'WRX', 'SUBARU'),
-- Tesla Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Tesla', 'TESLA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Model 3', 'TESLA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Model S', 'TESLA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Model X', 'TESLA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Model Y', 'TESLA'),
-- Toyota Make
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MAKES', 'Toyota', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', '4Runner', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Avalon', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Camry', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Corolla', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Highlander', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 6, 'VEHICLE_MODELS', 'Land Cruiser', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 7, 'VEHICLE_MODELS', 'Prius', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 8, 'VEHICLE_MODELS', 'RAV4', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 9, 'VEHICLE_MODELS', 'Sequoia', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 10, 'VEHICLE_MODELS', 'Sienna', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 11, 'VEHICLE_MODELS', 'Tacoma', 'TOYOTA'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 12, 'VEHICLE_MODELS', 'Tundra', 'TOYOTA'),

-- Volkswagen
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MAKES', 'Volkswagen', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'Arteon', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'Atlas', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'Golf', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'Jetta', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'Passat', 'VOLKSWAGEN'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'Tiguan', 'VOLKSWAGEN'),

-- Volvo
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MAKES', 'Volvo', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 0, 'VEHICLE_MODELS', 'S60', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 1, 'VEHICLE_MODELS', 'S90', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 2, 'VEHICLE_MODELS', 'V60', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 3, 'VEHICLE_MODELS', 'XC40', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 4, 'VEHICLE_MODELS', 'XC60', 'VOLVO'),
    (uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', 5, 'VEHICLE_MODELS', 'XC90', 'VOLVO');