-- CreateTable
CREATE TABLE "Timeslots" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "pickupTime" TIMESTAMP(3) NOT NULL,
    "accountMappingsId" UUID NOT NULL,
    "auctionsId" UUID NOT NULL,
    "staffId" UUID,
    "status" TEXT NOT NULL,
    "invoicesId" UUID NOT NULL,
    "index" INTEGER NOT NULL,

    CONSTRAINT "Timeslots_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_Timeslots_1_idx" ON "Timeslots"("auctionsId");
