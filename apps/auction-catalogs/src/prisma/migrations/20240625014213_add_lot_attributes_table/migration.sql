-- CreateTable
CREATE TABLE "LotAttributes" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "lotId" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "attributeKey" VARCHAR(50) NOT NULL,
    "value" VARCHAR(100) NOT NULL,

    CONSTRAINT "LotAttributes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LotAttributes_lotId_idx" ON "LotAttributes"("lotId");

-- CreateIndex
CREATE INDEX "LotAttributes_attributeKey_idx" ON "LotAttributes"("attributeKey");

-- CreateIndex
CREATE INDEX "LotAttributes_value_idx" ON "LotAttributes"("value");
