-- CreateTable
CREATE TABLE "TransactionResponses" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '********-0000-0000-0000-************'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "responseCode" VARCHAR(45),
    "authCode" VARCHAR(6) NOT NULL,
    "avsResultCode" CHAR(1) NOT NULL,
    "cvvResultCode" CHAR(1) NOT NULL,
    "cavvResultCode" CHAR(1) NOT NULL,
    "transId" VARCHAR(12) NOT NULL,
    "accountNumber" VARCHAR(8) NOT NULL,
    "accountType" VARCHAR(4) NOT NULL,
    "transHashSha2" VARCHAR(128) NOT NULL,
    "networkTransId" VARCHAR(25) NOT NULL,
    "transactionRequestsId" VARCHAR(36) NOT NULL,

    CONSTRAINT "TransactionResponses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionResponseMessages" (
    "id" UUID NOT NULL,
    "transactionResponsesId" UUID NOT NULL,
    "code" VARCHAR(6) NOT NULL,
    "description" VARCHAR(200) NOT NULL,

    CONSTRAINT "TransactionResponseMessages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_TransactionResponses_1_idx" ON "TransactionResponses"("transactionRequestsId");

-- CreateIndex
CREATE INDEX "fk_TransactionResponseMessages_1_idx" ON "TransactionResponseMessages"("transactionResponsesId");

-- AddForeignKey
ALTER TABLE "TransactionResponseMessages" ADD CONSTRAINT "TransactionResponseMessages_transactionResponsesId_fkey" FOREIGN KEY ("transactionResponsesId") REFERENCES "TransactionResponses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
