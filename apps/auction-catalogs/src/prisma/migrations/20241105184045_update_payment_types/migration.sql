/*
  Warnings:

  - The values [E-Transfer,In-Person Cash,In-Person Card,Online Card] on the enum `PaymentType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PaymentType_new" AS ENUM ('e-transfer', 'cash', 'inPersonCard', 'onlineCard', 'paypal');
ALTER TABLE "InvoicePayments" ALTER COLUMN "paymentTypes" TYPE "PaymentType_new" USING ("paymentTypes"::text::"PaymentType_new");
ALTER TYPE "PaymentType" RENAME TO "PaymentType_old";
ALTER TYPE "PaymentType_new" RENAME TO "PaymentType";
DROP TYPE "PaymentType_old";
COMMIT;
