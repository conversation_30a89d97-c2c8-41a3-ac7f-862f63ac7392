
-- Insert data into AttributeForms table
INSERT INTO "AttributeForms" (
    id, "createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", category
) VALUES (
     uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', NULL, NULL, 'Vehicles'
 );

-- Insert data into AttributeFormQuestions table
WITH vehicle_form AS (
    SELECT id FROM "AttributeForms" WHERE category = 'Vehicles' LIMIT 1
)
INSERT INTO "AttributeFormQuestions" (
    id, "createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", "attributeFormsId", label, "groupName", "displayOrder", "attributeKey"
) VALUES
(
    uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', NULL, NULL, (SELECT id FROM vehicle_form), 'Color', 'VEHICLE_COLORS', 2, 'COLOR'
),
(
    uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', NULL, NULL, (SELECT id FROM vehicle_form), 'Model', 'VEHICLE_MODELS', 1, 'MODEL'
),
(
    uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', NULL, NULL, (SELECT id FROM vehicle_form), 'Vehicle Identification Number', 'VIN', 3, 'VIN'
),
(
    uuid_generate_v4(), NOW(), '00000000-0000-0000-0000-000000000000', NOW(), '00000000-0000-0000-0000-000000000000', NULL, NULL, (SELECT id FROM vehicle_form), 'Make', 'VEHICLE_MAKES', 0, 'MAKE'
);