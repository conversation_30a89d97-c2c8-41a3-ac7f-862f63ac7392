/*
  Warnings:

  - Added the required column `invoiceText` to the `Auctions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `pickupLocation` to the `Auctions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `termsConditions` to the `Auctions` table without a default value. This is not possible if the table is not empty.

*/

-- AlterTable
ALTER TABLE "Auctions" ADD COLUMN     "invoiceText" VARCHAR(300) NOT NULL DEFAULT '',
ADD COLUMN     "pickupLocation" VARCHAR(45) NOT NULL DEFAULT '',
ADD COLUMN     "termsConditions" TEXT NOT NULL DEFAULT '';


-- CreateData
INSERT INTO "ApplicationPreferences" ("id", "preferenceKey", "value", "description")
VALUES
( gen_random_uuid(), 'DEFAULT_PICKUP_LOCATION', 'Dartmouth', 'The default pickup location');