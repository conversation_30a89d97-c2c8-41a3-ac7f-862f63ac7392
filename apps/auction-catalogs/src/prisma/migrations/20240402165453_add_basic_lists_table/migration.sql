-- CreateTable
CREATE TABLE "BasicLists" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "groupName" VARCHAR(45) NOT NULL,
    "value" VARCHAR(100) NOT NULL,

    CONSTRAINT "BasicLists_pkey" PRIMARY KEY ("id")
);
BEGIN;

INSERT INTO "BasicLists" ("id", "createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", "displayOrder", "groupName", "value")
VALUES
    (uuid_generate_v4(), '2024-04-02 17:34:29.253', 'SYSTEM', '2024-04-02 17:34:29.254', NULL, NULL, NULL, 1, 'REGIONS', 'Vancouver'),
    (uuid_generate_v4(), '2024-04-02 17:41:18.855', 'SYSTEM', '2024-04-02 17:41:18.856', NULL, NULL, NULL, 0, 'AUCTION_CATEGORIES', 'General'),
    (uuid_generate_v4(), '2024-04-02 17:41:41.17', 'SYSTEM', '2024-04-02 17:41:41.171', NULL, NULL, NULL, 2, 'AUCTION_CATEGORIES', 'Vehicles'),
    (uuid_generate_v4(), '2024-04-02 17:34:15.868', 'SYSTEM', '2024-04-02 17:34:15.87', NULL, NULL, NULL, 0, 'REGIONS', 'Halifax'),
    (uuid_generate_v4(), '2024-04-02 17:41:31.005', 'SYSTEM', '2024-04-02 17:41:31.006', NULL, NULL, NULL, 1, 'AUCTION_CATEGORIES', 'Furniture');

COMMIT;
