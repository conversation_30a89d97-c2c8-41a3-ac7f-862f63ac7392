-- CreateTable
CREATE TABLE "Vendors" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "name" VARCHAR(45) NOT NULL,
    "phone" VARCHAR(12) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "url" VARCHAR(100) NOT NULL,

    CONSTRAINT "Vendors_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Vendors_name_key" ON "Vendors"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Vendors_phone_key" ON "Vendors"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "Vendors_email_key" ON "Vendors"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Vendors_url_key" ON "Vendors"("url");
