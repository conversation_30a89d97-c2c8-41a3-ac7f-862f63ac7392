-- CreateTable
CREATE TABLE "LotTemplates" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "lotsId" TEXT NOT NULL,
    "templatesId" UUID NOT NULL,

    CONSTRAINT "LotTemplates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "LotTemplates_lotsId_key" ON "LotTemplates"("lotsId");

-- AddForeignKey
ALTER TABLE "LotTemplates" ADD CONSTRAINT "LotTemplates_lotsId_fkey" FOREIGN KEY ("lotsId") REFERENCES "Lots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LotTemplates" ADD CONSTRAINT "LotTemplates_templatesId_fkey" FOREIGN KEY ("templatesId") REFERENCES "Templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
