/*
  Warnings:

  - The primary key for the `ProductInventories` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `VendorProducts` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - Changed the type of `id` on the `ProductInventories` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `VendorProducts` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "ProductInventories" DROP CONSTRAINT "ProductInventories_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "ProductInventories_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "VendorProducts" DROP CONSTRAINT "VendorProducts_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "VendorProducts_pkey" PRIMARY KEY ("id");
