-- CreateTable
CREATE TABLE "AttributeFormQuestions" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "attributeFormsId" UUID NOT NULL,
    "label" VARCHAR(45) NOT NULL,
    "groupName" VARCHAR(45) NOT NULL DEFAULT '',
    "displayOrder" INTEGER NOT NULL,
    "attributeKey" VARCHAR(50) NOT NULL,

    CONSTRAINT "AttributeFormQuestions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AttributeForms" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_<PERSON><PERSON><PERSON><PERSON><PERSON>,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "category" VARCHAR(25) NOT NULL,

    CONSTRAINT "AttributeForms_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_AttributeFormQuestions_1_idx" ON "AttributeFormQuestions"("attributeFormsId");

-- AddForeignKey
ALTER TABLE "AttributeFormQuestions" ADD CONSTRAINT "AttributeFormQuestions_attributeFormsId_fkey" FOREIGN KEY ("attributeFormsId") REFERENCES "AttributeForms"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
