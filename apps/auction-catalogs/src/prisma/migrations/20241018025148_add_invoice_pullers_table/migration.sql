-- CreateTable
CREATE TABLE "InvoicePullers" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "assignerId" UUID NOT NULL,
    "pullerId" UUID NOT NULL,
    "startTime" TIMESTAMP(3),
    "endTime" TIMESTAMP(3),
    "invoicesId" UUID NOT NULL,

    CONSTRAINT "InvoicePullers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_InvoicePullers_1_idx" ON "InvoicePullers"("invoicesId");

-- AddForeignKey
ALTER TABLE "InvoicePullers" ADD CONSTRAINT "InvoicePullers_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
