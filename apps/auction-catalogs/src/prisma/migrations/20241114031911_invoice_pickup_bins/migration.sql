-- CreateTable
CREATE TABLE "InvoicePickupBins" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "invoicesId" UUID NOT NULL,
    "pickupBin" VARCHAR(50) NOT NULL,

    CONSTRAINT "InvoicePickupBins_pkey" PRIMARY KEY ("id")
);

-- AddForeignK<PERSON>
ALTER TABLE "InvoicePickupBins" ADD CONSTRAINT "InvoicePickupBins_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
