-- CreateTable
CREATE TABLE "Resource" (
    "id" TEXT NOT NULL,
    "resourceType" VARCHAR(45) NOT NULL,
    "location" VARCHAR(200) NOT NULL,
    "cdnPath" VARCHAR(255) NOT NULL,

    CONSTRAINT "Resource_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Upload" (
    "id" TEXT NOT NULL,
    "resourceId" UUID NOT NULL,
    "metadata" VARCHAR(100) NOT NULL,
    "proxyId" VARCHAR(36) NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3) NOT NULL,
    "updatedBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),

    CONSTRAINT "Upload_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UploadItems" (
    "id" TEXT NOT NULL,
    "uploadsId" UUID NOT NULL,
    "uploadFilename" VARCHAR(100) NOT NULL,
    "fileType" VARCHAR(5) NOT NULL,
    "size" INTEGER NOT NULL DEFAULT 0,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3) NOT NULL,
    "updatedBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),

    CONSTRAINT "UploadItems_pkey" PRIMARY KEY ("id")
);
