-- CreateTable
CREATE TABLE "PurchaseOrderItems" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "quantityOrdered" INTEGER,
    "quantityDelivered" INTEGER,
    "comments" TEXT NOT NULL,
    "productsId" UUID NOT NULL,
    "purchaseOrdersId" UUID NOT NULL,
    "barcode" VARCHAR(30) NOT NULL,

    CONSTRAINT "PurchaseOrderItems_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LotPurchaseOrderItems" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "purchaseOrderItemsId" UUID NOT NULL,
    "lotsId" VARCHAR(36) NOT NULL,

    CONSTRAINT "LotPurchaseOrderItems_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "LotPurchaseOrderItems_purchaseOrderItemsId_lotsId_key" ON "LotPurchaseOrderItems"("purchaseOrderItemsId", "lotsId") WHERE "deletedOn" IS NULL;

-- AddForeignKey
ALTER TABLE "LotPurchaseOrderItems" ADD CONSTRAINT "LotPurchaseOrderItems_purchaseOrderItemsId_fkey" FOREIGN KEY ("purchaseOrderItemsId") REFERENCES "PurchaseOrderItems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LotPurchaseOrderItems" ADD CONSTRAINT "LotPurchaseOrderItems_lotsId_fkey" FOREIGN KEY ("lotsId") REFERENCES "Lots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderItems" ADD CONSTRAINT "PurchaseOrderItems_productsId_fkey" FOREIGN KEY ("productsId") REFERENCES "Products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderItems" ADD CONSTRAINT "PurchaseOrderItems_purchaseOrdersId_fkey" FOREIGN KEY ("purchaseOrdersId") REFERENCES "PurchaseOrders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
