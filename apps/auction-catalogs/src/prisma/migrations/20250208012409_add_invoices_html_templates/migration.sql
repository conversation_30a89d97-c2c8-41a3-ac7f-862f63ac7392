-- Update all invoice templates with the html, unless something is already present. 
UPDATE "InvoiceTemplates" SET html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            max-width: 800px;
            margin: auto;
        }
        .header {
            background: #d33;
            color: white;
            padding: 10px;
            text-align: left;
        }
        .invoice-wrapper {
            display: flex;
            justify-content: space-between;
        }
        .invoice-info, .addresses {
            margin: 20px 0;
        }
        .addresses {
            display: flex;
            justify-content: space-evenly;
        }
        .address {
            width: 32%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: right;
        }
        .total {
            text-align: right;
            font-weight: bold;
        }
        .unpaid {
            color: red;
            font-weight: bold;
        }
        .paid {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Viewbid.ca</h2>
    </div>
    <div class="invoice-wrapper">
        <div class="invoice-info">
            <p><strong>HST Number:</strong> HST374856</p>
            <p><strong>Company Name:</strong> Viewbid Auction</p>
            <p><strong>Address:</strong> 11 Mount Hope Ave, Dartmouth, NS, B2Y 4S1</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Website:</strong> <a href="http://www.viewbid.ca">www.viewbid.ca</a></p>
            <p><strong>Contact No:</strong> +1 (913) 232-2452</p>
        </div>
        <div class="invoice-info">
            <p><strong>Invoice No:</strong> #{{invoice.number}}</p>
            <p><strong>Payment Status:</strong> <span class="{{#if invoice.isPaidInFull}}paid{{else}}unpaid{{/if}}">{{#if invoice.isPaidInFull}}Paid{{else}}Unpaid{{/if}}</span></p>
            <p><strong>Date of Purchase:</strong> {{invoice.createdOn}}</p>
        </div>
    </div>
    
    <div class="addresses">
        <div class="address">
            <h3>Billing Address</h3>
            <p><strong>Name:</strong> {{buyer.firstname}} {{buyer.lastname}}</p>
            <p>{{billingAddress.address1}}, {{billingAddress.city}}, {{billingAddress.province}}, {{billingAddress.country}} {{billingAddress.postalCode}}</p>
            <p><strong>Phone:</strong> {{buyer.telephone}}</p>
            <p><strong>Email:</strong> {{buyer.email}}</p>
        </div>
    </div>
    
    <table>
        <tr>
            <th>#</th>
            <th>Product Details</th>
            <th>Unit Price</th>
            <th>Quantity</th>
            <th>Amount</th>
        </tr>
        {{#each items}}
        <tr>
            <td>{{@index}}</td>
            <td>{{this.name}}</td>
            <td>${{this.price}}</td>
            <td>{{this.quantity}}</td>
            <td>${{this.price}}</td>
        </tr>
        {{/each}}
    </table>
    
    <table>
        <tr>
            <td class="total">Subtotal</td>
            <td>${{invoice.itemsSubtotal}}</td>
        </tr>
        <tr>
            <td class="total">Buyer Premium</td>
            <td>${{invoice.buyerPremiumTotal}}</td>
        </tr>
        <tr>
            <td class="total">Tax</td>
            <td>${{invoice.taxesSubtotal}}</td>
        </tr>
        <tr>
            <td class="total">Total</td>
            <td>${{invoice.totalAmount}}</td>
        </tr>
        <tr>
            <td class="total">Amount Paid</td>
            <td class="paid">${{invoice.amountPaid}}</td>
        </tr>
        <tr>
            <td class="total">Amount Owing</td>
            <td class="unpaid">${{invoice.amountDue}}</td>
        </tr>
    </table>
</body>
</html>'

WHERE html IS null or html like '';