/*
  Warnings:

  - The values [ready for review,awaiting payment,pay on pickup] on the enum `InvoiceStatusType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "InvoiceStatusType_new" AS ENUM ('pending admin review', 'pending buyer review', 'requires shipping quote', 'pending shipping quote review', 'awaiting assembly', 'awaiting pickup', 'completed');
ALTER TABLE "InvoiceStatuses" ALTER COLUMN "statusType" TYPE "InvoiceStatusType_new" USING ("statusType"::text::"InvoiceStatusType_new");
ALTER TYPE "InvoiceStatusType" RENAME TO "InvoiceStatusType_old";
ALTER TYPE "InvoiceStatusType_new" RENAME TO "InvoiceStatusType";
DROP TYPE "InvoiceStatusType_old";
COMMIT;

-- AlterTable
ALTER TABLE "Invoices" ADD COLUMN     "isPaidInFull" BOOLEAN NOT NULL DEFAULT false;
