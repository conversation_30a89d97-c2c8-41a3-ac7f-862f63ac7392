-- CreateTable
CREATE TABLE "LotItemUserNotes" (
    "id" TEXT NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" VARCHAR(36) NOT NULL DEFAULT 'SYSTEM',
    "updatedOn" TIMESTAMP(3),
    "updatedBy" VARCHAR(36),
    "deletedOn" TIMESTAMP(3),
    "deletedBy" VARCHAR(36),
    "lotsId" VARCHAR(36) NOT NULL,
    "accountMappingsId" VARCHAR(36) NOT NULL,
    "note" TEXT NOT NULL,

    CONSTRAINT "LotItemUserNotes_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON>ignK<PERSON>
ALTER TABLE "LotItemUserNotes" ADD CONSTRAINT "LotItemUserNotes_lotsId_fkey" FOREIGN KEY ("lotsId") REFERENCES "Lots"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
