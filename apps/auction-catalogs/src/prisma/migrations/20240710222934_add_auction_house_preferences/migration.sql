-- CreateEnum
CREATE TYPE "PreferenceGroup" AS ENUM ('Users', 'Emails', 'Invoices', 'Auctions');

-- CreateTable
CREATE TABLE "AuctionHousePreferences" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "auctionHousesId" UUID NOT NULL,
    "preferenceKey" VARCHAR(45) NOT NULL,
    "value" TEXT NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "preferenceGroup" "PreferenceGroup" NOT NULL,

    CONSTRAINT "AuctionHousePreferences_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_AuctionHousePreferences_1_idx" ON "AuctionHousePreferences"("auctionHousesId");

-- AddForeignKey
ALTER TABLE "AuctionHousePreferences" ADD CONSTRAINT "AuctionHousePreferences_auctionHousesId_fkey" FOREIGN KEY ("auctionHousesId") REFERENCES "AuctionHouses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
