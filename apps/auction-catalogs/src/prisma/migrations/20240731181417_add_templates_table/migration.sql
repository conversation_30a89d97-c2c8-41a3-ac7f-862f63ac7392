-- CreateTable
CREATE TABLE "Templates" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "itemMappingsId" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "body" JSONB NOT NULL,

    CONSTRAINT "Templates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_Templates_1_idx" ON "Templates"("itemMappingsId");

-- AddForeignKey
ALTER TABLE "Templates" ADD CONSTRAINT "Templates_itemMappingsId_fkey" FOREIGN KEY ("itemMappingsId") REFERENCES "ItemMappings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
