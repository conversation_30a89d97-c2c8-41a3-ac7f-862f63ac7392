-- CreateTable
CREATE TABLE "PaymentProfiles" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "accountMappingsId" UUID NOT NULL,
    "merchantCustomerId" VARCHAR(20) NOT NULL,
    "customerProfileId" VARCHAR(20) NOT NULL,
    "customerPaymentProfileId" VARCHAR(20) NOT NULL,

    CONSTRAINT "PaymentProfiles_pkey" PRIMARY KEY ("id")
);
