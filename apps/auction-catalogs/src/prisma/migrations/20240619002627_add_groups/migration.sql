-- AlterTable
ALTER TABLE "AuctionLots" ADD COLUMN     "groupsId" UUID;

-- CreateTable
CREATE TABLE "Groups" (
    "id" UUID NOT NULL,
    "auctionsId" VARCHAR(36) NOT NULL,
    "name" VARCHAR(45) NOT NULL,

    CONSTRAINT "Groups_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AuctionLots" ADD CONSTRAINT "AuctionLots_groupsId_fkey" FOREIGN KEY ("groupsId") REFERENCES "Groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Groups" ADD CONSTRAINT "Groups_auctionsId_fkey" FOREIGN KEY ("auctionsId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
