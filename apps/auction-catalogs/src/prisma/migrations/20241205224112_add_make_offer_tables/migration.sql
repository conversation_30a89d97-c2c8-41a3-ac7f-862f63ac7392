-- CreateEnum
CREATE TYPE "PurchaseOfferStatus" AS ENUM ('Pending', 'Rejected', 'Accepted');

-- CreateTable
CREATE TABLE "PurchaseOffers" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "accountMappingsId" VARCHAR(36) NOT NULL,
    "itemMappingsId" VARCHAR(36) NOT NULL,
    "quantity" INTEGER NOT NULL,
    "initialOffer" DECIMAL(10,2) NOT NULL,
    "acceptedOffer" DECIMAL(10,2) NOT NULL,
    "status" "PurchaseOfferStatus" NOT NULL,

    CONSTRAINT "PurchaseOffers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PurchaseOfferDiscussions" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "accountMappingsId" UUID NOT NULL,
    "isAdmin" BOOLEAN NOT NULL,
    "comments" VARCHAR(500) NOT NULL,
    "counterOffer" DECIMAL(10,2),
    "purchaseOffersId" UUID NOT NULL,

    CONSTRAINT "PurchaseOfferDiscussions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_PurchaseOfferDiscussions_1_idx" ON "PurchaseOfferDiscussions"("purchaseOffersId");

-- AddForeignKey
ALTER TABLE "PurchaseOfferDiscussions" ADD CONSTRAINT "PurchaseOfferDiscussions_purchaseOffersId_fkey" FOREIGN KEY ("purchaseOffersId") REFERENCES "PurchaseOffers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
