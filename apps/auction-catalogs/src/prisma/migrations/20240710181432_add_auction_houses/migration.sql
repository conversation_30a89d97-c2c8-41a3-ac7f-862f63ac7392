-- CreateEnum
CREATE TYPE "AuctionHouseMappingType" AS ENUM ('AUCTION', 'LOT');

-- CreateTable
CREATE TABLE "AuctionHouseMappings" (
    "id" UUID NOT NULL,
    "auctionHousesId" UUID NOT NULL,
    "mappingType" "AuctionHouseMappingType" NOT NULL,
    "proxyId" UUID NOT NULL,

    CONSTRAINT "AuctionHouseMappings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuctionHouses" (
    "id" UUID NOT NULL,
    "createdOn" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
    "updatedOn" TIMESTAMP(3),
    "updatedBy" UUID,
    "deletedOn" TIMESTAMP(3),
    "deletedBy" UUID,
    "name" VARCHAR(45) NOT NULL,
    "ownerId" VARCHAR(36) NOT NULL,

    CONSTRAINT "AuctionHouses_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "fk_AuctionHouseMappings_1_idx" ON "AuctionHouseMappings"("auctionHousesId");

-- CreateIndex
CREATE INDEX "AuctionHouseMappings_id_idx" ON "AuctionHouseMappings"("auctionHousesId");

-- CreateIndex
CREATE INDEX "AuctionHouseMappings_proxy_idx" ON "AuctionHouseMappings"("proxyId");

-- AddForeignKey
ALTER TABLE "AuctionHouseMappings" ADD CONSTRAINT "AuctionHouseMappings_auctionHousesId_fkey" FOREIGN KEY ("auctionHousesId") REFERENCES "AuctionHouses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

INSERT INTO "AuctionHouses" (
    id, "createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", name, "ownerId"
) VALUES (
             '19118a4e-5c1a-4332-8a72-d2386a120d58',
             '2024-02-14 17:07:08.449',
             '00000000-0000-0000-0000-000000000000',
             '2024-02-14 17:07:08.449',
             '00000000-0000-0000-0000-000000000000',
             NULL,
             NULL,
             'Viewbid',
             '00000000-0000-0000-0000-000000000000'
         );