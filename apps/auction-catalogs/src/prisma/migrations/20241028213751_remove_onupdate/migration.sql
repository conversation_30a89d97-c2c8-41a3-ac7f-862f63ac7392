-- DropF<PERSON>ign<PERSON>ey
ALTER TABLE "InvoiceShipping" DROP CONSTRAINT "InvoiceShipping_invoicesId_fkey";

-- DropFore<PERSON><PERSON>ey
ALTER TABLE "InvoiceShipping" DROP CONSTRAINT "InvoiceShipping_shippingVendorsId_fkey";

-- AddFore<PERSON>Key
ALTER TABLE "InvoiceShipping" ADD CONSTRAINT "InvoiceShipping_invoicesId_fkey" FOREIGN KEY ("invoicesId") REFERENCES "Invoices"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "InvoiceShipping" ADD CONSTRAINT "InvoiceShipping_shippingVendorsId_fkey" FOREIGN KEY ("shippingVendorsId") REFERENCES "ShippingVendors"("id") ON DELETE NO ACTION ON UPDATE CASCADE;
