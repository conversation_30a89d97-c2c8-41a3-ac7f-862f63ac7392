-- Delete trigger and function if they exist.
DROP TRIGGER IF <PERSON>XISTS delete_notes_trigger ON public."Auctions";

DROP FUNCTION IF EXISTS delete_notes_on_auction_complete();

-- Create trigger and function to handle auction status set to complete.
CREATE OR REPLACE FUNCTION delete_notes_on_auction_complete()
    RETURNS TRIGGER AS $$
    BEGIN
      PERFORM pg_notify('auction_complete_channel', NEW.id::text);
    RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER delete_notes_trigger
    AFTER UPDATE OF "status" ON public."Auctions"
    FOR EACH ROW
    WHEN (NEW."status" = 'COMPLETE')
    EXECUTE FUNCTION delete_notes_on_auction_complete();