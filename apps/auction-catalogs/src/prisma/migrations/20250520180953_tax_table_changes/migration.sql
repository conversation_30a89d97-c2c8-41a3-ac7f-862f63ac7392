/*
  Warnings:

  - You are about to drop the column `isTaxableLots` on the `Auctions` table. All the data in the column will be lost.
  - You are about to drop the column `isTaxable` on the `InvoiceItems` table. All the data in the column will be lost.
  - You are about to drop the column `isTaxable` on the `Lots` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Auctions" DROP COLUMN "isTaxableLots";

-- AlterTable
ALTER TABLE "InvoiceItems" DROP COLUMN "isTaxable";

-- AlterTable
ALTER TABLE "Lots" DROP COLUMN "isTaxable";

-- CreateTable
CREATE TABLE "AuctionTaxes" (
    "id" UUID NOT NULL,
    "auctionsId" TEXT NOT NULL,
    "taxTypesId" INTEGER NOT NULL,

    CONSTRAINT "AuctionTaxes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LotTaxes" (
    "id" UUID NOT NULL,
    "lotsId" TEXT NOT NULL,
    "taxTypesId" INTEGER NOT NULL,

    CONSTRAINT "LotTaxes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AuctionTaxes_auctionsId_key" ON "AuctionTaxes"("auctionsId");

-- CreateIndex
CREATE INDEX "AuctionTaxes_auctionsId_idx" ON "AuctionTaxes"("auctionsId");

-- CreateIndex
CREATE INDEX "AuctionTaxes_taxTypesId_idx" ON "AuctionTaxes"("taxTypesId");

-- CreateIndex
CREATE UNIQUE INDEX "LotTaxes_lotsId_key" ON "LotTaxes"("lotsId");

-- CreateIndex
CREATE INDEX "LotTaxes_lotsId_idx" ON "LotTaxes"("lotsId");

-- CreateIndex
CREATE INDEX "LotTaxes_taxTypesId_idx" ON "LotTaxes"("taxTypesId");

-- AddForeignKey
ALTER TABLE "AuctionTaxes" ADD CONSTRAINT "AuctionTaxes_auctionsId_fkey" FOREIGN KEY ("auctionsId") REFERENCES "Auctions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuctionTaxes" ADD CONSTRAINT "AuctionTaxes_taxTypesId_fkey" FOREIGN KEY ("taxTypesId") REFERENCES "TaxTypes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LotTaxes" ADD CONSTRAINT "LotTaxes_lotsId_fkey" FOREIGN KEY ("lotsId") REFERENCES "Lots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LotTaxes" ADD CONSTRAINT "LotTaxes_taxTypesId_fkey" FOREIGN KEY ("taxTypesId") REFERENCES "TaxTypes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
