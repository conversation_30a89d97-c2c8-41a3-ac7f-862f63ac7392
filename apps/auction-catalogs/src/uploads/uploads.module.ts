import { Module, forwardRef } from '@nestjs/common';
import { UploadsService } from './service/uploads.service';
import { UploadItemsModule } from '../upload-items/upload-items.module';
import { UploadsController } from './controller/uploads.controller';
import { S3Module } from '../aws/s3/s3.module';
import { ResourcesModule } from '../resources/resources.module';
import { AuctionModule } from '../auction/auction.module';
import { LotsModule } from '../lots/lots.module';

@Module({
  imports: [S3Module, ResourcesModule, UploadItemsModule, forwardRef(() => AuctionModule), forwardRef(() => LotsModule)],
  controllers: [UploadsController],
  providers: [UploadsService, UploadsController],
  exports: [UploadsController],
})
export class UploadsModule {}
