export class FileUploadMetadata {
  proxyId: string;
  resourceType: string;
  files: SingleFile[];
}

export class SingleFile {
  filename: string;
}

export class SingleFileUpload {
  uploadId: string;
  fileId: string;
  filename: string;
  type: ResourceType;
  fileType: string;
}

export class ResourceFile extends SingleFileUpload {
  fileLink: string;
  proxyId?: string;
}

export class StartUploadFileResponse {
  files: SingleFileUpload[];
}

export class UploadsByResourceResponse {
  proxyId: string;
  files: ResourceFile[];
  type: ResourceType;
}

export enum ResourceType {
  AUCTION_IMAGES = 'AUCTION_IMAGES',
  LOT_ITEM_IMAGES = 'LOT_ITEM_IMAGES',
  LOT_ITEM_DOCUMENTS = 'LOT_ITEM_DOCUMENTS',
  AUCTION_DOCUMENTS = 'AUCTION_DOCUMENTS',
}
export interface Asset {
  url: string;
  name: string;
  type: string;
}
