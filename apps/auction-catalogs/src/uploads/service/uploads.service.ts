import { Injectable } from '@nestjs/common';
import { CreateUploadDto } from '../dto/create-upload.dto';
import { ResourceByUpload, Upload } from '../entities/upload.entity';
import { PrismaService } from '../../prisma/prisma.service';
import { UploadsNotFoundError } from '../exceptions/uploads-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

@Injectable()
export class UploadsService {
  constructor(private prismaService: PrismaService) {}

  async create(createUploadDto: CreateUploadDto): Promise<Upload | null> {
    return await this.prismaService.upload.create({
      data: {
        resourceId: createUploadDto.resourceId,
        proxyId: createUploadDto.proxyId,
        metadata: '', //TODO Need to determine what we are going to save here.
      },
    });
  }

  async findOne(id: string): Promise<Upload | null> {
    return await this.prismaService.upload.findUnique({
      where: {
        id: id,
      },
    });
  }

  /**
   * Finds uploads by their IDs.
   *
   * This method queries the database for uploads that match the provided IDs.
   * If no uploads are found, it triggers a 404 error response.
   *
   * @param ids An array of IDs to search for.
   * @returns A promise that resolves with an array of found uploads.
   */
  async findMany(ids: string[]): Promise<Upload[]> {
    // Query the database for uploads with the specified IDs.
    const uploads: Upload[] = await this.prismaService.upload.findMany({
      where: {
        id: { in: ids },
        deletedOn: null,
      },
    });

    // If no uploads are found, trigger a 404 error.
    if (uploads.length === 0) {
      throw this.throw404(ids.join(', '));
    }

    // Return the found uploads.
    return uploads;
  }

  async findByProxyId(proxyId: string, resourceTypeId: string): Promise<Upload | null> {
    return await this.prismaService.upload.findFirst({
      where: {
        proxyId: proxyId,
        resourceId: resourceTypeId,
      },
    });
  }

  async findByProxyIds(proxyIds: string[]): Promise<Upload[] | null> {
    return await this.prismaService.upload.findMany({
      where: {
        proxyId: { in: proxyIds },
        deletedOn: null,
      },
    });
  }

  //TODO - This doesn't work yet, but much more efficient method to do this
  async findResourcesByProxyIds(proxyId: string[]): Promise<ResourceByUpload[] | null> {
    const proxyIdSQLString: string = "'" + proxyId.join(',') + "'";
    const sql: string = `u."proxyId", u.id as "uploadId", ui.id as "fileId", ui."uploadFilename", concat(r."cdnPath", text(ui.id)) as "fileLink" from "Upload" u left join "UploadItems" ui on uuid(u.id)  = uuid(ui."uploadsId") left join "Resources" r  on uuid(r.id) = uuid(u."resourceId") where u."proxyId" in (${proxyIdSQLString})`;
    return await this.prismaService.$queryRaw<ResourceByUpload[]>`SELECT ${sql}`;
  }

  /**
   * Throws a 404 error specifically for uploads not found.
   *
   * @param id An ID that was not found.
   * @returns An instance of `UploadsNotFoundError` with the missing ID.
   */
  throw404(id: string): Error {
    return new UploadsNotFoundError(id);
  }

  /**
   * clone the images of
   * @param userId
   * @param originalLotId
   * @param newLotId
   */
  async clone(userId: string, originalLotId: string, newLotId: string): Promise<any[]> {
    const sql: Prisma.Sql = Prisma.sql`
    -- 1. Get all uploads for the original lot
    WITH original_uploads AS (
      SELECT *
      FROM public."Upload"
      WHERE "proxyId" = ${originalLotId} and "deletedOn" IS NULL  
    ),
    -- 2. Insert new rows into the Upload table for each original upload
    new_uploads AS (
      INSERT INTO public."Upload" (id, "resourceId", metadata, "proxyId", "createdOn", "createdBy", "updatedOn", "updatedBy")
      SELECT 
        uuid_generate_v4(),          
        "resourceId", 
        metadata,
        ${newLotId},                 
        now(),                       
        ${userId},                  
        now(),                      
        ${userId}                   
      FROM original_uploads
      RETURNING id, "resourceId"     
    )
    -- 3. Clone the UploadItems for each new Upload
    INSERT INTO public."UploadItems" (id, "uploadsId", "uploadFilename", "fileType", size, "createdOn", "createdBy", "updatedOn", "updatedBy")
    SELECT 
      uuid_generate_v4(),            
      new_uploads.id::uuid,          
      "uploadFilename", 
      "fileType", 
      size,
      now(),                         
      ${userId},                    
      now(),                        
      ${userId}                     
    FROM public."UploadItems"
    JOIN original_uploads ON "UploadItems"."uploadsId" = original_uploads.id::uuid
    JOIN new_uploads ON new_uploads."resourceId" = original_uploads."resourceId"
    WHERE "UploadItems"."deletedOn" IS NULL
    RETURNING "uploadsId";
  `;

    return await this.prismaService.$queryRaw<any[]>(sql);
  }
}
