import { AnyFilesInterceptor, MemoryStorageFile, UploadedFiles } from '@blazity/nest-file-fastify';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Delete, Get, Logger, OnModuleInit, Param, Post, Req, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';
import { ConfigService } from 'libs/config-service/src/config.service';
import { find, forEach, groupBy, intersectionWith, keyBy, map } from 'lodash';
import { v4 as uuid } from 'uuid';
import { S3Service } from '../../aws/s3/data/s3.service';
import { ResourcesController } from '../../resources/controller/resources.controller';
import { UploadItemDto } from '../../upload-items/dto/create-upload-item.dto';
import { UploadItem } from '../../upload-items/entities/upload-item.entity';
import { UploadItemsService } from '../../upload-items/service/upload-items.service';
import { CreateUploadDto } from '../dto/create-upload.dto';
import { Upload } from '../entities/upload.entity';
import { FileUploadMetadata, ResourceFile, ResourceType, SingleFileUpload, StartUploadFileResponse, UploadsByResourceResponse } from '../entities/uploads.interfaces';
import { UploadsService } from '../service/uploads.service';

import { ListResultset } from '@mvc/data/list-resultset';
import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ResponseStatusInterceptor } from '@vb/nest/intercepters/response-status.interceptor';
import { RedisService } from '@vb/redis';
import { StandalonePubSubController } from '@vb/redis/controllers/standalone-pub-sub.controller';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { LotDto } from '../../lots/dtos/lot.dto';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { ResourcesDto } from '../../resources/dto/resources.dto';
import { ResourcesNotFoundError } from '../../resources/exceptions/resources-not-found-error';
import { ResourcesListResponse } from '../../resources/http/responses/resources-list.response';
import { ResourcesResponse } from '../../resources/http/responses/resources.response';
import { DeleteUploadDto } from '../dto/delete-upload.dto';
import { CouldNotCreateUploadError } from '../exceptions/could-not-create-upload';
import { CouldNotCreateUploadItemError } from '../exceptions/could-not-create-upload-item';
import { UploadItemNotFoundError } from '../exceptions/could-not-find-upload-item';
import { CouldNotUploadFileError } from '../exceptions/could-not-upload-file';
import { MultipleFileUploadAttemptError } from '../exceptions/multiple-file-upload-attempt';
import { S3UploadFailed } from '../exceptions/s3-upload-failed';
import { UploadItemsNotFoundError } from '../exceptions/upload-items-not-found';
import { UploadsNotFoundError } from '../exceptions/uploads-not-found-error';
@UseInterceptors(ResponseStatusInterceptor)
@Controller('uploads')
export class UploadsController extends StandalonePubSubController implements OnModuleInit {
  private readonly logger = new Logger(UploadsController.name);
  private bucketName: string = '';

  constructor(
    private readonly uploadsService: UploadsService,
    private readonly uploadItemsService: UploadItemsService,
    private readonly s3Service: S3Service,
    private readonly resourcesController: ResourcesController,
    private readonly configService: ConfigService,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly lotsHandler: LotsHandler,
    redisService: RedisService,
  ) {
    super(redisService, 'images');
  }

  async onModuleInit(): Promise<void> {
    const templateBucketName: string | undefined = await this.configService.getSetting('TEMPLATE_BUCKET_NAME');

    if (!templateBucketName) {
      this.logger.error('Auction Catalog Service was not provided with the required env variable: TEMPLATE_BUCKET_NAME');
      process.kill(process.pid, 'SIGTERM');
    } else {
      this.bucketName = templateBucketName ?? '';
    }
  }

  @Post('/startUpload')
  async startUpload(@Body() fileUploadMetadata: FileUploadMetadata): Promise<StartUploadFileResponse | ErrorResponse> {
    return this.executeAndNotify(
      'upload',
      async () => {
        //TODO Prevent a certain amount of files from being uploaded. > 10 should throw an error at one time.
        //TODO Check files for constraints (filetype, size, etc)
        try {
          const resourceType: ResourcesResponse | ViewbidError = await this.resourcesController.findByResourceType(fileUploadMetadata.resourceType);

          if (resourceType instanceof ViewbidError) {
            this.logger.error('Could not find resource type: ' + fileUploadMetadata.resourceType);
            return new ErrorResponse(resourceType, 'uploads');
          }

          const isResource: boolean = await this.checkIfResourceExists(resourceType.data.resourceType, fileUploadMetadata.proxyId);
          if (isResource) {
            let upload: any = await this.uploadsService.findByProxyId(fileUploadMetadata.proxyId, resourceType.data.id);
            if (!upload) {
              const createUpload: CreateUploadDto = {
                resourceId: resourceType.id,
                proxyId: fileUploadMetadata.proxyId,
              };

              upload = await this.uploadsService.create(createUpload);
              if (!upload) {
                this.logger.error('Could not create upload: ' + fileUploadMetadata.proxyId);
                return new ErrorResponse(new CouldNotCreateUploadError(fileUploadMetadata.proxyId), 'uploads');
              }
            }

            const responseFiles: SingleFileUpload[] = [];
            const createItems: UploadItem[] = [];
            let updateItems: UploadItem[] = [];

            const createdItems: any = await this.uploadItemsService.findByUploadId(upload.id);
            if (createdItems) {
              updateItems = intersectionWith(createdItems, fileUploadMetadata.files, (val: any, secondVal: any) => {
                return val.uploadFilename == secondVal.filename;
              });
            }

            for (const file of fileUploadMetadata.files) {
              const tmpId: any = uuid();
              const updateItem: any = find(updateItems, { uploadFilename: file.filename });
              if (!updateItem) {
                createItems.push({
                  id: tmpId,
                  uploadsId: upload.id,
                  uploadFilename: file.filename,
                });
              }
              responseFiles.push({
                uploadId: upload.id,
                fileId: updateItem?.id ?? tmpId,
                filename: file.filename,
                type: fileUploadMetadata.resourceType as ResourceType,
                fileType: upload.fileType,
              });
            }

            const newItems: any = await this.uploadItemsService.createAll(createItems);
            if (!newItems) {
              const newItemIds: string = map(createItems, 'id').join(',');

              this.logger.error('Could not create for the following files: ' + newItemIds);
              return new ErrorResponse(new CouldNotCreateUploadItemError(newItemIds), 'uploadItems');
            }

            const startupResponse: StartUploadFileResponse = {
              files: responseFiles,
            };
            return startupResponse;
          } else {
            this.logger.error('The resource you are uploading to does not exist. ProxyId: ' + fileUploadMetadata.proxyId);
            return new ErrorResponse(new CouldNotCreateUploadError(fileUploadMetadata.proxyId), 'uploads', fileUploadMetadata.proxyId);
          }
        } catch (error) {
          this.logger.error('Could not initiate upload for proxyId: ' + fileUploadMetadata.proxyId, error);
          return new ErrorResponse(new CouldNotCreateUploadError(fileUploadMetadata.proxyId), 'uploads');
        }
      },
      {},
    );
  }

  @Post('/:uploadId/:fileId')
  @UseInterceptors(AnyFilesInterceptor())
  async uploadFile(
    @Param('uploadId') uploadId: string,
    @Param('fileId') fileId: string,
    @UploadedFiles() files: MemoryStorageFile[],
    @Req() req: VBInterceptorRequest,
  ): Promise<Upload | string | ErrorResponse> {
    if (files.length > 1) {
      //TODO Prevent multiple files for now, because of the single upload id, and file id

      this.logger.error('Only upload one file at a time. Multiple will be supported later.');
      return new ErrorResponse(new MultipleFileUploadAttemptError(fileId), 'uploads');
    }

    const upload: Upload | null = await this.uploadsService.findOne(uploadId);
    if (!upload) {
      this.logger.error('Upload not found: ' + uploadId);
      return new ErrorResponse(new UploadsNotFoundError(fileId), 'uploads');
    }

    const uploadItem: UploadItem | null = await this.uploadItemsService.findByFileId(fileId);

    if (!uploadItem) {
      this.logger.error('Uploaded File not found: ' + fileId);
      return new ErrorResponse(new UploadsNotFoundError(fileId), 'uploads');
    }

    const resourceType: ResourcesResponse | ErrorResponse = await this.resourcesController.get(upload.resourceId, req);
    if ('error' in resourceType) {
      this.logger.error('Could not retrieve resource type. Resource Id: ' + upload?.resourceId);
      return new ErrorResponse(new ResourcesNotFoundError(upload?.resourceId), 'uploads');
    }

    const file: any = files[0];
    const updateUploadItem: UploadItemDto = {
      id: fileId,
      uploadsId: uploadId,
      fileType: uploadItem.uploadFilename ? uploadItem.uploadFilename.slice(((uploadItem.uploadFilename.lastIndexOf('.') - 1) >>> 0) + 2) : file.mimetype,
      size: file.size,
    };

    try {
      const updatedUploadItem: UploadItem | null = await this.uploadItemsService.update(updateUploadItem);
      if (!updatedUploadItem) {
        this.logger.error('Could not retrieve upload item for fileId: ' + fileId);
        return new ErrorResponse(new UploadItemNotFoundError(fileId), 'uploads');
      }
    } catch (error) {
      this.logger.error('Could not update upload item with uploadId: ' + uploadId, error);
      return new ErrorResponse(new CouldNotUploadFileError(uploadId), 'uploads');
    }

    try {
      this.logger.log('Uploading file: ' + resourceType.data.location + fileId);
      await this.s3Service.saveFile(this.bucketName, resourceType.data.location, fileId, file.buffer, file.mimetype);

      return upload;
    } catch (error: any) {
      this.logger.error('Could not upload to S3 Bucket. Error: ' + error.message);
      return new ErrorResponse(new S3UploadFailed(uploadId), 'uploads');
    }
  }

  @Get('byResource/:resourceId')
  async findUploadsForResource(@Param('resourceId') resourceId: string, isThumbnail: boolean = false): Promise<[] | UploadsByResourceResponse[] | ErrorResponse> {
    const resourceIds: string[] = resourceId.split(',');
    let uploads: Upload[] | null;
    try {
      uploads = await this.uploadsService.findByProxyIds(resourceIds);
      if (uploads == null || uploads.length <= 0) {
        this.logger.error('Could not retrieve any upload related to resourceIds: [ ' + resourceIds.join(',') + ' ]');
        return new ErrorResponse(new ResourcesNotFoundError(resourceId), 'uploads');
      }
    } catch (error) {
      this.logger.error('Could not retrieve any upload related to resourceIds: [ ' + resourceIds.join(',') + ' ]');
      return new ErrorResponse(new ResourcesNotFoundError(resourceId), 'uploads');
    }

    const resourceTypeIds: string[] = [];
    const uploadIds: string[] = [];
    uploads.forEach((elem: any) => {
      resourceTypeIds.push(elem.resourceId);
      uploadIds.push(elem.id);
    });

    try {
      const uploadsMap: { [key: string]: Upload } = keyBy(uploads, 'id');

      const uploadItems: UploadItem[] | null = await this.uploadItemsService.findByUploadIds(uploadIds);
      if (uploadItems == null) {
        this.logger.error('Could not retrieve any upload related to resourceIds: [ ' + resourceIds.join(',') + ' ]');
        return new ErrorResponse(new UploadItemsNotFoundError(resourceId), 'uploads');
      }

      const uploadItemsByUploadId: { [key: string]: UploadItem[] } = groupBy(uploadItems, 'uploadsId');

      const resources: ResourcesListResponse = await this.resourcesController.findByIds(resourceTypeIds.join(','));
      if (resources == null) {
        this.logger.error('Could not retrieve any upload related to resourceIds: [ ' + resourceIds.join(',') + ' ]');
        return new ErrorResponse(new ResourcesNotFoundError(resourceTypeIds.join(',')), 'resources');
      }
      const resourcesMap: { [key: string]: ResourcesDto } = keyBy(resources.data, 'id');

      const uploadResponse: UploadsByResourceResponse[] = [];
      forEach(uploadsMap, (value: Upload) => {
        const files: ResourceFile[] = [];
        forEach(uploadItemsByUploadId[value.id], (item: any) => {
          const file: ResourceFile = {
            uploadId: item.uploadsId,
            fileId: item.id,
            filename: item.uploadFilename ?? '',
            fileLink: resourcesMap[value.resourceId].cdnPath + (isThumbnail ? 'thumbnails/' : '') + item.id,
            type: resourcesMap[value.resourceId].resourceType as ResourceType,
            fileType: item.fileType,
            proxyId: value.proxyId,
          };
          files.push(file);
        });
        uploadResponse.push({
          proxyId: value.proxyId,
          files: files,
          type: resourcesMap[value.resourceId].resourceType as ResourceType,
        });
      });
      return uploadResponse;
    } catch (error) {
      this.logger.error('Could not retrieve any upload related to resourceIds: [ ' + resourceIds.join(',') + ' ]');
      return new ErrorResponse(new UploadItemsNotFoundError(resourceIds.join(',')), 'uploads');
    }
  }

  /**
   * Handles the deletion of upload items based on their uploadItem IDs.
   * This endpoint requires the user to be authenticated via JWT.
   *
   * @param deleteUploadDto DTO containing an array of uploadItem IDs to be deleted.
   * @param req The request object, used to extract the user's ID.
   * @param res The response object, used to send back the HTTP status.
   * @returns A promise that resolves to void or an ErrorResponse if an error occurs.
   */
  @Delete('/')
  @UsePipes(new ValidationPipe({ transform: true }))
  async delete(@Body() deleteUploadDto: DeleteUploadDto, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    const { ids } = deleteUploadDto;
    try {
      await this.uploadItemsService.deleteByUploadItemsIds(req.accountMappingsId, ids);
    } catch (error) {
      return new ErrorResponse(error, 'delete');
    }
  }

  async checkIfResourceExists(resourceType: string, proxyId: string): Promise<boolean> {
    const handlerMap: { [key: string]: (id: string) => Promise<any> } = {
      [ResourceType.AUCTION_IMAGES]: this.auctionsHandler.get.bind(this.auctionsHandler),
      [ResourceType.LOT_ITEM_IMAGES]: this.lotsHandler.get.bind(this.lotsHandler),
      [ResourceType.LOT_ITEM_DOCUMENTS]: this.lotsHandler.get.bind(this.lotsHandler),
      [ResourceType.AUCTION_DOCUMENTS]: this.auctionsHandler.get.bind(this.auctionsHandler),
    };

    const handler: (id: string) => Promise<any> = handlerMap[resourceType];

    if (!handler) {
      throw new Error(`Unsupported resource type: ${resourceType}`);
    }

    try {
      await handler(proxyId);
      return true;
    } catch (error) {
      return false;
    }
  }

  @Post('regenerateThumbnails')
  async regenerateThumbnails(): Promise<void> {
    let page: number = 1;
    const size: number = 20;
    const RESOURCE_TYPE_LOT_ITEM_IMAGE: string = 'lot_item_images';

    let firstList: ListResultset<LotDto>;

    do {
      // Fetch the current page of lots
      firstList = await this.lotsHandler.getAll(page, size, []);
      const thumbnailIds: string[] = firstList.list
        .filter((dto: LotDto) => dto.thumbnailId) // Ensure thumbnailId is not undefined
        .map((dto: LotDto) => dto.thumbnailId!); // Collect thumbnailIds

      // Generate thumbnails for the current page
      if (thumbnailIds.length > 0) {
        await this.s3Service.generateThumbnails(this.bucketName, RESOURCE_TYPE_LOT_ITEM_IMAGE, thumbnailIds);
      }

      // Increment the page number for the next iteration
      page++;
    } while (firstList.list.length > 0); // Continue until no more items are returned
  }

  async clone(userId: string, lotId: string, newLotId: string): Promise<any[]> {
    return await this.uploadsService.clone(userId, lotId, newLotId);
  }
}
