import { BaseHandler } from '@mvc/handlers/base.handler';
import { ProductInventoriesDbService } from '../data/product-inventories-db.service';
import { ProductInventoryDto } from '../dtos/product-inventory.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ProductInventoriesHandler extends BaseHandler<ProductInventoriesDbService, ProductInventoryDto> {
  constructor(dbService: ProductInventoriesDbService) {
    super(dbService);
  }
}
