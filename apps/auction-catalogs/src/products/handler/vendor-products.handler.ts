import { BaseHand<PERSON> } from '@mvc/handlers/base.handler';
import { VendorProductsDbService } from '../data/vendor-products-db.service';
import { VendorProductDto } from '../dtos/vendor-product.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class VendorProductsHandler extends BaseHandler<VendorProductsDbService, VendorProductDto> {
  constructor(dbService: VendorProductsDbService) {
    super(dbService);
  }
}
