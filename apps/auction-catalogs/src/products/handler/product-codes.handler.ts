import { BaseHandler } from '@mvc/handlers/base.handler';
import { ProductCodesDbService } from '../data/product-codes-db.service';
import { ProductCodeDto } from '../dtos/product-code.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ProductCodesHandler extends BaseHandler<ProductCodesDbService, ProductCodeDto> {
  constructor(dbService: ProductCodesDbService) {
    super(dbService);
  }

  async getByBarcode(barcode: string): Promise<ProductCodeDto> {
    return await this.dbService.getByBarcode(barcode);
  }
}
