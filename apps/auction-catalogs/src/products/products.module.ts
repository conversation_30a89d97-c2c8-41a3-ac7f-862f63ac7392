import { forwardRef, Module } from '@nestjs/common';
import { ProductsController } from './controller/admin/products.controller';
import { ProductsDbService } from './data/products-db.service';
import { ProductsHandler } from './handler/products.handler';
import { ProductCodesDbService } from './data/product-codes-db.service';
import { ProductInventoriesDbService } from './data/product-inventories-db.service';
import { VendorProductsDbService } from './data/vendor-products-db.service';
import { VendorsModule } from '../vendors/vendors.module';
import { GoUpcService } from './services/go-upc.service';
import { HttpModule } from '@nestjs/axios';
import { ProductCodesHandler } from './handler/product-codes.handler';
import { ProductInventoriesHandler } from './handler/product-inventories.handler';
import { VendorProductsHandler } from './handler/vendor-products.handler';
import { LotsModule } from '../lots/lots.module';
import { AuctionLotsModule } from '../auction-lots/auction-lots.module';
import { AuctionModule } from '../auction/auction.module';
import { TaxTypeDbService } from '../invoicing/data/tax-types-db.service';
import { TaxTypeHandler } from '../invoicing/handlers/tax-type.handler';
import { LotTaxesDbService } from '../lots/data/lot-taxes-db.service';
import { LotTaxesHandler } from '../lots/handlers/lot-taxes.handler';
import { InvoicesModule } from '../invoicing/invoices.module';

@Module({
  controllers: [ProductsController],
  providers: [
    ProductsDbService,
    ProductsHandler,
    ProductCodesDbService,
    ProductCodesHandler,
    ProductInventoriesDbService,
    ProductInventoriesHandler,
    VendorProductsDbService,
    VendorProductsHandler,
    GoUpcService,
    TaxTypeDbService,
    TaxTypeHandler,
    LotTaxesDbService,
    LotTaxesHandler,
  ],
  imports: [VendorsModule, HttpModule, LotsModule, AuctionLotsModule, AuctionModule, forwardRef(() => InvoicesModule)],
  exports: [ProductsHandler],
})
export class ProductsModule {}
