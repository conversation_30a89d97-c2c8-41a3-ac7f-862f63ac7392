import { ProductDto } from './product.dto';

export class ProductCode {
  code: string;
  codeType: string;

  constructor(row: any) {
    this.code = row.code;
    this.codeType = row.codeType;
  }
}

export class ProductInventory {
  condition: string;
  quantityInStock: number;

  constructor(row: any) {
    this.condition = row.condition;
    this.quantityInStock = row.quantityInStock;
  }
}

export class ExtendedProductDto extends ProductDto {
  codes: ProductCode[];
  inventories: ProductInventory[];
  sellingVendorIds: string[];

  constructor(row: any) {
    super(row);

    this.codes = row.codes?.map((pc: any) => new ProductCode(pc));
    this.inventories = row.inventories?.map((pi: any) => new ProductInventory(pi));
    this.sellingVendorIds = row.sellingVendorIds;
  }
}
