import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

function castIfDecimal(value: any): any {
  return value instanceof Prisma.Decimal ? value.toNumber() : value;
}

export class ProductDto {
  id: string;
  title: string;
  description: string;
  tags: string;
  category: string;
  thumbnailId: string | null;
  unitCost: number | null;
  retailPrice: number | null;

  constructor(row: any) {
    this.id = row.id;
    this.title = row.title;
    this.description = row.description;
    this.tags = row.tags;
    this.category = row.category;
    this.thumbnailId = row.thumbnailId;
    this.unitCost = castIfDecimal(row.unitCost);
    this.retailPrice = castIfDecimal(row.retailPrice);
  }
}
