export class GoUpcNestedProduct {
  name: string;
  description: string;
  imageUrl: string;
  categoryPath: string[];
  prices: number[];

  constructor(data: any) {
    this.name = data.name;
    this.description = data.description;
    this.imageUrl = data.imageUrl;
    this.categoryPath = data.categoryPath;
    this.prices = data.prices;
  }
}

export class GoUpcDto {
  code: string;
  codeType: string;
  product: GoUpcNestedProduct;

  constructor(data: any) {
    this.code = data.code;
    this.codeType = data.codeType;
    this.product = new GoUpcNestedProduct(data.product);
  }
}
