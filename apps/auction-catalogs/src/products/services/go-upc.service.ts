import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { BarcodeNotFoundError } from '../exceptions/barcode-not-found.error';
import { AxiosResponse } from 'axios';
import { ConfigService } from '@vb/config/src/config.service';
import { join } from 'lodash';
import { ExtendedProductDto } from '../dtos/extended-product.dto';

@Injectable()
export class GoUpcService {
  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
  ) {}

  async parsePrices(rawPrices: string[]): Promise<number[]> {
    const prices: number[] = [];

    const usdConversionFactor: number = Number(await this.configService.getSetting('USD_TO_CAD_CONVERSION_FACTOR'));

    rawPrices.forEach((p: string) => {
      if (p.length > 0) {
        let matches: RegExpMatchArray | null = p.match('\\d+(.\\d{1,2})?');
        if (matches && matches.length > 0) {
          if (p.indexOf('CAD') >= 0) {
            prices.push(Number(matches[0]));
          } else if (p.indexOf('USD') >= 0) {
            prices.push(usdConversionFactor * Number(matches[0]));
          } else {
            matches = p.match('^\\$?\\d+(.\\d{1,2})?$');
            if (matches && matches.length > 0) {
              prices.push(Number(matches[0].replace('$', '')));
            }
          }
        }
      }
    });
    return prices;
  }

  async getProduct(barcode: string): Promise<ExtendedProductDto> {
    const apiKey: string = await this.configService.getSetting('GO_UPC_API_KEY', '');
    const baseUrl: string = await this.configService.getSetting('GO_UPC_URL', '');
    const url: string = `${baseUrl}/${barcode}?key=${apiKey}&pricing=t`;

    const response: AxiosResponse = await this.httpService.axiosRef.get(url);

    if (!response.data || !response.data.product) {
      throw new BarcodeNotFoundError(barcode);
    }

    try {
      const data: any = response.data;
      const pricing: number[] = data.product.pricing ? await this.parsePrices(data.product.pricing.knownPrices) : [];

      const product: ExtendedProductDto = new ExtendedProductDto({
        title: data.product!.name,
        description: data.product!.description,
        category: data.product!.categoryPath[0],
        tags: join(data.product?.categoryPath.slice(1), ','),
        retailPrice: pricing ? Math.min(...pricing) : null,
        unitCost: null,
        thumbnailId: null,
        codes: [
          {
            code: data.code,
            codeType: data.codeType,
          },
        ],
        inventories: [],
        sellingVendorIds: [],
      });

      return product;
    } catch (error) {
      throw new BarcodeNotFoundError(barcode);
    }
  }
}
