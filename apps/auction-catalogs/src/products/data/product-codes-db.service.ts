import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { ProductCodeDto } from '../dtos/product-code.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ProductCodeNotFoundError } from '../exceptions/product-code-not-found.error';
import { BarcodeNotFoundError } from '../exceptions/barcode-not-found.error';

@Injectable()
export class ProductCodesDbService extends BaseDbService<Prisma.ProductCodesDelegate<DefaultArgs>, ProductCodeDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.productCodes, true, false, true);
  }

  async getByBarcode(barcode: string): Promise<ProductCodeDto> {
    const row: any = await this.table.findFirst({
      where: {
        code: barcode,
        deletedOn: null,
      },
    });

    if (!row) {
      throw new BarcodeNotFoundError(barcode);
    }

    return new ProductCodeDto(row);
  }

  createFilter(params: any[]): any {
    const productsId: string | undefined = params.find((r: any) => r.hasOwnProperty('productsId'))?.productsId;

    return { productsId };
  }

  mapToDto(model: any): ProductCodeDto {
    return new ProductCodeDto(model);
  }

  throw404(id: string): Error {
    throw new ProductCodeNotFoundError(id);
  }
}
