import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { ProductInventoryDto } from '../dtos/product-inventory.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { ProductInventoryNotFoundError } from '../exceptions/product-inventory-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ProductInventoriesDbService extends BaseDbService<Prisma.ProductInventoriesDelegate<DefaultArgs>, ProductInventoryDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.productInventories, true, true, true);
  }

  mapToDto(model: any): ProductInventoryDto {
    return new ProductInventoryDto(model);
  }

  createFilter(params: any[]): any {
    const productsId: string | undefined = params.find((r: any) => r.hasOwnProperty('productsId'))?.productsId;

    return { productsId };
  }

  throw404(id: string): Error {
    throw new ProductInventoryNotFoundError(id);
  }
}
