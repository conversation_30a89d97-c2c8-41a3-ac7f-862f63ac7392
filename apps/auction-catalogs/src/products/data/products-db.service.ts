import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { ProductDto } from '../dtos/product.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { ProductNotFoundError } from '../exceptions/product-not-found.error';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { ExtendedProductDto } from '../dtos/extended-product.dto';

@Injectable()
export class ProductsDbService extends BaseDbService<Prisma.ProductsDelegate<DefaultArgs>, ProductDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.products);
  }

  throw404(id: string): Error {
    throw new ProductNotFoundError(id);
  }

  mapToDto(model: any): ProductDto {
    return new ProductDto(model);
  }

  mapToExtendedDto(model: any): ProductDto {
    const product: any = model.product;
    product.codes = model.codes;
    product.inventories = model.inventories;
    product.sellingVendorIds = model.sellingVendorIds;
    return new ExtendedProductDto(product);
  }

  async getAll(page: number, size: number, params: any[]): Promise<ListResultset<ProductDto>> {
    const filter: Prisma.Sql = this.createFilter(params);

    const skip: number = size > 0 ? (page - 1) * size : 0;
    const limit: number | Prisma.Sql = size > 0 ? size : Prisma.sql`ALL`;

    const products: ProductDto[] = (
      await this.prismaService.$queryRaw<any[]>`
    SELECT ROW_TO_JSON(p.*) as product,
    	     JSON_AGG(DISTINCT pc.*) FILTER (WHERE pc.id IS NOT NULL AND pc."deletedOn" IS NULL) as codes,
           JSON_AGG(DISTINCT pi.*) FILTER (WHERE pi.id IS NOT NULL AND pi."deletedOn" IS NULL) as inventories,
    	     ARRAY_AGG(DISTINCT vp."vendorsId") FILTER (WHERE vp.id IS NOT NULL
                                                      AND vp."deletedOn" IS NULL
                                                      AND v."deletedOn" IS NULL) as "sellingVendorIds"
    FROM public."Products" p
    LEFT JOIN public."ProductCodes" pc ON p.id = pc."productsId"
    LEFT JOIN public."ProductInventories" pi ON p.id = pi."productsId"
    LEFT JOIN public."VendorProducts" vp ON p.id = vp."productsId"
	  LEFT JOIN public."Vendors" v ON v.id = vp."vendorsId"
    WHERE p."deletedOn" IS NULL
    ${filter} OFFSET ${skip} LIMIT ${limit};`
    ).map((row: any) => this.mapToExtendedDto(row));

    const totalCount: number =
      (
        await this.prismaService.$queryRaw<any[]>`
    SELECT SUM("count")::int as "totalCount" FROM (
      SELECT COUNT(DISTINCT p.id) as "count"
      FROM public."Products" p
      LEFT JOIN public."ProductCodes" pc ON p.id = pc."productsId"
      LEFT JOIN public."ProductInventories" pi ON p.id = pi."productsId"
      LEFT JOIN public."VendorProducts" vp ON p.id = vp."productsId"
	    LEFT JOIN public."Vendors" v ON v.id = vp."vendorsId"
      WHERE p."deletedOn" IS NULL ${filter});`
      )[0].totalCount ?? 0;

    return new ListResultset(products, page, size, totalCount, Math.ceil(totalCount / size));
  }

  createFilter(params: any[]): Prisma.Sql {
    const barcode: string | undefined = params.find((r: any) => r.hasOwnProperty('code'))?.code;
    const condition: string | undefined = params.find((r: any) => r.hasOwnProperty('condition'))?.condition;
    const vendorsId: string | undefined = params.find((r: any) => r.hasOwnProperty('vendorsId'))?.vendorsId;
    const query: string = params.find((r: any) => r.hasOwnProperty('query'))?.query ?? '';

    const queryWhere: Prisma.Sql = Prisma.sql`AND p.title ILIKE '%' || ${query} || '%' OR p.description ILIKE '%' || ${query} || '%'`;
    const barcodeWhere: Prisma.Sql = barcode ? Prisma.sql`AND ${barcode}=ANY(ARRAY_AGG(pc.code))` : Prisma.empty;
    const conditionWhere: Prisma.Sql = condition ? Prisma.sql`AND ${condition}=ANY(ARRAY_AGG(pi.condition))` : Prisma.empty;
    const vendorWhere: Prisma.Sql = vendorsId ? Prisma.sql`AND ${vendorsId}::uuid=ANY(ARRAY_AGG(vp."vendorsId"))` : Prisma.empty;

    return Prisma.sql`${queryWhere} GROUP BY p.id HAVING TRUE ${barcodeWhere} ${conditionWhere} ${vendorWhere}`;
  }
}
