import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { VendorProductDto } from '../dtos/vendor-product.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { VendorProductNotFoundError } from '../exceptions/vendor-product-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class VendorProductsDbService extends BaseDbService<Prisma.VendorProductsDelegate<DefaultArgs>, VendorProductDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.vendorProducts, true, false, true);
  }

  mapToDto(model: any): VendorProductDto {
    return new VendorProductDto(model);
  }

  createFilter(params: any[]): any {
    const productsId: string | undefined = params.find((r: any) => r.hasOwnProperty('productsId'))?.productsId;

    return { productsId };
  }

  throw404(id: string): Error {
    throw new VendorProductNotFoundError(id);
  }
}
