import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { Transactional } from '@transactional/core';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { LotCondition } from '@vb/nest/enums/lotCondition';
import { LotStatus } from '@vb/nest/enums/lotStatus';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { AuctionLotDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot.dto';
import { AuctionLotsHandler } from 'apps/auction-catalogs/src/auction-lots/handlers/auction-lots.handler';
import { AuctionDto } from 'apps/auction-catalogs/src/auction/dtos/auction.dto';
import { AuctionsHandler } from 'apps/auction-catalogs/src/auction/handlers/auctions.handler';
import { LotDto } from 'apps/auction-catalogs/src/lots/dtos/lot.dto';
import { LotsHandler } from 'apps/auction-catalogs/src/lots/handlers/lots.handler';
import { LotDetailedAdminResponse } from 'apps/auction-catalogs/src/lots/http/responses/lot-detailed-admin.response';
import { LotResponse } from 'apps/auction-catalogs/src/lots/http/responses/lot-response';
import { VendorDto } from 'apps/auction-catalogs/src/vendors/dtos/vendor.dto';
import { VendorNotFoundError } from 'apps/auction-catalogs/src/vendors/exceptions/vendor-not-found.error';
import { VendorsHandler } from 'apps/auction-catalogs/src/vendors/handler/vendors.handler';
import * as _ from 'lodash';
import { ExtendedProductDto } from '../../dtos/extended-product.dto';
import { ProductCodeDto } from '../../dtos/product-code.dto';
import { ProductInventoryDto } from '../../dtos/product-inventory.dto';
import { ProductDto } from '../../dtos/product.dto';
import { VendorProductDto } from '../../dtos/vendor-product.dto';
import { ProductCodesHandler } from '../../handler/product-codes.handler';
import { ProductInventoriesHandler } from '../../handler/product-inventories.handler';
import { ProductsHandler } from '../../handler/products.handler';
import { VendorProductsHandler } from '../../handler/vendor-products.handler';
import { ProductRequest } from '../../http/requests/product.request';
import { ProductListResponse } from '../../http/responses/product-list.response';
import { ProductResponse } from '../../http/responses/product.response';
import { GoUpcService } from '../../services/go-upc.service';
import { TaxTypeHandler } from 'apps/auction-catalogs/src/invoicing/handlers/tax-type.handler';
import { LotTaxesHandler } from 'apps/auction-catalogs/src/lots/handlers/lot-taxes.handler';
import { TaxTypeDto } from 'apps/auction-catalogs/src/invoicing/dtos/tax-type.dto';
import { TaxType } from '@viewbid/ts-node-client';
import { LotTaxesDto } from 'apps/auction-catalogs/src/lots/dtos/lot-taxes.dto';

@Controller('admin/products')
@UseGuards(IsAdmin)
export class ProductsController extends BaseController<ProductsHandler, ProductRequest, ProductDto, ProductResponse, ProductListResponse> {
  constructor(
    handler: ProductsHandler,
    private codesHandler: ProductCodesHandler,
    private inventoriesHandler: ProductInventoriesHandler,
    private vendorProductsHandler: VendorProductsHandler,
    private vendorsHandler: VendorsHandler,
    private goUpcService: GoUpcService,
    private lotsHandler: LotsHandler,
    private auctionLotsHandler: AuctionLotsHandler,
    private auctionsHandler: AuctionsHandler,
    private taxTypeHandler: TaxTypeHandler,
    private lotTaxesHandler: LotTaxesHandler,
  ) {
    super(handler);
  }

  @Post('/')
  @Transactional()
  async create(body: ProductRequest, req: VBInterceptorRequest): Promise<ProductResponse | ErrorResponse> {
    try {
      let product: ProductDto = this.createDtoFromRequest(body);

      await this.validateVendorIds(body.sellingVendorIds);

      product = await this.handler.create(req.accountMappingsId, product);

      const dto: ExtendedProductDto = new ExtendedProductDto(body);
      dto.id = product.id;

      await this.updateChildTables(product.id, dto);

      return new ProductResponse(dto);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Patch('/:id')
  @Transactional()
  async update(@Param('id') id: string, @Body() body: ProductRequest, @Req() req: VBInterceptorRequest): Promise<ProductResponse | ErrorResponse> {
    try {
      let product: ProductDto = await this.handler.get(id);

      await this.validateVendorIds(body.sellingVendorIds);

      product = await this.handler.update(req.accountMappingsId, this.createDtoFromRequest(body), product.id);

      const dto: ExtendedProductDto = new ExtendedProductDto(body);
      dto.id = product.id;

      await this.updateChildTables(product.id, dto);

      return new ProductResponse(dto);
    } catch (error) {
      return new ErrorResponse(error, 'update');
    }
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<ProductResponse | ErrorResponse> {
    req;
    try {
      const product: ProductDto = await this.handler.get(id);
      const codes: ListResultset<ProductCodeDto> = await this.codesHandler.getAll(1, -1, [{ productsId: product.id }]);
      const inventories: ListResultset<ProductInventoryDto> = await this.inventoriesHandler.getAll(1, -1, [{ productsId: product.id }]);
      const vendorProducts: ListResultset<VendorProductDto> = await this.vendorProductsHandler.getAll(1, -1, [{ productsId: product.id }]);

      const extendedProduct: ExtendedProductDto = new ExtendedProductDto({
        ...product,
        codes: codes.list,
        inventories: inventories.list,
        sellingVendorIds: vendorProducts.list.map((vp: VendorProductDto) => vp.vendorsId),
      });

      return new ProductResponse(extendedProduct);
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  @Get('/barcode/:barcode')
  async getByBarcode(@Param('barcode') barcode: string, @Req() req: VBInterceptorRequest): Promise<ProductResponse | ErrorResponse> {
    try {
      const code: ProductCodeDto = await this.codesHandler.getByBarcode(barcode);
      return await this.get(code.productsId, req);
    } catch (error) {
      return new ErrorResponse(error, 'getByBarcode');
    }
  }

  @Post('barcode/:barcode')
  async createByBarcode(@Param('barcode') barcode: string, @Req() req: VBInterceptorRequest): Promise<ProductResponse | ErrorResponse> {
    try {
      const newProduct: ExtendedProductDto = await this.goUpcService.getProduct(barcode);

      const dto: ProductDto = await this.handler.create(req.accountMappingsId, new ProductDto(newProduct));
      newProduct.id = dto.id;
      await this.updateChildTables(dto.id, newProduct);

      return new ProductResponse(newProduct);
    } catch (error) {
      return new ErrorResponse(error, 'createByBarcode');
    }
  }

  @Get('/')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<ProductListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const vendorsId: string | undefined = params.find((r: any) => r.hasOwnProperty('vendorsId'))?.vendorsId;

      if (vendorsId) {
        await this.vendorsHandler.get(vendorsId);
      }

      const products: ListResultset<ProductDto> = await this.handler.getAll(page, size, params);

      return this.createResponseList(products);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Post('create-lot/:auctionId/product/:productId')
  async createLotFromProduct(@Param('auctionId') auctionId: string, @Param('productId') productId: string, @Req() req: VBInterceptorRequest): Promise<LotResponse | ErrorResponse> {
    try {
      const product: ProductDto = await this.handler.get(productId);
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      const clonedLot: LotDto = await this.lotsHandler.create(
        req.accountMappingsId,
        new LotDto({
          ..._.omit(product, ['id']),
          status: LotStatus.Draft,
          code: '',
          reserveAmount: 0,
          condition: LotCondition.Other,
          region: '',
        }),
      );

      const hstTaxType: TaxTypeDto = (await this.taxTypeHandler.getAll(1, -1, [{ name: TaxType.Hst.toString() }])).list[0];
      await this.lotTaxesHandler.create(
        req.accountMappingsId,
        new LotTaxesDto({
          lotsId: clonedLot.id,
          taxTypesId: hstTaxType.id,
        }),
      );

      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.createFromTemplate(
        req.accountMappingsId,
        auction,
        new AuctionLotDto({
          lotId: clonedLot.id,
          auctionId: auction.id,
          suffix: '',
          status: 'DRAFT',
        }),
      );

      return new LotDetailedAdminResponse(clonedLot, auctionLot, [], [], []);
    } catch (error) {
      return new ErrorResponse(error, 'cloneLotFromProduct');
    }
  }

  createDtoFromRequest(request: ProductRequest): ProductDto {
    return new ProductDto(request);
  }

  createResponseFromDto(dto: ProductDto): ProductResponse {
    return new ProductResponse(dto);
  }

  createResponseList(list: ListResultset<ProductDto>): ProductListResponse {
    return new ProductListResponse(list);
  }

  async validateVendorIds(vendorIds: string[]): Promise<void> {
    const result: ListResultset<VendorDto> = await this.vendorsHandler.getAll(1, -1, [{ ids: vendorIds }]);
    const foundIds: string[] = result.list.map((vendor: VendorDto) => vendor.id);
    const missingIds: string[] = vendorIds.filter((id: string) => !foundIds.includes(id));

    if (missingIds.length > 0) {
      throw new VendorNotFoundError(missingIds.join(','));
    }
  }

  private async updateChildTables(productsId: string, request: ExtendedProductDto): Promise<void> {
    const codes: ProductCodeDto[] = request.codes.map(
      (item: any) =>
        new ProductCodeDto({
          ...item,
          productsId: request.id,
        }),
    );
    await this.codesHandler.createOrReplaceBulk(codes, { productsId });

    const inventories: ProductInventoryDto[] = request.inventories.map(
      (item: any) =>
        new ProductInventoryDto({
          ...item,
          productsId: request.id,
        }),
    );
    await this.inventoriesHandler.createOrReplaceBulk(inventories, { productsId });

    const vendorProducts: VendorProductDto[] = request.sellingVendorIds.map(
      (vendorsId: string) =>
        new VendorProductDto({
          vendorsId,
          productsId: request.id,
        }),
    );
    await this.vendorProductsHandler.createOrReplaceBulk(vendorProducts, { productsId });
  }
}
