import { ProductCodeRequest } from './product-code.request';
import { IsArray, IsDefined, IsNotEmpty, IsNumber, IsString, IsUUID, Min, ValidateIf } from 'class-validator';
import { ProductInventoryRequest } from './product-inventory-request';

export class ProductRequest {
  @IsString()
  @IsNotEmpty()
  title: string;
  @IsString()
  description: string;
  @IsString()
  tags: string;
  @IsString()
  @IsNotEmpty()
  category: string;
  @IsUUID()
  @ValidateIf((object: any, value: any) => value !== null)
  thumbnailId: string | null;
  @IsNumber()
  @Min(0.01)
  @ValidateIf((object: any, value: any) => value !== null)
  unitCost: number | null;
  @IsNumber()
  @Min(0.01)
  @ValidateIf((object: any, value: any) => value !== null)
  retailPrice: number | null;
  @IsDefined({ each: true })
  @IsArray()
  codes: ProductCodeRequest[];
  @IsDefined({ each: true })
  @IsArray()
  inventories: ProductInventoryRequest[];
  @IsUUID(undefined, { each: true })
  @IsArray()
  sellingVendorIds: string[];
}
