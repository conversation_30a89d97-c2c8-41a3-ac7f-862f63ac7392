import { Injectable, Logger } from '@nestjs/common';
import { Asset, ResourceFile, ResourceType, UploadsByResourceResponse } from '../uploads/entities/uploads.interfaces';
import { UploadsController } from '../uploads/controller/uploads.controller';

import { ErrorResponse } from '@mvc/http/responses/error-response';
import { keyBy, forEach } from 'lodash';
import { ListResultset } from '@mvc/data/list-resultset';
import { LotBiddingData, GetAccountMappingBySSOId200Response, ProfilesApi } from '@viewbid/ts-node-client';
import { AuctionLotDto } from '../auction-lots/dto/auction-lot.dto';
import { AuctionDto } from '../auction/dtos/auction.dto';
import { BidIncrementDto } from '../bid-increments/dtos/bid-increment.dto';
import { LotBiddingDataDto } from '../lots/dtos/lot-bidding-data.dto';
import { LotDto } from '../lots/dtos/lot.dto';
import { RedisClientType } from 'redis';
import { RedisClientNotObtainedError } from '@vb/redis/exceptions/redis-client-not-obtained-error';
import { AccountMappingNotFoundError } from '../auction-lots/exceptions/account-mapping-not-found.error';
import { RedisService } from '@vb/redis';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import * as axios from 'axios';
import { AuctionLotDetailedDto } from '../auction-lots/dto/auction-lot-detailed.dto';
import { maskString } from '@vb/utils/src/data/mask-string';
import { AuctionLotDetailedHighlightedDto } from '../auction-lots/dto/auction-lot-detailed-highlighted.dto';
import { AuctionLotListHighlightedDto } from '../auction-lots/dto/auction-lot-list-highlighted.dto';
import { ResourcesController } from '../resources/controller/resources.controller';
import { S3Service } from '../aws/s3/data/s3.service';
import { ResourcesResponse } from '../resources/http/responses/resources.response';

@Injectable()
export class SharedService {
  constructor(
    private uploadsController: UploadsController,
    private redisService: RedisService,
    private profilesApi: ProfilesApi,
    private bidIncrementFactory: BidIncrementFactory,
    private resourcesController: ResourcesController,
    private s3Service: S3Service,
  ) {}

  async handleImageCloning(
    clonedLotId: string,
    lotId: string,
    bucketName: string,
    logger: Logger,
  ): Promise<{ clonedLotImages: Array<{ [p: string]: any; id?: string }>; clonedLotDocs: Array<{ [p: string]: any; id?: string }> }> {
    const clonedLotImages: Array<{
      [p: string]: any;
      id?: string;
    }> = await this.getResourceURLById(clonedLotId, ResourceType.LOT_ITEM_IMAGES);
    const clonedLotDocs: Array<{
      [p: string]: any;
      id?: string;
    }> = await this.getResourceURLById(clonedLotId, ResourceType.LOT_ITEM_DOCUMENTS);

    const refImages: Array<{
      [p: string]: any;
      id?: string;
    }> = await this.getResourceURLById(lotId, ResourceType.LOT_ITEM_IMAGES);

    const refDocs: Array<{
      [p: string]: any;
      id?: string;
    }> = await this.getResourceURLById(lotId, ResourceType.LOT_ITEM_DOCUMENTS);

    if (clonedLotImages && clonedLotImages.length > 0) {
      await this.cloneLotResources(refImages, clonedLotImages, ResourceType.LOT_ITEM_IMAGES, bucketName, logger);
    }
    if (clonedLotDocs && clonedLotDocs.length > 0) {
      await this.cloneLotResources(refDocs, clonedLotDocs, ResourceType.LOT_ITEM_DOCUMENTS, bucketName, logger);
    }
    return { clonedLotImages, clonedLotDocs };
  }

  async getResourceURLById(id: string, resourceType: ResourceType, addIdToMap: boolean = false, thumbnails: boolean = false): Promise<Array<{ [key: string]: any; id?: string }>> {
    const resources: ErrorResponse | UploadsByResourceResponse[] = await this.uploadsController.findUploadsForResource(id, thumbnails);
    const resourceURLs: { [key: string]: any }[] = [];

    if (resources !== null) {
      const uploadsPerLot: any = keyBy(resources, 'type');
      forEach(uploadsPerLot[resourceType]?.files, (elem: { fileLink: string; type: ResourceType; filename: string; fileType: string; fileId: string; proxyId: string }) => {
        if (elem.type == resourceType) {
          resourceURLs.push({
            url: elem.fileLink,
            name: elem.filename,
            type: elem.fileType,
            id: elem.fileId,
            ...(addIdToMap && { id: elem.proxyId }),
          });
        }
      });
    }

    return resourceURLs;
  }

  private async cloneLotResources(
    refResources: Array<{ [key: string]: any; id?: string }>,
    clonedResources: Array<{ [key: string]: any; id?: string }>,
    resourceType: ResourceType,
    bucketName: string,
    logger: Logger,
  ): Promise<void> {
    const resourceResponse: ResourcesResponse = await this.resourcesController.findByResourceType(resourceType);
    const resourceLocation: string = resourceResponse.data.location;
    const clonedResourcesMap: Map<string, any> = new Map(clonedResources.map((resource: any) => [resource.name, resource]));

    const copyResources: (Promise<any> | null)[] = refResources.map((refResource: any) => {
      const clonedResource: any = clonedResourcesMap.get(refResource.name);

      if (clonedResource) {
        return this.s3Service.copyFileInS3(bucketName, `${resourceLocation}${refResource.id}`, bucketName, `${resourceLocation}${clonedResource.id}`).catch((error: Error) => {
          logger.error(`Error copying ${resourceType === ResourceType.LOT_ITEM_DOCUMENTS ? 'document' : 'image'} ${refResource.id} to ${clonedResource.id}: ${error.message}`);
        });
      }

      return null;
    });

    await Promise.all(copyResources.filter(Boolean)); // Filter out null values
  }

  async applyImages<T>(objects: ListResultset<T & { id: string }>): Promise<ListResultset<T>> {
    const objectIds: string = objects.list.map((object: T & { id: string }) => object.id).join(',');
    const resources: UploadsByResourceResponse[] | ErrorResponse = await this.uploadsController.findUploadsForResource(objectIds);

    if (!(resources instanceof ErrorResponse)) {
      const uploadsPerObject: Record<string, UploadsByResourceResponse> = keyBy(resources, 'proxyId');
      const objectsWithThumbnail: ListResultset<T> = {
        ...objects,
        list: objects.list.map((object: any) => {
          const upload: UploadsByResourceResponse = uploadsPerObject[object.id];
          const images: Array<Asset> =
            upload && upload.files && upload.files.length > 0
              ? [{ url: upload.files[0].fileLink, name: upload.files[0].filename, type: upload.files[0].fileType }]
              : [{ url: '', name: '', type: '' }];

          // Create a generic object type by spreading object and adding the thumbnail
          return {
            ...object,
            images,
          } as T;
        }),
      };

      return objectsWithThumbnail;
    }

    return objects;
  }

  async createLotsBiddingDataDto(lot: LotDto, lotBiddingDataList: LotBiddingData[], auctionLot: AuctionLotDto, auction: AuctionDto): Promise<LotBiddingDataDto> {
    let highestBidder: string = '';
    const highBidderId: string = lotBiddingDataList?.length === 0 ? '' : (lotBiddingDataList[0].biddingData?.highBidder ?? '');
    if (highBidderId) {
      const accountsMapping: GetAccountMappingBySSOId200Response = await this.getAccountMappingsById(highBidderId);
      highestBidder = maskString(
        accountsMapping.data.user ? `${accountsMapping.data.user.firstname} ${accountsMapping.data.user.lastname}` : (accountsMapping.data.organization?.name ?? ''),
      );
    }
    const currentHighBid: number = lotBiddingDataList?.length === 0 ? 0 : Number(lotBiddingDataList![0].currentPrice);
    const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, currentHighBid);
    const minimumBidAmount: number = this.calculateMinimumBidAmount(currentHighBid, bidIncrement.increment, lot, auction);
    const bidCount: number = lotBiddingDataList?.length === 0 ? 0 : Number(lotBiddingDataList![0].biddingData?.bidCount ?? 0);
    const numberOfWatchers: number = lotBiddingDataList?.length === 0 ? 0 : Number(lotBiddingDataList![0].biddingData?.numberOfWatchers ?? 0);
    const currentPrice: number = lotBiddingDataList?.length === 0 ? 0 : Number(lotBiddingDataList![0].biddingData?.currentPrice ?? 0);
    const isProxy: number = lotBiddingDataList?.length === 0 ? 0 : Number(lotBiddingDataList![0].biddingData?.isProxy ?? 0);

    return new LotBiddingDataDto(minimumBidAmount, bidCount, numberOfWatchers, currentPrice, highBidderId, highestBidder, bidIncrement.increment, isProxy);
  }

  private async getAccountMappingsById(accountMappingsId: string): Promise<GetAccountMappingBySSOId200Response> {
    const client: RedisClientType | null = this.redisService.getClient();
    if (!client) {
      throw new RedisClientNotObtainedError();
    }

    if (await client.exists(accountMappingsId)) {
      const accountsMapping: string | null = await client.GET(accountMappingsId);
      return JSON.parse(accountsMapping!);
    } else {
      const accountsMapping: axios.AxiosResponse<GetAccountMappingBySSOId200Response> = await this.profilesApi.getAccountMappingsById(accountMappingsId).catch(() => {
        throw new AccountMappingNotFoundError(accountMappingsId);
      });
      if (!accountsMapping || !accountsMapping.data) {
        throw new AccountMappingNotFoundError(accountMappingsId);
      }
      client.setEx(accountMappingsId, 60 * 60, JSON.stringify(accountsMapping.data));
      return accountsMapping.data;
    }
  }

  public calculateHighlightedMinimumBidAmount(currentHighBid: number, bidIncrement: number, auctionLot: AuctionLotListHighlightedDto): number {
    if (Number(currentHighBid) > 0) {
      // Current high bid exists
      return Number(currentHighBid) + Number(bidIncrement);
    } else if (auctionLot.isReserveMinBid && auctionLot.reserveAmount) {
      // No high bid but there's a reserve minimum bid
      return Math.max(Number(bidIncrement), Number(auctionLot.reserveAmount));
    } else if (auctionLot.startAmount > 0) {
      // No high bid but there's a startAmount
      return Math.max(Number(bidIncrement), Number(auctionLot.startAmount));
    }
    // Default case: no high bid and no reserve minimum bid
    return Number(bidIncrement);
  }

  public calculateMinimumBidAmount(currentHighBid: number, bidIncrement: number, lot: LotDto | undefined, auction: AuctionDto | undefined): number {
    if (Number(currentHighBid) > 0) {
      // Current high bid exists
      return Number(currentHighBid) + Number(bidIncrement);
    } else if (auction && auction.isReserveMinBid && lot && lot.reserveAmount && Number(lot.reserveAmount) > 0) {
      // No high bid but there's a reserve minimum bid
      return Math.max(Number(bidIncrement), Number(lot.reserveAmount));
    } else if (lot && lot.startAmount > 0) {
      // No high bid but there's a startAmount
      return Math.max(Number(bidIncrement), Number(lot.startAmount));
    }
    // Default case: no high bid and no reserve minimum bid
    return Number(bidIncrement);
  }

  public async getImagesAndDocumentByLotId(
    lotIds: string[],
    auctionLotDetailedList: ListResultset<AuctionLotDetailedDto | AuctionLotDetailedHighlightedDto>,
  ): Promise<ListResultset<AuctionLotDetailedDto | AuctionLotDetailedHighlightedDto>> {
    const resources: UploadsByResourceResponse[] | ErrorResponse = await this.uploadsController.findUploadsForResource(lotIds.toString());
    if (Array.isArray(resources) && resources.length > 0) {
      const uploadsPerAuction: Map<string, ResourceFile[]> = new Map();

      // Iterate over the resources and accumulate files by proxyId
      resources.forEach((resource: UploadsByResourceResponse) => {
        // Check if the resource.type already exists in the Map
        if (uploadsPerAuction.has(resource.proxyId)) {
          // Append files to the existing array
          uploadsPerAuction.get(resource.proxyId)!.push(...resource.files);
        } else {
          // Initialize a new array with the files for this proxyId
          uploadsPerAuction.set(resource.proxyId, [...resource.files]);
        }
      });

      auctionLotDetailedList.list.forEach((elem: AuctionLotDetailedDto) => {
        elem.images = [];
        elem.documents = [];

        const imgFiles: ResourceFile[] | undefined = uploadsPerAuction.get(elem.lotId);

        if (imgFiles) {
          imgFiles.forEach((file: ResourceFile) => {
            elem.images?.push({ url: file.fileLink, name: file.filename, type: file.type });
          });
        }

        const docuFiles: ResourceFile[] | undefined = uploadsPerAuction.get(elem.lotId);

        if (docuFiles) {
          docuFiles.forEach((file: ResourceFile) => {
            elem.documents?.push({ url: file.fileLink, name: file.filename, type: file.type });
          });
        }
      });
    }

    return auctionLotDetailedList;
  }
}
