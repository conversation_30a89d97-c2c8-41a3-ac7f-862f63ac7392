import { LoadEntitiesInterceptor } from '@mvc/interceptors/load-entities.interceptor';
import { Global, Module, forwardRef } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { prismaTransactional } from '@transactional/prisma';
import { AccountMappingCachingService } from '@vb/account-mappings';
import { StandaloneCachingService } from '@vb/caching/services/standalone-caching.service';
import { ConfigService } from '@vb/config/src/config.service';
import { IConsumerHandlers } from '@vb/nest/interfaces/iConsumer.handlers.interface';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { PdfModule, PdfService } from '@vb/pdf';
import { RedisService } from '@vb/redis';
import * as api from '@viewbid/ts-node-client';
import { axiosSecureInstance } from 'libs/ssl/src';
import { AuctionLotsModule } from '../auction-lots/auction-lots.module';
import { AuctionLotsController } from '../auction-lots/controller/auction-lots.controller';
import { AuctionLotsDbService } from '../auction-lots/data/auction-lots-db.service';
import { AuctionLotsHandler } from '../auction-lots/handlers/auction-lots.handler';
import { IsLastLotClosedListener } from '../auction-lots/listeners/is-last-lot-closed.listener';
import { AuctionModule } from '../auction/auction.module';
import { AuctionsDbService } from '../auction/data/auctions-db.service';
import { AuctionsHandler } from '../auction/handlers/auctions.handler';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import { LotItemUserNoteDbService } from '../lot-item-user-notes/data/lot-item-user-notes-db.service';
import { LotItemUserNotesHandler } from '../lot-item-user-notes/handler/lot-item-user-notes.handler';
import { DeleteLotItemUserNotesListener } from '../lot-item-user-notes/listeners/delete-lot-item-user-notes-listener';
import { LotsDbService } from '../lots/data/lots-db.service';
import { LotsHandler } from '../lots/handlers/lots.handler';
import { PrismaService } from '../prisma/prisma.service';
import { ResourcesModule } from '../resources/resources.module';
import { UploadsModule } from '../uploads/uploads.module';
import { SharedService } from './shared.service';
import { BidIncrementsHandler } from '../bid-increments/handler/bid-increments.handler';
import { BidIncrementsDbService } from '../bid-increments/data/bid-increments-db.service';
import { ApplicationPreferencesHandler } from '../application-preferences/handler/app-preferences.handler';
import { ApplicationPreferencesDbService } from '../application-preferences/data/app-preferences-db.service';
import { S3Service } from '../aws/s3/data/s3.service';
import { PurchaseOffersHandler } from '../purchase-offers/handlers/purchase-offers.handler';
import { PurchaseOffersDbService } from '../purchase-offers/data/purchase-offers-db.service';
import { CachePrimingService } from '@vb/caching/services/cache-priming.service';

@Global()
@Module({
  imports: [ScheduleModule.forRoot(), forwardRef(() => AuctionLotsModule), forwardRef(() => AuctionModule), PdfModule, ResourcesModule, forwardRef(() => UploadsModule)],
  providers: [
    ApplicationPreferencesDbService,
    ApplicationPreferencesHandler,
    AuctionsDbService,
    AuctionsHandler,
    AuctionLotsDbService,
    AuctionLotsHandler,
    BidIncrementFactory,
    BidIncrementsDbService,
    BidIncrementsHandler,
    LotsDbService,
    LotsHandler,
    S3Service,
    SharedService,
    {
      provide: PrismaService,
      useValue: new PrismaService().$extends(prismaTransactional),
    },
    {
      provide: api.BiddingApi,
      useFactory: async (configService: ConfigService): Promise<api.BiddingApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('BIDDING_SERVICE_URL');

        return new api.BiddingApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.ProfilesApi,
      useFactory: async (configService: ConfigService): Promise<api.ProfilesApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('USERS_SERVICE_URL');
        return new api.ProfilesApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.UsersApi,
      useFactory: async (configService: ConfigService): Promise<api.UsersApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('USERS_SERVICE_URL');

        return new api.UsersApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.SocketsApi,
      useFactory: async (configService: ConfigService): Promise<api.SocketsApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('SOCKETS_SERVICE_URL');

        return new api.SocketsApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: 'queueUrls',
      useFactory: async (configService: ConfigService): Promise<any> => {
        const notificationsUrl: string | undefined = await configService.getSetting('MESSAGES_QUEUE_URL');
        const endingSoonUrl: string | undefined = await configService.getSetting('LOT_ENDING_SOON_QUEUE_URL');
        const auctionPublishUrl: string | undefined = await configService.getSetting('AUCTION_PUBLISHED_QUEUE_URL');
        const auctionCloseUrl: string | undefined = await configService.getSetting('AUCTION_CLOSED_QUEUE_URL');
        const auctionOpenUrl: string | undefined = await configService.getSetting('AUCTION_OPEN_QUEUE_URL');
        const auctionEndingSoonUrl: string | undefined = await configService.getSetting('AUCTION_REMINDER_QUEUE_URL');
        const lotItemClosedUrl: string | undefined = await configService.getSetting('LOT_ITEM_CLOSED_QUEUE_URL');
        const deleteLotItemUserNotesUrl: string | undefined = await configService.getSetting('LOT_ITEM_USER_NOTES_TO_DELETE_QUEUE_URL');
        const isLastLotClosedUrl: string | undefined = await configService.getSetting('IS_LAST_LOT_CLOSED_QUEUE_URL');
        return {
          notifications: notificationsUrl,
          endingSoon: endingSoonUrl,
          auctionPublish: auctionPublishUrl,
          auctionClose: auctionCloseUrl,
          auctionOpen: auctionOpenUrl,
          auctionEndingSoon: auctionEndingSoonUrl,
          lotItemClosed: lotItemClosedUrl,
          deleteLotItemUserNotes: deleteLotItemUserNotesUrl,
          isLastLotClosed: isLastLotClosedUrl,
        };
      },
      inject: [ConfigService],
    },
    {
      provide: 'consumerHandlers',
      inject: [ConfigService, AuctionLotsController, AuctionLotsHandler, LotItemUserNotesHandler],
      useFactory: (
        configService: ConfigService,
        auctionLotsController: AuctionLotsController,
        auctionLotsHandler: AuctionLotsHandler,
        lotItemUserNotesHandler: LotItemUserNotesHandler,
      ): IConsumerHandlers => {
        const listeners: any = {
          deleteLotItemUserNotes: new DeleteLotItemUserNotesListener(configService, auctionLotsHandler, lotItemUserNotesHandler),
          isLastLotClosed: new IsLastLotClosedListener(auctionLotsController, configService),
        };
        Object.values(listeners).forEach((listener: any) => {
          if (listener.onModuleInit && typeof listener.onModuleInit === 'function') {
            listener.onModuleInit();
          }
        });
        return listeners;
      },
    },
    {
      provide: AccountMappingCachingService,
      useFactory: async (redisService: RedisService, profilesApi: api.ProfilesApi): Promise<AccountMappingCachingService> => {
        return new AccountMappingCachingService(redisService, profilesApi);
      },
      inject: [RedisService, api.ProfilesApi],
    },
    AwsSqsService,
    EventBridgeService,
    PdfService,
    PurchaseOffersDbService,
    PurchaseOffersHandler,
    SharedService,
    StandaloneCachingService,
    LotItemUserNotesHandler,
    LotItemUserNoteDbService,
    {
      provide: 'SERVICES_MAP',
      useFactory: (
        auctionLotsHandler: AuctionLotsHandler,
        auctionsHandler: AuctionsHandler,
        lotsHandler: LotsHandler,
        purchaseOffersHandler: PurchaseOffersHandler,
      ): Record<string, any> => {
        return {
          _auctionId: auctionsHandler,
          _auctionLotId: auctionLotsHandler,
          _lotId: lotsHandler,
          _offerId: purchaseOffersHandler,
        };
      },
      inject: [AuctionsHandler, AuctionLotsHandler, LotsHandler, PurchaseOffersHandler],
    },
    {
      provide: LoadEntitiesInterceptor,
      useFactory: (serviceMap: Record<string, any>): LoadEntitiesInterceptor => {
        return new LoadEntitiesInterceptor(serviceMap);
      },
      inject: ['SERVICES_MAP'],
    },
    CachePrimingService,
  ],
  exports: [
    PrismaService,
    api.BiddingApi,
    api.ProfilesApi,
    api.UsersApi,
    api.SocketsApi,
    AwsSqsService,
    EventBridgeService,
    SharedService,
    'queueUrls',
    'consumerHandlers',
    AccountMappingCachingService,
    'SERVICES_MAP',
    LoadEntitiesInterceptor,
  ],
})
export class SharedModule {}
