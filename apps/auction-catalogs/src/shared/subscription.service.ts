import { Injectable } from '@nestjs/common';
import { EventManager } from '@vb/nest/events/event.manager';
import { LoggingSubscriber } from '@vb/nest/events/logging.subscriber';
import { NotifyWatcherLotUpdatedSubscriber } from '../lots/subscribers/notify-watcher-lot-updated.subscriber';
import { EventType } from '@vb/nest/events/event-type';
import { Subscription, SubscriptionBase } from '@mvc/events/subscription-base';
import { UpdateCurrentBidSubscriber } from '../auction-lots/subscribers/update-current-bid.subscriber';
import { ExtendAuctionLotClosingTimeSubscriber } from '../auction-lots/subscribers/extend-auction-lot-closing-time.subscriber';
import { UpdateAuctionLotCountSubscriber } from '../auction/subscribers/update-auction-lot-count.subscriber';
import { SaveLotEditHistorySubscriber } from '../lots/subscribers/save-lot-edit-history.subscriber';
import { SetThumbnailSubscriber } from '../lots/subscribers/set-thumbnail.subscriber';
import { SetInvoiceStatusSubscriber } from '../invoicing/subscribers/set-invoice-status.subscriber';
import { NotifyAdminShippingRequestedSubscriber } from '../invoicing/subscribers/notify-admin-shipping-requested.subscriber';
import { NotifyBuyerShippingQuoteApprovedSubscriber } from '../invoicing/subscribers/notify-buyer-shipping-quote-approved.subscriber';
import { DeleteAllAuctionGroupsSubscriber } from '../auction/subscribers/delete-all-auction-groups.subscriber';
import { SendAllInvoicesSubscriber } from '../invoicing/subscribers/send-all-invoices.subscriber';
import { PaymentCompleteNotificationSubscriber } from '../invoicing/subscribers/payment-complete-notification.subscriber';
import { PaymentConfirmationNotificationSubscriber } from '../invoicing/subscribers/payment-confirmation-notification.subscriber';
import { UserNotFoundSubscriber } from '../invoicing/subscribers/user-not-found.subscriber';
import { NotifyAdminBuyerShippingQuoteApprovedSubscriber } from '../invoicing/subscribers/notify-admin-buyer-shipping-quote-approved.subscriber';
import { UpdateAuctionLotTimesSubscriber } from '../auction/subscribers/update-auction-lot-times.subscriber';
import { NotifyBuyerTimeslotCancelledSubscriber } from '../invoicing/subscribers/notify-buyer-timeslot-cancelled.subscriber';
import { CloseLotWithAcceptedOfferSubscriber } from '../purchase-offers/subscribers/close-lot-with-accepted-offer.subscriber';
import { MultiCacheInvalidationSubscriber } from './multi-cache-invalidation.subscriber';
import { NotifyInvoiceStatusUpdatedSubscriber } from '../invoicing/subscribers/notify-invoice-status-updated.subscriber';
import { NotifyBuyerPurchaseOfferCounteredSubscriber } from '../purchase-offers/subscribers/notify-buyer-purchase-offer-countered.subscriber';
import { NotifyBuyerPurchaseOfferRejectedSubscriber } from '../purchase-offers/subscribers/notify-buyer-purchase-offer-rejected.subscriber';
import { NotifyBuyerPurchaseOfferAcceptedSubscriber } from '../purchase-offers/subscribers/notify-buyer-purchase-offer-accepted.subscriber';
import { NotifyAdminPurchaseOfferAcceptedSubscriber } from '../purchase-offers/subscribers/notify-admin-purchase-offer-accepted.subscriber';
import { NotifyAdminPurchaseOfferCounteredSubscriber } from '../purchase-offers/subscribers/notify-admin-purchase-offer-countered.subscriber';
import { NotifyAdminPurchaseOfferRejectedSubscriber } from '../purchase-offers/subscribers/notify-admin-purchase-offer-rejected.subscriber';

@Injectable()
export class SubscriptionService extends SubscriptionBase {
  constructor(
    readonly eventManager: EventManager,
    private readonly notifyBuyerShippingQuoteApprovedSubscriber: NotifyBuyerShippingQuoteApprovedSubscriber,
    private readonly loggingSubscriber: LoggingSubscriber,
    private readonly notifyWatcherLotUpdatedSubscriber: NotifyWatcherLotUpdatedSubscriber,
    private readonly updateCurrentBidSubscriber: UpdateCurrentBidSubscriber,
    private readonly extendAuctionLotClosingTimeSubscriber: ExtendAuctionLotClosingTimeSubscriber,
    private readonly updateLotCountSubscriber: UpdateAuctionLotCountSubscriber,
    private readonly saveLotEditHistorySubscriber: SaveLotEditHistorySubscriber,
    private readonly setThumbnailSubscriber: SetThumbnailSubscriber,
    private readonly setInvoiceStatusSubscriber: SetInvoiceStatusSubscriber,
    private readonly notifyAdminShippingRequestedSubscriber: NotifyAdminShippingRequestedSubscriber,
    private readonly sendAllInvoicesSubscriber: SendAllInvoicesSubscriber,
    private readonly deleteAllAuctionGroupsSubscriber: DeleteAllAuctionGroupsSubscriber,
    private readonly updateAuctionLotTimesSubscriber: UpdateAuctionLotTimesSubscriber,
    private readonly paymentCompleteNotificationSubscriber: PaymentCompleteNotificationSubscriber,
    private readonly paymentConfirmationNotificationSubscriber: PaymentConfirmationNotificationSubscriber,
    private readonly userNotFoundSubscriber: UserNotFoundSubscriber,
    private readonly notifyAdminBuyerShippingQuoteApprovedSubscriber: NotifyAdminBuyerShippingQuoteApprovedSubscriber,
    private readonly notifyInvoiceStatusUpdatedSubscriber: NotifyInvoiceStatusUpdatedSubscriber,
    private readonly notifyBuyerTimeslotCancelledSubscriber: NotifyBuyerTimeslotCancelledSubscriber,
    private readonly closeLotWithAcceptedOfferSubscriber: CloseLotWithAcceptedOfferSubscriber,
    private readonly multiCacheInvalidationSubscriber: MultiCacheInvalidationSubscriber,
    private readonly notifyBuyerPurchaseOfferCounteredSubscriber: NotifyBuyerPurchaseOfferCounteredSubscriber,
    private readonly notifyBuyerPurchaseOfferAcceptedSubscriber: NotifyBuyerPurchaseOfferAcceptedSubscriber,
    private readonly notifyBuyerPurchaseOfferRejectedSubscriber: NotifyBuyerPurchaseOfferRejectedSubscriber,
    private readonly notifyAdminPurchaseOfferAcceptedSubscriber: NotifyAdminPurchaseOfferAcceptedSubscriber,
    private readonly notifyAdminPurchaseOfferCounteredSubscriber: NotifyAdminPurchaseOfferCounteredSubscriber,
    private readonly notifyAdminPurchaseOfferRejectedSubscriber: NotifyAdminPurchaseOfferRejectedSubscriber,
  ) {
    super(eventManager);
  }

  private subscriptions: Subscription[] = [
    { type: EventType.CreateLotSuccess, subscriber: this.loggingSubscriber },

    { type: EventType.CreateLotFailed, subscriber: this.loggingSubscriber },

    { type: EventType.UpdateLotPreprocess, subscriber: this.saveLotEditHistorySubscriber },
    { type: EventType.UpdateLotSuccess, subscriber: this.loggingSubscriber },
    { type: EventType.UpdateLotSuccess, subscriber: this.notifyWatcherLotUpdatedSubscriber },
    { type: EventType.UpdateLotSuccess, subscriber: this.updateLotCountSubscriber },

    { type: EventType.UpdateLotFailed, subscriber: this.loggingSubscriber },
    //commenting out for now as we ensure the new call in the proxybidservice works directly through the auctionsApi
    // { type: EventType.CreateBidSuccess, subscriber: this.updateCurrentBidSubscriber },
    { type: EventType.ExtendAuctionLotClosingTimeSuccess, subscriber: this.extendAuctionLotClosingTimeSubscriber },

    { type: EventType.CreateAdminLotSuccess, subscriber: this.updateLotCountSubscriber },
    { type: EventType.DeleteAdminLotSuccess, subscriber: this.updateLotCountSubscriber },
    { type: EventType.UpdateAdminLotSuccess, subscriber: this.updateLotCountSubscriber },
    { type: EventType.UploadImageSuccess, subscriber: this.setThumbnailSubscriber },

    { type: EventType.CreateInvoiceSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.CreateInvoiceSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceRequestShippingSuccess, subscriber: this.notifyAdminShippingRequestedSubscriber },
    { type: EventType.InvoiceRequestShippingSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceRequestShippingSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.CreateInvoiceSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.SplitInvoiceSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceShippingQuoteCreatedSuccess, subscriber: this.notifyAdminShippingRequestedSubscriber },
    { type: EventType.InvoiceShippingQuoteCreatedSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceShippingQuoteAdminApprovedSuccess, subscriber: this.notifyBuyerShippingQuoteApprovedSubscriber },
    { type: EventType.InvoiceShippingQuoteCreatedSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceShippingQuoteAdminApprovedSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceShippingQuoteAdminApprovedSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceShippingQuoteBuyerApproveSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceShippingQuoteBuyerApproveSuccess, subscriber: this.notifyAdminBuyerShippingQuoteApprovedSubscriber },
    { type: EventType.InvoiceShippingQuoteBuyerApproveSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceAssembledSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceAssembledSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceAssemblyValidatedSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceAssemblyValidatedSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoiceCompleteSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceCompleteSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.UpdateAuctionSuccess, subscriber: this.deleteAllAuctionGroupsSubscriber },
    { type: EventType.UpdateAuctionSuccess, subscriber: this.updateAuctionLotTimesSubscriber },
    { type: EventType.InvoicePaymentCompleteSuccess, subscriber: this.paymentCompleteNotificationSubscriber },
    { type: EventType.InvoiceApproveSuccess, subscriber: this.sendAllInvoicesSubscriber },
    { type: EventType.InvoiceApproveSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.InvoiceApproveSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.CreateTimeslotSuccess, subscriber: this.setInvoiceStatusSubscriber },
    { type: EventType.CreateTimeslotSuccess, subscriber: this.notifyInvoiceStatusUpdatedSubscriber },
    { type: EventType.InvoicePaymentMadeSuccess, subscriber: this.paymentConfirmationNotificationSubscriber },
    { type: EventType.InvoicesGenerateInvoicesError, subscriber: this.userNotFoundSubscriber },
    { type: EventType.TimeSlotsDeletedSuccess, subscriber: this.notifyBuyerTimeslotCancelledSubscriber },
    { type: EventType.PurchaseOffersAcceptedSuccess, subscriber: this.closeLotWithAcceptedOfferSubscriber },
    { type: EventType.PurchaseOffersAcceptedSuccess, subscriber: this.notifyAdminPurchaseOfferAcceptedSubscriber },
    { type: EventType.PurchaseOffersCounteredSuccess, subscriber: this.notifyAdminPurchaseOfferCounteredSubscriber },
    { type: EventType.PurchaseOffersRejectedSuccess, subscriber: this.notifyAdminPurchaseOfferRejectedSubscriber },
    { type: EventType.PurchaseOffersAdminAcceptedSuccess, subscriber: this.closeLotWithAcceptedOfferSubscriber },
    { type: EventType.PurchaseOffersAdminAcceptedSuccess, subscriber: this.notifyBuyerPurchaseOfferAcceptedSubscriber },
    { type: EventType.PurchaseOffersAdminCounterSuccess, subscriber: this.notifyBuyerPurchaseOfferCounteredSubscriber },
    { type: EventType.PurchaseOffersAdminRejectedSuccess, subscriber: this.notifyBuyerPurchaseOfferRejectedSubscriber },
    { type: EventType.UpdateAuctionSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.UpdateAuctionStatusSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.BidIncrementsSaveSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.UpdateAuctionLotSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.UpdateAdminLotSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.DeleteLotSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.CloneAuctionLotSuccess, subscriber: this.multiCacheInvalidationSubscriber },
  ];

  getSubscriptions(): Subscription[] {
    return this.subscriptions;
  }
}
