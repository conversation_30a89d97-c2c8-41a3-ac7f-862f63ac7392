import { Injectable } from '@nestjs/common';
import { BaseInvalidateCacheSubscriber } from '@vb/caching/subscribers/base-invalidate-cache-subscriber';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { RedisService } from '@vb/redis';
import { CachingServiceNotFoundError } from '@vb/caching/exceptions/caching-service-not-found.error';
import { AuctionsCachingService } from '../services/auctions-caching.service';
import { BidIncrementsCachingService } from '../../../bidding/src/bids/services/bid-increments-caching.service';
import { AuctionLotsCachingService } from '../services/auction-lots-caching.service';
import { LotsCachingService } from '../services/lots-caching.service';

@Injectable()
export class MultiCacheInvalidationSubscriber extends BaseInvalidateCacheSubscriber {
  constructor(protected readonly redisService: RedisService) {
    super(redisService);
  }

  protected async deleteCacheByEventType(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    // Determine the cache key prefix based on the event type
    if (eventType.startsWith('AUCTIONS_STATUS')) {
      await AuctionsCachingService.deleteCache(this.redisService, eventPayload.items.data.id);
      await AuctionsCachingService.deleteKeysByPrefix(this.redisService, 'GET-/auctions*');
      return;
    } else if (eventType.startsWith('AUCTIONS')) {
      this.logger.log('deleting auction cache in auction catalogs');
      await AuctionsCachingService.deleteCache(this.redisService, eventPayload.items.data.id, true);
      await AuctionsCachingService.deleteKeysByPrefix(this.redisService, 'GET-/auctions*');
      return;
    } else if (eventType.startsWith('BIDINCREMENTS')) {
      this.logger.log('deleting bid increments cache in auction catalogs');
      await BidIncrementsCachingService.deleteCache(this.redisService, eventPayload.items.data[0].auctionId);
      return;
    } else if (eventType.startsWith('AUCTIONLOTS')) {
      this.logger.log('deleting auction lots cache in auction catalogs');
      await AuctionLotsCachingService.deleteCache(this.redisService, eventPayload.items.id, true);
      await AuctionLotsCachingService.deleteKeysByPrefix(this.redisService, 'GET-/auction-lots*');
      return;
    } else if (eventType === EventType.UpdateAdminLotSuccess) {
      this.logger.log('deleting lot cache in auction catalogs for UpdateAdminLotSuccess');
      await LotsCachingService.deleteCache(this.redisService, eventPayload.items.id);
      await AuctionsCachingService.deleteCache(this.redisService, eventPayload.items.auctionId, true);
      await AuctionLotsCachingService.deleteCache(this.redisService, eventPayload.items.auctionLotId, true);
      return;
    } else if (eventType === EventType.DeleteLotSuccess) {
      this.logger.log('deleting lot cache in auction catalogs for DeleteLotSuccess');
      await LotsCachingService.deleteCache(this.redisService, eventPayload.id);
      await AuctionsCachingService.deleteCache(this.redisService, eventPayload.otherId, true);
      return;
    }
    throw new CachingServiceNotFoundError(eventType);
  }
}
