import { Injectable, Logger } from '@nestjs/common';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { RedisService } from '@vb/redis';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { AuctionsHandler } from '../handlers/auctions.handler';
import { StandalonePubSubController } from '@vb/redis/controllers/standalone-pub-sub.controller';
import { AuctionDto } from '../dtos/auction.dto';

@Injectable()
export class UpdateAuctionLotCountSubscriber extends StandalonePubSubController implements SubscriberInterface {
  private readonly logger = new Logger(UpdateAuctionLotCountSubscriber.name);

  constructor(
    private readonly auctionsHandler: AuctionsHandler,
    redisService: RedisService,
  ) {
    super(redisService, 'auctions');
  }

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const details: any = this.getDetails(eventPayload);

    this.auctionsHandler
      .updateNumberOfLots(details.userId, details.id)
      .then(async () => {
        await this.notify('success', 'update', { data: new AuctionDto({ id: details.auctionsId }) });
      })
      .catch((error: Error) => {
        this.logger.error(error);
      });
  }

  private getDetails(eventPayload: EventPayload): any {
    if (eventPayload.action === 'create') {
      return { userId: eventPayload.userId, id: eventPayload.items.id, auctionsId: eventPayload.items.data.auctionsId };
    } else if (eventPayload.action === 'update') {
      return { userId: eventPayload.userId, id: eventPayload.items.id, auctionId: eventPayload.items.auctionId, auctionLotId: eventPayload.items.auctionLotId };
    }

    return { userId: eventPayload.userId, id: eventPayload.id, otherId: eventPayload.otherId };
  }
}
