import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { GroupsHandler } from '../../groups/handlers/groups.handler';
import { AuctionDto } from '../dtos/auction.dto';

@Injectable()
export class DeleteAllAuctionGroupsSubscriber implements SubscriberInterface {
  private logger = new Logger(DeleteAllAuctionGroupsSubscriber.name);

  constructor(private groupsHandler: GroupsHandler) {}
  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      //this may throw an error if items.data does not exist - that means it's not
      //an event we need to handle in this subscriber
      const auction: AuctionDto = new AuctionDto(eventPayload.items.data);
      await this.groupsHandler.deleteAllGroupsInAuction(auction.id);
    } catch (error) {
      this.logger.error('Error occurred while deleting all groups in Auction: ' + error.message);
    }
    //  }
  }
}
