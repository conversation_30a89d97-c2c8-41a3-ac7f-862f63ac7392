import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { AuctionDto } from '../dtos/auction.dto';
import { AuctionStatus } from '@viewbid/ts-node-client';
import { AuctionUtilService, UpdateEventSchedulerAndLotTimes } from '../services/auction-util.service';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { AuctionsHandler } from '../handlers/auctions.handler';
import { AdminAuctionListDto } from '../dtos/admin-auction-list.dto';
import { ConfigService } from '@vb/config/src/config.service';

@Injectable()
export class UpdateAuctionLotTimesSubscriber implements SubscriberInterface, OnModuleInit {
  private logger = new Logger(UpdateAuctionLotTimesSubscriber.name);

  private auctionPublishedARN: string;
  private auctionClosedARN: string;
  private isLastLotClosedARN: string;
  private delayInSeconds: number;
  private checkLastClosingLotDelay: number;

  async onModuleInit(): Promise<void> {
    this.auctionPublishedARN = await this.configService.getSetting('AUCTION_PUBLISHED_QUEUE_ARN');
    this.auctionClosedARN = await this.configService.getSetting('AUCTION_CLOSED_QUEUE_ARN');
    this.isLastLotClosedARN = await this.configService.getSetting('IS_LAST_LOT_CLOSED_QUEUE_ARN');
    this.delayInSeconds = Number(await this.configService.getSetting('LOT_CLOSING_TIME_DELAY_IN_SECONDS'));
    this.checkLastClosingLotDelay = Number(await this.configService.getSetting('CHECK_LAST_CLOSING_LOT_DELAY'));
  }

  constructor(
    private auctionUtilService: AuctionUtilService,
    private auctionHandler: AuctionsHandler,
    private auctionLotsHandler: AuctionLotsHandler,
    private configService: ConfigService,
  ) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      const receivedAuction: AuctionDto = new AuctionDto(eventPayload.items.data);

      const auctions: AdminAuctionListDto[] = (await this.auctionHandler.getAllAdmin(1, -1, [{ id: { in: [receivedAuction.id] } }])).list;
      if (auctions.length > 0) {
        const auction: AdminAuctionListDto = auctions[0];
        await this.auctionHandler.addTaxesToAuctions([auction]);
        if (auction.status == AuctionStatus.Public || auction.status == AuctionStatus.Open) {
          this.logger.log(`Updating lot item times for auction: ${receivedAuction.id}`);
          const lastClosingAuctionLot: AuctionLotDto = await this.auctionLotsHandler.checkLastItemClosed(auction.id);

          const updateEvent: UpdateEventSchedulerAndLotTimes = {
            auctionsDto: auction,
            lastClosingAuctionLot: lastClosingAuctionLot,
            auctionPublishedARN: this.auctionPublishedARN,
            auctionClosedARN: this.auctionClosedARN,
            isLastLotClosedARN: this.isLastLotClosedARN,
            checkLastClosingLotDelay: this.checkLastClosingLotDelay,
            delayInSeconds: this.delayInSeconds,
          };

          await this.auctionUtilService.setEventSchedulers(updateEvent);
          this.logger.log(`Finished updating lot item times for auction: ${auction.id}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error updating lot item times for auction: ${eventPayload.items.data}`, error);
    }
  }
}
