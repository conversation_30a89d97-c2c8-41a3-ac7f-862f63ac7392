import { BaseDbService } from '@mvc/data/base-db.service';
import { AuctionTaxesDto } from '../dtos/auction-taxes.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class AuctionTaxesDbService extends BaseDbService<Prisma.AuctionTaxesDelegate, AuctionTaxesDto> {
  constructor(prisma: PrismaService) {
    super(prisma.auctionTaxes, false, false, false);
  }

  mapToDto(model: any): AuctionTaxesDto {
    return new AuctionTaxesDto(model);
  }

  throw404(id: string): Error {
    throw new Error();
  }

  async getByAuctionsId(auctionsId: string): Promise<AuctionTaxesDto> {
    const row: any = await this.table.findFirst({
      where: {
        auctionsId: auctionsId,
      },
    });

    if (!row) this.throw404(auctionsId);

    return this.mapToDto(row);
  }
}
