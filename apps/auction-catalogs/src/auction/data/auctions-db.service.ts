import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AuctionDto } from '../dtos/auction.dto';
import { AuctionNotFoundError } from '../exceptions/auction-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';
import { AuctionStatus } from '@viewbid/ts-node-client';
import { BaseDbService } from '@mvc/data/base-db.service';
import { Auctions, Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from 'prisma/prisma-client/runtime/library';
import { MappedAuctionNotFoundError } from '../exceptions/mapped-auction-not-found.error';
import { AuctionSummaryDto } from '../dtos/auction-summary.dto';

@Injectable()
export class AuctionsDbService extends BaseDbService<Prisma.AuctionsDelegate<DefaultArgs>, AuctionDto> {
  constructor(private readonly prismaService: PrismaService) {
    super(prismaService.auctions);
  }

  createFilter(params: any[]): any {
    if (params.length === 0) {
      return {};
    }

    return params.reduce((acc: Record<string, any>, param: any) => {
      Object.keys(param).forEach((key: string) => {
        const value: any = param[key];

        if (key === 'AND' || key === 'OR') {
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key] = acc[key].concat(value);
        } else if (key.includes('.')) {
          const keys: string[] = key.split('.');
          let current: Record<string, any> = acc;

          keys.forEach((k: string, index: number) => {
            if (index === keys.length - 1) {
              current[k] = value;
            } else {
              current[k] = current[k] || {};
              current = current[k];
            }
          });
        } else {
          acc[key] = value;
        }
      });

      return acc;
    }, {});
  }

  protected getExcludedFields(): (keyof AuctionDto)[] {
    return ['images', 'defaultTaxPercentage'];
  }

  public async publishAuctions(auctionId: string[], updatedBy: string): Promise<boolean> {
    const response: { count: number } = await this.table.updateMany({
      where: {
        id: {
          in: auctionId,
        },
      },
      data: {
        status: AuctionStatus.Public,
        updatedBy: updatedBy,
        updatedOn: new Date().toISOString(),
        isViewable: 1,
      },
    });
    return response != null && response.count > 0;
  }

  public async updateNumberOfLots(userId: string, id: string): Promise<number> {
    // Step 1: Count the active AuctionLots for the auctionId
    const result: any = await this.prismaService.$queryRaw<Array<{ count: number; auctionId: string }>>(
      Prisma.sql`
        SELECT "auctionId", COUNT(*) as count
        FROM "AuctionLots"
        WHERE "auctionId" = (
          SELECT "auctionId"
          FROM "AuctionLots"
          WHERE ("lotId" = ${id} OR "id" = ${id})
          LIMIT 1
        ) AND "deletedOn" IS NULL
        GROUP BY "auctionId";
      `,
    );

    if (result.length === 0) {
      throw new MappedAuctionNotFoundError(id);
    }

    const { count, auctionId } = result[0];

    // Step 2: Update the Auctions.numLots field
    await this.table.update({
      where: { id: auctionId },
      data: { numLots: Number(count) },
    });

    return Number(count);
  }

  async updateAuctionLotsToDateTime(auctionId: string, toDateTime: Date, userId: string): Promise<AuctionDto> {
    const item: any = await this.table.update({
      where: {
        id: auctionId,
      },
      data: {
        toDatetime: toDateTime,
        updatedBy: userId,
      },
    });
    if (!item) {
      this.throw404(auctionId);
    }
    return this.mapToDto(item);
  }

  public async updateAuctionStatus(auctionIds: string[], status: AuctionStatus, isViewable: boolean): Promise<boolean> {
    const response: { count: number } = await this.table.updateMany({
      where: {
        id: {
          in: auctionIds,
        },
      },
      data: {
        status: status,
        isViewable: Number(isViewable),
      },
    });

    return response != null && response.count > 0;
  }

  public async findAllByStatusAndActiveInTime(status: AuctionStatus, isViewable: boolean, currentTime: string, thresholdTime: string): Promise<ListResultset<AuctionDto>> {
    const items: any = await this.table.findMany({
      where: {
        AND: [
          {
            isViewable: Number(isViewable),
            fromDatetime: {
              lte: thresholdTime,
            },
            toDatetime: {
              gt: currentTime,
            },
          },
          {
            deletedOn: null,
          },
        ],
      },
    });

    return new ListResultset(items.map(this.mapToDto), 0, 0, 0, 0);
  }

  public async findClosingAuctions(endTime: string): Promise<ListResultset<AuctionDto>> {
    const items: any = await this.table.findMany({
      where: {
        AND: [
          {
            status: AuctionStatus.Open,
            isViewable: 1,
            toDatetime: {
              lte: endTime,
            },
          },
          {
            deletedOn: null,
          },
        ],
      },
    });

    return new ListResultset(items.map(this.mapToDto), 0, 0, 0, 0);
  }

  mapToDto(model: any): AuctionDto {
    return new AuctionDto(model);
  }

  throw404(id: string): Error {
    throw new AuctionNotFoundError(id);
  }

  async getMostRecentAuctionByLotId(id: string): Promise<AuctionDto> {
    const result: any = await this.prismaService.auctionLots.findFirst({
      where: {
        lotId: id,
        deletedOn: null,
      },
      orderBy: {
        createdOn: 'desc',
      },
    });
    if (!result) {
      throw new AuctionNotFoundError(`lotId: $id`);
    }

    return this.mapToDto(result);
  }

  //returning all expired auctions in case we wanna show expired auctions to users in future
  async getPastOrExpiredAuctions(auctionIds: Array<string>): Promise<AuctionDto[]> {
    const result: Auctions[] = await this.prismaService.$queryRaw<Auctions[]>`SELECT *
      FROM public."Auctions"
      WHERE ("fromDatetime" < NOW() OR "toDatetime" < NOW())
      AND id IN (${Prisma.join(auctionIds)});`;

    if (!result) return [];

    return result.map((auction: Auctions) => this.mapToDto(auction));
  }

  async getSummary(auction: AuctionDto): Promise<Partial<AuctionSummaryDto>> {
    const totalOfBidsWithReserveMet: number = Number(
      await this.prismaService.$queryRaw<any[]>`
      SELECT SUM(al."currentBid") as "result"
      FROM public."AuctionLots" al
      INNER JOIN public."Lots" l ON al."lotId" = l.id
      WHERE al."auctionId" = ${auction.id}
      AND l."reserveAmount" <= al."currentBid"
      AND al."deletedOn" IS NULL
    `.then((rows: any[]) => rows[0].result),
    );

    const lotsBelowReserveMetrics: any = await this.prismaService.$queryRaw<any[]>`
      SELECT COUNT(al.id) as "count", SUM(al."currentBid") as "sum"
      FROM public."AuctionLots" al
      INNER JOIN public."Lots" l ON al."lotId" = l.id
      WHERE al."auctionId" = ${auction.id}
      AND l."reserveAmount" > al."currentBid"
      AND al."deletedOn" IS NULL
    `.then((rows: any[]) => rows[0]);

    const auctionLotMetrics: any = await this.prismaService.$queryRaw`
      SELECT COUNT(DISTINCT al."currentHighBidderId") as "numWinningBidders", COUNT(al.id) as "numLotsWithBids"
      FROM public."AuctionLots" al
      WHERE al."auctionId" = ${auction.id}
      AND al."currentHighBidderId" <> ''
      AND al."deletedOn" IS NULL
    `.then((rows: any[]) => ({
      numWinningBidders: Number(rows[0].numWinningBidders),
      numLotsWithBids: Number(rows[0].numLotsWithBids),
    }));

    return {
      auctionsId: auction.id,
      numLots: auction.numLots,
      totalOfBidsWithReserveMet: totalOfBidsWithReserveMet,
      numLotsWithBids: auctionLotMetrics.numLotsWithBids,
      numWinningBidders: auctionLotMetrics.numWinningBidders,
      numLotsBelowReserve: Number(lotsBelowReserveMetrics.count),
      totalOfLotsBelowReserve: Number(lotsBelowReserveMetrics.sum),
    };
  }

  async addTaxesToAuctions(auctions: any[]): Promise<void> {
    if (!auctions || auctions.length === 0) {
      return;
    }

    const auctionIds: string[] = auctions.map((item: any) => item.id);

    const taxTypeInfo: any[] = await this.prismaService.$queryRaw`
      SELECT a_t."auctionsId", tt.percent
      FROM public."AuctionTaxes" a_t
      JOIN public."TaxTypes" tt ON tt.id = a_t."taxTypesId"
      WHERE a_t."auctionsId" IN (${Prisma.join(auctionIds)});
    `;
    const taxMap: { [key: string]: number } = taxTypeInfo.reduce((prev: any, cur: any) => {
      prev[cur.auctionsId] = cur.percent;
      return prev;
    }, {});

    auctions.forEach((auction: any) => {
      auction.defaultTaxPercentage = taxMap[auction.id];
    });
  }
}
