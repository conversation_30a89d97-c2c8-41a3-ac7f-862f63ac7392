export class AuctionSummaryDto {
  auctionsId: string;
  numLots: number;
  numLotsWithBids: number;
  numLotsBelowReserve: number;
  totalOfBidsWithReserveMet: number;
  totalOfLotsBelowReserve: number;
  numWinningBidders: number;
  numBidders: number;
  numBids: number;
  maxProxyBidsTotal: number;

  constructor(data: any) {
    this.auctionsId = data.auctionsId;
    this.numLots = data.numLots;
    this.numLotsWithBids = data.numLotsWithBids;
    this.numLotsBelowReserve = data.numLotsBelowReserve;
    this.totalOfBidsWithReserveMet = data.totalOfBidsWithReserveMet;
    this.totalOfLotsBelowReserve = data.totalOfLotsBelowReserve;
    this.numWinningBidders = data.numWinningBidders;
    this.numBidders = data.numBidders;
    this.numBids = data.numBids;
    this.maxProxyBidsTotal = data.maxProxyBidsTotal;
  }
}
