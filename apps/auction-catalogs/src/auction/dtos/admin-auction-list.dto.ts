import { Asset } from '../../uploads/entities/uploads.interfaces';

export class AdminAuctionListDto {
  id: string;
  buyerPremium: number;
  numLots: number;
  fromDatetime: string;
  toDatetime: string;
  name: string;
  isViewable: boolean;
  status: string;
  thumbnail: string;
  seller: string;
  pickupLocation: string;
  paymentOptions: Array<string>;
  isShipping: boolean;
  images: Asset[];
  auctionType: string;
  code: string;
  defaultTaxPercentage: number;

  constructor(row: any) {
    this.id = row.id;
    this.defaultTaxPercentage = row.defaultTaxPercentage;
    this.buyerPremium = row.buyerPremium;
    this.numLots = row.numLots;
    this.fromDatetime = row.fromDatetime;
    this.toDatetime = row.toDatetime;
    this.name = row.name;
    this.isViewable = Boolean(row.isViewable);
    this.status = row.status;
    this.thumbnail = row.thumbnail;
    this.seller = row.seller;
    this.paymentOptions = row.paymentOptions;
    this.isShipping = row.isShipping;
    this.images = row.images ?? [];
    this.auctionType = row.auctionType ?? '';
    this.code = row.code;
  }
}
