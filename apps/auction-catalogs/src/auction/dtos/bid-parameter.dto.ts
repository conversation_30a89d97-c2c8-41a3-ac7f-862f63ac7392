import { ParametersDto } from '@vb-utils/messaging/dtos/parameters.dto';

export class BidParameterDto implements ParametersDto {
  readonly firstname: string;
  readonly lastname: string;
  readonly lotItemName: string;
  readonly lotItemLink: string;
  readonly currentBid: number;
  readonly auctionLotsId: string;

  constructor(firstname: string, lastname: string, lotItemName: string, lotItemLink: string, currentBid: number, auctionLotsId: string) {
    this.firstname = firstname;
    this.lastname = lastname;
    this.lotItemName = lotItemName;
    this.lotItemLink = lotItemLink;
    this.currentBid = currentBid;
    this.auctionLotsId = auctionLotsId;
  }
}
