import { AuctionStatus, PaymentType } from '@viewbid/ts-node-client';

export class PublicAuctionDto {
  id: string;
  name: string;
  description: string;
  fromDatetime: string;
  toDatetime: string;
  isViewable: boolean;
  status: AuctionStatus;
  numLots: number;
  isShip: boolean;
  isProxyBid: boolean;
  isReservable: boolean;
  isBuyNow: boolean;
  isMakeOffer: boolean;
  isQuickBid: boolean;
  isInlineBidding: boolean;
  isLargeBidConfirm: boolean;
  isReserveMinBid: boolean;
  isCharity: boolean;
  code: string;
  buyerPremium: number;
  paymentTypes: Array<PaymentType>;
  thumbnailId?: string;
  pickupLocation: string;
  termsConditions: string;
  isDisplayFinalPrice: boolean;
  isDisplaySoldItems: boolean;
  auctionType: string;
  tags: string;
  invoiceTemplatesId: string;
  featureDisplayOrder: number;
  invoiceText: string;
  isSchedulingRequired: boolean;
  isPreviewLots: boolean;
  snipingExtension: number;
  images?: { [key: string]: any; id?: string | undefined }[];
  documents?: { [key: string]: any; id?: string | undefined }[];
  bidSnipeWindow: number;
  defaultTaxPercentage: number;

  constructor(row: any, images?: { [key: string]: any; id?: string | undefined }[], documents?: { [key: string]: any; id?: string | undefined }[]) {
    this.id = row.id;
    this.name = row.name;
    this.defaultTaxPercentage = row.defaultTaxPercentage;
    this.description = row.description;
    this.fromDatetime = row.fromDatetime;
    this.toDatetime = row.toDatetime;
    this.isViewable = row.isViewable;
    this.status = row.status;
    this.numLots = row.numLots;
    this.isShip = row.isShip;
    this.isProxyBid = Boolean(row.isProxyBid);
    this.isReservable = Boolean(row.isReservable);
    this.isBuyNow = Boolean(row.isBuyNow);
    this.isMakeOffer = Boolean(row.isMakeOffer);
    this.isQuickBid = Boolean(row.isQuickBid);
    this.isInlineBidding = Boolean(row.isInlineBidding);
    this.isLargeBidConfirm = Boolean(row.isLargeBidConfirm);
    this.isReserveMinBid = Boolean(row.isReserveMinBid);
    this.isCharity = Boolean(row.isCharity);
    this.code = row.code;
    this.buyerPremium = row.buyerPremium;
    this.paymentTypes = row.paymentTypes;
    this.thumbnailId = row.thumbnailId;
    this.pickupLocation = row.pickupLocation;
    this.termsConditions = row.termsConditions;
    this.isDisplayFinalPrice = Boolean(row.isDisplayFinalPrice);
    this.isDisplaySoldItems = Boolean(row.isDisplaySoldItems);
    this.auctionType = row.auctionType;
    this.featureDisplayOrder = row.featureDisplayOrder;
    this.tags = row.tags;
    this.invoiceTemplatesId = row.invoiceTemplatesId;
    this.invoiceText = row.invoiceText;
    this.isSchedulingRequired = Boolean(row.isSchedulingRequired);
    this.isPreviewLots = Boolean(row.isPreviewLots);
    this.snipingExtension = row.snipingExtension;
    this.images = images;
    this.documents = documents;
    this.bidSnipeWindow = row.bidSnipeWindow;
  }
}
