import { AuctionLotFlattenedDto } from '../../auction-lots/dto/auction-lot-flattened.dto';
import { Asset } from '../../uploads/entities/uploads.interfaces';

export class PublicAuctionListWithAuctionLotsDto {
  id: string;
  buyerPremium: number;
  numLots: number;
  fromDatetime: string;
  toDatetime: string;
  name: string;
  isViewable: boolean;
  status: string;
  auctionType: string;
  images: Asset[];
  lots: AuctionLotFlattenedDto[];
  defaultTaxPercentage: number;

  constructor(row: any) {
    this.id = row.id;
    this.defaultTaxPercentage = row.defaultTaxPercentage;
    this.buyerPremium = row.buyerPremium;
    this.numLots = row.numLots;
    this.fromDatetime = row.fromDatetime;
    this.toDatetime = row.toDatetime;
    this.name = row.name;
    this.isViewable = Boolean(row.isViewable);
    this.status = row.status;
    this.auctionType = row.auctionType;
    this.images = row.images;
    this.lots = row.lots;
  }
}
