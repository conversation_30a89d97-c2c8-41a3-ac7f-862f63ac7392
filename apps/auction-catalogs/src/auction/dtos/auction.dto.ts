import { AuctionStatus, PaymentType } from '@viewbid/ts-node-client';
import { Asset } from '../../uploads/entities/uploads.interfaces';

export class AuctionDto {
  id: string;
  name: string;
  description: string;
  fromDatetime: string;
  toDatetime: string;
  isViewable: number;
  status: AuctionStatus;
  numLots: number;
  isShip: number;
  isProxyBid: number;
  isReservable: number;
  isBuyNow: number;
  isMakeOffer: number;
  isQuickBid: number;
  isInlineBidding: number;
  isLargeBidConfirm: number;
  isReserveMinBid: number;
  isCharity: number;
  tags: string;
  code: string;
  invoiceTemplatesId: string;
  buyerPremium: number;
  featureDisplayOrder: number;
  paymentTypes: Array<PaymentType>;
  thumbnailId?: string;
  invoiceInformation: string;
  pickupLocation: string;
  termsConditions: string;
  invoiceText: string;
  isDisplayFinalPrice: boolean;
  isDisplaySoldItems: boolean;
  auctionType: string;
  images: Asset[];
  isSchedulingRequired: boolean;
  isPreviewLots: boolean;
  invoicesApproved: boolean;
  isLiveAuction: boolean;
  snipingExtension: number;
  bidSnipeWindow: number;
  defaultTaxPercentage: number;

  constructor(item: any) {
    this.id = item.id;
    this.name = item.name;
    this.defaultTaxPercentage = item.defaultTaxPercentage;
    this.description = item.description;
    this.fromDatetime = item.fromDatetime;
    this.toDatetime = item.toDatetime;
    this.isViewable = Number(item.isViewable);
    this.status = item.status;
    this.numLots = item.numLots;
    this.isShip = Number(item.isShip);
    this.isProxyBid = Number(item.isProxyBid);
    this.isReservable = Number(item.isReservable);
    this.isBuyNow = Number(item.isBuyNow);
    this.isMakeOffer = Number(item.isMakeOffer);
    this.isQuickBid = Number(item.isQuickBid);
    this.isInlineBidding = Number(item.isInlineBidding);
    this.isLargeBidConfirm = Number(item.isLargeBidConfirm);
    this.isReserveMinBid = Number(item.isReserveMinBid);
    this.isCharity = Number(item.isCharity);
    this.tags = item.tags;
    this.code = item.code;
    this.invoiceTemplatesId = item.invoiceTemplatesId;
    this.buyerPremium = item.buyerPremium;
    this.featureDisplayOrder = item.featureDisplayOrder;
    this.paymentTypes = item.paymentTypes;
    this.thumbnailId = item.thumbnailId;
    this.pickupLocation = item.pickupLocation;
    this.termsConditions = item.termsConditions;
    this.invoiceText = item.invoiceText;
    this.invoiceInformation = item.invoiceInformation;
    this.isDisplayFinalPrice = item.isDisplayFinalPrice;
    this.isDisplaySoldItems = item.isDisplaySoldItems;
    this.auctionType = item.auctionType;
    this.images = item.images;
    this.isSchedulingRequired = item.isSchedulingRequired;
    this.isPreviewLots = item.isPreviewLots;
    this.invoicesApproved = item.invoicesApproved;
    this.isLiveAuction = item.isLiveAuction;
    this.snipingExtension = item.snipingExtension;
    this.bidSnipeWindow = item.bidSnipeWindow;
  }
}
