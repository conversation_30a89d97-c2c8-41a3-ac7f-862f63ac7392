import { Lo<PERSON>, Module, forwardRef } from '@nestjs/common';
import { AuctionsDbService } from './data/auctions-db.service';
import { UploadsModule } from '../uploads/uploads.module';
import { RedisService } from '@vb/redis';
import { ApplicationPreferencesModule } from '../application-preferences/app-preferences.module';
import { AuctionsHandler } from './handlers/auctions.handler';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import { BidIncrementsHandler } from '../bid-increments/handler/bid-increments.handler';
import { BidIncrementsDbService } from '../bid-increments/data/bid-increments-db.service';
import { SharedService } from '../shared/shared.service';
import { SearchServiceModule } from 'libs/search-service/src';
import { AuctionsController as AdminAuctionsController } from './controller/admin/auctions.controller';
import { AuctionsController as PublicAuctionsController } from './controller/public/auctions.controller';
import { AuctionLotsModule } from '../auction-lots/auction-lots.module';
import { DBTriggersListener } from '../lot-item-user-notes/listeners/db-triggers-listener';
import { CachingModule } from '@vb/caching';
import { AuctionUtilService } from './services/auction-util.service';
import { ResourcesModule } from '../resources/resources.module';
import { S3Module } from '../aws/s3/s3.module';
import { AuctionTaxesDbService } from './data/auction-taxes-db.service';
import { AuctionTaxesHandler } from './handlers/auction-taxes.handler';
import { LotsModule } from '../lots/lots.module';

@Module({
  controllers: [AdminAuctionsController, PublicAuctionsController],
  providers: [
    AuctionsDbService,
    AuctionsHandler,
    RedisService,
    Logger,
    AdminAuctionsController,
    BidIncrementFactory,
    BidIncrementsHandler,
    BidIncrementsDbService,
    PublicAuctionsController,
    SharedService,
    DBTriggersListener,
    AuctionUtilService,
    AuctionTaxesDbService,
    AuctionTaxesHandler,
  ],
  imports: [
    forwardRef(() => UploadsModule),
    ApplicationPreferencesModule,
    SearchServiceModule,
    forwardRef(() => AuctionLotsModule),
    CachingModule,
    ResourcesModule,
    S3Module,
    forwardRef(() => LotsModule),
  ],
  exports: [AdminAuctionsController, AuctionsHandler, PublicAuctionsController, AuctionUtilService, AuctionTaxesHandler],
})
export class AuctionModule {}
