import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { AuctionDto } from '../dtos/auction.dto';
import { AuctionsDbService } from '../data/auctions-db.service';
import { AuctionStatus } from '@viewbid/ts-node-client';
import { ListResultset } from '@mvc/data/list-resultset';
import { PublicAuctionListDto } from '../dtos/public-auction-list.dto';
import { AdminAuctionListDto } from '../dtos/admin-auction-list.dto';
import { AuctionSummaryDto } from '../dtos/auction-summary.dto';

@Injectable()
export class AuctionsHandler extends BaseHandler<AuctionsDbService, AuctionDto> {
  constructor(dbService: AuctionsDbService) {
    super(dbService);
  }

  async addTaxesToAuctions(auctions: any[]): Promise<void> {
    await this.dbService.addTaxesToAuctions(auctions);
  }

  async publishAuctions(auctionIds: string[], updatedBy: string): Promise<boolean> {
    return await this.dbService.publishAuctions(auctionIds, updatedBy);
  }
  public async updateNumberOfLots(userId: string, auctionId: string): Promise<number> {
    return await this.dbService.updateNumberOfLots(userId, auctionId);
  }

  async updateAuctionLotsToDateTime(auctionId: string, toDateTime: Date, userId: string): Promise<AuctionDto> {
    return await this.dbService.updateAuctionLotsToDateTime(auctionId, toDateTime, userId);
  }

  public async updateAuctionStatus(auctionIds: string[], status: AuctionStatus, isViewable: boolean): Promise<boolean> {
    return await this.dbService.updateAuctionStatus(auctionIds, status, isViewable);
  }

  public async findAllByStatusAndActiveInTime(status: AuctionStatus, isViewable: boolean, currentTime: string, thresholdTime: string): Promise<ListResultset<AuctionDto>> {
    return await this.dbService.findAllByStatusAndActiveInTime(status, isViewable, currentTime, thresholdTime);
  }

  public async findClosingAuctions(endTime: string): Promise<ListResultset<AuctionDto>> {
    return await this.dbService.findClosingAuctions(endTime);
  }

  async getAllPublic(page: number, size: number, params: Record<string, any>[]): Promise<ListResultset<PublicAuctionListDto>> {
    return await this.dbService.getAllCustom(page, size, params, PublicAuctionListDto);
  }

  async getAllAdmin(page: number, size: number, params: Record<string, any>[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<AdminAuctionListDto>> {
    return await this.dbService.getAllCustom(page, size, params, AdminAuctionListDto, orderBy);
  }
  async getMostRecentAuctionByLotId(id: string): Promise<AuctionDto> {
    return await this.dbService.getMostRecentAuctionByLotId(id);
  }

  async getAuctionCodesInUse(): Promise<string[]> {
    const auctionsResults: ListResultset<AuctionDto> = await this.dbService.getAllCustom(1, -1, [{ status: { notIn: ['COMPLETE', 'CLOSED'] }, deletedOn: null }], AuctionDto);

    return await Promise.all(auctionsResults.list.map(async (auction: AuctionDto) => auction.code));
  }

  async getPastOrExpiredAuctions(auctionIds: Array<string>): Promise<AuctionDto[]> {
    return await this.dbService.getPastOrExpiredAuctions(auctionIds);
  }

  async getSummary(auction: AuctionDto): Promise<Partial<AuctionSummaryDto>> {
    return await this.dbService.getSummary(auction);
  }
}
