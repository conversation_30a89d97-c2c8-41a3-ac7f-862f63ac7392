import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { AuctionTaxesDbService } from '../data/auction-taxes-db.service';
import { AuctionTaxesDto } from '../dtos/auction-taxes.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AuctionTaxesHandler extends BaseHandler<AuctionTaxesDbService, AuctionTaxesDto> {
  constructor(dbService: AuctionTaxesDbService) {
    super(dbService);
  }

  async getByAuctionsId(auctionsId: string): Promise<AuctionTaxesDto> {
    return this.dbService.getByAuctionsId(auctionsId);
  }
}
