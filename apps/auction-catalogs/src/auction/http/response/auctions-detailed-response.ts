import { Response } from '@mvc/http/responses/response';
import { PublicAuctionDto } from '../../dtos/public-auction.dto';
import { AuctionDto } from '../../dtos/auction.dto';

export class AuctionsDetailedResponse extends Response {
  constructor(data: AuctionDto | PublicAuctionDto, images?: { [key: string]: any; id?: string | undefined }[], thumbNail?: string, details?: object) {
    super(data, 'Auction', data.id, details);
  }
}
