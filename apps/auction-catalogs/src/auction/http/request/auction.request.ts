import { isPercentage } from '@vb/nest/validators/isPercentage';
import { isValidDateFormat } from '@vb/nest/validators/isValidDateFormat';
import { AuctionStatus, PaymentType } from '@viewbid/ts-node-client';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsBoolean, IsEnum, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, Length, Min } from 'class-validator';

export class AuctionRequest {
  @IsNotEmpty()
  @IsString()
  @Length(1, 300)
  name: string;

  @IsInt()
  @Min(1)
  taxTypesId: number;

  @IsString()
  description: string;

  @IsNotEmpty()
  @IsString()
  @isValidDateFormat({
    message: (): string => 'Invalid Date Format',
  })
  fromDatetime: string;

  @IsNotEmpty()
  @IsString()
  @isValidDateFormat({
    message: (): string => 'Invalid Date Format',
  })
  toDatetime: string;

  @IsNotEmpty()
  @IsEnum(AuctionStatus)
  status: AuctionStatus;

  @IsNotEmpty()
  @IsBoolean()
  isShip: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isProxyBid: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isReservable: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isBuyNow: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isMakeOffer: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isQuickBid: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isInlineBidding: boolean;

  @IsNotEmpty()
  @IsBoolean()
  isLargeBidConfirm: boolean;

  @IsString()
  @Length(0, 250)
  tags: string;

  @IsOptional()
  @IsString()
  @Length(0, 2)
  code: string;

  @IsNotEmpty()
  @IsString()
  @Length(1, 36)
  invoiceTemplatesId: string;

  @IsNotEmpty()
  @IsNumber()
  @isPercentage({
    message: (): string => 'Invalid percentage value.',
  })
  buyerPremium: number;

  @IsNotEmpty()
  @IsNumber()
  featureDisplayOrder: number;

  @IsNotEmpty()
  @IsArray()
  @IsEnum(PaymentType, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(4)
  paymentTypes: PaymentType[];
  thumbnailId: string;
  pickupLocation: string;
  termsConditions: string;
  invoiceText: string;
  isViewable: boolean;
  numLots: number;
  isReserveMinBid: boolean;
  isCharity: boolean;
  isDisplayFinalPrice: boolean;
  isDisplaySoldItems: boolean;
  isSchedulingRequired: boolean;
  isPreviewLots: boolean;

  @IsString()
  @Length(0, 50)
  auctionType: string;

  @IsOptional()
  @IsNumber()
  bidSnipeWindow: number;
}
