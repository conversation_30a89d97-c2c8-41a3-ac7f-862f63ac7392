import { AuctionStatus } from '@viewbid/ts-node-client';
import { IsArray, IsBoolean, IsDefined, IsEnum, IsNotEmpty } from 'class-validator';

export class AuctionsUpdateRequest {
  @IsDefined()
  @IsNotEmpty()
  @IsArray()
  auctionIds: Array<string>;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(AuctionStatus)
  status: AuctionStatus;
  @IsDefined()
  @IsNotEmpty()
  @IsBoolean()
  isViewable: boolean;
}
