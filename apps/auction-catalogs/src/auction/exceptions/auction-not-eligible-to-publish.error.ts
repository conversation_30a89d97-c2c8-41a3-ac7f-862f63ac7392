import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';

export class AuctionNotEligibleToPublish extends ViewbidError {
  constructor() {
    super(
      'Auctions are not eligible to be published please check the auctions start or end date times.',
      Constants.AUCTION_NOT_ELIGIBLE_TO_PUBLISH,
      '/auctions#auctionNotEligibleToPublish',
    );
  }
}
