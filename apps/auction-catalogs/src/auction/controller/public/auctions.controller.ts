import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseInterceptors } from '@nestjs/common';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';
import { CacheInterceptor } from '@vb/caching/interceptors/cache.interceptor';
import { ConfigService } from '@vb/config/src/config.service';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { GetParent } from '@vb/nest/interfaces/getParent.interface';
import { AuctionStatus } from '@viewbid/ts-node-client';
import { ResourceType } from 'apps/auction-catalogs/src/uploads/entities/uploads.interfaces';
import { AuctionLotFlattenedDto } from '../../../auction-lots/dto/auction-lot-flattened.dto';
import { AuctionLotsHandler } from '../../../auction-lots/handlers/auction-lots.handler';
import { InvalidRequestError } from '../../../invoicing/exceptions/invalid-request.error';
import { SharedService } from '../../../shared/shared.service';
import { AuctionDto } from '../../dtos/auction.dto';
import { PublicAuctionListWithAuctionLotsDto } from '../../dtos/public-auction-list-with-auction-lots.dto';
import { PublicAuctionListDto } from '../../dtos/public-auction-list.dto';
import { PublicAuctionDto } from '../../dtos/public-auction.dto';
import { NoOpenAuctionsFoundError } from '../../exceptions/no-open-auctions-found.error';
import { AuctionsHandler } from '../../handlers/auctions.handler';
import { AuctionRequest } from '../../http/request/auction.request';
import { AuctionResponse } from '../../http/response/auction-response';
import { AuctionsDetailedResponse } from '../../http/response/auctions-detailed-response';
import { AuctionsListResponse } from '../../http/response/auctions-list-response';
import { AuctionsListWithAuctionLotsResponse } from '../../http/response/auctions-list-with-auction-lots.response';
import { Cacheable } from '@vb/caching/decorators/cacheable.decorator';

@UseInterceptors(CacheInterceptor)
@Controller('auctions')
export class AuctionsController extends BaseController<AuctionsHandler, AuctionRequest, AuctionDto, AuctionResponse, AuctionsListResponse> implements GetParent<string> {
  public closedAuctionWindowDays: number;

  constructor(
    handler: AuctionsHandler,
    private sharedService: SharedService,
    private auctionLotsHandler: AuctionLotsHandler,
    private configService: ConfigService,
  ) {
    super(handler);
  }

  async onModuleInit(): Promise<void> {
    this.closedAuctionWindowDays = Number(await this.configService.getSetting('CLOSED_AUCTION_WINDOW_DAYS', '3'));
  }

  async getParent(userId: string, req: any): Promise<string> {
    userId;
    req;
    //TODO Fill with auction houses based on request or userId. For now attach everything to the root.
    return '00000000-0000-0000-0000-000000000000';
  }

  createDtoFromRequest(request: AuctionRequest): AuctionDto {
    return new AuctionDto(request);
  }

  createResponseFromDto(dto: AuctionDto): AuctionResponse {
    return new AuctionResponse(dto, {});
  }
  createResponseList(list: ListResultset<AuctionDto>): AuctionsListResponse {
    return new AuctionsListResponse(list);
  }

  /**
   * this is the endpoint for the homescreen so we'll customize the filter to
   * display what is showcased for the home page
   *
   * @param page
   * @param size
   */
  @Cacheable(3600)
  @Anonymous()
  @Get('getAuctions')
  async getAuctions(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('filter') filter: string[],
    @Query('category') category: string,
  ): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    // Define the allowed statuses
    const allowedStatuses: Array<string> = [AuctionStatus.Open, AuctionStatus.Public, AuctionStatus.Closed];
    const filterArray: Array<string> = Array.isArray(filter) ? filter : filter ? [filter] : [];

    // Handle default case: if no filter is provided or it's empty, default to OPEN and PUBLIC
    const normalizedFilter: Array<string> = filter?.length
      ? filterArray.map((status: string) => status.toUpperCase()) // Normalize input to upper case for comparison
      : [AuctionStatus.Open, AuctionStatus.Public]; // Default to OPEN and PUBLIC

    // Filter out any invalid statuses from the input
    const validStatuses: Array<AuctionStatus> = normalizedFilter
      .filter((status: string) => allowedStatuses.includes(status as AuctionStatus)) // Ensure it's an allowed status
      .map((status: string) => status as AuctionStatus); // Type assertion to AuctionStatus

    // Validate: Ensure at least one valid status exists
    if (validStatuses.length === 0) {
      throw new ErrorResponse(new InvalidRequestError(), 'getAuctions');
    }

    // Pass the validated statuses to getAuctionsForBidder
    return this.getAuctionsForBidder(page, size, validStatuses, category);
  }

  /**
   * This is the endpoint for the home screen so we'll customize the filter to
   * display what is showcased for the home page.
   */
  @Cacheable(3600)
  @Anonymous()
  @Get('getFeaturedAuctions')
  async getFeaturedAuctions(@Query('page') page: number, @Query('size') size: number): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    // Pass parameters directly for PUBLIC and OPEN
    return this.getAuctionsForBidder(page, size, [AuctionStatus.Open, AuctionStatus.Public]);
  }

  async getAuctionsForBidder(page: number, size: number, statusList: Array<AuctionStatus>, category?: string): Promise<AuctionsListWithAuctionLotsResponse | ErrorResponse> {
    try {
      const today: string = new Date().toISOString();
      const params: Record<string, any>[] = [];

      // Build the base conditions for each status
      const conditions: Record<string, any>[] = [];

      if (statusList.includes(AuctionStatus.Closed)) {
        conditions.push({
          OR: [
            {
              status: AuctionStatus.Closed,
              isDisplaySoldItems: true,
              toDatetime: {
                lte: today,
                gte: new Date(new Date().setDate(new Date().getDate() - this.closedAuctionWindowDays)).toISOString(),
              },
            },
            {
              status: AuctionStatus.Closed,
              auctionLots: {
                some: {
                  toDatetime: {
                    gt: today,
                  },
                },
              },
            },
          ],
        });
      }

      if (statusList.includes(AuctionStatus.Public)) {
        conditions.push({
          status: AuctionStatus.Public,
          toDatetime: { gte: today },
        });
      }

      if (statusList.includes(AuctionStatus.Open)) {
        conditions.push({
          status: AuctionStatus.Open,
          fromDatetime: { lte: today },
        });
      }

      // Only add params if we have conditions
      if (conditions.length > 0) {
        params.push({
          AND: [{ OR: conditions }, { deletedOn: null }, category != undefined && category.length > 0 ? { auctionType: category } : {}],
        });
      }

      const auctions: ListResultset<PublicAuctionListDto> = await this.handler.getAllPublic(page, size, params);

      if (auctions.list.length <= 0) {
        throw new NoOpenAuctionsFoundError();
      }

      const auctionIds: Array<string> = auctions.list.map((dto: PublicAuctionListDto) => dto.id);
      const auctionLotItems: ListResultset<AuctionLotFlattenedDto> = await this.auctionLotsHandler.getLotItemsGroupedByAuction(auctionIds);

      const auctionMap: Map<string, PublicAuctionListWithAuctionLotsDto> = auctions.list.reduce<Map<string, PublicAuctionListWithAuctionLotsDto>>(
        (map: Map<string, PublicAuctionListWithAuctionLotsDto>, auction: PublicAuctionListDto) => {
          map.set(auction.id, { ...auction, lots: [] });
          return map;
        },
        new Map<string, PublicAuctionListWithAuctionLotsDto>(),
      );

      auctionLotItems.list.forEach((lotItem: AuctionLotFlattenedDto) => {
        const auction: PublicAuctionListWithAuctionLotsDto | undefined = auctionMap.get(lotItem.auctionId);
        if (auction) {
          auction.lots?.push(lotItem);
        }
      });

      const mappedAuctions: Array<PublicAuctionListWithAuctionLotsDto> = Array.from(auctionMap.values());
      const listResultset: ListResultset<PublicAuctionListWithAuctionLotsDto> = new ListResultset<PublicAuctionListWithAuctionLotsDto>(
        mappedAuctions,
        page,
        auctions.pageSize,
        auctions.totalCount,
        auctions.totalPages,
      );

      await this.handler.addTaxesToAuctions(listResultset.list);

      return new AuctionsListWithAuctionLotsResponse(listResultset);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAuctionsForBidder': " + error);

      return new ErrorResponse(error, 'getAuctionsForBidder');
    }
  }

  /**
   * this is used in the list auctions area of the bidding site
   *
   * @param page
   * @param size
   */
  @Anonymous()
  @Get('getAllAuctions')
  async getAllAuctions(@Query('page') page: number, @Query('size') size: number, @Query('filter') filter: string[]): Promise<AuctionsListResponse | ErrorResponse> {
    try {
      let params: Record<string, any>[] = [];
      if (filter) {
        params = this.createFilterFromStatusArray(filter);
      } else {
        params.push({
          status: AuctionStatus.Review,
        });
      }
      let auctions: ListResultset<PublicAuctionListDto> = await this.handler.getAllPublic(page, size, params);
      auctions = await this.sharedService.applyImages<PublicAuctionListDto>(auctions);
      await this.handler.addTaxesToAuctions(auctions.list);

      return new AuctionsListResponse(auctions);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAllAuctions': " + error);

      return new ErrorResponse(error, 'getAllLots');
    }
  }

  /**
   * Get an auction by id
   *
   * @param id
   * @returns  AuctionGetResponse | ErrorResponse
   */
  @Anonymous()
  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<AuctionsDetailedResponse | ErrorResponse> {
    req;
    try {
      const auction: AuctionDto = await this.handler.get(id);
      const images: { [key: string]: any; id?: string | undefined }[] = await this.sharedService.getResourceURLById(id, ResourceType.AUCTION_IMAGES);

      if (!auction.termsConditions) {
        auction.termsConditions = await this.configService.getSetting('DEFAULT_TERMS_AND_CONDITIONS');
      }
      if (!auction.pickupLocation) {
        auction.pickupLocation = await this.configService.getSetting('DEFAULT_PICKUP_LOCATION');
      }
      if (!auction.invoiceText) {
        auction.invoiceText = await this.configService.getSetting('DEFAULT_INVOICE_TEXT');
      }

      await this.handler.addTaxesToAuctions([auction]);

      return new AuctionsDetailedResponse(new PublicAuctionDto(auction, images));
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionById');
    }
  }

  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    req;
    id;
    return this.blockedEndpoint();
  }

  @Post('/')
  async create(@Body() body: AuctionRequest, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    req;
    body;
    return this.blockedEndpoint();
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<AuctionsListResponse | ErrorResponse> {
    page;
    size;
    query;
    req;
    return this.blockedEndpoint();
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: AuctionRequest, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    req;
    return this.blockedEndpoint();
  }

  @Patch('/restore/:id')
  async restore(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    req;
    id;
    return this.blockedEndpoint();
  }

  createFilterFromStatusArray(filter: string | string[]): Record<string, any>[] {
    const statusList: string[] = Array.isArray(filter) ? filter : [filter];
    const today: string = new Date().toISOString();
    const params: Record<string, any>[] = [];
    const conditions: Record<string, any>[] = [];

    if (statusList.includes(AuctionStatus.Closed)) {
      conditions.push({
        OR: [
          {
            status: AuctionStatus.Closed,
            isDisplaySoldItems: true,
            toDatetime: {
              lte: today,
              gte: new Date(new Date().setDate(new Date().getDate() - this.closedAuctionWindowDays)).toISOString(),
            },
          },
          {
            status: AuctionStatus.Closed,
            auctionLots: {
              some: {
                toDatetime: {
                  gt: today,
                },
              },
            },
          },
        ],
      });
    }

    if (statusList.includes(AuctionStatus.Public)) {
      conditions.push({
        status: AuctionStatus.Public,
        fromDatetime: { lte: today },
        toDatetime: { gte: today },
      });
    }

    if (statusList.includes(AuctionStatus.Open)) {
      conditions.push({
        status: AuctionStatus.Open,
        fromDatetime: { lte: today },
      });
    }

    // Only add params if we have conditions
    if (conditions.length > 0) {
      params.push({
        AND: [{ OR: conditions }, { deletedOn: null }],
      });
    }

    return params;
  }
}
