import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { SuccessResponse } from '@mvc/http/responses/success.response';
import { Body, Controller, Get, HttpCode, OnModuleInit, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { ConfigService } from '@vb/config/src/config.service';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { GetParent } from '@vb/nest/interfaces/getParent.interface';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { AuctionLotDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot.dto';
import { AuctionLotsHandler } from 'apps/auction-catalogs/src/auction-lots/handlers/auction-lots.handler';
import { ResourceType } from 'apps/auction-catalogs/src/uploads/entities/uploads.interfaces';
import * as _ from 'lodash';
import { SharedService } from '../../../shared/shared.service';
import { AdminAuctionListDto } from '../../dtos/admin-auction-list.dto';
import { AdminAuctionDto } from '../../dtos/admin-auction.dto';
import { AuctionDto } from '../../dtos/auction.dto';
import { AuctionNotEligibleToPublish } from '../../exceptions/auction-not-eligible-to-publish.error';
import { AuctionsHandler } from '../../handlers/auctions.handler';
import { AuctionRequest } from '../../http/request/auction.request';
import { AuctionsPublishRequest } from '../../http/request/auctions-publish-request';
import { AuctionsUpdateRequest } from '../../http/request/auctions-update-request';
import { AuctionResponse } from '../../http/response/auction-response';
import { AuctionsDetailedResponse } from '../../http/response/auctions-detailed-response';
import { AuctionsListResponse } from '../../http/response/auctions-list-response';
import { AuctionUpdateResponse } from '../../http/response/autions-update-response';
import { AuctionUtilService, UpdateEventSchedulerAndLotTimes } from '../../services/auction-util.service';
import * as moment from 'moment';
import { InvalidAuctionStartDate } from '../../exceptions/invalid-auction-start-date';
import { InvalidAuctionEndDate } from '../../exceptions/invalid-auction-end-date';
import { AuctionStatus, BiddingApi } from '@viewbid/ts-node-client';
import { AuctionsCachingService } from '../../../services/auctions-caching.service';
import { AuctionCodesResponse } from '../../http/response/auction-codes.response';
import { AuctionSummaryDto } from '../../dtos/auction-summary.dto';
import { AuctionSummaryResponse } from '../../http/response/auction-summary.response';
import { AuctionTaxesHandler } from '../../handlers/auction-taxes.handler';
import { AuctionTaxesDto } from '../../dtos/auction-taxes.dto';
import { TaxTypeHandler } from 'apps/auction-catalogs/src/invoicing/handlers/tax-type.handler';
import { TaxTypeDto } from 'apps/auction-catalogs/src/invoicing/dtos/tax-type.dto';

@Controller('admin/auctions')
@UseGuards(IsAdmin)
export class AuctionsController
  extends PubSubController<AuctionsHandler, AuctionRequest, AuctionDto, AuctionResponse, AuctionsListResponse>
  implements OnModuleInit, GetParent<string>
{
  private auctionPublishedARN: string;
  private auctionClosedARN: string;
  private isLastLotClosedARN: string;
  private delayInSeconds: number;
  private checkLastClosingLotDelay: number;

  constructor(
    handler: AuctionsHandler,
    private readonly auctionTaxesHandler: AuctionTaxesHandler,
    private readonly sharedService: SharedService,
    private readonly eventBridgeService: EventBridgeService,
    private readonly configService: ConfigService,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    redisService: RedisService,
    private readonly auctionUtilService: AuctionUtilService,
    private readonly biddingApi: BiddingApi,
  ) {
    super(handler, redisService, 'auctions');
  }

  async onModuleInit(): Promise<void> {
    this.auctionPublishedARN = await this.configService.getSetting('AUCTION_PUBLISHED_QUEUE_ARN');
    this.auctionClosedARN = await this.configService.getSetting('AUCTION_CLOSED_QUEUE_ARN');
    this.isLastLotClosedARN = await this.configService.getSetting('IS_LAST_LOT_CLOSED_QUEUE_ARN');
    this.delayInSeconds = Number(await this.configService.getSetting('LOT_CLOSING_TIME_DELAY_IN_SECONDS'));
    this.checkLastClosingLotDelay = Number(await this.configService.getSetting('CHECK_LAST_CLOSING_LOT_DELAY'));
  }

  createDtoFromRequest(request: AuctionRequest): AuctionDto {
    return new AuctionDto(request);
  }

  createResponseFromDto(dto: AuctionDto): AuctionResponse {
    return new AuctionResponse(dto);
  }

  createResponseList(list: ListResultset<AuctionDto>): AuctionsListResponse {
    return new AuctionsListResponse(list);
  }

  @Post('/')
  async create(@Body() body: AuctionRequest, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    try {
      this.validateAuctionDates(body.fromDatetime, body.toDatetime);
      const auctionDto: AuctionDto = await this.handler.create(req.accountMappingsId, this.createDtoFromRequest(body));

      await this.auctionTaxesHandler.create(
        req.accountMappingsId,
        new AuctionTaxesDto({
          auctionsId: auctionDto.id,
          taxTypesId: body.taxTypesId,
        }),
      );
      await this.handler.addTaxesToAuctions([auctionDto]);

      await this.notify('success', 'create', auctionDto);

      return this.createResponseFromDto(auctionDto);
    } catch (error) {
      this.logger.error("Error caught in POST 'createAuction': " + error);
      return new ErrorResponse(error, 'createAuction');
    }
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: AuctionRequest, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    return this.executeAndNotify(
      'update',
      async () => {
        this.validateAuctionDates(body.fromDatetime, body.toDatetime, body.status);
        const userId: string = req.accountMappingsId;
        const dto: AuctionDto = this.createDtoFromRequest(body);
        const item: AuctionDto = await this.handler.update(userId, dto, id);

        // TO DO: Merging it for demo now but get it reviewed from Allan
        try {
          const auctionTaxes: AuctionTaxesDto = await this.auctionTaxesHandler.getByAuctionsId(id);
          auctionTaxes.taxTypesId = body.taxTypesId;
          await this.auctionTaxesHandler.update(userId, auctionTaxes, auctionTaxes.id);
        } catch (error) {
          const auctionTaxes: AuctionTaxesDto = new AuctionTaxesDto({
            auctionsId: id,
            taxTypesId: body.taxTypesId,
          });
          await this.auctionTaxesHandler.create(userId, auctionTaxes);
        }

        await Promise.all([this.auctionLotsHandler.updateAuctionLotsToDateTimes(item.id, this.delayInSeconds), this.handler.addTaxesToAuctions([item])]);

        return this.createResponseFromDto(item);
      },
      body,
    );
  }

  @Get('getAllAuctions')
  async getAllAuctions(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Query('status') status: string | undefined,
    @Query('filter') filter: string[] | undefined,
  ): Promise<AuctionsListResponse | ErrorResponse> {
    try {
      const params: Record<string, any>[] = this.buildFilterParams(query, status, filter);
      const auctions: ListResultset<AdminAuctionListDto> = await this.handler.getAllAdmin(page, size, params, { toDatetime: 'desc' });
      await this.handler.addTaxesToAuctions(auctions.list);
      return new AuctionsListResponse(auctions);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAllAuctions': " + error);

      return new ErrorResponse(error, 'getAllAuctions');
    }
  }

  /**
   * deal with all the possible varieties of params passed
   *
   * example usage:
   * /admin/auctions/getAllAuctions?page=1&size=15&filter=isShip&filter=isCharity&status=DRAFT&query=cars
   * /admin/auctions/getAllAuctions?page=1&size=15&filter=isCharity
   *
   * @param query
   * @param status
   * @param filter
   * @private
   */
  private buildFilterParams(query: string | undefined, status: string | undefined, filter: string[] | undefined): Record<string, any>[] {
    const today: string = new Date().toISOString();
    const paramsArray: Record<string, any>[] = [];

    // Add the status filter if provided
    if (status) {
      if (Array.isArray(status)) {
        // Build the 'IN' filter for the status array
        paramsArray.push({
          AND: [
            {
              status: { in: status }, // Use the 'in' operator for all status values
            },
          ],
        });
      } else {
        // Handle single status value
        paramsArray.push({ status });
      }
    } else {
      // Ensure we exclude completed auctions
      paramsArray.push({ status: { not: 'COMPLETE' } });
    }

    // Normalize filter to an array if it's not already one
    let filterArray: Array<string>;
    if (Array.isArray(filter)) {
      filterArray = filter;
    } else if (filter) {
      filterArray = [filter];
    } else {
      filterArray = [];
    }
    // Process the filter array to add boolean filters dynamically
    filterArray.forEach((filterParam: string) => {
      if (filterParam.startsWith('is')) {
        const key: string = filterParam;
        const value: number = 1; // Prisma treats boolean-like numeric values as 1 (true)
        paramsArray.push({ [key]: value });
      }
    });

    // Combine 'tags' and 'name' filters with AND condition
    if (query) {
      paramsArray.push({
        AND: [
          {
            OR: [{ tags: { contains: query, mode: 'insensitive' } }, { name: { contains: query, mode: 'insensitive' } }, { code: { contains: query, mode: 'insensitive' } }],
          },
        ],
      });
    }

    // Ensure toDatetime filter based on isExpired flag
    if (!filter?.includes('allDates')) {
      if (filter?.includes('expired')) {
        paramsArray.push({ toDatetime: { lte: today } });
      }
      if (filter?.includes('open')) {
        paramsArray.push({ toDatetime: { gte: today } });
      }
    }

    return paramsArray;
  }

  @Patch('publishAuctions')
  @HttpCode(204)
  async publishAuction(@Body() body: AuctionsPublishRequest, @Req() req: VBInterceptorRequest): Promise<AuctionResponse | ErrorResponse> {
    try {
      const pastOrExpiredAuctions: AuctionDto[] = await this.handler.getPastOrExpiredAuctions(body.auctionIds);
      if (pastOrExpiredAuctions.length > 0) {
        throw new AuctionNotEligibleToPublish();
      }
      const auctionsDto: AdminAuctionListDto[] = (await this.handler.getAllAdmin(1, -1, [{ id: { in: body.auctionIds } }])).list;

      const response: boolean = await this.handler.publishAuctions(body.auctionIds, req.accountMappingsId);

      _.forEach(body.auctionIds, async (auctionId: string) => {
        //since we are only publishing an auction no other services are aware of them yet.
        //so we only need to delete it locally
        await AuctionsCachingService.deleteCache(this.redisService, auctionId);
      });

      for (const auction of auctionsDto) {
        const lastClosingAuctionLot: AuctionLotDto = await this.auctionLotsHandler.checkLastItemClosed(auction.id);
        this.setEventSchedulers(auction, lastClosingAuctionLot);
        await this.notify('success', 'status_update', { data: new AuctionDto({ id: auction.id }) });
      }

      return new AuctionResponse(response);
    } catch (error) {
      this.logger.error("Error caught in PATCH 'publishAuction': " + error);

      return new ErrorResponse(error, 'publishAuctions');
    }
  }

  async setEventSchedulers(auction: AdminAuctionListDto, lastClosingAuctionLot: AuctionLotDto): Promise<void> {
    const updateEvent: UpdateEventSchedulerAndLotTimes = {
      auctionsDto: auction,
      lastClosingAuctionLot: lastClosingAuctionLot,
      auctionPublishedARN: this.auctionPublishedARN,
      auctionClosedARN: this.auctionClosedARN,
      isLastLotClosedARN: this.isLastLotClosedARN,
      checkLastClosingLotDelay: this.checkLastClosingLotDelay,
      delayInSeconds: this.delayInSeconds,
    };

    await this.auctionUtilService.setEventSchedulers(updateEvent);
  }

  /**
   * Get an auction by id
   *
   * @param id
   * @returns  AuctionGetResponse | ErrorResponse
   */
  @Get('/:id')
  async get(@Param('id') id: string, @Req() _req: VBInterceptorRequest): Promise<AuctionsDetailedResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.handler.get(id);
      await this.handler.addTaxesToAuctions([auction]);
      const images: { [key: string]: any; id?: string }[] = await this.sharedService.getResourceURLById(id, ResourceType.AUCTION_IMAGES);
      const documents: { [key: string]: any; id?: string }[] = await this.sharedService.getResourceURLById(id, ResourceType.AUCTION_DOCUMENTS);
      const response: AuctionsDetailedResponse = new AuctionsDetailedResponse(new AdminAuctionDto(auction, images, documents));

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionById');
    }
  }

  @Patch('updateAuctionStatus')
  @HttpCode(204)
  async updateAuctionStatus(@Body() body: AuctionsUpdateRequest): Promise<AuctionUpdateResponse | ErrorResponse> {
    try {
      const response: boolean = await this.handler.updateAuctionStatus(body.auctionIds, body.status, body.isViewable);
      _.forEach(body.auctionIds, async (auctionId: string) => {
        await this.notify('success', 'status_update', { data: new AuctionDto({ id: auctionId }) });
      });

      return new AuctionUpdateResponse(response);
    } catch (error) {
      this.logger.error("Error caught in PATCH 'updateAuctionStatus': " + error);
      return new ErrorResponse(error, 'updateAuctionStatus');
    }
  }

  async getParent(_userId: string, _req: any): Promise<string> {
    //TODO Fill with auction houses based on request or userId. For now attach everything to the root.
    return '00000000-0000-0000-0000-000000000000';
  }

  @Get('getCodesInUse')
  async getAuctionCodesInUse(): Promise<AuctionCodesResponse | ErrorResponse> {
    try {
      const codes: string[] = await this.handler.getAuctionCodesInUse();

      return new AuctionCodesResponse({ codes });
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionCodesInUse');
    }
  }

  @Get('getByCode/:code')
  async checkAuctionCodeExists(@Param('code') code: string): Promise<SuccessResponse | ErrorResponse> {
    try {
      const existingAuction: AuctionDto = await this.handler.getCustom([{ code: code, status: { notIn: ['COMPLETE', 'CLOSED'] }, deletedOn: null }]);
      return new SuccessResponse({ auctionId: existingAuction.id }, 'checkAuctionCodeExists', undefined, { exists: true });
    } catch (error) {
      return new SuccessResponse({}, 'checkAuctionCodeExists', undefined, { exists: false });
    }
  }

  @Get('/:id/summary')
  async getAuctionSummary(@Param('id') id: string): Promise<AuctionSummaryResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.handler.get(id);

      const auctionSummary: Partial<AuctionSummaryDto> = await this.handler.getSummary(auction);
      const biddingSummary: any = await this.biddingApi.getAuctionBiddingSummary(auction.id).then((res: any) => res.data.data);

      const summaryDto: AuctionSummaryDto = new AuctionSummaryDto({
        ...auctionSummary,
        ...biddingSummary,
      });

      return new AuctionSummaryResponse(summaryDto);
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionSummary');
    }
  }

  validateAuctionDates(fromDatetime: string, toDatetime: string, status?: AuctionStatus): void {
    const now: moment.Moment = moment();
    const startDate: moment.Moment = moment(fromDatetime, moment.ISO_8601, true);
    if ((status === undefined || status !== AuctionStatus.Open) && (!startDate.isValid() || startDate.isBefore(now.add(5, 'minutes')))) {
      throw new InvalidAuctionStartDate();
    }

    const endDate: moment.Moment = moment(toDatetime, moment.ISO_8601, true);
    if (!startDate.isValid() || !endDate.isValid() || endDate.isBefore(startDate)) {
      throw new InvalidAuctionEndDate();
    }
  }
}
