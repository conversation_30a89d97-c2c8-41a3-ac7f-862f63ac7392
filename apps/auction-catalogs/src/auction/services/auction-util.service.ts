import { Injectable, Logger } from '@nestjs/common';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { AdminAuctionListDto } from '../dtos/admin-auction-list.dto';

export type UpdateEventSchedulerAndLotTimes = {
  auctionsDto: AdminAuctionListDto;
  lastClosingAuctionLot: AuctionLotDto;
  auctionPublishedARN: string;
  auctionClosedARN: string;
  isLastLotClosedARN: string;
  checkLastClosingLotDelay: number;
  delayInSeconds: number;
};

@Injectable()
export class AuctionUtilService {
  constructor(
    private eventBridgeService: EventBridgeService,
    private auctionLotsHandler: AuctionLotsHandler,
    private logger: Logger,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  public async setEventSchedulers(updateEvent: UpdateEventSchedulerAndLotTimes): Promise<void> {
    const auctionfromDateTime: Date = new Date(updateEvent.auctionsDto.fromDatetime);
    const auctiontoDateTime: Date = new Date(updateEvent.auctionsDto.toDatetime);
    const lastClosingAuctionLotToDatetime: Date = new Date(updateEvent.lastClosingAuctionLot.toDatetime);

    // Subtract one-minute from the request date-time - so that we can get around EventBridge 2 minute guarantee. We need it to be real-time.
    // So we take in the message a minute early, and then handle the queue at the right time on our end for the last minute.
    this.eventBridgeService.subtractDatetimes([auctionfromDateTime, auctiontoDateTime], 2);

    this.eventBridgeService.manageScheduler(`${updateEvent.auctionsDto.id}_AUCTION_PUBLISH`, updateEvent.auctionsDto.id, auctionfromDateTime, updateEvent.auctionPublishedARN);
    this.logger.log(`Eventbridge scheduler has been created for opening of auction id ${updateEvent.auctionsDto.id}`);

    this.eventBridgeService.manageScheduler(`${updateEvent.auctionsDto.id}_AUCTION_CLOSED`, updateEvent.auctionsDto.id, auctiontoDateTime, updateEvent.auctionClosedARN);
    this.logger.log(`Eventbridge scheduler has been created for closing of auction id ${updateEvent.auctionsDto.id}`);

    this.eventBridgeService.manageScheduler(
      `${updateEvent.auctionsDto.id}_IS_LAST_LOT_CLOSED`,
      updateEvent.auctionsDto.id,
      lastClosingAuctionLotToDatetime,
      updateEvent.isLastLotClosedARN,
      updateEvent.checkLastClosingLotDelay,
    );
    this.logger.log(`Eventbridge scheduler has been created to check last closing lot for auction id ${updateEvent.auctionsDto.id}`);

    // setting delay in lot closing with configured interval
    await this.auctionLotsHandler.updateAuctionLotsToDateTimes(updateEvent.auctionsDto.id, updateEvent.delayInSeconds);
  }
}
