import { S3DeleteItemKeyMappingDto } from './s3-delete-item-key-mapping.dto';

/**
 * Represents the parameters required to construct a command for deleting items from an S3 bucket.
 */
export class S3DeleteItemCommandParametersDto {
  // The name of the S3 bucket from which objects will be deleted.
  Bucket: string;
  // An object containing an array of `S3DeleteItemKeyMappingDto` instances representing the keys of the objects to be deleted.
  Delete: { Objects: { Key: string }[] };

  /**
   * Constructs an instance of `S3DeleteItemCommandParametersDto`.
   * @param {string} bucketName - The name of the S3 bucket from which objects will be deleted.
   * @param {Array<{id: string; location: string}>} keys - An array of objects containing the `id` and `location` of each object to be deleted, used to construct the S3 object keys.
   */
  constructor(bucketName: string, keys: { id: string; location: string }[]) {
    this.Bucket = bucketName;
    this.Delete = {
      Objects: keys.map((key: { id: string; location: string }) => new S3DeleteItemKeyMappingDto(key.id, key.location)),
    };
  }
}
