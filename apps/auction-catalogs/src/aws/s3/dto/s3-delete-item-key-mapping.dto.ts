/**
 * Represents a mapping structure for associating unique identifiers and their locations with a specific S3 object key.
 */
export class S3DeleteItemKeyMappingDto {
  // The key string that represents the S3 object key, constructed from `location` and `id`.
  Key: string;

  /**
   * Constructs an instance of `S3DeleteItemKeyMappingDto`.
   * @param {string} id - The unique identifier of the object.
   * @param {string} location - The location prefix used to construct the S3 object key.
   */
  constructor(id: string, location: string) {
    this.Key = `${location}${id}`;
  }
}
