import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectsCommand, DeleteObjectCommandOutput, HeadObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';
import { ConfigService } from 'libs/config-service/src/config.service';
import { S3DeleteItemError } from '../exceptions/s3-delete-item-error';
import { S3DeleteItemCommandParametersDto } from '../dto/s3-delete-item-command-parameters.dto';
import * as sharp from 'sharp';
import { Readable } from 'stream';

/**
 * Type definition for an object representing a key to be deleted from S3.
 *
 * @property {string} id - The unique identifier of the object to be deleted.
 * @property {string} location - The S3 bucket location of the object to be deleted.
 */
export type S3DeleteKeyObject = { id: string; location: string };

@Injectable()
export class S3Service implements OnModuleInit {
  private s3Client: S3Client;
  private THUMBNAIL_HEIGHT: number;
  private THUMBNAIL_WIDTH: number;
  private LOT_IMAGE_HEIGHT: number;
  private LOT_IMAGE_WIDTH: number;
  private readonly logger = new Logger(S3Service.name);
  constructor(private readonly configService: ConfigService) {}

  async onModuleInit(): Promise<void> {
    const region: string | undefined = await this.configService.getSetting('AWS_REGION');
    this.s3Client = new S3Client({ region });
    this.THUMBNAIL_HEIGHT = Number(await this.configService.getSetting('THUMBNAIL_IMAGE_HEIGHT'));
    this.THUMBNAIL_WIDTH = Number(await this.configService.getSetting('THUMBNAIL_IMAGE_WIDTH'));
    this.LOT_IMAGE_HEIGHT = Number(await this.configService.getSetting('LOT_ITEM_IMAGE_HEIGHT'));
    this.LOT_IMAGE_WIDTH = Number(await this.configService.getSetting('LOT_ITEM_IMAGE_WIDTH'));
  }

  async getObjectFromBucket(bucketName: string, key: string): Promise<any> {
    const command: GetObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
    try {
      const response: any = await this.s3Client.send(command);
      const data: any = await response?.Body.transformToString();

      const transformedObj: any = {
        AcceptRanges: response.AcceptRanges,
        LastModified: response.LastModified,
        ContentLength: response.ContentLength,
        ETag: response.ETag,
        VersionId: response.VersionId,
        ContentEncoding: response.ContentEncoding,
        ContentType: response.ContentType,
        ServerSideEncryption: response.ServerSideEncryption,
        Metadata: response.Metadata,
        Body: {
          data: data,
        },
      };
      return transformedObj;
    } catch (error) {
      throw new Error(`Error fetching object: ${error.message}`);
    }
  }

  async saveFile(bucketName: string, fullFilePath: string, fileId: string, data: any, mimeType: string): Promise<any> {
    const originalPath: string = `${fullFilePath}originals/${fileId}`;
    const displayPath: string = `${fullFilePath}${fileId}`;
    const thumbnailPath: string = `${fullFilePath}thumbnails/${fileId}`;

    let originalResponse: any, displayResponse: any, thumbnailResponse: any;

    if (mimeType.startsWith('image/')) {
      // If it's an image, handle resizing and uploading to all paths
      const uploadOriginal: any = this.uploadToS3(bucketName, originalPath, data, mimeType);

      const displayImageBuffer: any = await sharp(data).resize(this.LOT_IMAGE_HEIGHT, this.LOT_IMAGE_WIDTH).toBuffer();
      const uploadDisplay: any = this.uploadToS3(bucketName, displayPath, displayImageBuffer, mimeType);

      const thumbnailBuffer: any = await sharp(data).resize(this.THUMBNAIL_HEIGHT, this.THUMBNAIL_WIDTH).toBuffer();
      const uploadThumbnail: any = this.uploadToS3(bucketName, thumbnailPath, thumbnailBuffer, mimeType);

      try {
        [originalResponse, displayResponse, thumbnailResponse] = await Promise.all([uploadOriginal, uploadDisplay, uploadThumbnail]);
      } catch (error) {
        throw new Error(`Error saving image files: ${error.message}`);
      }
    } else {
      // If it's a document, upload only to the displayPath
      const uploadDisplay: any = this.uploadToS3(bucketName, displayPath, data, mimeType);

      try {
        displayResponse = await uploadDisplay;
      } catch (error) {
        throw new Error(`Error saving document: ${error.message}`);
      }
    }

    // Return responses (originalResponse and thumbnailResponse will be undefined for documents)
    return { originalResponse, displayResponse, thumbnailResponse };
  }

  async uploadToS3(bucketName: string, filePath: string, data: any, mimeType: string): Promise<any> {
    const input: any = {
      Bucket: bucketName,
      Key: filePath,
      Body: data,
      ContentType: mimeType,
    };
    const command: any = new PutObjectCommand(input);

    try {
      const response: any = await this.s3Client.send(command);
      return response;
    } catch (error) {
      throw new Error(`Error uploading to S3: ${error.message}`);
    }
  }

  /**
   * Asynchronously marks an array of files as deleted within a specified S3 bucket.
   *
   * @param {string} bucketName - The name of the target S3 bucket.
   * @param {Array<S3DeleteKeyObject>} keys - An array of objects containing id and location to identify a file to be marked as deleted.
   * @returns {Promise<any>} - A promise that resolves with the response from the S3 client upon successful deletion of the objects, or rejects with an error.
   * @throws {S3DeleteItemError} - An error with a descriptive message if the deletion operation fails.
   */
  async markFilesAsDeleted(bucketName: string, keys: S3DeleteKeyObject[]): Promise<any> {
    // Use the S3DeleteItemCommandParametersDto to encapsulate the command options
    const deleteCommandOptions: S3DeleteItemCommandParametersDto = new S3DeleteItemCommandParametersDto(bucketName, keys);
    // Construct the delete command with the options provided by the DTO
    const deleteCommand: DeleteObjectsCommand = new DeleteObjectsCommand(deleteCommandOptions);

    try {
      // Attempt to send the delete command to the S3 client and wait for the response
      const response: DeleteObjectCommandOutput = await this.s3Client.send(deleteCommand);
      return response;
    } catch (error) {
      // Construct and throw a custom error with the IDs of items that failed to delete
      throw new S3DeleteItemError(keys.map((key: S3DeleteKeyObject) => key.id).join(', '));
    }
  }

  async generateThumbnails(bucketName: string, fullFilePath: string, imageIds: Array<string>): Promise<void> {
    const thumbnailPromises: any = imageIds.map(async (id: string) => {
      try {
        const displayPath: string = `app_data/${fullFilePath}/${id}`;

        // Check if the image object exists in S3
        const headObjectCommand: HeadObjectCommand = new HeadObjectCommand({
          Bucket: bucketName,
          Key: displayPath,
        });

        try {
          await this.s3Client.send(headObjectCommand);
        } catch (headError) {
          if (headError.name === 'NotFound') {
            this.logger.warn(`Image ${displayPath} does not exist in S3 bucket. Skipping thumbnail generation.`);
            return;
          } else {
            throw headError;
          }
        }

        // Get the image object from S3
        const getObjectCommand: GetObjectCommand = new GetObjectCommand({
          Bucket: bucketName,
          Key: displayPath,
        });
        const response: any = await this.s3Client.send(getObjectCommand);

        // Convert the response body to a buffer
        const stream: Readable = response.Body as Readable;
        const chunks: Uint8Array[] = [];
        for await (const chunk of stream) {
          chunks.push(chunk);
        }
        const buffer: any = Buffer.concat(chunks);

        // Use Sharp to resize the image
        const thumbnailBuffer: any = await sharp(buffer)
          .resize(this.THUMBNAIL_HEIGHT, this.THUMBNAIL_WIDTH) // Adjust the dimensions as needed
          .toBuffer();

        // Get the metadata to determine the image format
        const metadata: any = await sharp(thumbnailBuffer).metadata();
        const contentType: string = `image/${metadata.format}`;

        // Define the path for the thumbnail
        const thumbnailPath: string = `${fullFilePath}/thumbnails/${id}`;

        // Upload the thumbnail back to S3
        const putObjectCommand: PutObjectCommand = new PutObjectCommand({
          Bucket: bucketName,
          Key: thumbnailPath,
          Body: thumbnailBuffer,
          ContentType: contentType, // Adjust based on the image type
        });
        await this.s3Client.send(putObjectCommand);
      } catch (error) {
        this.logger.warn(`Error processing image ${id}:`, error);
      }
    });

    // Wait for all thumbnails to be generated
    await Promise.all(thumbnailPromises);
  }

  async copyFileInS3(sourceBucket: string, sourceKey: string, destinationBucket: string, destinationKey: string): Promise<any> {
    try {
      const copyParams: any = {
        Bucket: destinationBucket,
        CopySource: `/${sourceBucket}/${sourceKey}`,
        Key: destinationKey,
      };

      const command: CopyObjectCommand = new CopyObjectCommand(copyParams);
      const result: any = await this.s3Client.send(command);

      this.logger.log('File copied successfully:', result);
      return result;
    } catch (error) {
      this.logger.error('Error copying file in S3:', error);
      throw error;
    }
  }
}
