import { BadRequestException, Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { S3Service } from '../data/s3.service';

@Controller('s3')
@UseGuards(AuthGuard('jwt'))
export class S3Controller {
  constructor(private readonly s3Service: S3Service) {}

  @Get(':bucketName/:key')
  async getObjectFromS3(@Param('bucketName') bucketName: string, @Param('key') key: string): Promise<any> {
    if (!bucketName || !key) {
      throw new BadRequestException('Bucket name and key are required.');
    }
    return this.s3Service.getObjectFromBucket(bucketName, key);
  }
}
