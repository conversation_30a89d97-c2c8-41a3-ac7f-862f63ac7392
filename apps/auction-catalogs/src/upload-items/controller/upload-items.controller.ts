import { Controller, Post, Body } from '@nestjs/common';
import { UploadItemsService } from '../service/upload-items.service';
import { UploadItem } from '../entities/upload-item.entity';

@Controller('upload-items')
export class UploadItemsController {
  constructor(private uploadItemsService: UploadItemsService) {}

  @Post()
  create(@Body() createUploadItemDtos: UploadItem[]): any {
    return this.uploadItemsService.createAll(createUploadItemDtos);
  }
}
