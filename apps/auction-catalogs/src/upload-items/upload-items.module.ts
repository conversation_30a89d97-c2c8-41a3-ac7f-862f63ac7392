import { Module } from '@nestjs/common';
import { UploadItemsController } from './controller/upload-items.controller';
import { UploadItemsService } from './service/upload-items.service';
import { S3Service } from '../aws/s3/data/s3.service';
import { UploadsService } from '../uploads/service/uploads.service';
import { ResourcesModule } from '../resources/resources.module';

@Module({
  imports: [ResourcesModule],
  controllers: [UploadItemsController],
  providers: [UploadItemsService, S3Service, UploadsService],
  exports: [UploadItemsService],
})
export class UploadItemsModule {}
