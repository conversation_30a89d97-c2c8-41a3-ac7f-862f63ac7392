import { Injectable, OnModuleInit } from '@nestjs/common';
import { UploadItemDto } from '../dto/create-upload-item.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { UploadItem } from '../entities/upload-item.entity';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { UploadItemsNotFoundError } from '../exceptions/upload-items-not-found-error';
import { S3DeleteKeyObject, S3Service } from '../../aws/s3/data/s3.service';
import { ResourcesController } from '../../resources/controller/resources.controller';
import { UploadsService } from '../../uploads/service/uploads.service';
import { Upload } from '../../uploads/entities/upload.entity';
import { UploadsNotFoundError } from '../../uploads/exceptions/uploads-not-found-error';
import { ResourcesNotFoundError } from '../../resources/exceptions/resources-not-found-error';
import { ConfigService } from 'libs/config-service/src/config.service';
import { ResourcesResponse } from '../../resources/http/responses/resources.response';
import { ResourcesDto } from '../../resources/dto/resources.dto';

@Injectable()
export class UploadItemsService implements OnModuleInit {
  private bucketName: string;

  constructor(
    private prismaService: PrismaService,
    private s3Service: S3Service,
    private resourcesController: ResourcesController,
    private uploadsService: UploadsService,
    private configService: ConfigService,
  ) {}

  async onModuleInit(): Promise<void> {
    this.bucketName = await this.configService.getSetting('TEMPLATE_BUCKET_NAME');
  }

  async createAll(createUploadItemDtos: UploadItem[]): Promise<Prisma.BatchPayload | void> {
    return await this.prismaService.uploadItems.createMany({
      data: createUploadItemDtos,
    });
  }

  async findByUploadId(uploadsId: string): Promise<UploadItem[] | null> {
    return await this.prismaService.uploadItems.findMany({
      where: {
        uploadsId: uploadsId,
        deletedOn: null,
      },
    });
  }

  async findByUploadIds(uploadsIds: string[]): Promise<UploadItem[] | null> {
    return await this.prismaService.uploadItems.findMany({
      where: {
        uploadsId: { in: uploadsIds },
        deletedOn: null,
      },
    });
  }

  async findByFileId(fileId: string): Promise<UploadItem | null> {
    return await this.prismaService.uploadItems.findFirst({
      where: {
        id: fileId,
        deletedOn: null,
      },
    });
  }

  /**
   * Finds upload items by their uploadItem IDs.
   *
   * This method queries the database for upload items that match the provided uploadItem IDs.
   * If no upload items are found, it triggers a 404 error response.
   *
   * @param uploadItemsIds An array of uploadItem IDs to search for.
   * @returns A promise that resolves with an array of found upload items.
   */
  async findByUploadItemsIds(uploadItemsIds: string[]): Promise<UploadItem[]> {
    // Query the database for upload items with the specified uploadItem IDs.
    const uploadItems: UploadItem[] = await this.prismaService.uploadItems.findMany({
      where: {
        id: { in: uploadItemsIds },
        deletedOn: null,
      },
    });

    // If no upload items are found, trigger a 404 error.
    if (uploadItems.length === 0) {
      throw this.throw404(uploadItemsIds.join(', '));
    }

    // Return the found upload items.
    return uploadItems;
  }

  findOne(id: number): any {
    return `This action returns a #${id} uploadItem`;
  }

  async update(_uploadItem: UploadItemDto): Promise<UploadItem | null> {
    return await this.prismaService.uploadItems.update({
      where: {
        id: _uploadItem.id,
      },
      data: _uploadItem,
    });
  }

  async updateAll(_uploadItems: UploadItem[]): Promise<Prisma.BatchPayload | void> {
    return await this.prismaService.uploadItems.updateMany({
      data: _uploadItems,
    });
  }

  remove(id: number): any {
    return `This action removes a #${id} uploadItem`;
  }

  /**
   * Performs a soft deletion of upload items identified by their IDs.
   *
   * This method updates the `deletedBy` and `deletedOn` fields of the specified upload items to mark them as deleted.
   * It also handles the deletion of associated resources in the S3 bucket by marking them as deleted.
   *
   * @param userId The ID of the user who is performing the deletion operation.
   * @param uploadItemsIds An array containing the IDs of the upload items to be deleted.
   * @returns A promise that resolves to void.
   * @throws {UploadsNotFoundError} Throws an error if an upload item is not found.
   * @throws {ResourcesNotFoundError} Throws an error if an associated resource is not found.
   */
  async deleteByUploadItemsIds(userId: string, uploadItemsIds: string[]): Promise<void> {
    // Retrieve the upload items based on the provided IDs.
    const uploadItems: UploadItem[] = await this.findByUploadItemsIds(uploadItemsIds);
    // Retrieve the uploads associated with the fetched upload items.
    const uploads: Upload[] = await this.uploadsService.findMany(uploadItems.map((item: UploadItem) => item.uploadsId));
    // Retrieve the resources associated with the fetched uploads.
    const resources: ResourcesResponse = await this.resourcesController.findByIds(uploads.map((upload: Upload) => upload.resourceId).join(','));

    // Prepare the objects for deletion by merging the information of upload items and resources.
    const objectsToDelete: S3DeleteKeyObject[] = uploadItems.map((uploadItem: UploadItem) => {
      const relatedUpload: Upload | undefined = uploads.find((upload: Upload) => upload.id === uploadItem.uploadsId);
      const relatedResource: ResourcesDto = resources.data?.find((resource: ResourcesDto) => resource.id === relatedUpload?.resourceId);

      if (!relatedUpload) throw new UploadsNotFoundError(uploadItemsIds.join(', '));
      if (!relatedResource) throw new ResourcesNotFoundError(uploadItemsIds.join(', '));

      return { id: uploadItem.id, location: relatedResource.location };
    });

    // Update the specified upload items to mark them as deleted.
    await this.prismaService.uploadItems.updateMany({
      where: {
        id: { in: uploadItemsIds },
      },
      data: {
        deletedBy: userId,
        deletedOn: new Date().toISOString(),
      },
    });

    // Mark files in the S3 bucket as deleted for any associated resources with the upload items.
    await this.s3Service.markFilesAsDeleted(this.bucketName, objectsToDelete);
  }

  /**
   * Throws a 404 error specifically for upload items not found.
   *
   * @param id An ID that was not found.
   * @returns An instance of `UploadItemsNotFoundError` with the missing ID.
   */
  throw404(id: string): Error {
    return new UploadItemsNotFoundError(id);
  }
}
