import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Logger } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { ConfigService } from '@vb/config/src/config.service';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { PurchaseOfferDto } from '../dtos/purchase-offer.dto';
import { LotDto } from '../../lots/dtos/lot.dto';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { PurchaseOfferResponseParametersDto } from '../dtos/purchase-offer-response-parameters.dto';

export abstract class BasePurchaseOfferNotificationSubscriber implements SubscriberInterface {
  private logger: Logger = new Logger(this.constructor.name);
  private baseUrl: string;
  private eventKey: string;

  constructor(
    private awsSqsService: AwsSqsService,
    private configService: ConfigService,
    eventKey: string,
  ) {
    this.eventKey = eventKey;
  }

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }
  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      this.logger.log(`Assembling notification for event ${eventType}`);
      const auctionLot: AuctionLotDto = eventPayload.items.data.auctionLot;
      const offer: PurchaseOfferDto = eventPayload.items.data.offer;
      const lot: LotDto = eventPayload.items.data.lot;

      const message: Message = MessageFactory.build(
        this.eventKey,
        new AccountMappingCommonListDto([new AccountMappingCommonDto('', '', '', offer.accountMappingsId, '', '')]),
        new PurchaseOfferResponseParametersDto(lot, auctionLot, offer, this.baseUrl),
      );

      this.awsSqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error caught in ${this.constructor.name}: ${error}`);
    }
  }
}
