import { Injectable } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { ConfigService } from '@vb/config/src/config.service';
import { BasePurchaseOfferNotificationSubscriber } from './base-purchase-offer-notification.subscriber';

@Injectable()
export class NotifyAdminPurchaseOfferAcceptedSubscriber extends BasePurchaseOfferNotificationSubscriber {
  constructor(awsSqsService: AwsSqsService, configService: ConfigService) {
    super(awsSqsService, configService, 'NOTIFY_ADMIN_PURCHASE_OFFER_ACCEPTED');
  }
}
