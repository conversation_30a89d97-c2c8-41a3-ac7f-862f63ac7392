import { GetScheduleCommandOutput } from '@aws-sdk/client-scheduler';
import { Injectable, Logger } from '@nestjs/common';
import { AccountMappingCachingService } from '@vb/account-mappings';
import { AccountMappingDto } from '@vb/nest/common-dtos/users/account-mapping.dto';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { EventBridgeService } from '@vb/nest/services/eventbridge.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { maskString } from '@vb/utils/src/data/mask-string';
import { SocketsApi } from '@viewbid/ts-node-client';
import { BiddingData } from 'apps/bidding/src/bids/handler/bids.handler.interfaces';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { PurchaseOfferDto } from '../dtos/purchase-offer.dto';

@Injectable()
export class CloseLotWithAcceptedOfferSubscriber implements SubscriberInterface {
  private logger = new Logger(CloseLotWithAcceptedOfferSubscriber.name);

  constructor(
    private eventBridgeService: EventBridgeService,
    private socketService: SocketsApi,
    private accountMappingsService: AccountMappingCachingService,
  ) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      const auctionLot: AuctionLotDto = eventPayload.items.data.auctionLot;
      const acceptedOffer: PurchaseOfferDto = eventPayload.items.data.offer;

      this.logger.log(`Sending messages and cleaning up scheduled for an auction lot ( ${auctionLot.id} ) due to an accepted offer ( ${acceptedOffer.id} )`);

      this.sendSocketMessage(auctionLot);
      this.deleteSchedules(auctionLot.id);
    } catch (error) {
      this.logger.error(`Error while closing lot for accepted offer ( ${eventPayload.items.data.offer.id} ) `);
    }
  }

  async sendSocketMessage(auctionLot: AuctionLotDto): Promise<void> {
    const biddingDate: BiddingData = await this.prepareBiddingData(auctionLot);

    const pageLoadStringfy: string = JSON.stringify({ action: 'PAGE_LOAD', data: biddingDate }, (key: string, value: any) =>
      typeof value === 'bigint' ? value.toString() : value,
    );
    const socketMessageNewBid: any = { message: { message: pageLoadStringfy }, messageType: 'LOT_ITEM_CLOSED', resourceId: auctionLot.id };
    await this.socketService
      .postSocketMessage({
        message: JSON.stringify(socketMessageNewBid),
      })
      .catch((error: any) => {
        this.logger.error(`Error while closing socketMessage ${error.message} in CloseLotWithAcceptedOfferSubscriber`);
      });
  }

  async deleteSchedules(auctionLotId: string): Promise<void> {
    const lotEndingSoonSchedule: GetScheduleCommandOutput | null | void = await this.eventBridgeService
      .getScheduleDetails(`${auctionLotId}_LOT_ENDING_SOON`)
      .catch((error: Error) => {
        this.logger.error(`During deletion scheduler wasn't found: ${auctionLotId}_LOT_ENDING_SOON`, error);
      });
    if (lotEndingSoonSchedule?.ScheduleExpression) {
      this.eventBridgeService.deleteScheduler(`${auctionLotId}_LOT_ENDING_SOON`);
    }

    const lotClosedSchedule: GetScheduleCommandOutput | null | void = await this.eventBridgeService.getScheduleDetails(`${auctionLotId}_LOT_CLOSED`).catch((error: Error) => {
      this.logger.error(`During deletion scheduler wasn't found: ${auctionLotId}_LOT_CLOSED`, error);
    });
    if (lotClosedSchedule?.ScheduleExpression) {
      this.eventBridgeService.deleteScheduler(`${auctionLotId}_LOT_CLOSED`);
    }
  }

  async prepareBiddingData(updatedAuctionLot: AuctionLotDto): Promise<BiddingData> {
    const accountMapping: AccountMappingDto = await this.accountMappingsService.getAccountMapping(updatedAuctionLot.currentHighBidderId);
    const currentHighBidderName: string = `${accountMapping.user?.firstname ?? ''} ${accountMapping.user?.lastname ?? accountMapping.organization?.name ?? ''}`;
    const biddingData: BiddingData = {
      minimumBid: updatedAuctionLot.minBidAmount ?? 0,
      endDate: updatedAuctionLot.toDatetime,
      remainingTime: 'Expired',
      serverTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      auctionLotsId: updatedAuctionLot.id,
      bidIncrement: 0,
      highBidderName: maskString(currentHighBidderName),
      isProxy: 0,
      bidCount: 0,
      currentPrice: updatedAuctionLot.currentBid,
      numberOfWatchers: 0,
      highBidder: updatedAuctionLot.currentHighBidderId,
    };
    return biddingData;
  }
}
