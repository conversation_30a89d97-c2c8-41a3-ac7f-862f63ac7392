import { UserDto } from '@vb/nest/common-dtos/users/user.dto';

export class DetailedPurchaseOfferDto {
  id: string; // Unique identifier for the purchase offer
  createdOn: string; // Timestamp of when the offer was created
  accountMappingsId: string; // Reference to account mappings
  itemMappingsId: string; // Reference to item mappings
  quantity: number; // Quantity of items in the offer
  status: string; // Status of the offer (e.g., pending, accepted, rejected)
  isAdmin: boolean; // Indicates if the offer was made by an admin
  message: string;
  price: number; // Price associated with the offer
  mappingType: string; // Mapping type (e.g., Lot, Product)
  lotId: string;
  title: string; // Title of the lot
  condition: string; // Condition of the lot
  username: string;

  constructor(row: any) {
    this.id = row.id;
    this.createdOn = row.createdOn;
    this.accountMappingsId = row.accountMappingsId;
    this.itemMappingsId = row.itemMappingsId;
    this.quantity = row.quantity;
    this.status = row.status;
    this.isAdmin = row.isAdmin;
    this.message = row.message;
    this.price = row.price;
    this.mappingType = row.mappingType;
    this.lotId = row.lotId;
    this.title = row.title;
    this.condition = row.condition;
  }

  setUserDetails(user: UserDto): void {
    this.username = user.username;
  }
}
