export class PurchaseOfferListDto {
  id: number; // Unique identifier for the purchase offer
  createdOn: Date; // Timestamp of when the offer was created
  accountMappingsId: string; // Reference to account mappings
  itemMappingsId: number; // Reference to item mappings
  quantity: number; // Quantity of items in the offer
  status: string; // Status of the offer (e.g., pending, accepted, rejected)
  isAdmin: boolean; // Indicates if the offer was made by an admin
  message: string | null; // Optional message accompanying the offer
  price: number; // Price associated with the offer
  mappingType: string; // Mapping type (e.g., Lot, Product)
  lotId: string; // ID of the lot associated with the offer
  title: string; // Title of the lot
  condition: string; // Condition of the lot
  lotNo: string;
  auctionId: string;
  currentBidPrice: number;
  auctionCode: string;
  username: string;
  thumbnailUrl: string;

  constructor(row: any) {
    this.id = row.id;
    this.createdOn = row.createdOn;
    this.accountMappingsId = row.accountMappingsId;
    this.itemMappingsId = row.itemMappingsId;
    this.quantity = row.quantity;
    this.status = row.status;
    this.isAdmin = row.isAdmin;
    this.message = row.message;
    this.price = parseInt(row.price);
    this.mappingType = row.mappingType;
    this.lotId = row.lotId;
    this.title = row.title;
    this.condition = row.condition;
    this.lotNo = row.lotNo;
    this.auctionId = row.auctionId;
    this.currentBidPrice = parseInt(row.currentBidPrice);
    this.auctionCode = row.auctionCode;
    this.username = row.username;
  }
}
