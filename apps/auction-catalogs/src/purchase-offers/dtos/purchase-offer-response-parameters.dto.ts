import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { PurchaseOfferDto } from './purchase-offer.dto';
import { LotDto } from '../../lots/dtos/lot.dto';
import { TimeFunctions } from '@vb/utils/src/common/time-functions';

export class PurchaseOfferResponseParametersDto {
  offerUrl: string;
  title: string;
  lotNumber: string;
  auctionName: string;
  quantity: number;
  offerDate: string;
  amount: number;
  imageUrl: string;
  adminUrl: string;

  constructor(lot: LotDto, auctionLot: AuctionLotDto, rejectedOffer: PurchaseOfferDto, baseUrl: string) {
    this.title = lot.title;
    this.lotNumber = auctionLot.number + auctionLot.suffix;
    this.amount = rejectedOffer.price;
    this.quantity = rejectedOffer.quantity;
    this.offerDate = TimeFunctions.formatDateForEmail(new Date().toISOString());
    this.offerUrl = `${baseUrl}/profiles/my-profile/my-offers`;
    this.adminUrl = `${baseUrl}/admin/auctions/offers`;
  }
}
