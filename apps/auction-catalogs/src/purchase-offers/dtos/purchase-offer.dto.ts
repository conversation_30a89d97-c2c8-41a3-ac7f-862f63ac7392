import { PurchaseOfferStatus } from '../../prisma/generated/auction-catalogs-db-client';

export class PurchaseOfferDto {
  id: string;
  accountMappingsId: string;
  itemMappingsId: string;
  quantity: number;
  status: any;
  isAdmin: boolean;
  message: string;
  price: number;
  resolvedOn: Date;

  constructor(row: any) {
    this.id = row.id;
    this.accountMappingsId = row.accountMappingsId;
    this.itemMappingsId = row.itemMappingsId;
    this.quantity = row.quantity;
    this.status = row.status;
    this.isAdmin = row.isAdmin;
    this.message = row.message;
    this.price = row.price;
    this.resolvedOn = row.resolvedOn;
  }

  static createCounterOffer(purchaseOffer: PurchaseOfferDto, dto: PurchaseOfferDto, isAdmin: boolean): PurchaseOfferDto {
    return new PurchaseOfferDto({
      accountMappingsId: purchaseOffer.accountMappingsId,
      itemMappingsId: purchaseOffer.itemMappingsId,
      quantity: dto.quantity,
      status: PurchaseOfferStatus.Pending,
      isAdmin: isAdmin,
      message: dto.message,
      price: dto.price,
    });
  }
}
