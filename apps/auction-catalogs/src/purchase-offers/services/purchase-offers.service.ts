import { Transactional } from '@transactional/core';
import { PurchaseOfferDto } from '../dtos/purchase-offer.dto';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { BidResponse } from '../../../../bidding/src/bids/http/responses/bid.response';
import { RemoveHigherBidsRequest } from '../../../../bidding/src/bids/http/requests/remove-higher-bids.request';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { BiddingApi, LotStatus, PurchaseOfferStatus } from '@viewbid/ts-node-client';
import { LotsHandler } from '../../lots/handlers/lots.handler';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { PurchaseOffersHandler } from '../handlers/purchase-offers.handler';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class PurchaseOffersService {
  private readonly logger = new Logger(PurchaseOffersService.name);

  constructor(
    private readonly lotsHandler: LotsHandler,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    private readonly biddingApi: BiddingApi,
    private readonly purchaseOffersHandler: PurchaseOffersHandler,
  ) {}

  /**
   * Sets the purchase offer as sold and closes the auction lot.
   *
   * @param purchaseOffer
   * @param auctionLot
   * @param accountMappingsId
   * @param Accepted
   */
  @Transactional()
  async setItemSold(purchaseOffer: PurchaseOfferDto, auctionLot: AuctionLotDto, accountMappingsId: string, _Accepted: 'Accepted'): Promise<PurchaseOfferDto> {
    const [offer] = await Promise.all([
      this.setOfferStatus(purchaseOffer, accountMappingsId, PurchaseOfferStatus.Accepted),
      this.closeAuctionLot(auctionLot, accountMappingsId, purchaseOffer),
      this.deleteHigherBids(accountMappingsId, purchaseOffer, auctionLot),
    ]);

    return offer;
  }

  /**
   * Sets the purchase offer status.
   *
   * @param purchaseOffer
   * @param accountMappingsId
   * @param status
   */
  async setOfferStatus(purchaseOffer: PurchaseOfferDto, accountMappingsId: string, status: string): Promise<PurchaseOfferDto> {
    purchaseOffer.status = status;
    purchaseOffer.resolvedOn = new Date();
    return await this.purchaseOffersHandler.update(accountMappingsId, purchaseOffer, purchaseOffer.id);
  }

  /**
   * Closes the auction lot and updates its status.
   *
   * @param auctionLot
   * @param accountMappingsId
   * @param purchaseOffer
   * @private
   */
  private async closeAuctionLot(auctionLot: AuctionLotDto, accountMappingsId: string, purchaseOffer: PurchaseOfferDto): Promise<AuctionLotDto> {
    auctionLot.toDatetime = new Date(Date.now()).toISOString();
    auctionLot.currentBid = purchaseOffer.price;
    auctionLot.currentHighBidderId = purchaseOffer.accountMappingsId;

    await Promise.all([this.auctionLotsHandler.update(accountMappingsId, auctionLot, auctionLot.id), this.lotsHandler.updateStatuses([auctionLot.lotId], LotStatus.Complete)]);

    return auctionLot;
  }

  /**
   * Deletes higher bids for the given purchase offer.
   *
   * @param accountMappingsId
   * @param purchaseOffer
   * @param auctionLot
   */
  async deleteHigherBids(accountMappingsId: string, purchaseOffer: PurchaseOfferDto, auctionLot: AuctionLotDto): Promise<BidResponse> {
    const highestPrice: number = purchaseOffer.price;
    const removeHigherBidsRequest: RemoveHigherBidsRequest = {
      auctionId: auctionLot.auctionId,
      auctionLotId: auctionLot.id,
      amount: highestPrice,
      userId: purchaseOffer.accountMappingsId,
      metaData: `{ "bidType": "purchase offer", "isAdmin": "${purchaseOffer.isAdmin}", "acceptedBy": "${accountMappingsId}" }`,
    } as RemoveHigherBidsRequest;
    this.logger.log(removeHigherBidsRequest);
    const bidResponse: any = await this.biddingApi.removeHigherBids(removeHigherBidsRequest);
    if (bidResponse instanceof ErrorResponse) {
      throw new Error(bidResponse.getMessage());
    }

    return bidResponse;
  }
}
