import { ListResultset } from '@mvc/data/list-resultset';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { UploadsController } from '../../uploads/controller/uploads.controller';
import { UploadsByResourceResponse } from '../../uploads/entities/uploads.interfaces';
import { PurchaseOffersDbService } from '../data/purchase-offers-db.service';
import { DetailedPurchaseOfferDto } from '../dtos/detailed-purchase-offer.dto';
import { PurchaseOfferListDto } from '../dtos/purchase-offer-list.dto';
import { PurchaseOfferDto } from '../dtos/purchase-offer.dto';

@Injectable()
export class PurchaseOffersHandler extends BaseHandler<PurchaseOffersDbService, PurchaseOfferDto> {
  constructor(
    dbService: PurchaseOffersDbService,
    public uploadsController: UploadsController,
  ) {
    super(dbService);
  }

  async getAllOffers(page: number, size: number, params: any[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<PurchaseOfferListDto>> {
    const items: ListResultset<PurchaseOfferListDto> = await this.dbService.getAllOffers(page, size, params, orderBy);
    if (items.list.length) {
      const lotIds: string[] = items.list.map((item: PurchaseOfferListDto) => item.lotId);
      const images: [] | UploadsByResourceResponse[] | ErrorResponse = await this.uploadsController.findUploadsForResource(lotIds.join(','), true);
      if (images != null && !(images instanceof ErrorResponse)) {
        const imageMap: any = _.keyBy(images, (value: any) => value.proxyId);

        _.forEach(items.list, (value: any) => {
          value.thumbnailUrl = imageMap[value.lotId]?.files[0]?.fileLink;
        });
      }
    }
    return items;
  }

  async getDetailed(id: string): Promise<DetailedPurchaseOfferDto> {
    return await this.dbService.getDetailed(id);
  }

  async getPendingOffersByAuctionId(auctionId: string): Promise<ListResultset<PurchaseOfferDto>> {
    return await this.dbService.getPendingOffersByAuctionId(auctionId);
  }
}
