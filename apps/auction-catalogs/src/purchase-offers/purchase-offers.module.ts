import { Module } from '@nestjs/common';
import { PurchaseOffersController as AdminPurchaseOffersController } from './controllers/admin/purchase-offers.controller';
import { PurchaseOffersController as PurchaseOffersController } from './controllers/public/purchase-offers.controller';
import { PurchaseOffersDbService } from './data/purchase-offers-db.service';
import { PurchaseOffersHandler } from './handlers/purchase-offers.handler';
import { LotsModule } from '../lots/lots.module';
import { LotsDbService } from '../lots/data/lots-db.service';
import { LotsHandler } from '../lots/handlers/lots.handler';
import { ItemMappingsModule } from '../item-mappings/item-mappings.module';
import { ItemMappingsFactory } from '../item-mappings/factories/item-mappings.factory';
import { ProductsModule } from '../products/products.module';
import { ProductsDbService } from '../products/data/products-db.service';
import { ProductsHandler } from '../products/handler/products.handler';
import { SharedService } from '../shared/shared.service';
import { BidIncrementsModule } from '../bid-increments/bid-increments.module';
import { UploadsModule } from '../uploads/uploads.module';
import { AuctionLotsHandler } from '../auction-lots/handlers/auction-lots.handler';
import { AuctionLotsDbService } from '../auction-lots/data/auction-lots-db.service';
import { ResourcesModule } from '../resources/resources.module';
import { S3Module } from '../aws/s3/s3.module';
import { PurchaseOffersService } from './services/purchase-offers.service';

@Module({
  controllers: [AdminPurchaseOffersController, PurchaseOffersController],
  providers: [
    ItemMappingsFactory,
    LotsDbService,
    LotsHandler,
    ProductsDbService,
    ProductsHandler,
    PurchaseOffersDbService,
    PurchaseOffersHandler,
    PurchaseOffersService,
    SharedService,
    AuctionLotsHandler,
    AuctionLotsDbService,
  ],
  imports: [LotsModule, ItemMappingsModule, ProductsModule, UploadsModule, BidIncrementsModule, ResourcesModule, S3Module],
  exports: [],
})
export class PurchaseOffersModule {}
