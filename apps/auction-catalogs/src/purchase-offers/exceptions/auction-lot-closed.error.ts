import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';

export class AuctionLotClosedError extends ViewbidError {
  constructor(lotId: string, lotTitle: string) {
    super(
      `Cannot process purchase offer action. The auction lot "${lotTitle}" has been closed.`,
      Constants.AUCTION_LOT_CLOSED_ERROR_NUMBER,
      lotId,
      'purchase-offers#auction-lot-closed',
    );
  }
}
