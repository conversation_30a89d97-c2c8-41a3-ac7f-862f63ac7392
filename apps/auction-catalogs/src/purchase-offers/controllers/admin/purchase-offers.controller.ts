import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Query, Req, UseInterceptors } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { UsersApi } from '@viewbid/ts-node-client';
import { AuctionLotDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot.dto';
import * as _ from 'lodash';
import { $Enums } from '../../../prisma/generated/auction-catalogs-db-client';
import { DetailedPurchaseOfferDto } from '../../dtos/detailed-purchase-offer.dto';
import { PurchaseOfferListDto } from '../../dtos/purchase-offer-list.dto';
import { PurchaseOfferDto } from '../../dtos/purchase-offer.dto';
import { InvalidStatusError } from '../../exceptions/invalid-status.error';
import { PurchaseOffersHandler } from '../../handlers/purchase-offers.handler';
import { AdminPurchaseOfferRequest } from '../../http/requests/admin-purchase-offer.request';
import { PurchaseOfferRequest } from '../../http/requests/purchase-offer.request';
import { PurchaseOfferResponse } from '../../http/responses/purchase-offer.response';
import { PurchaseOffersListResponse } from '../../http/responses/purchase-offers-list.response';
import PurchaseOfferStatus = $Enums.PurchaseOfferStatus;
import { AuctionLotsHandler } from 'apps/auction-catalogs/src/auction-lots/handlers/auction-lots.handler';
import { LoadEntitiesInterceptor } from '@mvc/interceptors/load-entities.interceptor';
import { LotDto } from '../../../lots/dtos/lot.dto';
import { ItemNotAvailableError } from '../../exceptions/item-not-available.error';
import { AuctionLotClosedError } from '../../exceptions/auction-lot-closed.error';
import { PurchaseOffersService } from '../../services/purchase-offers.service';

@Controller('admin/purchase-offers')
@UseInterceptors(LoadEntitiesInterceptor)
export class PurchaseOffersController extends PubSubController<PurchaseOffersHandler, PurchaseOfferRequest, PurchaseOfferDto, PurchaseOfferResponse, PurchaseOffersListResponse> {
  constructor(
    handler: PurchaseOffersHandler,
    redisService: RedisService,
    private readonly usersApi: UsersApi,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    private readonly purchaseOffersService: PurchaseOffersService,
  ) {
    super(handler, redisService, 'admin_purchase_offers');
  }

  createDtoFromRequest(request: PurchaseOfferRequest): PurchaseOfferDto {
    return new PurchaseOfferDto(request);
  }

  createResponseFromDto(dto: PurchaseOfferDto): PurchaseOfferResponse {
    return new PurchaseOfferResponse(dto, {});
  }

  createResponseList(_list: ListResultset<PurchaseOfferDto>): PurchaseOffersListResponse {
    // Currently not using this, so prevents the compile issue.
    // TODO: Fix it so the general base controller can have a different list dto vs the base dto.
    return new PurchaseOffersListResponse({} as ListResultset<PurchaseOfferListDto>);
  }

  @Get('list')
  async getAllOffers(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOffersListResponse | ErrorResponse> {
    try {
      const params: any[] = await this.addAccountMapping(query, this.convertQueryToArray(req.url));

      const orderBy: Record<string, 'asc' | 'desc'> = { 'po."createdOn"': 'desc' };
      const items: ListResultset<PurchaseOfferListDto> = await this.handler.getAllOffers(page, size, params, orderBy);

      await this.mapUsernames(items);

      return new PurchaseOffersListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Get(':_offerId')
  async get(@Param('_offerId') offerId: string, @Req() req: VBInterceptorRequest): Promise<PurchaseOfferResponse | ErrorResponse> {
    try {
      const purchaseOffer: DetailedPurchaseOfferDto = req.attributes['_offer'];

      return new PurchaseOfferResponse(purchaseOffer);
    } catch (error) {
      return new ErrorResponse(error, 'getPurchaseOffer');
    }
  }

  @Patch(':_lotId/offer/:_offerId')
  async counterOffer(
    @Body() body: AdminPurchaseOfferRequest,
    @Param('_lotId') _lotId: string,
    @Param('_offerId') _offerId: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOfferResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'update',
      async () => {
        try {
          const dto: PurchaseOfferDto = this.createDtoFromRequest(body);
          const purchaseOffer: PurchaseOfferDto = req.attributes['_offer'];
          const lot: LotDto = req.attributes['_lot'];

          // Check to see if the auction lot is still available for placing an offer
          await this.checkAuctionLotStatus(lot, purchaseOffer);

          const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(lot.id);

          if (new Date(auctionLot.toDatetime) < new Date()) {
            throw new AuctionLotClosedError(lot.id, lot.title);
          }

          if (body.status === PurchaseOfferStatus.Rejected) {
            const offer: PurchaseOfferDto = await this.purchaseOffersService.setOfferStatus(purchaseOffer, req.accountMappingsId, PurchaseOfferStatus.Rejected);
            this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });
            return new PurchaseOfferResponse(offer);
          }

          if (body.status === PurchaseOfferStatus.Accepted) {
            const offer: PurchaseOfferDto = await this.purchaseOffersService.setItemSold(purchaseOffer, auctionLot, req.accountMappingsId, PurchaseOfferStatus.Accepted);
            this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });

            return new PurchaseOfferResponse(offer);
          }

          // Reject the previous offer and make a new counter offer
          await this.purchaseOffersService.setOfferStatus(purchaseOffer, req.accountMappingsId, PurchaseOfferStatus.Rejected);
          //create new offer
          const offer: PurchaseOfferDto = await this.handler.create(req.accountMappingsId, PurchaseOfferDto.createCounterOffer(purchaseOffer, dto, true));

          this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });
          return new PurchaseOfferResponse(offer);
        } catch (error) {
          if (error instanceof InvalidStatusError) {
            return new ErrorResponse(error, 'InvalidStatusError');
          }
          if (error instanceof AuctionLotClosedError) {
            return new ErrorResponse(error, 'AuctionLotClosedError');
          }
          return new ErrorResponse(error, 'counterPurchaseOffer');
        }
      },
      {},
    );
  }

  @Patch(':auctionId/reject-all-pending')
  async rejectAllPendingOffers(@Param('auctionId') auctionId: string, @Req() req: VBInterceptorRequest): Promise<PurchaseOffersListResponse | ErrorResponse> {
    try {
      // Get all pending offers for the auction
      const pendingOffers: ListResultset<PurchaseOfferDto> = await this.handler.getPendingOffersByAuctionId(auctionId);

      let rejectedCount: number = 0;
      const rejectedOffers: PurchaseOfferListDto[] = [];

      // Reject each pending offer
      for (const offer of pendingOffers.list) {
        const rejectedOffer: PurchaseOfferDto = await this.purchaseOffersService.setOfferStatus(offer, req.accountMappingsId, PurchaseOfferStatus.Rejected);
        rejectedOffers.push(new PurchaseOfferListDto(rejectedOffer));
        rejectedCount++;
      }

      // Create a new ListResultset with the rejected offers
      const resultset: ListResultset<PurchaseOfferListDto> = new ListResultset<PurchaseOfferListDto>(rejectedOffers, 1, rejectedCount, rejectedCount, 1);

      // Map usernames for the rejected offers
      await this.mapUsernames(resultset);

      return new PurchaseOffersListResponse(resultset);
    } catch (error) {
      return new ErrorResponse(error, 'rejectAllPendingOffers');
    }
  }

  /**
   * Check to see if the auction lot is still available for placing an offer
   * - check to see if the current offer is still pending
   * - check to see if another bidder has an offer in accepted status
   *
   * @param lot
   * @param purchaseOffer
   * @private
   */
  private async checkAuctionLotStatus(lot: LotDto, purchaseOffer: PurchaseOfferDto): Promise<void> {
    if (purchaseOffer.status != PurchaseOfferStatus.Pending) {
      throw new InvalidStatusError();
    }
    //check to see if someone else already has an accepted offer
    const params: any = [
      {
        itemMappingsId: purchaseOffer.itemMappingsId,
        status: PurchaseOfferStatus.Accepted,
        deletedOn: null,
      },
    ];
    try {
      await this.handler.getCustom(params, PurchaseOfferDto);
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.log(`Checking auction lot status; no offer accepted on lot ${lot.id}`);
      }
      //no other accepted offer exists
      return;
    }
    throw new ItemNotAvailableError(lot.id, lot.title);
  }

  private async mapUsernames(items: ListResultset<PurchaseOfferListDto>): Promise<void> {
    const accountMappingIds: string[] = items.list.map((item: PurchaseOfferListDto) => item.accountMappingsId);
    const userResponse: any = await this.usersApi.getAllByAccountMappingsIds(accountMappingIds.join(','), 1, -1);
    const usersResult: any = userResponse.data.data;
    const userMap: any = _.keyBy(usersResult, (value: any) => value.accountMappingsId);

    _.forEach(items.list, (value: any) => {
      value.username = userMap[value.accountMappingsId]?.username;
    });
  }

  protected convertQueryToArray(uri: string, additionalParams?: { [key: string]: any }): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    searchParams.delete('page');
    searchParams.delete('size');

    const statusType: string | null = searchParams.get('status');
    if (statusType) {
      searchParams.delete('status');
    }

    // If additionalParams is provided, add or override the parameters in the searchParams
    if (additionalParams) {
      for (const [key, value] of Object.entries(additionalParams)) {
        searchParams.set(key, value.toString()); // Use .set to add or override a parameter
      }
    }

    const array: any[] = [];
    const auctionsId: string | null = searchParams.get('auctionsId');
    if (auctionsId) {
      searchParams.delete('auctionsId');
      array.push({ 'a."id"': auctionsId });
    }

    const query: string | null = searchParams.get('query');
    if (query) {
      searchParams.delete('query');
      if (this.isQueryADate(query)) {
        array.push({ OR: [{ 'po."createdOn"': { contains: query } }] });
      } else if (!isNaN(Number(query))) {
        array.push({ OR: [{ 'al."number"': { contains: parseInt(query) } }] });
      } else {
        const orConditions: Record<string, any>[] = [{ 'a."name"': { contains: query, mode: 'insensitive' } }, { 'l."title"': { contains: query, mode: 'insensitive' } }];

        array.push({ OR: orConditions });
      }
    }

    searchParams.forEach((value: string, key: string) => {
      array.push({ [key]: value });
    });

    if (statusType) {
      array.push({ 'po."status"': statusType });
    }

    return array;
  }

  private isQueryADate(query: string): boolean {
    return Boolean(RegExp(/^\d{4}[/-]\d{2}[/-]\d{2}$/).exec(query) || RegExp(/^\d{2}[/-]\d{2}[/-]\d{4}$/).exec(query));
  }

  public async addAccountMapping(query: string, params: Array<Record<string, any>>): Promise<Array<Record<string, any>>> {
    const ids: Array<string> = (await this.usersApi.getAllUsers(1, -1, query)).data.data.map((result: any) => result?.accountMappingsId);
    const q: any = { 'po."accountMappingsId"': { in: ids.join(',') } };
    const idx: any = params.findIndex((v: any) => Object.keys(v)[0] == 'OR');
    params[idx]?.OR.push(q);

    return params;
  }
}
