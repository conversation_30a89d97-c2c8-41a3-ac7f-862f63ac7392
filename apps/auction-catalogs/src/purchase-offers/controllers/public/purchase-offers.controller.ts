import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseInterceptors } from '@nestjs/common';
import { PurchaseOffersHandler } from '../../handlers/purchase-offers.handler';
import { PurchaseOfferRequest } from '../../http/requests/purchase-offer.request';
import { PurchaseOfferDto } from '../../dtos/purchase-offer.dto';
import { PurchaseOffersListResponse } from '../../http/responses/purchase-offers-list.response';
import { PurchaseOfferResponse } from '../../http/responses/purchase-offer.response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { LotsHandler } from '../../../lots/handlers/lots.handler';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { ItemMappingDto } from '../../../item-mappings/dtos/item-mapping.dto';
import { ItemMappingsFactory } from '../../../item-mappings/factories/item-mappings.factory';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { PurchaseOfferStatus } from '../../../prisma/generated/auction-catalogs-db-client';
import { ItemMismatchException } from '../../exceptions/item-mismatch.error';
import { RedisService } from '@vb/redis';
import { LotStatus, UsersApi } from '@viewbid/ts-node-client';
import { UserDto } from '@vb/account-mappings/dtos/user.dto';
import { InvalidStatusError } from '../../exceptions/invalid-status.error';
import { PurchaseOfferListDto } from '../../dtos/purchase-offer-list.dto';
import { PurchaseOfferAlreadyExistError } from '../../exceptions/purchase-offer-already-exist-error';
import { SuccessResponse } from '@mvc/http/responses/success.response';
import { AuctionLotsHandler } from 'apps/auction-catalogs/src/auction-lots/handlers/auction-lots.handler';
import { AuctionLotDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot.dto';
import { LotDto } from '../../../lots/dtos/lot.dto';
import { ItemNotAvailableError } from '../../exceptions/item-not-available.error';
import { LoadEntitiesInterceptor } from '@mvc/interceptors/load-entities.interceptor';
import { PurchaseOffersService } from '../../services/purchase-offers.service';

@Controller('purchase-offers')
@UseInterceptors(LoadEntitiesInterceptor)
export class PurchaseOffersController extends PubSubController<PurchaseOffersHandler, PurchaseOfferRequest, PurchaseOfferDto, PurchaseOfferResponse, PurchaseOffersListResponse> {
  constructor(
    handler: PurchaseOffersHandler,
    private readonly lotsHandler: LotsHandler,
    private readonly itemMappingsFactory: ItemMappingsFactory,
    private readonly usersApi: UsersApi,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    redisService: RedisService,
    private readonly purchaseOffersService: PurchaseOffersService,
  ) {
    super(handler, redisService, 'purchase_offers');
  }

  createDtoFromRequest(request: PurchaseOfferRequest): PurchaseOfferDto {
    const dto: PurchaseOfferDto = new PurchaseOfferDto(request);
    dto.isAdmin = false;
    dto.status = dto.status ?? PurchaseOfferStatus.Pending;

    return dto;
  }

  createResponseFromDto(dto: PurchaseOfferDto): PurchaseOfferResponse {
    return new PurchaseOfferResponse(dto, {});
  }

  createResponseList(_list: ListResultset<PurchaseOfferDto>): PurchaseOffersListResponse {
    return new PurchaseOffersListResponse({} as ListResultset<PurchaseOfferListDto>);
  }

  @Post(':_lotId')
  async createOffer(@Body() body: PurchaseOfferRequest, @Param('_lotId') _lotId: string, @Req() req: VBInterceptorRequest): Promise<PurchaseOfferResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'create',
      async () => {
        try {
          const lot: LotDto = req.attributes['_lot'];
          const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(lot.id);

          // Check if the lot is closed or if the auctionLot's end date has passed
          if (lot.status === LotStatus.Complete || lot.status === LotStatus.Archived || (auctionLot.toDatetime && new Date(auctionLot.toDatetime) < new Date())) {
            throw new ItemNotAvailableError(lot.id, lot.title);
          }

          const dto: PurchaseOfferDto = this.createDtoFromRequest(body);

          const itemMapping: ItemMappingDto = await this.itemMappingsFactory.getItemMapping(lot);
          //check user has already made an offer on the lot
          await this.checkPurchaseOfferExists(itemMapping.id, req.accountMappingsId);

          dto.itemMappingsId = itemMapping.id;
          dto.accountMappingsId = req.accountMappingsId;

          const purchaseOffer: PurchaseOfferDto = await this.handler.create(req.accountMappingsId, dto);

          return new PurchaseOfferResponse(purchaseOffer);
        } catch (error) {
          return new ErrorResponse(error, 'createPurchaseOffer');
        }
      },
      {},
    );
  }

  @Patch(':_lotId/offer/:offerId')
  async counterOffer(
    @Body() body: PurchaseOfferRequest,
    @Param('_lotId') _lotId: string,
    @Param('offerId') offerId: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOfferResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'update',
      async () => {
        try {
          const dto: PurchaseOfferDto = this.createDtoFromRequest(body);
          const purchaseOffer: PurchaseOfferDto = await this.handler.get(offerId);
          const lot: LotDto = req.attributes['_lot'];
          await this.checkAuctionLotStatus(lot, purchaseOffer, req.accountMappingsId);

          const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(lot.id);

          if (dto.status === PurchaseOfferStatus.Rejected) {
            const offer: PurchaseOfferDto = await this.purchaseOffersService.setOfferStatus(purchaseOffer, req.accountMappingsId, dto.status);
            this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });
            return new PurchaseOfferResponse(offer);
          }

          if (dto.status === PurchaseOfferStatus.Accepted) {
            const offer: PurchaseOfferDto = await this.purchaseOffersService.setItemSold(purchaseOffer, auctionLot, req.accountMappingsId, PurchaseOfferStatus.Accepted);
            this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });

            return new PurchaseOfferResponse(offer);
          }
          // reject the previous offer
          await this.purchaseOffersService.setOfferStatus(purchaseOffer, req.accountMappingsId, PurchaseOfferStatus.Rejected);
          // make a new offer
          const offer: PurchaseOfferDto = await this.handler.create(req.accountMappingsId, PurchaseOfferDto.createCounterOffer(purchaseOffer, dto, false));
          this.notify('success', `${body.status.toString()}`, { data: { auctionLot, offer, lot } });

          return new PurchaseOfferResponse(offer);
        } catch (error) {
          return new ErrorResponse(error, 'counterPurchaseOffer');
        }
      },
      {},
    );
  }

  /**
   * Check to see if the auction lot is still available for placing an offer
   * - check to see if the current offer is still pending
   * - check to see if another bidder has an offer in accepted status
   *
   * @param lot
   * @param purchaseOffer
   * @private
   */
  private async checkAuctionLotStatus(lot: LotDto, purchaseOffer: PurchaseOfferDto, accountMappingsId: string): Promise<void> {
    if (purchaseOffer.accountMappingsId != accountMappingsId) {
      throw new ItemMismatchException();
    }
    if (purchaseOffer.status != PurchaseOfferStatus.Pending) {
      throw new InvalidStatusError();
    }

    //check to see if someone else already has an accepted offer
    const params: any = [
      {
        itemMappingsId: purchaseOffer.itemMappingsId,
        status: PurchaseOfferStatus.Accepted,
        deletedOn: null,
      },
    ];
    try {
      await this.handler.getCustom(params, PurchaseOfferDto);
    } catch (error: unknown) {
      // No other accepted offer exists, which is the expected case
      if (error instanceof Error) {
        this.logger.debug(`No accepted offer found: ${error.message}`);
      }
      return;
    }
    throw new ItemNotAvailableError(lot.id, lot.title);
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') _query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOffersListResponse | ErrorResponse> {
    try {
      const params: any[] = [{ 'po."accountMappingsId"': req.accountMappingsId }];
      const orderBy: Record<string, 'asc' | 'desc'> = { 'po."createdOn"': 'desc' };

      const items: ListResultset<PurchaseOfferListDto> = await this.handler.getAllOffers(page, size, params, orderBy);

      return new PurchaseOffersListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Get('checkExistingOffer/:lotId')
  async checkExistingOffer(@Param('lotId') lotId: string, @Req() req: VBInterceptorRequest): Promise<SuccessResponse | ErrorResponse> {
    try {
      const [lot] = await Promise.all([this.lotsHandler.get(lotId), this.getUser(req.accountMappingsId)]);
      const itemMapping: ItemMappingDto = await this.itemMappingsFactory.getItemMapping(lot);
      const purchaseOffer: PurchaseOfferResponse = await this.handler.getCustom([
        { itemMappingsId: itemMapping.id },
        { accountMappingsId: req.accountMappingsId },
        { status: { in: [PurchaseOfferStatus.Pending, PurchaseOfferStatus.Accepted] } },
      ]);
      return new SuccessResponse({ offerId: purchaseOffer.id }, 'checkPurchaseOfferExistOnLot', undefined, { exists: true });
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.debug(`No existing offer found: ${error.message}`);
      }
      return new SuccessResponse({}, 'checkPurchaseOfferExistOnLot', undefined, { exists: false });
    }
  }

  private async getUser(accountMappingsId: string): Promise<UserDto> {
    const result: any = await this.usersApi.getByAccountMappingId(accountMappingsId);
    return new UserDto(result.data.data.user);
  }

  private async checkPurchaseOfferExists(itemMappingId: string, accountMappingsId: string): Promise<void> {
    try {
      await this.handler.getCustom([{ itemMappingsId: itemMappingId }, { accountMappingsId: accountMappingsId }, { status: PurchaseOfferStatus.Pending }]);
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.debug(`No pending offer found: ${error.message}`);
      }
      return;
    }
    throw new PurchaseOfferAlreadyExistError();
  }
}
