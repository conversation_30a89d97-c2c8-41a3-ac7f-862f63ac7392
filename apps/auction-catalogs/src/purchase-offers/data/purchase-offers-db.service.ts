import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { PurchaseOfferDto } from '../dtos/purchase-offer.dto';
import { PurchaseOfferNotFoundError } from '../exceptions/purchase-offers-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ListResultset } from '@mvc/data/list-resultset';
import { PurchaseOfferListDto } from '../dtos/purchase-offer-list.dto';
import { DetailedPurchaseOfferDto } from '../dtos/detailed-purchase-offer.dto';
import { PurchaseOfferStatus } from '@viewbid/ts-node-client';

@Injectable()
export class PurchaseOffersDbService extends BaseDbService<Prisma.PurchaseOffersDelegate, PurchaseOfferDto> {
  constructor(private readonly prisma: PrismaService) {
    super(prisma.purchaseOffers);
  }

  mapToDto(model: any): PurchaseOfferDto {
    return new PurchaseOfferDto(model);
  }

  throw404(id: string): Error {
    throw new PurchaseOfferNotFoundError(id);
  }

  async getDetailed(id: string): Promise<DetailedPurchaseOfferDto> {
    const query: Prisma.Sql = Prisma.sql`
            select po.id, po."createdOn", po."accountMappingsId", po."itemMappingsId", po.quantity, po.status, po."isAdmin", po.message, po.price,
                   im."mappingType",
                   l.id as "lotId", l.title, l.condition
            from "PurchaseOffers" po
                     inner join "ItemMappings" im on im.id::uuid = po."itemMappingsId"::uuid
            inner join "Lots" l on l.id::uuid  = im."proxyId"`;

    const item: any = await this.prisma.$queryRaw(query);

    if (!item) {
      this.throw404(id);
    }

    return new DetailedPurchaseOfferDto(item[0]);
  }

  async getAllOffers(
    page: number,
    size: number,
    params: Record<string, any>[], // Pass params as an array of objects
    orderBy: Record<string, 'asc' | 'desc'> | undefined,
  ): Promise<ListResultset<PurchaseOfferListDto>> {
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;

    const { whereClause, whereValues } = this.buildWhereClause(params);
    const orderClause: string = orderBy
      ? 'ORDER BY ' +
        Object.entries(orderBy)
          .map(([key, direction]: [string, 'asc' | 'desc']) => `${key} ${direction}`)
          .join(', ')
      : '';
    const where: string = whereClause ? `WHERE ${whereClause}` : '';

    const queryStr: string = `
      SELECT po.id, po."createdOn", po."accountMappingsId", po."itemMappingsId", po.quantity, po.status, po."isAdmin", po.message, po.price,
             im."mappingType",
             l.id AS "lotId", l.title, l.condition, al.number as "lotNo", al."auctionId", al."currentBid" as "currentBidPrice", a.code as "auctionCode"
      FROM "PurchaseOffers" po
      INNER JOIN "ItemMappings" im ON im.id::uuid = po."itemMappingsId"::uuid
      INNER JOIN "Lots" l ON l.id::uuid = im."proxyId"
      INNER JOIN "AuctionLots" al ON al."lotId"::uuid = l."id"::uuid
      INNER JOIN "Auctions" a ON a.id::uuid = al."auctionId"::uuid
      ${where}
      ${orderClause}
      LIMIT ${take} OFFSET ${skip};
    `;

    const countQueryStr: string = `
      SELECT COUNT(*)
      FROM "PurchaseOffers" po
             INNER JOIN "ItemMappings" im ON im.id::uuid = po."itemMappingsId"::uuid
      INNER JOIN "Lots" l ON l.id::uuid = im."proxyId"
      INNER JOIN "AuctionLots" al ON al."lotId"::uuid = l."id"::uuid
      INNER JOIN "Auctions" a ON a.id::uuid = al."auctionId"::uuid
      ${where}`;

    const [items, totalCount]: [any, any] = await Promise.all([this.prisma.$queryRawUnsafe(queryStr, ...whereValues), this.prisma.$queryRawUnsafe(countQueryStr, ...whereValues)]);

    return new ListResultset(
      items.map((row: any) => new PurchaseOfferListDto(row)) as unknown as PurchaseOfferListDto[],
      page,
      size,
      Number(totalCount[0]?.count ?? 0), // Convert BigInt to Number
      Math.ceil(Number(totalCount[0]?.count ?? 0) / size), // Perform calculations with Number
    );
  }

  protected buildWhereClause(params: Record<string, any>[]): { whereClause: string; whereValues: any[] } {
    let whereClause: string = '';
    let whereValues: any[] = [];
    let paramIndex: number = 1;
    if (params == undefined || params.length < 1) return { whereClause, whereValues };

    params.forEach((condition: Record<string, any>) => {
      if (condition.OR && Array.isArray(condition.OR)) {
        ({ paramIndex, whereValues, whereClause } = this.handleOrConditions(condition, paramIndex, whereValues, whereClause));
      } else {
        ({ paramIndex, whereValues, whereClause } = this.handleAndConditions(condition, whereClause, paramIndex, whereValues));
      }
    });

    return { whereClause, whereValues };
  }

  private handleAndConditions(
    condition: Record<string, any>,
    whereClause: string,
    paramIndex: number,
    whereValues: any[],
  ): { whereClause: string; paramIndex: number; whereValues: any[] } {
    const [key, value]: [string, any] = Object.entries(condition)[0];
    if (value === null) {
      whereClause += `${whereClause ? ' AND ' : ''}${key} IS $${paramIndex}`;
      whereValues.push('NULL');
    } else if (key === 'po."status"') {
      whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}::"PurchaseOfferStatus"`;
      whereValues.push(value);
    } else {
      whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
      whereValues.push(value);
    }

    paramIndex++;
    return { whereClause, paramIndex, whereValues };
  }

  private handleOrConditions(
    condition: Record<string, any>,
    paramIndex: number,
    whereValues: any[],
    whereClause: string,
  ): { paramIndex: number; whereValues: any[]; whereClause: string } {
    let orConditions: string = '';
    condition.OR.forEach((orCondition: any) => {
      const [key, value]: [string, any] = Object.entries(orCondition)[0];
      if (key == 'po."createdOn"') {
        // String comparison, use ILIKE with wildcards
        orConditions += `${orConditions ? ' OR ' : ''}${key} between CAST($${paramIndex} AS DATE) and CAST($${paramIndex} AS DATE) + interval '1 day'`;
        whereValues.push(value.contains);
      } else if (key == 'al."number"') {
        orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}`;
        whereValues.push(value.contains);
      } else if (key == 'po."accountMappingsId"') {
        orConditions += `${orConditions ? ' OR ' : ''}${key} IN ($${paramIndex})`;
        whereValues.push(value.in);
      } else {
        // String comparison, use ILIKE with wildcards
        orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE $${paramIndex}`;
        whereValues.push(`%${value.contains}%`);
      }
      paramIndex++;
    });

    whereClause += `${whereClause ? ' AND ' : ''}(${orConditions})`;
    return { paramIndex, whereValues, whereClause };
  }

  async getPendingOffersByAuctionId(auctionId: string): Promise<ListResultset<PurchaseOfferDto>> {
    const queryStr: string = `
      SELECT po.id, po."createdOn", po."createdBy", po."updatedOn", po."updatedBy", po."deletedOn", po."deletedBy",
             po."accountMappingsId", po."itemMappingsId", po.quantity, po.status, po."isAdmin", po.message, po.price, po."resolvedOn"
      FROM "PurchaseOffers" po
      INNER JOIN "ItemMappings" im ON im.id::uuid = po."itemMappingsId"::uuid
      INNER JOIN "Lots" l ON l.id::uuid = im."proxyId"
      INNER JOIN "AuctionLots" al ON al."lotId"::uuid = l."id"::uuid
      WHERE al."auctionId"::uuid = $1::uuid
        AND po.status = $2::"PurchaseOfferStatus"
        AND po."deletedOn" IS NULL;
    `;

    const countQueryStr: string = `
      SELECT COUNT(*)
      FROM "PurchaseOffers" po
      INNER JOIN "ItemMappings" im ON im.id::uuid = po."itemMappingsId"::uuid
      INNER JOIN "Lots" l ON l.id::uuid = im."proxyId"
      INNER JOIN "AuctionLots" al ON al."lotId"::uuid = l."id"::uuid
      WHERE al."auctionId"::uuid = $1::uuid
        AND po.status = $2::"PurchaseOfferStatus"
        AND po."deletedOn" IS NULL;
    `;

    const [offers, totalCount]: [any, any] = await Promise.all([
      this.prisma.$queryRawUnsafe(queryStr, auctionId, PurchaseOfferStatus.Pending),
      this.prisma.$queryRawUnsafe(countQueryStr, auctionId, PurchaseOfferStatus.Pending),
    ]);

    const totalOffers: number = Number(totalCount[0]?.count ?? 0);

    return new ListResultset(offers.map((offer: any) => new PurchaseOfferDto(offer)) as unknown as PurchaseOfferDto[], 1, totalOffers, totalOffers, 1);
  }
}
