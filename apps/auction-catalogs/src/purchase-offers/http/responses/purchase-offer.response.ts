import { Response } from '@mvc/http/responses/response';
import { PurchaseOfferDto } from '../../dtos/purchase-offer.dto';
import { DetailedPurchaseOfferDto } from '../../dtos/detailed-purchase-offer.dto';

export class PurchaseOfferResponse extends Response {
  constructor(data: PurchaseOfferDto | DetailedPurchaseOfferDto, details?: object) {
    super(data, 'PurchaseOffer', data.id, details);
  }
}
