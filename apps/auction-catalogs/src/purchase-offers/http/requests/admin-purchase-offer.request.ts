import { IsDefined, IsOptional, IsString } from 'class-validator';
import { PurchaseOfferRequest } from './purchase-offer.request';

export class AdminPurchaseOfferRequest {
  itemMappingsId: string;

  @IsDefined()
  quantity: number;

  @IsDefined()
  status: any;

  @IsOptional()
  @IsString()
  message: string;

  @IsDefined()
  price: number;

  isAdmin: boolean = true;

  @IsOptional()
  resolvedOn: Date;

  public static fromPurchaseOfferRequest(offerRequest: PurchaseOfferRequest): AdminPurchaseOfferRequest {
    return { ...offerRequest } as AdminPurchaseOfferRequest;
  }
}
