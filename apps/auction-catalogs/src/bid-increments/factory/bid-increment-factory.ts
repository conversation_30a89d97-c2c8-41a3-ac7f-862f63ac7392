import { BidIncrementsHandler } from '../handler/bid-increments.handler';
import { BidIncrementDto } from '../dtos/bid-increment.dto';
import { Injectable, Logger } from '@nestjs/common';
import { ApplicationPreferencesHandler } from '../../application-preferences/handler/app-preferences.handler';
import { InvalidDefaultBidIncrementFormatError } from '../exceptions/invalid-default-bid-increment-format-error';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class BidIncrementFactory {
  private static readonly BID_INCREMENTS_KEY: string = 'DEFAULT_BID_INCREMENTS';

  private readonly logger: Logger = new Logger(BidIncrementFactory.name);

  constructor(
    private readonly handler: BidIncrementsHandler,
    private readonly appPreferencesHandler: ApplicationPreferencesHandler,
  ) {}

  async getNextIncrement(auctionId: string, currentBid: number): Promise<BidIncrementDto> {
    try {
      const bidIncrement: BidIncrementDto = await this.handler.getNextIncrement(auctionId, currentBid);
      return bidIncrement;
    } catch (error) {
      const defaultBidIncrements: ApplicationPreferencesDto = await this.appPreferencesHandler.getByKey(BidIncrementFactory.BID_INCREMENTS_KEY);

      const data: any[] = this.parseValueField(defaultBidIncrements.value);
      const value: BidIncrementItem | undefined = await this.findHighestUnder(currentBid, data);
      if (!value) {
        throw new InvalidDefaultBidIncrementFormatError(BidIncrementFactory.BID_INCREMENTS_KEY);
      }
      return new BidIncrementDto(value);
    }
  }
  async findHighestUnder(value: number, data: BidIncrementItem[]): Promise<BidIncrementItem | undefined> {
    const filteredData: BidIncrementItem[] = data
      .filter((item: BidIncrementItem) => item.priceAbove <= value)
      .sort((a: BidIncrementItem, b: BidIncrementItem) => b.priceAbove - a.priceAbove);
    return filteredData.length > 0 ? filteredData[0] : undefined;
  }

  parseValueField(value: string): any[] {
    // Replace single quotes with double quotes and remove the trailing commas
    const correctedJson: string = value.replace(/'/g, '"').replace(/,\s+]/, ']');

    try {
      const parsedValue: any[] = JSON.parse(correctedJson);
      return parsedValue;
    } catch (error) {
      throw new InvalidDefaultBidIncrementFormatError(BidIncrementFactory.BID_INCREMENTS_KEY);
    }
  }

  async getAllAuctionIncrementsByAuctionId(auctionId: string): Promise<ListResultset<BidIncrementDto>> {
    try {
      const result: ListResultset<BidIncrementDto> = await this.handler.getAllByAuctionId(auctionId);

      if (result.totalCount > 0) {
        result.list.sort((a: any, b: any) => a.priceAbove - b.priceAbove);
        return result;
      }
    } catch (error) {
      this.logger.error(error.message());
    }

    const defaultBidIncrements: ApplicationPreferencesDto = await this.appPreferencesHandler.getByKey(BidIncrementFactory.BID_INCREMENTS_KEY);

    const data: BidIncrementDto[] = this.parseValueField(defaultBidIncrements.value) as BidIncrementDto[];

    data.sort((a: any, b: any) => a.priceAbove - b.priceAbove);

    return new ListResultset<BidIncrementDto>(data, 1, data.length, data.length, 1);
  }
}
export interface BidIncrementItem {
  priceAbove: number;
  // Add other properties of items in the array if necessary
  increment: number;
}
