import { BidIncrementsHandler } from '../handler/bid-increments.handler';
import { BidIncrementRequest } from '../http/requests/bid-increment.request';
import { BidIncrementDto } from '../dtos/bid-increment.dto';
import { BidIncrementResponse } from '../http/responses/bid-increment.response';
import { BidIncrementListResponse } from '../http/responses/bid-increment-list.response';
import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Delete, Get, Param, Post, Query, Req, ValidationPipe } from '@nestjs/common';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { BidIncrementFactory } from '../factory/bid-increment-factory';
import { ApplicationPreferencesHandler } from '../../application-preferences/handler/app-preferences.handler';
import { ApplicationPreferencesDto } from '../../../../../libs/application-preferences/src/dtos/app-preferences-dto';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { RedisService } from '@vb/redis';
import { Cacheable } from '@vb/caching/decorators/cacheable.decorator';
import { BidIncrementListByIdResponse } from '../http/responses/bid-increment-list-by-id-response';

/**
 * usage:
 * This controller is for Administrative Crud only
 *
 * When you want to get the bid increment amount use this:
 *  const bidIncrement: BidIncrementDto | undefined = await this.factory.getNextIncrement(auctionId, currentBidAmount);
 *
 */
@Controller('bid-increments')
export class BidIncrementsController extends PubSubController<BidIncrementsHandler, BidIncrementRequest, BidIncrementDto, BidIncrementResponse, BidIncrementListResponse> {
  private static readonly DEFAULT_BID_INCREMENTS = 'DEFAULT_BID_INCREMENTS';

  constructor(
    handler: BidIncrementsHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly bidIncrementFactory: BidIncrementFactory,
    private readonly applicationPreferencesHandler: ApplicationPreferencesHandler,
    redisService: RedisService,
  ) {
    super(handler, redisService, 'bidincrements');
  }

  createDtoFromRequest(request: BidIncrementRequest): BidIncrementDto {
    return new BidIncrementDto(request);
  }

  createResponseFromDto(dto: BidIncrementDto): BidIncrementResponse {
    return new BidIncrementResponse(dto);
  }

  createResponseList(list: ListResultset<BidIncrementDto>): BidIncrementListResponse {
    return new BidIncrementListResponse(list);
  }

  createResponseListById(list: ListResultset<BidIncrementDto>, id: string): BidIncrementListByIdResponse {
    return new BidIncrementListByIdResponse(list, id);
  }

  @Post('/:auctionId')
  async createBidIncrement(@Param('auctionId') auctionId: string, @Body() body: any[]): Promise<BidIncrementResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'save',
      async () => {
        try {
          const auction: AuctionDto = await this.auctionsHandler.get(auctionId);

          const dtos: BidIncrementDto[] = this.createDtoArray(body, BidIncrementDto, { auctionId: auction.id });
          const items: ListResultset<BidIncrementDto> = await this.handler.createOrReplaceBulk(dtos, { auctionId: auction.id });

          return this.createResponseList(items);
        } catch (error) {
          return new ErrorResponse(error, 'create');
        }
      },
      {},
    );
  }

  @Get(':id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<BidIncrementResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(id);
      const params: any[] = this.convertQueryToArray(req.url, { auctionId: auction.id });
      const items: ListResultset<BidIncrementDto> = await this.handler.getAll(1, 100, params);

      return this.createResponseList(items);
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  @Delete('/:id')
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(id);
      //if it doesn't exist it will throw an error and get caught
      //to throw the error response
      await this.handler.get(id);

      await this.handler.deleteBulk(req.accountMappingsId, { auctionId: auction.id });
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  @Cacheable()
  @Get('getNextIncrement/:auctionId')
  async getNextIncrement(@Param('auctionId') auctionId: string, @Query('currentBid', ValidationPipe) currentBid: number): Promise<BidIncrementResponse> {
    try {
      const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionId, currentBid);

      return this.createResponseFromDto(bidIncrement);
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  @Cacheable()
  @Get('allBidIncrementsByAuctionId/:auctionId')
  async getAllByAuctionId(@Param('auctionId') auctionId: string): Promise<BidIncrementListByIdResponse | ErrorResponse> {
    try {
      const list: ListResultset<BidIncrementDto> = await this.bidIncrementFactory.getAllAuctionIncrementsByAuctionId(auctionId);
      return this.createResponseListById(list, auctionId);
    } catch (error) {
      return new ErrorResponse(error, 'getAllByAuctionId');
    }
  }

  @Get('defaultIncrements')
  async defaultIncrements(): Promise<BidIncrementListResponse | ErrorResponse> {
    try {
      const item: ApplicationPreferencesDto = await this.applicationPreferencesHandler.getByKey(BidIncrementsController.DEFAULT_BID_INCREMENTS);
      const list: Array<BidIncrementDto> = JSON.parse(item.value);

      return this.createResponseList(new ListResultset(list, 1, list.length, list.length, 1));
    } catch (error) {
      return new ErrorResponse(error, 'defaultIncrements');
    }
  }
}
