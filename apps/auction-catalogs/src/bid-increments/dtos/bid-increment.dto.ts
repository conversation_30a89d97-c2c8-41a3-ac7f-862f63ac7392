export class BidIncrementDto {
  id: string;
  auctionId: string;
  priceAbove: number;
  increment: number;

  constructor(row: any) {
    this.id = row.id;
    this.auctionId = row.auctionId;
    this.priceAbove = row.priceAbove;
    this.increment = row.increment;
  }

  static build(row: any): BidIncrementDto {
    const body: any = row.data;
    return new BidIncrementDto({
      id: body.id,
      auctionId: body.auctionId,
      priceAbove: body.priceAbove,
      increment: body.increment,
    });
  }
}
