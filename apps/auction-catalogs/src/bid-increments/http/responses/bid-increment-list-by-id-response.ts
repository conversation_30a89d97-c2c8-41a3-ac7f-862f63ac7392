import { ListResultset } from '@mvc/data/list-resultset';
import { Response } from '@mvc/http/responses/response';

export class BidIncrementListByIdResponse extends Response {
  constructor(resultSet: ListResultset<any>, id: string) {
    const details: { resource: string; id: string } = {
      resource: 'bidIncrementListById',
      id: id,
    };
    super(resultSet.list, details.resource, id, details);
  }
}
