import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { BidIncrementsDbService } from '../data/bid-increments-db.service';
import { BidIncrementDto } from '../dtos/bid-increment.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class BidIncrementsHandler extends BaseHandler<BidIncrementsDbService, BidIncrementDto> {
  constructor(dbService: BidIncrementsDbService) {
    super(dbService);
  }
  async getNextIncrement(auctionId: string, amount: number): Promise<BidIncrementDto> {
    return await this.dbService.getNextIncrement(auctionId, amount);
  }

  async getAllByAuctionId(auctionId: string): Promise<ListResultset<BidIncrementDto>> {
    const params: Record<string, any>[] = [{ auctionId: auctionId }];
    const result: ListResultset<BidIncrementDto> = await this.dbService.getAllCustom(1, -1, params, BidIncrementDto);

    return result as unknown as ListResultset<BidIncrementDto>;
  }
}
