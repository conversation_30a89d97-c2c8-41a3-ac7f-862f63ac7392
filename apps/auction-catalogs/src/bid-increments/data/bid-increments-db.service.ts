import { BaseDbService } from '@mvc/data/base-db.service';
import { BidIncrementDto } from '../dtos/bid-increment.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { BidIncrementNotFoundError } from '../exceptions/bid-increment-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BidIncrementsDbService extends BaseDbService<Prisma.BidIncrementsDelegate, BidIncrementDto> {
  constructor(prisma: PrismaService) {
    super(prisma.bidIncrements, false, false, false);
  }

  createFilter(params: any[]): any {
    const auctionIdParam: string = params.find((param: string) => param.hasOwnProperty('auctionId'))?.auctionId;

    return {
      auctionId: {
        equals: auctionIdParam,
      },
    };
  }

  mapToDto(model: any): BidIncrementDto {
    return new BidIncrementDto(model);
  }

  throw404(id: string): Error {
    return new BidIncrementNotFoundError(id);
  }

  async getNextIncrement(auctionId: string, amount: number): Promise<BidIncrementDto> {
    const row: any = await this.table.findFirst({
      where: {
        AND: [
          {
            auctionId: {
              equals: auctionId,
            },
          },
          {
            priceAbove: {
              lt: amount,
            },
          },
        ],
      },
      orderBy: {
        priceAbove: 'desc',
      },
    });

    if (!row) {
      throw new BidIncrementNotFoundError(auctionId);
    }
    return new BidIncrementDto(row);
  }

  getOrderBy(): any {
    return {
      priceAbove: 'asc',
    };
  }
}
