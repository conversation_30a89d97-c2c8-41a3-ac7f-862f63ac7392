import { Modu<PERSON> } from '@nestjs/common';
import { BidIncrementsHandler } from './handler/bid-increments.handler';
import { BidIncrementsDbService } from './data/bid-increments-db.service';
import { BidIncrementsController } from './controller/bid-increments.controller';
import { PrismaService } from '../prisma/prisma.service';
import { AuctionModule } from '../auction/auction.module';
import { BidIncrementFactory } from './factory/bid-increment-factory';
import { ApplicationPreferencesModule } from '../application-preferences/app-preferences.module';
@Module({
  controllers: [BidIncrementsController],
  exports: [BidIncrementsHandler, BidIncrementsDbService, BidIncrementFactory],
  providers: [BidIncrementsHandler, BidIncrementsDbService, PrismaService, BidIncrementFactory],
  imports: [AuctionModule, ApplicationPreferencesModule],
})
export class BidIncrementsModule {}
