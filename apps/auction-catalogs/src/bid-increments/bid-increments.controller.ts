import { Controller, Get, Post, Patch, Delete } from '@nestjs/common';

@Controller('bid-increments')
export class BidIncrementsController {
  constructor() {}

  @Post()
  create(/*@Body() createBidIncrementDto: CreateBidIncrementDto*/): any {
    //     return this.bidIncrementsService.create(createBidIncrementDto);
  }

  @Get()
  findAll(): any {
    //     return this.bidIncrementsService.findAll();
  }

  @Get(':id')
  findOne(/*@Param('id') id: string*/): any {
    //     return this.bidIncrementsService.findOne(+id);
  }

  @Patch(':id')
  update(/*@Param('id') id: string, @Body() updateBidIncrementDto: UpdateBidIncrementDto*/): any {
    //     return this.bidIncrementsService.update(+id, updateBidIncrementDto);
  }

  @Delete(':id')
  remove(/*@Param('id') id: string*/): any {
    //     return this.bidIncrementsService.remove(+id);
  }
}
