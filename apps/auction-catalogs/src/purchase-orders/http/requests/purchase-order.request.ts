import { Type } from 'class-transformer';
import { IsString, IsDefined, IsDate, IsNumber, ValidateIf, IsArray, IsUUID, IsIn } from 'class-validator';
import { ShippingMethod } from 'apps/auction-catalogs/src/prisma/generated/auction-catalogs-db-client';

export class PurchaseOrderRequest {
  @IsIn(Object.values(ShippingMethod))
  shippingMethod: string;
  @IsString()
  @IsDefined()
  shippingTerms: string;
  @IsDate()
  @Type(() => Date)
  deliveryDate: Date;
  @IsNumber()
  @ValidateIf((object: any, value: any) => value !== null)
  subTotal: number | null;
  @IsNumber()
  @ValidateIf((object: any, value: any) => value !== null)
  tax: number | null;
  @IsNumber()
  @ValidateIf((object: any, value: any) => value !== null)
  other: number | null;
  @IsString()
  @IsDefined()
  comments: string;
  @IsUUID()
  vendorsId: string;
  @IsArray()
  @IsUUID(undefined, { each: true })
  auctionIds: string[];
}
