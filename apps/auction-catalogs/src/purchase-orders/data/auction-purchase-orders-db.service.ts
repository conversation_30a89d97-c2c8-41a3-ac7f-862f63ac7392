import { BaseDbService } from '@mvc/data/base-db.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { AuctionPurchaseOrderDto } from '../dtos/auction-purchase-order.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { AuctionPurchaseOrderNotFoundError } from '../exceptions/auction-purchase-order-not-found.error';

@Injectable()
export class AuctionPurchaseOrdersDbService extends BaseDbService<Prisma.AuctionPurchaseOrdersDelegate<DefaultArgs>, AuctionPurchaseOrderDto> {
  constructor(prisma: PrismaService) {
    super(prisma.auctionPurchaseOrders, true, false, true);
  }

  mapToDto(model: any): AuctionPurchaseOrderDto {
    return new AuctionPurchaseOrderDto(model);
  }

  throw404(id: string): Error {
    return new AuctionPurchaseOrderNotFoundError(id);
  }

  createFilter(params: any[]): any {
    const auctionsId: string | undefined = params.find((r: any) => r.hasOwnProperty('auctionsId'))?.auctionsId;
    const purchaseOrdersId: string | undefined = params.find((r: any) => r.hasOwnProperty('purchaseOrdersId'))?.purchaseOrdersId;

    return { auctionsId, purchaseOrdersId };
  }
}
