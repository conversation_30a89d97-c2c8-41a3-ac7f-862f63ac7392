import { Injectable } from '@nestjs/common';
import { DefaultArgs } from 'prisma/prisma-client/runtime/library';
import { BaseDbService } from '@mvc/data/base-db.service';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PurchaseOrderDto } from '../dtos/purchase-order.dto';
import { PurchaseOrderNotFoundError } from '../exceptions/purchase-order-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class PurchaseOrdersDbService extends BaseDbService<Prisma.PurchaseOrdersDelegate<DefaultArgs>, PurchaseOrderDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.purchaseOrders);
  }

  async getAll(page: number, size: number, params: any[]): Promise<ListResultset<PurchaseOrderDto>> {
    const filter: any = this.createFilter(params);
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const limit: number | Prisma.Sql = size > 0 ? size : Prisma.sql`ALL`;

    const purchaseOrders: PurchaseOrderDto[] = (
      await this.prismaService.$queryRaw<any[]>`
    SELECT ROW_TO_JSON(po.*) as "purchaseOrder",
           COALESCE(ARRAY_AGG(a.id) 
           FILTER (WHERE a.id IS NOT NULL AND a."deletedOn" IS NULL), '{}'::text[]) as "auctionIds"
    FROM public."PurchaseOrders" po
    JOIN public."Vendors" v ON po."vendorsId" = v.id
    LEFT JOIN public."AuctionPurchaseOrders" apo ON po."id" = apo."purchaseOrdersId"
    LEFT JOIN public."Auctions" a ON a."id" = apo."auctionsId"
    WHERE po."deletedOn" IS NULL AND v."deletedOn" IS NULL
    ${filter}
    OFFSET ${skip} LIMIT ${limit};`
    ).map((row: any) => this.mapToExtendedDto(row));

    const totalCount: number =
      (
        await this.prismaService.$queryRaw<any[]>`
    SELECT SUM("count")::int AS "totalCount" FROM (
      SELECT COUNT(DISTINCT po.id) as "count"
      FROM public."PurchaseOrders" po
      JOIN public."Vendors" v ON po."vendorsId" = v.id
      LEFT JOIN public."AuctionPurchaseOrders" apo ON po."id" = apo."purchaseOrdersId"
      LEFT JOIN public."Auctions" a ON a."id" = apo."auctionsId"
      WHERE po."deletedOn" IS NULL AND v."deletedOn" IS NULL
      ${filter});`
      )[0]?.totalCount ?? 0;

    return new ListResultset(purchaseOrders, page, size, totalCount, Math.ceil(totalCount / size));
  }

  mapToExtendedDto(row: any): PurchaseOrderDto {
    const po: any = this.mapToDto(row.purchaseOrder);
    po.auctionIds = row.auctionIds;

    return po;
  }

  mapToDto(model: any): PurchaseOrderDto {
    return new PurchaseOrderDto(model);
  }

  createFilter(params: any[]): any {
    const vendorsId: string | undefined = params.find((r: any) => r.hasOwnProperty('vendorsId'))?.vendorsId;
    const auctionsId: string | undefined = params.find((r: any) => r.hasOwnProperty('auctionsId'))?.auctionsId;

    const vendorsSql: Prisma.Sql = vendorsId ? Prisma.sql`AND v.id = ${vendorsId}::uuid` : Prisma.empty;
    const auctionsSql: Prisma.Sql = auctionsId ? Prisma.sql`HAVING ${auctionsId}=ANY(ARRAY_AGG(a.id))` : Prisma.empty;

    return Prisma.sql`${vendorsSql} GROUP BY po.id ${auctionsSql}`;
  }

  throw404(id: string): Error {
    throw new PurchaseOrderNotFoundError(id);
  }
}
