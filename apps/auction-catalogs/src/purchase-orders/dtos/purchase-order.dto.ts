import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

function castIfDecimal(value: any): any {
  return value instanceof Prisma.Decimal ? value.toNumber() : value;
}

export class PurchaseOrderDto {
  id: string;
  shippingMethod: string;
  shippingTerms: string;
  deliveryDate: Date;
  subTotal?: number;
  tax?: number;
  other?: number;
  total?: number;
  comments: string;
  vendorsId: string;

  constructor(row: any) {
    this.id = row.id;
    this.shippingMethod = row.shippingMethod;
    this.shippingTerms = row.shippingTerms;
    this.deliveryDate = row.deliveryDate;
    this.subTotal = castIfDecimal(row.subTotal);
    this.tax = castIfDecimal(row.tax);
    this.other = castIfDecimal(row.other);
    this.total = castIfDecimal(row.total);
    this.comments = row.comments;
    this.vendorsId = row.vendorsId;
  }
}
