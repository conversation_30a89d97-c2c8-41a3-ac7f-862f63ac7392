import { forwardRef, Module } from '@nestjs/common';
import { PurchaseOrdersDbService } from './data/purchase-orders-db.service';
import { PurchaseOrdersHandler } from './handler/purchase-orders.handler';
import { PurchaseOrdersController } from './controller/admin/purchase-orders.controller';
import { AuctionModule } from '../auction/auction.module';
import { VendorsModule } from '../vendors/vendors.module';
import { AuctionPurchaseOrdersDbService } from './data/auction-purchase-orders-db.service';
import { AuctionPurchaseOrdersHandler } from './handler/auction-purchase-orders.handler';

@Module({
  controllers: [PurchaseOrdersController],
  providers: [PurchaseOrdersDbService, PurchaseOrdersHandler, AuctionPurchaseOrdersDbService, AuctionPurchaseOrdersHandler],
  exports: [PurchaseOrdersHandler],
  imports: [forwardRef(() => AuctionModule), forwardRef(() => VendorsModule)],
})
export class PurchaseOrdersModule {}
