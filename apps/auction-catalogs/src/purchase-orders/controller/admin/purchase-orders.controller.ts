import { BaseController } from '@mvc/controllers/base.controller';
import { PurchaseOrdersHandler } from '../../handler/purchase-orders.handler';
import { PurchaseOrderRequest } from '../../http/requests/purchase-order.request';
import { PurchaseOrderDto } from '../../dtos/purchase-order.dto';
import { PurchaseOrderResponse } from '../../http/responses/purchase-order.response';
import { PurchaseOrderListResponse } from '../../http/responses/purchase-order-list.response';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { Body, Controller, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { AuctionPurchaseOrdersHandler } from '../../handler/auction-purchase-orders.handler';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { AuctionPurchaseOrderDto } from '../../dtos/auction-purchase-order.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { VendorsHandler } from '../../../vendors/handler/vendors.handler';
import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { AuctionDto } from '../../../auction/dtos/auction.dto';

@Controller('admin/purchase-orders')
export class PurchaseOrdersController extends BaseController<PurchaseOrdersHandler, PurchaseOrderRequest, PurchaseOrderDto, PurchaseOrderResponse, PurchaseOrderListResponse> {
  constructor(
    handler: PurchaseOrdersHandler,
    private auctionsHandler: AuctionsHandler,
    private auctionPurchaseOrdersHandler: AuctionPurchaseOrdersHandler,
    private vendorsHandler: VendorsHandler,
  ) {
    super(handler);
  }

  @Post('/')
  async create(@Body() body: PurchaseOrderRequest, @Req() req: VBInterceptorRequest): Promise<PurchaseOrderResponse | ErrorResponse> {
    try {
      await this.queryAuctions(body.auctionIds);
      const purchaseOrder: any = await this.handler.create(req.accountMappingsId, this.createDtoFromRequest(body));
      await this.upsertAuctionPurchaseOrders(body.auctionIds, purchaseOrder.id);

      purchaseOrder.auctionIds = body.auctionIds;

      return this.createResponseFromDto(purchaseOrder);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: PurchaseOrderRequest, @Req() req: VBInterceptorRequest): Promise<PurchaseOrderResponse | ErrorResponse> {
    try {
      await this.queryAuctions(body.auctionIds);
      const purchaseOrder: any = await this.handler.update(req.accountMappingsId, this.createDtoFromRequest(body), id);
      await this.upsertAuctionPurchaseOrders(body.auctionIds, purchaseOrder.id);

      purchaseOrder.auctionIds = body.auctionIds;
      return this.createResponseFromDto(purchaseOrder);
    } catch (error) {
      return new ErrorResponse(error, 'update', id);
    }
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<PurchaseOrderResponse | ErrorResponse> {
    try {
      req;
      const purchaseOrder: any = await this.handler.get(id);

      const auctionPurchaseOrders: ListResultset<AuctionPurchaseOrderDto> = await this.auctionPurchaseOrdersHandler.getAll(1, -1, [{ purchaseOrdersId: purchaseOrder.id }]);
      purchaseOrder.auctionIds = auctionPurchaseOrders.list.map((apo: AuctionPurchaseOrderDto) => apo.auctionsId);

      return this.createResponseFromDto(purchaseOrder);
    } catch (error) {
      return new ErrorResponse(error, 'get', id);
    }
  }

  @Get('/')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOrderListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const vendorsId: string | undefined = params.find((r: any) => r.hasOwnProperty('vendorsId'))?.vendorsId;
      const auctionsId: string | undefined = params.find((r: any) => r.hasOwnProperty('auctionsId'))?.vendorsId;

      if (vendorsId) {
        await this.vendorsHandler.get(vendorsId);
      }

      if (auctionsId) {
        await this.auctionsHandler.get(auctionsId);
      }
      const purchaseOrders: ListResultset<PurchaseOrderDto> = await this.handler.getAll(page, size, params);

      return this.createResponseList(purchaseOrders);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  createDtoFromRequest(request: PurchaseOrderRequest): PurchaseOrderDto {
    let total: number | null = null;
    if (request.subTotal !== null || request.tax !== null || request.other !== null) {
      total = (request.subTotal ?? 0) + (request.tax ?? 0) + (request.other ?? 0);
    }

    return new PurchaseOrderDto({
      ...request,
      total,
    });
  }

  createResponseFromDto(dto: PurchaseOrderDto): PurchaseOrderResponse {
    return new PurchaseOrderResponse(dto);
  }

  createResponseList(list: ListResultset<PurchaseOrderDto>): PurchaseOrderListResponse {
    return new PurchaseOrderListResponse(list);
  }

  async queryAuctions(auctionIds: string[]): Promise<void> {
    const foundAuctionIds: string[] = (
      await this.auctionsHandler.getAll(1, -1, [
        {
          id: {
            in: auctionIds,
          },
        },
      ])
    ).list.map((a: AuctionDto) => a.id);

    if (foundAuctionIds.length != auctionIds.length) {
      const missingIds: string[] = auctionIds.filter((id: string) => !foundAuctionIds.includes(id));

      throw new ViewbidError(`Error while updating PO. The following Auction ids were not found: ${missingIds}`, 404);
    }
  }

  async upsertAuctionPurchaseOrders(auctionIds: string[], purchaseOrdersId: string): Promise<void> {
    await this.auctionPurchaseOrdersHandler.createOrReplaceBulk(
      auctionIds.map(
        (auctionsId: string) =>
          new AuctionPurchaseOrderDto({
            purchaseOrdersId: purchaseOrdersId,
            auctionsId: auctionsId,
          }),
      ),
      { purchaseOrdersId },
    );
  }
}
