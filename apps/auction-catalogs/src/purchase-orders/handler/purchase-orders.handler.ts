import { BaseHandler } from '@mvc/handlers/base.handler';
import { PurchaseOrdersDbService } from '../data/purchase-orders-db.service';
import { PurchaseOrderDto } from '../dtos/purchase-order.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PurchaseOrdersHandler extends BaseHandler<PurchaseOrdersDbService, PurchaseOrderDto> {
  constructor(dbService: PurchaseOrdersDbService) {
    super(dbService);
  }
}
