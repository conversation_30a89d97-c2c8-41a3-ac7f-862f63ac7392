import { BaseHandler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { AuctionPurchaseOrdersDbService } from '../data/auction-purchase-orders-db.service';
import { AuctionPurchaseOrderDto } from '../dtos/auction-purchase-order.dto';

@Injectable()
export class AuctionPurchaseOrdersHandler extends BaseHandler<AuctionPurchaseOrdersDbService, AuctionPurchaseOrderDto> {
  constructor(dbService: AuctionPurchaseOrdersDbService) {
    super(dbService);
  }
}
