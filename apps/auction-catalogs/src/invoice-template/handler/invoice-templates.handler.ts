import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { InvoiceTemplateDto } from '../dtos/invoice-template.dto';
import { InvoiceTemplatesDbService } from '../data/invoice-templates-db.service';

@Injectable()
export class InvoiceTemplatesHandler extends BaseHandler<InvoiceTemplatesDbService, InvoiceTemplateDto> {
  constructor(dbService: InvoiceTemplatesDbService) {
    super(dbService);
  }
}
