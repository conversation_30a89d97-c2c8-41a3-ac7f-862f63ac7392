import { InvoiceTemplateErrorCodes } from '@mvc/exceptions/viewbid-error';
import { NotFoundViewbidError } from '@mvc/exceptions/not-found-viewbid.error';

export class InvoiceTemplateNotFoundError extends NotFoundViewbidError {
  constructor(id: string) {
    super('Requested invoice template not found', InvoiceTemplateErrorCodes.INVOICE_TEMPLATE_NOT_FOUND, id, '/invoiceTepmlates#invoiceTemplateNotFound');
  }
}
