import { InvoiceTemplateRequest as ParentTemplateRequest } from '@viewbid/ts-node-client';
import { IsNotEmpty, IsString } from 'class-validator';

export class InvoiceTemplateRequest implements ParentTemplateRequest {
  @IsNotEmpty()
  @IsString()
  filename: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsString()
  thumbnail: string;

  @IsNotEmpty()
  @IsString()
  html: string;
}
