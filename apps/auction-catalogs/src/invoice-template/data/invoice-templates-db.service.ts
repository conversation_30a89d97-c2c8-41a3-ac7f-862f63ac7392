import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoiceTemplateDto } from '../dtos/invoice-template.dto';
import { InvoiceTemplateNotFoundError } from '../exceptions/invoice-template-not-found-error';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class InvoiceTemplatesDbService extends BaseDbService<Prisma.InvoiceTemplatesDelegate, InvoiceTemplateDto> {
  constructor(prisma: PrismaService) {
    super(prisma.invoiceTemplates);
  }

  mapToDto(model: any): InvoiceTemplateDto {
    return new InvoiceTemplateDto(model);
  }

  throw404(id: string): Error {
    throw new InvoiceTemplateNotFoundError(id);
  }
}
