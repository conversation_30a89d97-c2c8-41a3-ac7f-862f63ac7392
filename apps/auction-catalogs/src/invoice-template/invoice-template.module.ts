import { Lo<PERSON>, <PERSON><PERSON><PERSON>, forwardRef } from '@nestjs/common';
import { InvoiceTemplateController } from './controller/invoice-template.controller';
import { InvoiceTemplatesDbService } from './data/invoice-templates-db.service';
import { InvoiceTemplatesHandler } from './handler/invoice-templates.handler';
import { UploadsModule } from '../uploads/uploads.module';
import { SharedServicesModule } from 'libs/shared_services/shared.services.module';
@Module({
  controllers: [InvoiceTemplateController],
  exports: [InvoiceTemplateController],
  providers: [InvoiceTemplateController, InvoiceTemplatesDbService, InvoiceTemplatesHandler, Logger],
  imports: [forwardRef(() => UploadsModule), SharedServicesModule],
})
export class InvoiceTemplateModule {}
