import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { Controller } from '@nestjs/common';
import { InvoiceTemplateDto } from '../dtos/invoice-template.dto';
import { InvoiceTemplatesHandler } from '../handler/invoice-templates.handler';
import { InvoiceTemplateRequest } from '../http/requests/invoice-template.requests';
import { InvoiceTemplateListResponse } from '../http/responses/invoice-template-list.response';
import { InvoiceTemplateResponse } from '../http/responses/invoice-template.response';

@Controller('invoiceTemplate')
export class InvoiceTemplateController extends BaseController<
  InvoiceTemplatesHandler,
  InvoiceTemplateRequest,
  InvoiceTemplateDto,
  InvoiceTemplateResponse,
  InvoiceTemplateListResponse
> {
  constructor(invoiceTemplatesHandler: InvoiceTemplatesHandler) {
    super(invoiceTemplatesHandler);
  }

  createDtoFromRequest(request: InvoiceTemplateRequest): InvoiceTemplateDto {
    return new InvoiceTemplateDto(request);
  }

  createResponseFromDto(dto: InvoiceTemplateDto): InvoiceTemplateResponse {
    return new InvoiceTemplateResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceTemplateDto>): InvoiceTemplateListResponse {
    return new InvoiceTemplateListResponse(list, 'invoiceTemplates');
  }
}
