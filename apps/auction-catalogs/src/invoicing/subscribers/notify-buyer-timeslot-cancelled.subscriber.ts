import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { TimeslotDto } from '../dtos/timeslots/timeslot.dto';
import { TimeslotCancelledNotificationParametersDto } from '../dtos/notifications/timeslot-cancelled-notification.parameters.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';

@Injectable()
export class NotifyBuyerTimeslotCancelledSubscriber implements SubscriberInterface {
  private readonly logger = new Logger(NotifyBuyerTimeslotCancelledSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly awsSqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly buyersHandler: InvoiceBuyersHandler,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(_eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      this.logger.log('sending TIMESLOT_CANCELLED_NOTIFICATION');
      const invoice: InvoiceDto = new InvoiceDto(eventPayload.items.invoice);
      const eventKey: string = 'TIMESLOT_CANCELLED_NOTIFICATION';
      const timeslot: TimeslotDto = new TimeslotDto(eventPayload.items.timeslot);
      const buyer: InvoiceBuyerDto = await this.buyersHandler.getCustom([{ invoicesId: invoice.id }]);

      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([new AccountMappingCommonDto('', '', '', invoice.bidderId, '', '')]),
        new TimeslotCancelledNotificationParametersDto(
          buyer,
          timeslot,
          invoice,
          `${this.baseUrl}/profiles/my-profile/view-invoice/${invoice.id}`,
          this.formatDate(new Date(timeslot.pickupTime)),
          new Date().toISOString(),
        ),
      );

      this.awsSqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      if (error instanceof Error) {
        this.logger.debug(`Error while notifying buyer timeslot cancelled: ${error.message}`);
      }
      this.logger.error(`Error while notifying buyer of timeslot cancelled for id ${eventPayload.items.id}`);
    }
  }

  formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    };

    const formattedDate: string = date.toLocaleDateString('en-CA', { year: 'numeric', month: '2-digit', day: '2-digit' });
    const formattedTime: string = date.toLocaleTimeString('en-US', options);

    return `${formattedDate}, ${formattedTime}`;
  }
}
