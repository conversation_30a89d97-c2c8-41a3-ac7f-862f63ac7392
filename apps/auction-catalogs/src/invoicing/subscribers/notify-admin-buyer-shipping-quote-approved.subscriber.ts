import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { ShippingQuiteBuyerResponseAdminDto } from '../dtos/notifications/shipping-quote-buyer-response-admin.dto';

@Injectable()
export class NotifyAdminBuyerShippingQuoteApprovedSubscriber implements SubscriberInterface {
  private readonly logger = new Logger(NotifyAdminBuyerShippingQuoteApprovedSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly awsSqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(_eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      this.logger.log('sending SHIPPING_QUOTE_APPROVED_ADMIN_NOTIFICATION');
      const invoice: InvoiceDto = new InvoiceDto(eventPayload.items.data.invoice);
      const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]);
      const eventKey: string = 'SHIPPING_QUOTE_APPROVED_ADMIN_NOTIFICATION';

      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([]),
        new ShippingQuiteBuyerResponseAdminDto(
          buyer.firstname,
          buyer.lastname,
          buyer.email,
          String(invoice.number),
          `${this.baseUrl}/admin/invoices/${invoice.id}`,
          String(new Date()),
        ),
      );

      this.awsSqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`Error while notifying buyer of shipping quote for id ${eventPayload.items.id}`);
      }
    }
  }
}
