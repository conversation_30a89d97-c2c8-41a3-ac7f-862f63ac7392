import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { InvoiceShippingNotificationParameters } from '../dtos/notifications/invoice-shipping-notification-parameters.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';

@Injectable()
export class NotifyBuyerShippingQuoteApprovedSubscriber implements SubscriberInterface {
  private readonly logger = new Logger(NotifyBuyerShippingQuoteApprovedSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly awsSqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(_eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      this.logger.log('sending SHIPPING_QUOTE_NOTIFICATION');
      const invoice: InvoiceDto = new InvoiceDto(eventPayload.items.data.invoice);
      const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]);
      const eventKey: string = 'SHIPPING_QUOTE_NOTIFICATION';

      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([new AccountMappingCommonDto('', '', '', invoice.bidderId, '', '')]),
        new InvoiceShippingNotificationParameters(buyer.firstname, buyer.lastname, `${this.baseUrl}/profiles/my-profile/view-invoice/${invoice.id}`),
      );

      this.awsSqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.debug(`Error while notifying buyer of shipping quote: ${error.message}`);
      }
      this.logger.error(`Error while notifying buyer of shipping quote for id ${eventPayload.items.id}`);
    }
  }
}
