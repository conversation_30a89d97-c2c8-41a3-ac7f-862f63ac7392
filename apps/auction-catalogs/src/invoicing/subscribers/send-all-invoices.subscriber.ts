import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { EventType, EventPayload } from '@vb/nest/events/event-type';
import { InvoicesHandler } from '../handlers/invoices.handler';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceDto } from '../dtos/invoice.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { AdminAuctionListDto } from '../../auction/dtos/admin-auction-list.dto';

@Injectable()
export class SendAllInvoicesSubscriber implements SubscriberInterface {
  private logger = new Logger(SendAllInvoicesSubscriber.name);

  public constructor(
    private sqsService: AwsSqsService,
    private invoicesHandler: InvoicesHandler,
  ) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const pageSize: number = 2;
    let currentPage: number = 1;
    let hasMoreInvoices: boolean = true;
    eventType;
    try {
      const eventKey: string = 'ADMIN_AUCTION_INVOICES_APPROVED_NOTIFICATION';
      const auction: AuctionDto = new AuctionDto(eventPayload.items);

      // Loop through pagination, fetching invoices in each iteration
      while (hasMoreInvoices) {
        const invoices: ListResultset<InvoiceDto> = await this.invoicesHandler.getAll(currentPage, pageSize, [{ auctionsId: auction.id }]);

        // If no more invoices, stop the loop
        if (invoices.list.length === 0) {
          hasMoreInvoices = false;
          break;
        }
        const accountMappings: AccountMappingCommonListDto = this.buildAccountMappings(invoices.list);
        const message: Message = MessageFactory.build(eventKey, accountMappings, new AdminAuctionListDto(auction));
        await this.sqsService.sendMessage(JSON.stringify(message), 'notifications');

        this.logger.log(`Processed page ${currentPage} of invoices for auction ${auction.id}`);

        currentPage++;
      }
    } catch (error) {
      this.logger.error(`Error sending auction invoice notification: ${error}`);
    }
  }

  /**
   * Builds AccountMappingCommonListDto from an array of InvoiceDto.
   *
   * @param invoices Array of InvoiceDto
   * @returns AccountMappingCommonListDto
   */
  private buildAccountMappings(invoices: InvoiceDto[]): AccountMappingCommonListDto {
    const accountMappings: AccountMappingCommonDto[] = invoices.map((invoice: InvoiceDto) => {
      return new AccountMappingCommonDto('', '', '', invoice.bidderId, '', '');
    });

    return new AccountMappingCommonListDto(accountMappings);
  }
}
