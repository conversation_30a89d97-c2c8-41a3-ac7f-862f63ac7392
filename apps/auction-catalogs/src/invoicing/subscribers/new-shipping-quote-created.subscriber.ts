import { Injectable } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';

@Injectable()
export class NewShippingQuoteCreatedSubscriber implements SubscriberInterface {
  constructor(private awsSqsService: AwsSqsService) {}
  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const eventKey: string = 'ADMIN_NEW_SHIPPING_QUOTE_CREATED_NOTIFICATION';

    const message: Message = MessageFactory.build(eventKey, new AccountMappingCommonListDto([]), new InvoiceShippingDto(eventPayload.items));

    this.awsSqsService.sendMessage(JSON.stringify(message), 'notifications');
  }
}
