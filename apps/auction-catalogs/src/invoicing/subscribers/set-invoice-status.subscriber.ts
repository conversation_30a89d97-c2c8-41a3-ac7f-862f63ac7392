import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { InvoiceStatusesHandler } from '../handlers/invoice-statuses.handler';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { InvoiceStatusDto } from '../dtos/invoice-status.dto';
import { Injectable, Logger } from '@nestjs/common';
import { InvoiceStatusFactory } from '../factories/invoice-status.factory';

@Injectable()
export class SetInvoiceStatusSubscriber implements SubscriberInterface {
  private logger = new Logger(SetInvoiceStatusSubscriber.name);

  constructor(
    private readonly invoiceStatusHandler: InvoiceStatusesHandler,
    private invoiceStatusFactory: InvoiceStatusFactory,
  ) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    this.logger.log('setting invoice status ' + eventType);
    try {
      const statuses: Array<InvoiceStatusDto> = (await this.invoiceStatusFactory.determineInvoiceStatus(eventType, eventPayload)).statuses;
      this.invoiceStatusHandler.createOrReplaceBulk(statuses);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
