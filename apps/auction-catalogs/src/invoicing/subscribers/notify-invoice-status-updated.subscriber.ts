import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { ConfigService } from '@vb/config/src/config.service';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { InvoiceStatusUpdateNotificationDto } from '../dtos/notifications/invoice-status-update-notification.dto';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { InvoiceStatusDto } from '../dtos/invoice-status.dto';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { Message } from '@vb-utils/messaging/dtos/message';
import { InvoiceStatusesResult, InvoiceStatusFactory } from '../factories/invoice-status.factory';

@Injectable()
export class NotifyInvoiceStatusUpdatedSubscriber implements SubscriberInterface, OnModuleInit {
  private readonly logger = new Logger(NotifyInvoiceStatusUpdatedSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
    private readonly invoiceStatusFactory: InvoiceStatusFactory,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      const invoiceStatusesResult: InvoiceStatusesResult = await this.invoiceStatusFactory.determineInvoiceStatus(eventType, eventPayload);
      const statuses: Array<InvoiceStatusDto> = invoiceStatusesResult.statuses;
      const invoices: Array<InvoiceDto> = invoiceStatusesResult.invoices;

      const statusMap: Map<string, InvoiceStatusDto> = new Map(statuses.map((status: InvoiceStatusDto) => [status.invoicesId, status]));
      await Promise.all(
        invoices.map(async (invoice: InvoiceDto) => {
          const matchingStatus: InvoiceStatusDto | undefined = statusMap.get(invoice.id);
          if (matchingStatus) {
            await this.sendNotificaition(invoice, matchingStatus).catch((error: Error) =>
              this.logger.error(`Error sending shipping request notification for invoice ${invoice.id}: ${error.message}`),
            );
          } else {
            this.logger.error(`InvoiceStatusNotFoundError: No status found for invoice ID ${invoice.id}`);
          }
        }),
      );
    } catch (error) {
      this.logger.error(`Error caught in NotifyInvoiceStatusUpdatedSubscriber: ${error}`);
    }
  }

  async sendNotificaition(invoice: InvoiceDto, status: InvoiceStatusDto): Promise<void> {
    const eventKey: string = 'INVOICE_STATUS_UPDATED_NOTIFICATION';
    const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]);
    let updatedBy: string = 'seller';
    // added check for buyer-initiated statuses
    if (status.statusType === 'REQUIRES_SHIPPING_QUOTE' || status.statusType === 'AWAITING_ASSEMBLY') {
      updatedBy = `${buyer.firstname} ${buyer.lastname}`;
    }
    const message: Message = MessageFactory.build(
      eventKey,
      new AccountMappingCommonListDto([]),
      new InvoiceStatusUpdateNotificationDto(
        this.toFormatStatus(status.statusType),
        updatedBy,
        String(invoice.number),
        `${this.baseUrl}/admin/invoices/${invoice.id}`,
        String(new Date()),
      ),
    );

    this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
  }

  toFormatStatus(input: string): string {
    return input
      .toLowerCase()
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (char: string) => char.toUpperCase());
  }
}
