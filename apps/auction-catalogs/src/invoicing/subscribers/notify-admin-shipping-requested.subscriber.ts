import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { ShippingRequestNotificationDto } from '../dtos/notifications/shipping-request-notification.dto';
import { ConfigService } from '@vb/config/src/config.service';

@Injectable()
export class NotifyAdminShippingRequestedSubscriber implements SubscriberInterface, OnModuleInit {
  private readonly logger = new Logger(NotifyAdminShippingRequestedSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(_eventType: EventType, payload: EventPayload): Promise<void> {
    try {
      const eventKey: string = 'ADMIN_SHIPPING_REQUEST_NOTIFICATION';
      // Get current date and time
      const now: Date = new Date();
      const year: number = now.getFullYear();
      const month: string = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
      const day: string = String(now.getDate()).padStart(2, '0');
      const hours: string = String(now.getHours()).padStart(2, '0');
      const minutes: string = String(now.getMinutes()).padStart(2, '0');
      const seconds: string = String(now.getSeconds()).padStart(2, '0');

      // Format date and time
      const currentDate: string = `${year}-${month}-${day}`;
      const currentTime: string = `${hours}:${minutes}:${seconds}`;

      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([]),
        new ShippingRequestNotificationDto(
          payload.items.data.buyer,
          currentDate,
          currentTime,
          `${this.baseUrl}/admin/invoices/${payload.items.data.invoice.id}`,
          payload.items.data.invoice.number,
        ),
      );

      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending shipping request notification: ${error}`);
    }
  }
}
