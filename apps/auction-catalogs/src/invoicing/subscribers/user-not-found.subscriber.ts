import { Injectable, Logger } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { UserNotFoundParametersDto } from '@vb-utils/messaging/dtos/user-not-found-parameters.dto';

@Injectable()
export class UserNotFoundSubscriber implements SubscriberInterface {
  private readonly logger = new Logger(UserNotFoundSubscriber.name);

  constructor(private readonly sqsService: AwsSqsService) {}

  async handleEvent(_eventType: EventType, payload: EventPayload): Promise<void> {
    try {
      const eventKey: string = 'USER_NOT_FOUND';
      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([]),
        new UserNotFoundParametersDto({
          accountMappingsId: payload.items.accountMappingsId,
          applicationSection: payload.items.applicationSection,
          timestamp: new Date().toISOString(),
        }),
      );
      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending shipping request notification: ${error}`);
    }
  }
}
