import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { InvoicePaymentNotificationParametersDto } from '../dtos/notifications/invoice-payment-notification-parameters.dto';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { InvoicesHandler } from '../handlers/invoices.handler';

@Injectable()
export class PaymentCompleteNotificationSubscriber implements SubscriberInterface, OnModuleInit {
  private logger = new Logger(PaymentCompleteNotificationSubscriber.name);
  private baseUrl: string;

  constructor(
    private sqsService: AwsSqsService,
    private configService: ConfigService,
    private invoiceBuyersHandler: InvoiceBuyersHandler,
    private invoicesHandler: InvoicesHandler,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(eventType: EventType, payload: EventPayload): Promise<void> {
    try {
      const eventKey: string = 'USER_PAID_IN_FULL_NOTIFICATION';
      const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: payload.items.invoice.id }]);
      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([]),
        new InvoicePaymentNotificationParametersDto(
          payload.items.invoice.id,
          payload.items.invoice.number,
          payload.items.invoicePayment.paymentTypes,
          payload.items.invoicePayment.paymentAmount,
          payload.items.invoice.amountDue,
          new Date().toISOString(),
          buyer.firstname,
          buyer.lastname,
          `${this.baseUrl}/admin/invoices/${payload.items.invoice.id}`,
        ),
      );

      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending shipping request notification: ${error}`);
    }
  }
}
