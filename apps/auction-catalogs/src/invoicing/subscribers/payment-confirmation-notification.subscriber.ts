import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { PaymentConfirmationNotificationParametersDto } from '../dtos/notifications/payment-confirmation-notification-parameters.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';

@Injectable()
export class PaymentConfirmationNotificationSubscriber implements SubscriberInterface, OnModuleInit {
  private readonly logger = new Logger(PaymentConfirmationNotificationSubscriber.name);
  private baseUrl: string;

  constructor(
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
  ) {}

  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
  }

  async handleEvent(_eventType: EventType, payload: EventPayload): Promise<void> {
    try {
      const eventKey: string = 'PAYMENT_CONFIRMATION_NOTIFICATION';
      const invoice: InvoiceDto = new InvoiceDto(payload.items.invoice?.[0]);
      const invoicePayment: InvoicePaymentDto = new InvoicePaymentDto(payload.items.invoicePayment);
      const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]);

      const message: Message = MessageFactory.build(
        eventKey,
        new AccountMappingCommonListDto([new AccountMappingCommonDto('', '', '', buyer.accountMappingsId, '', '')]),
        new PaymentConfirmationNotificationParametersDto(
          buyer.firstname,
          buyer.lastname,
          this.formatAmount(invoicePayment.amountPaid),
          this.formatDate(new Date()),
          this.formatPaymentType(invoicePayment.paymentTypes.toString()),
          String(invoice.number),
          invoicePayment.paymentTypes.toString() === 'Cash' ? 'N/A' : invoicePayment.transactionCode,
          `${this.baseUrl}/profiles/my-profile/view-invoice/${invoice.id}`,
          this.formatAmount(invoicePayment.currentOwing),
        ),
      );

      this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
    } catch (error) {
      this.logger.error(`Error sending shipping request notification: ${error}`);
    }
  }

  formatPaymentType(input: string): string {
    return input
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, (char: string) => char.toUpperCase())
      .trim();
  }

  formatAmount(value: number): string {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
  }

  formatDate(date: Date): string {
    const formatter: Intl.DateTimeFormat = new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });

    return formatter.format(date);
  }
}
