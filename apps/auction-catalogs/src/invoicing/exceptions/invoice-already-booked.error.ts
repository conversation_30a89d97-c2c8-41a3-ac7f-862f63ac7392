import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { InvoiceDto } from '../dtos/invoice.dto';
import { TimeslotDto } from '../dtos/timeslots/timeslot.dto';
import { Constants } from '../config/constants';

export class InvoiceAlreadyBookedError extends ViewbidError {
  constructor(invoice: InvoiceDto, timeslot: TimeslotDto) {
    super(`A booking already exists at ${timeslot.pickupTime} for invoice ${invoice.number}`, Constants.INVOICE_ALREADY_BOOKED_ERROR, '', '/timeslots#invoice-already-booked');
  }
}
