import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';
import { InvoiceDto } from '../dtos/invoice.dto';

export class ShippingInvoiceAlreadyExistsError extends ViewbidError {
  constructor(invoice: InvoiceDto) {
    super(`A shipping invoice already exists for invoice ${invoice.number}`, Constants.SHIPPING_INVOICE_ALREADY_EXISTS, invoice.id, '/shipping-invoices#shipping-invoice-exists');
  }
}
