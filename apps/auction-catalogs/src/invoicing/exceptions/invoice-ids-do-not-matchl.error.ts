import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';
import { InvoiceDto } from '../dtos/invoice.dto';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';

export class InvoiceIdsDoNotMatch extends ViewbidError {
  constructor(invoice: InvoiceDto, invoiceShipping: InvoiceShippingDto) {
    super(
      `The invoice ID in the invoice record does not match the ID in the invoice shipping record. Invoice record ID: ${invoice.id}, Invoice shipping record ID: ${invoiceShipping.id}.`,
      Constants.INOICE_IDS_DO_NOT_MATCH,
      invoice.id,
      '/shipping-invoices#shipping-not-found',
    );
  }
}
