import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';
import { InvoiceDto } from '../dtos/invoice.dto';

export class ShippingInvoiceNotFoundError extends ViewbidError {
  constructor(invoice: InvoiceDto) {
    super(`A shipping invoice was not found for invoice id ${invoice.id}`, Constants.SHIPPING_NOT_FOUND_ERROR, invoice.id, '/shipping-invoices#shipping-not-found');
  }
}
