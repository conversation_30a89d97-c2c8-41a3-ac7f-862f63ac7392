import { NotFoundViewbidError } from '@mvc/exceptions/not-found-viewbid.error';
import { Constants } from '../config/constants';

export class SplittableAuctionItemsNotFoundError extends NotFoundViewbidError {
  constructor(invoiceNumber: string) {
    super(
      `No splittable auction items found for invoiceId: ${invoiceNumber}`,
      Constants.SPLITTABLE_AUCTION_ITEMS_NOT_FOUND,
      `${invoiceNumber}`,
      'auctions#splittable-auction-items-not-found',
    );
  }
}
