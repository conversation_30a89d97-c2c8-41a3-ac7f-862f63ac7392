import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';
import { PaymentType } from '@viewbid/ts-node-client';

export class UnsupportedPaymentTypeError extends ViewbidError {
  constructor(paymentType: PaymentType) {
    super(`${paymentType} is an unsupported payment type publically`, Constants.UNSUPPORTED_PAYMENT_TYPE, '', '/invoice-payment#unsupported-payment-type');
  }
}
