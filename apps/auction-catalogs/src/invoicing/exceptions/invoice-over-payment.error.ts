import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { Constants } from '../config/constants';

export class InvoiceOverPaymentError extends ViewbidError {
  constructor(invoice: InvoiceDto, invoicePayment: InvoicePaymentDto) {
    super(
      `Received payment of $${invoicePayment.amountPaid} exceeds remaining amount of $${invoice.amountDue}`,
      Constants.INVOICE_OVERPAYMENT_ERROR,
      '',
      '/invoices#invoices-overpayment',
    );
  }
}
