import { PaymentType } from '@viewbid/ts-node-client';
import { IsDate, IsDefined, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class InvoicePaymentRequest {
  @IsString()
  @IsDefined()
  @IsNotEmpty()
  invoicesId: string;

  @IsDefined()
  @IsNotEmpty()
  paymentTypes: PaymentType;

  @IsDefined()
  @IsNotEmpty()
  @IsNumber()
  amountPaid: number;

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  customerPaymentProfileId: string;

  @IsOptional()
  transactionCode: string;

  @IsOptional() // Make the field optional
  @IsDate() // Validate that it is a valid Date object
  @Type(() => Date) // Transform the input into a Date object
  paymentDate: Date;

  @IsOptional()
  @IsString()
  location: string;

  constructor() {
    // Set paymentDate to today's date if it is not provided
    if (!this.paymentDate) {
      this.paymentDate = new Date();
    }
  }
}
