import { IsNotEmpty, IsNumber, IsPositive, IsString, Min, IsOptional } from 'class-validator';

export class InvoiceLineItemRequest {
  @IsNumber()
  @IsPositive()
  quantity: number;

  @IsNumber()
  price: number;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  @Min(0)
  buyerPremiumAmount: number;

  @IsNumber()
  @Min(0)
  tax: number;

  @IsString()
  @IsOptional()
  serialNumber?: string;
}
