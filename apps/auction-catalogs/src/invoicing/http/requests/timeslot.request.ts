import { Is<PERSON><PERSON>, <PERSON><PERSON>ptional, IsNotEmpty, IsBoolean, IsEmail } from 'class-validator';

export class TimeslotRequest {
  @IsNotEmpty()
  pickupTime: string;

  @IsOptional()
  @IsString()
  staffId: string;

  @IsOptional()
  status: any;

  @IsOptional()
  index: number;

  @IsOptional()
  @IsBoolean()
  isProxyPickup: boolean;

  @IsOptional()
  @IsString()
  proxyName: string;

  @IsOptional()
  @IsEmail()
  proxyEmail: string;
}
