import { Response } from '@mvc/http/responses/response';
import { ListResultset } from '@mvc/data/list-resultset';
import { ShippingVendorDto } from '../../dtos/shipping-vendor.dto';
import { InvoiceSplitDetailDto } from '../../dtos/invoice-split-detail.dto';

export class InvoiceResponse extends Response {
  constructor(data: any, shippingVendorsList?: ListResultset<ShippingVendorDto>, relatedInvoices?: InvoiceSplitDetailDto[]) {
    const shippingVendors: ShippingVendorDto[] | undefined = shippingVendorsList?.list;
    super({ ...data, shippingVendors, relatedInvoices }, 'Invoice', data.invoice.id, {});
  }
}
