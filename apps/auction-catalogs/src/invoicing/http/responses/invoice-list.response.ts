import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceListDto } from '../../dtos/invoice-list.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';

export class InvoiceListResponse extends ListResponse {
  constructor(resultSet: ListResultset<InvoiceDto | InvoiceListDto>) {
    super(resultSet, 'Invoices', {});
  }
}
