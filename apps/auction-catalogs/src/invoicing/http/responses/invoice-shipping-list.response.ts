import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceShippingDto } from '../../dtos/shipping/invoice-shipping.dto';
import { InvoiceShippingListDto } from '../../dtos/shipping/invoice-shipping-list.dto';

export class InvoiceShippingListResponse extends ListResponse {
  constructor(resultSet: ListResultset<InvoiceShippingDto | InvoiceShippingListDto>) {
    super(resultSet, 'invoiceShippingList', {});
  }
}
