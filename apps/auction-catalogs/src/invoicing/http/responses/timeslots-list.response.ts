import { ListResponse } from '@mvc/http/responses/list-response';
import { ListResultset } from '@mvc/data/list-resultset';
import { TimeslotAvailabilityListDto } from '../../dtos/timeslots/timeslot-availability-list.dto';
import { TimeslotDto } from '../../dtos/timeslots/timeslot.dto';
import { AdminTimeslotListDto } from '../../dtos/timeslots/admin-timeslot-list.dto';

export class TimeslotsListResponse extends ListResponse {
  constructor(list: ListResultset<TimeslotAvailabilityListDto | TimeslotDto | AdminTimeslotListDto>) {
    super(list, 'TimeslotsListResponse');
  }
}
