import { Response } from '@mvc/http/responses/response';
import { InvoicePullerDto } from '../../dtos/invoice-puller.dto';
import { InvoicePullerDetailedDto } from '../../dtos/invoice-puller-detailed.dto';

export class InvoicePullerResponse extends Response {
  constructor(data: InvoicePullerDto | InvoicePullerDetailedDto, details?: object) {
    super(data, 'invoicePuller', data.id, details);
  }
}
