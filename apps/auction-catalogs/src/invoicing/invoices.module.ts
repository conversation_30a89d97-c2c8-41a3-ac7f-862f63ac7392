import { Module } from '@nestjs/common';

import { InvoicesController as AdminInvoicesController } from './controllers/admin/invoices.controller';
import { InvoicesDbService } from './data/invoices-db.service';
import { InvoicesHandler } from './handlers/invoices.handler';
import { InvoiceBuyersDbService } from './data/invoice-buyers-db.service';
import { InvoiceBuyersHandler } from './handlers/invoice-buyers.handler';
import { InvoiceItemsDbService } from './data/invoice-items-db.service';
import { InvoiceItemsHandler } from './handlers/invoice-items.handler';
import { InvoicesShippingAddressesDbService } from './data/invoices-shipping-addresses-db.service';
import { InvoicesShippingAddressesHandler } from './handlers/invoices-shipping-addresses.handler';
import { AuctionsDbService } from '../auction/data/auctions-db.service';
import { AuctionsHandler } from '../auction/handlers/auctions.handler';
import { ItemMappingsDbService } from '../item-mappings/data/item-mappings-db.service';
import { ItemMappingsHandler } from '../item-mappings/handler/item-mappings.handler';
import { InvoiceFactory } from './factories/invoice.factory';
import { InvoiceStatusesDbService } from './data/invoice-statuses-db.service';
import { InvoiceStatusesHandler } from './handlers/invoice-statuses.handler';
import { InvoicesController } from './controllers/public/invoices.controller';
import { InvoiceShippingDbService } from './data/invoice-shipping-db.service';
import { InvoiceShippingHandler } from './handlers/invoice-shipping.handler';
import { InvoiceShippingController as AdminInvoiceShippingController } from './controllers/admin/invoice-shipping.controller';
import { TrackingNumbersDbService } from './data/tracking-numbers-db.service';
import { TrackingNumbersHandler } from './handlers/tracking-numbers.handler';
import { TrackingNumbersController } from './controllers/admin/tracking-numbers.controller';
import { ShippingVendorsDbService } from './data/shipping-vendors-db.service';
import { ShippingVendorsHandler } from './handlers/shipping-vendors.handler';
import { InvoicePaymentsDbService } from './data/invoice-payments-db.service';
import { InvoicePaymentsHandler } from './handlers/invoice-payments.handler';
import { InvoicePaymentsController as AdminInvoicePaymentsController } from './controllers/admin/invoice-payments.controller';
import { InvoicePaymentsController } from './controllers/public/invoice-payments.controller';
import { InvoiceNotesHandler } from './handlers/invoice-notes.handler';
import { InvoiceNotesDbService } from './data/invoice-notes-db.service';
import { AdminInvoiceNotesController } from './controllers/admin/invoice-notes.controller';
import { PublicInvoiceNotesController } from './controllers/public/invoice-notes.controller';
import { AwsSqsService } from '../sqs/aws.sqs.service';
import { InvoicePullersController } from './controllers/admin/invoice-pullers.controller';
import { InvoicePullersDbService } from './data/invoice-pullers-db.service';
import { InvoicePullersHandler } from './handlers/invoice-pullers.handler';
import { TimeslotsController as AdminTimeslotsController } from './controllers/admin/timeslots.controller';
import { TimeslotsController } from './controllers/public/timeslots.controller';
import { TimeslotsDbService } from './data/timeslots-db.service';
import { TimeslotsHandler } from './handlers/timeslots.handler';
import { InvoiceItemsController } from './controllers/admin/invoice-items.controller';
import { AuctionLotsModule } from '../auction-lots/auction-lots.module';
import { VendorProductsDbService } from '../products/data/vendor-products-db.service';
import { ShippingVendorsController } from './controllers/admin/shipping-vendors.controller';
import { PaymentProcessingModule } from '../payment-processing/payment-processing.module';
import { InvoiceShippingController } from './controllers/public/invoice-shipping.controller';
import { InvoicePickupBinsController } from './controllers/admin/invoice-pickup-bins.controller';
import { InvoicePickupBinsDbService } from './data/invoice-pickup-bins-db.service';
import { InvoicePickupBinsHandler } from './handlers/invoice-pickup-bins.handler';
import { InvoiceItemTaxesDbService } from './data/invoice-item-taxes-db.service';
import { TaxTypeDbService } from './data/tax-types-db.service';
import { TaxTypeHandler } from './handlers/tax-type.handler';
import { InvoiceItemTaxesHandler } from './handlers/invoice-item-taxes.handler';
import { InvoiceStatusFactory } from './factories/invoice-status.factory';
import { InvoiceShippingTaxesDbService } from './data/invoice-shipping-taxes-db.service';
import { InvoiceShippingTaxesHandler } from './handlers/invoice-shipping-taxes.handler';
import { SharedModule } from '../shared/shared.module';
import { InvoiceSplitsDbService } from './data/invoice-splits-db.service';
import { InvoiceSplitsHandler } from './handlers/invoice-splits.handler';
import { PdfService } from '@vb/pdf';
import { TemplatesModule } from '../lot-templates/templates.module';
import { InvoiceTemplatesDbService } from '../invoice-template/data/invoice-templates-db.service';
import { InvoiceTemplatesHandler } from '../invoice-template/handler/invoice-templates.handler';
import { S3Service } from '../aws/s3/data/s3.service';
import { ApplicationPreferencesModule } from '../application-preferences/app-preferences.module';
import { DateFilterService } from '@vb/nest/services/date-filter.service';
import { LotAttributesModule } from '../lot-attributes/lot-attributes.module';
import { LotAttributesHandler } from '../lot-attributes/handler/lot-attributes.handler';

@Module({
  controllers: [
    InvoicesController,
    AdminInvoicesController,
    AdminInvoicePaymentsController,
    AdminInvoiceShippingController,
    AdminTimeslotsController,
    InvoiceItemsController,
    InvoicePaymentsController,
    InvoicePullersController,
    InvoiceShippingController,
    TrackingNumbersController,
    AdminInvoiceNotesController,
    PublicInvoiceNotesController,
    TimeslotsController,
    ShippingVendorsController,
    InvoicePickupBinsController,
  ],
  providers: [
    AuctionsDbService,
    AuctionsHandler,
    AwsSqsService,
    InvoiceBuyersDbService,
    InvoiceBuyersHandler,
    InvoiceItemTaxesHandler,
    InvoiceItemTaxesDbService,
    InvoiceTemplatesDbService,
    InvoiceTemplatesHandler,
    TaxTypeDbService,
    TaxTypeHandler,
    InvoiceFactory,
    InvoiceStatusFactory,
    InvoiceItemsHandler,
    InvoiceItemsDbService,
    InvoicePullersDbService,
    InvoicePullersHandler,
    InvoicesDbService,
    InvoicesHandler,
    InvoiceNotesDbService,
    InvoiceNotesHandler,
    InvoicePaymentsDbService,
    InvoicePaymentsHandler,
    InvoicesShippingAddressesDbService,
    InvoicesShippingAddressesHandler,
    InvoiceShippingDbService,
    InvoiceShippingHandler,
    InvoiceSplitsDbService,
    InvoiceSplitsHandler,
    InvoiceStatusesDbService,
    InvoiceStatusesHandler,
    ItemMappingsDbService,
    ItemMappingsHandler,
    PdfService,
    S3Service,
    ShippingVendorsDbService,
    ShippingVendorsHandler,
    TrackingNumbersDbService,
    TrackingNumbersHandler,
    TimeslotsDbService,
    TimeslotsHandler,
    VendorProductsDbService,
    InvoicePickupBinsDbService,
    InvoicePickupBinsHandler,
    InvoiceShippingTaxesDbService,
    InvoiceShippingTaxesHandler,
    DateFilterService,
    LotAttributesHandler,
  ],
  imports: [AuctionLotsModule, PaymentProcessingModule, SharedModule, TemplatesModule, ApplicationPreferencesModule, LotAttributesModule],
  exports: [InvoicesHandler, TaxTypeHandler],
})
export class InvoicesModule {}
