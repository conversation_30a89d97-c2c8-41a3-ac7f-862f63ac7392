import { Injectable, Logger } from '@nestjs/common';
import { InvoiceDto } from '../dtos/invoice.dto';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { InvoiceItemDto } from '../dtos/items/invoice-item.dto';
import { ItemMappingsHandler } from '../../item-mappings/handler/item-mappings.handler';
import { InvoicesHandler } from '../handlers/invoices.handler';
import { InvoiceItemsHandler } from '../handlers/invoice-items.handler';
import { InvoiceBuyersHandler } from '../handlers/invoice-buyers.handler';
import { UserBillingDetailDto } from '../../../../users/src/users/dtos/user-billing-detail.dto';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { TaxTypeHandler } from '../handlers/tax-type.handler';
import { TaxTypeDto } from '../dtos/tax-type.dto';
import { TaxType } from '@vb/nest/enums/taxTypes';
import { ListResultset } from '@mvc/data/list-resultset';
import { TaxTypeNotFoundError } from '../exceptions/tax-type-not-found.error';
import { InvoiceItemTaxDto } from '../dtos/items/invoice-item-tax.dto';
import { InvoiceItemTaxesHandler } from '../handlers/invoice-item-taxes.handler';
import { InvoiceDetailDto } from '../dtos/invoice-detail.dto';
import { InvoiceShippingHandler } from '../handlers/invoice-shipping.handler';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';
import { InvoiceSplitDto } from '../dtos/invoice-split.dto';
import { InvoiceSplitsHandler } from '../handlers/invoice-splits.handler';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { AuctionLotFlattenedDto } from '../../auction-lots/dto/auction-lot-flattened.dto';

@Injectable()
export class InvoiceFactory {
  private readonly logger = new Logger(InvoiceFactory.name);

  constructor(
    private readonly itemMappingsHandler: ItemMappingsHandler,
    private readonly taxTypesHandler: TaxTypeHandler,
    private readonly invoicesHandler: InvoicesHandler,
    private readonly invoiceItemsHandler: InvoiceItemsHandler,
    private readonly invoiceItemsTaxesHandler: InvoiceItemTaxesHandler,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
    private readonly invoiceShippingHandler: InvoiceShippingHandler,
    private readonly invoiceSplitsHandler: InvoiceSplitsHandler,
    private readonly auctionLotsHandler: AuctionLotsHandler,
  ) {}

  /**
   * Split an invoice into two separate invoices
   *
   * @param userId
   * @param invoiceId
   * @param auction
   * @param userBillingDetail
   * @param selectedInvoiceItems
   */
  async splitInvoice(
    userId: string,
    invoiceId: string,
    auction: AuctionDto,
    userBillingDetail: UserBillingDetailDto,
    selectedInvoiceItems: ListResultset<InvoiceItemDto>,
    buyer: InvoiceBuyerDto,
  ): Promise<InvoiceDetailDto> {
    const itemsSubtotal: number = selectedInvoiceItems.list.reduce((sum: number, item: InvoiceItemDto): number => sum + item.price, 0);
    const buyerPremiumSubTotal: number = selectedInvoiceItems.list.reduce(
      (sum: number, item: InvoiceItemDto): number => sum + this.getBuyerPremiumSubTotal(auction, item.price),
      0,
    );

    const bids: any = selectedInvoiceItems.list.map((invoiceItem: InvoiceItemDto) => {
      return { auctionLotsId: invoiceItem.auctionLotId, amount: invoiceItem.price };
    });
    const auctionLots: ListResultset<AuctionLotFlattenedDto> = await this.auctionLotsHandler.getFlattenedByIds(
      selectedInvoiceItems.list.map((invoiceItem: InvoiceItemDto) => invoiceItem.auctionLotId),
    );

    const shippingSubtotal: number = 0;
    const taxesSubtotal: number = await this.getTotalTaxes(auctionLots, buyerPremiumSubTotal, shippingSubtotal, bids);
    const totalAmount: number = itemsSubtotal + shippingSubtotal + taxesSubtotal + buyerPremiumSubTotal;
    const amountDue: number = totalAmount;

    // Generate a new invoice number using database auto-increment
    const newInvoice: InvoiceDto = InvoiceDto.generate(
      auction,
      userBillingDetail.accountMappingsId,
      'CAD',
      itemsSubtotal,
      amountDue,
      totalAmount,
      taxesSubtotal,
      buyerPremiumSubTotal,
    );

    // Save the invoice in the database and get the numeric sequence number
    //await it because we need to invoice for the buyer info
    await this.storeInvoice(newInvoice);
    await this.storeInvoiceBuyer(userBillingDetail, newInvoice);
    const invoiceItems: Array<InvoiceItemDto> = await this.reallocateInvoiceItems(
      userId,
      invoiceId,
      newInvoice.id,
      selectedInvoiceItems.list.map((item: InvoiceItemDto) => item.id),
    );
    await this.storeItemTaxes(invoiceItems, auctionLots.list);

    await this.recalculateInvoiceItemTotals(invoiceId, auction);
    await this.invoiceSplitsHandler.create(
      userId,
      new InvoiceSplitDto({
        invoiceId: invoiceId,
        newInvoiceId: newInvoice.id,
      }),
    );

    return new InvoiceDetailDto(newInvoice, auction, buyer, selectedInvoiceItems);
  }

  /**
   * Recalculate the invoice totals after splitting an invoice
   *
   * @param invoiceId
   * @param taxDto
   * @param auction
   * @private
   */
  private async recalculateInvoiceItemTotals(invoiceId: string, auction: AuctionDto): Promise<void> {
    // Recalculate values for the original invoice
    let shippingCost: InvoiceShippingDto;
    try {
      shippingCost = await this.invoiceShippingHandler.getByInvoiceId(invoiceId);
    } catch (error) {
      shippingCost = { subtotal: 0 } as InvoiceShippingDto; // Default value if shipping cost is not found
    }

    // Update the original invoice with the new values
    const originalInvoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
    const remainingItems: ListResultset<InvoiceItemDto> = await this.invoiceItemsHandler.getAllByInvoiceId(originalInvoice.id);

    const remainingItemsSubtotal: number = remainingItems.list.reduce((sum: number, item: InvoiceItemDto): number => sum + item.price, 0);
    const remainingBuyerPremiumSubTotal: number = remainingItems.list.reduce(
      (sum: number, item: InvoiceItemDto): number => sum + this.getBuyerPremiumSubTotal(auction, item.price),
      0,
    );

    const remainingTaxesSubtotal: number = await this.getTotalTaxesFromInvoiceItems(remainingItems, remainingBuyerPremiumSubTotal, originalInvoice.shippingSubtotal);
    const remainingTotalAmount: number = remainingItemsSubtotal + shippingCost.subtotal + remainingTaxesSubtotal + remainingBuyerPremiumSubTotal;
    const remainingAmountDue: number = remainingTotalAmount;

    originalInvoice.itemsSubtotal = remainingItemsSubtotal;
    originalInvoice.buyerPremiumTotal = remainingBuyerPremiumSubTotal;
    originalInvoice.taxesSubtotal = remainingTaxesSubtotal;
    originalInvoice.totalAmount = remainingTotalAmount;
    originalInvoice.amountDue = remainingAmountDue;

    await this.storeInvoice(originalInvoice);
  }

  /**
   * Reallocate invoice items to a new invoice
   *
   * @param userId
   * @param invoiceId
   * @param newInvoiceId
   * @param ids
   * @private
   */
  private async reallocateInvoiceItems(userId: string, invoiceId: string, newInvoiceId: string, ids: string[]): Promise<Array<InvoiceItemDto>> {
    const invoiceItems: ListResultset<InvoiceItemDto> = await this.invoiceItemsHandler.splitInvoice(userId, invoiceId, newInvoiceId, ids);
    return invoiceItems.list;
  }

  /**
   * Generate an invoice for an auction
   *
   * @param auction
   * @param accountData
   * @param userBillingDetail
   */
  async generateInvoice(auction: AuctionDto, accountData: any, userBillingDetail: UserBillingDetailDto): Promise<InvoiceDto> {
    const { _auctionId, accountMappingId, bids } = accountData;
    _auctionId; // Unused variable
    // Calculate invoice amounts
    const itemsSubtotal: number = (Array.isArray(bids) ? bids : [bids]).reduce(
      (
        sum: number,
        bid: {
          amount: number;
        },
      ) => sum + bid.amount,
      0,
    );
    const auctionLots: ListResultset<AuctionLotFlattenedDto> = await this.auctionLotsHandler.getFlattenedByIds(bids.map((bid: any) => bid.auctionLotsId));

    const buyerPremiumSubTotal: number = (Array.isArray(bids) ? bids : [bids]).reduce(
      (sum: number, bid: { amount: number }) => sum + this.getBuyerPremiumSubTotal(auction, bid.amount),
      0,
    );

    const shippingSubtotal: number = 0;
    //this value has already been calculated against the tax rate
    const taxesSubtotal: number = await this.getTotalTaxes(auctionLots, buyerPremiumSubTotal, shippingSubtotal, bids);

    const totalAmount: number = itemsSubtotal + shippingSubtotal + taxesSubtotal + buyerPremiumSubTotal;

    // Generate a new invoice number using database auto-increment
    const newInvoice: InvoiceDto = InvoiceDto.generate(auction, accountMappingId, 'CAD', itemsSubtotal, totalAmount, totalAmount, taxesSubtotal, buyerPremiumSubTotal);

    newInvoice.taxesSubtotal = taxesSubtotal;
    // Save the invoice in the database and get the numeric sequence number
    //await it because we need to invoice for the buyer info
    await this.storeInvoice(newInvoice);
    await this.storeInvoiceBuyer(userBillingDetail, newInvoice);
    const invoiceItems: Array<InvoiceItemDto> = await this.storeInvoiceItems(auction, newInvoice, bids);

    await this.storeItemTaxes(invoiceItems, auctionLots.list);
    return newInvoice;
  }

  /**
   * Store the taxes for the invoice items
   *
   * @param invoiceItems
   * @param taxDto
   */
  async storeItemTaxes(invoiceItems: InvoiceItemDto[], auctionLots: AuctionLotFlattenedDto[]): Promise<number> {
    const lotIds: string[] = auctionLots.map((al: AuctionLotFlattenedDto) => al.lotId);
    const taxTypesMap: { [key: string]: TaxTypeDto } = await this.taxTypesHandler.getMapByLotIds(lotIds);
    const lotIdsMap: { [key: string]: string } = auctionLots.reduce((prev: any, cur: AuctionLotFlattenedDto) => {
      prev[cur.id] = cur.lotId;
      return prev;
    }, {});

    const hstTaxType: TaxTypeDto = await this.retrieveTaxType(TaxType.Hst.toString());

    const invoiceItemTaxes: InvoiceItemTaxDto[] = [];
    let taxTotal: number = 0;
    //itemize the taxable items in the ItemTaxes table
    invoiceItems.forEach((invoiceItem: InvoiceItemDto) => {
      const lotId: string = lotIdsMap[invoiceItem.auctionLotId];

      if (!lotId) {
        this.logger.error(`[storeItemTaxes] ERROR: No lot ID found for auction lot ID: ${invoiceItem.auctionLotId}`);
        throw new Error(`No lot ID mapping found for auction lot ID: ${invoiceItem.auctionLotId}`);
      }

      const taxType: TaxTypeDto | undefined = taxTypesMap[lotId];
      if (!taxType) {
        this.logger.error(`[storeItemTaxes] ERROR: No tax type found for lot ID: ${lotId}`);
        this.logger.error(`[storeItemTaxes] Available tax type lot IDs: ${JSON.stringify(Object.keys(taxTypesMap))}`);
        this.logger.error(`[storeItemTaxes] Invoice item details: ${JSON.stringify(invoiceItem)}`);
        throw new Error(`No tax type found for lot ID: ${lotId}`);
      }

      const taxAmount: number = invoiceItem.price * taxType.percent + invoiceItem.buyerPremiumAmount * hstTaxType.percent;

      taxTotal += taxAmount;
      invoiceItemTaxes.push(
        new InvoiceItemTaxDto({
          invoiceItemsId: invoiceItem.id, //Setting this to null doesn't write
          taxTypesId: taxType.id,
          taxAmount: taxAmount,
        }),
      );
    });

    if (invoiceItemTaxes.length > 0) {
      await this.invoiceItemsTaxesHandler.createInvoiceItemTaxes(invoiceItemTaxes);
    }

    return taxTotal;
  }

  /**
   * Store the invoice in the database.
   * if the invoice has an ID, it will update the invoice
   * @param invoice
   * @param userId
   * @private
   */
  private async storeInvoice(invoice: InvoiceDto): Promise<void> {
    try {
      if (invoice.id) {
        await this.invoicesHandler.update('00000000-0000-0000-0000-000000000000', invoice, invoice.id);
      } else {
        // Save the invoice to the database and use the auto-increment sequence for the number
        const result: InvoiceDto = await this.invoicesHandler.create('00000000-0000-0000-0000-000000000000', invoice);
        invoice.id = result.id; // Set the generated ID after saving
        invoice.number = result.number;
      }
    } catch (error) {
      // Check if this is a unique constraint violation (duplicate invoice)
      if (error.code === '23505' && error.constraint === 'Invoices_auctionsId_bidderId_unique') {
        this.logger.warn(`Invoice already exists for auction ${invoice.auctionsId} and bidder ${invoice.bidderId}. Skipping duplicate creation.`);
        // Fetch the existing invoice to populate the ID and number
        const existingInvoices = await this.invoicesHandler.getAll(1, 1, [
          { auctionsId: invoice.auctionsId },
          { bidderId: invoice.bidderId }
        ]);
        if (existingInvoices.list.length > 0) {
          const existingInvoice = existingInvoices.list[0];
          invoice.id = existingInvoice.id;
          invoice.number = existingInvoice.number;
        }
        return; // Don't throw error, just return
      }
      this.logger.error('Error storing invoice:', error);
      throw error;
    }
  }

  /**
   * Store the invoice items in the database
   *
   * @param auction
   * @param invoice
   * @param bids
   * @private
   */
  private async storeInvoiceItems(
    auction: AuctionDto,
    invoice: InvoiceDto,
    bids: Array<{ auctionLotsId: string; amount: number }>, // Adding types for the bid properties
  ): Promise<Array<InvoiceItemDto>> {
    // Extract auctionLotIds from bids
    const auctionLotIds: string[] = bids.map((bid: any) => bid.auctionLotsId);

    // Get or create item mappings by proxy ID
    const itemMappingsIdMap: Map<string, string> = await this.itemMappingsHandler.getOrCreateMappingByProxyId(auctionLotIds, 'Lots');

    // Fetch the InvoiceItemDto objects mapped by itemMappingsId
    const invoiceItems: Array<InvoiceItemDto> = await this.invoiceItemsHandler.getAllByItemMappingsIdMap(itemMappingsIdMap);

    // Create invoice items using the mapped item mappings IDs
    const finalInvoiceItems: Array<InvoiceItemDto> = bids.map((bid: any) => {
      const itemMappingsId: string | undefined = itemMappingsIdMap.get(bid.auctionLotsId);

      // Find the corresponding invoice item details from the fetched invoiceItems array
      const invoiceItemDetail: InvoiceItemDto | undefined = invoiceItems.find((item: any) => item.itemMappingsId === itemMappingsId);

      const invoiceItem: InvoiceItemDto = new InvoiceItemDto({
        itemMappingsId: itemMappingsId ?? '', // Ensure itemMappingsId is a non-null string
        quantity: 1, // TODO: some day this will be a variable
        price: bid.amount,
        name: invoiceItemDetail ? invoiceItemDetail.name : 'Unknown Item',
        buyerPremiumAmount: this.getBuyerPremiumSubTotal(auction, bid.amount),
        invoicesId: invoice.id,
        auctionLotId: bid.auctionLotsId,
      });

      return invoiceItem;
    });

    // Save the invoice items in the database
    return await this.invoiceItemsHandler.createInvoiceItems(finalInvoiceItems);
  }

  /**
   * Store the invoice buyer in the database
   *
   * @param userBillingDetail
   * @param invoice
   * @private
   */
  private async storeInvoiceBuyer(userBillingDetail: UserBillingDetailDto, invoice: InvoiceDto): Promise<void> {
    this.invoiceBuyersHandler.create('00000000-0000-0000-0000-000000000000', new InvoiceBuyerDto(userBillingDetail, invoice.id));
  }

  /**
   * Calculate the buyer premium subtotal
   *
   * @param auction
   * @param amount
   * @private
   */
  private getBuyerPremiumSubTotal(auction: AuctionDto, amount: number): number {
    return Number(auction.buyerPremium) * 0.01 * amount;
  }

  /**
   * Retrieve the tax type by name
   *
   * @param taxName
   */
  async retrieveTaxType(taxName: string): Promise<TaxTypeDto> {
    const types: ListResultset<TaxTypeDto> = await this.taxTypesHandler.getAll(1, -1, [{ name: taxName }]);

    // Return the first one that matches
    if (types.totalCount > 0) {
      return types.list[0];
    }
    throw new TaxTypeNotFoundError(taxName);
  }

  async getTotalTaxesFromInvoiceItems(invoiceItems: ListResultset<InvoiceItemDto>, buyerPremiumSubTotal: number, shippingSubtotal: number): Promise<number> {
    let totalItemTaxes: number = 0;

    const auctionLots: ListResultset<AuctionLotFlattenedDto> = await this.auctionLotsHandler.getFlattenedByIds(invoiceItems.list.map((item: InvoiceItemDto) => item.auctionLotId));
    const lotIds: string[] = auctionLots.list.map((al: AuctionLotFlattenedDto) => al.lotId);
    const taxTypesMap: { [key: string]: TaxTypeDto } = await this.taxTypesHandler.getMapByLotIds(lotIds);
    const lotIdsMap: { [key: string]: string } = auctionLots.list.reduce((prev: any, cur: AuctionLotFlattenedDto) => {
      prev[cur.id] = cur.lotId;
      return prev;
    }, {});

    invoiceItems.list.forEach((item: InvoiceItemDto) => {
      if (item.price !== undefined) {
        const lotId: string = lotIdsMap[item.auctionLotId];
        totalItemTaxes += item.price * taxTypesMap[lotId].percent;
      }
    });
    const hstTaxType: TaxTypeDto = await this.retrieveTaxType(TaxType.Hst.toString());

    return (buyerPremiumSubTotal + shippingSubtotal) * hstTaxType.percent + totalItemTaxes;
  }

  async getTotalTaxes(
    auctionLots: ListResultset<AuctionLotFlattenedDto>,
    buyerPremiumSubTotal: number,
    shippingSubtotal: number,
    bids: Array<{ auctionLotsId: string; amount: number }>,
  ): Promise<number> {
    const totalItemTaxes: number = this.calculateItemTaxes(auctionLots, bids);
    const hstTaxType: TaxTypeDto = await this.retrieveTaxType(TaxType.Hst.toString());

    return totalItemTaxes + (buyerPremiumSubTotal + shippingSubtotal) * hstTaxType.percent;
  }

  private calculateItemTaxes(
    auctionLots: ListResultset<AuctionLotFlattenedDto>,
    bids: Array<{
      auctionLotsId: string;
      amount: number;
    }>,
  ): number {
    const bidMap: Map<string, number> = new Map(bids.map((bid: { auctionLotsId: string; amount: number }) => [bid.auctionLotsId, bid.amount]));
    let totalItemTaxes: number = 0;
    auctionLots.list.forEach((auctionLot: AuctionLotFlattenedDto) => {
      const bidAmount: number | undefined = bidMap.get(auctionLot.id);
      if (bidAmount !== undefined) {
        totalItemTaxes += bidAmount * auctionLot.taxPercentage;
      }
    });
    return totalItemTaxes;
  }
}
