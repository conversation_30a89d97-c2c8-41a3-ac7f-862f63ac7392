import { Injectable } from '@nestjs/common';
import { InvoiceDto } from '../dtos/invoice.dto';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { InvoiceStatusDto } from '../dtos/invoice-status.dto';
import { InvoicesHandler } from '../handlers/invoices.handler';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class InvoiceStatusFactory {
  static invoiceStatuses: { [key: string]: string } = {
    INVOICES_SPLIT_SUCCESS: 'PENDING_ADMIN_REVIEW', // Awaiting admin approval after splitting
    INVOICES_GENERATE_SUCCESS: 'PENDING_ADMIN_REVIEW', // Awaiting admin approval after generation
    INVOICES_APPROVE_SUCCESS: 'PENDING_BUYER_REVIEW', // Buyer can review the invoice
    INVOICES_REQUEST_SHIPPING_SUCCESS: 'REQUIRES_SHIPPING_QUOTE', // Buyer has requested a shipping quote
    INVOICE_SHIPPING_CREATE_SUCCESS: 'PENDING_SHIPPING_QUOTE_REVIEW', // Awaiting admin approval of shipping quote
    INVOICE_SHIPPING_ADMIN_APPROVE_SUCCESS: 'PENDING_BUYER_SHIPPING_QUOTE_REVIEW', // Awaiting buyer approval of shipping quote
    INVOICE_SHIPPING_BUYER_APPROVE_SUCCESS: 'AWAITING_ASSEMBLY',
    INVOICE_REQUEST_PICKUP_SUCCESS: 'AWAITING_ASSEMBLY', // Invoice approved and ready for assembly
    TIMESLOTS_BOOKED_SUCCESS: 'AWAITING_ASSEMBLY', // User booked a timeslot and ready for assembly
    INVOICES_SET_ASSEMBLED_SUCCESS: 'PENDING_ASSEMBLY_REVIEW',
    INVOICES_SET_VALIDATED_SUCCESS: 'AWAITING_PICKUP',
    INVOICE_ASSEMBLED_SUCCESS: 'AWAITING_PICKUP', // Items assembled and ready for pickup
    INVOICES_SET_COMPLETE_SUCCESS: 'COMPLETED',
  };

  constructor(private invoicesHandler: InvoicesHandler) {}

  async determineInvoiceStatus(eventType: EventType, eventPayload: EventPayload): Promise<InvoiceStatusesResult> {
    if (eventPayload.items.invoices) {
      return this.getNewInvoiceStatuses(eventType, eventPayload.items.invoices);
    } else if (eventPayload.items.data?.invoice) {
      return this.getNewInvoiceStatuses(eventType, [new InvoiceDto(eventPayload.items.data.invoice)]);
    } else if (eventPayload.items.data?.invoicesId) {
      const invoice: InvoiceDto = await this.invoicesHandler.get(eventPayload.items.data.invoicesId);
      return this.getNewInvoiceStatuses(eventType, [invoice]);
    } else {
      const invoices: ListResultset<InvoiceDto> = (await this.invoicesHandler.getAllCustom(
        1,
        -1,
        [{ auctionsId: eventPayload.items.id }],
        InvoiceDto,
      )) as unknown as ListResultset<InvoiceDto>;
      return this.getNewInvoiceStatuses(eventType, invoices.list);
    }
  }

  private getNewInvoiceStatuses(eventType: EventType, invoices: Array<InvoiceDto>): InvoiceStatusesResult {
    const statuses: Array<InvoiceStatusDto> = [];

    for (const invoice of invoices) {
      const status: InvoiceStatusDto = InvoiceStatusDto.setInvoiceStatus(invoice, this.getStatus(eventType));
      statuses.push(status);
    }
    return { statuses: statuses, invoices: invoices };
  }

  private getStatus(eventType: string): string {
    if (eventType in InvoiceStatusFactory.invoiceStatuses) {
      return InvoiceStatusFactory.invoiceStatuses[eventType];
    } else {
      throw new Error(`EventType ${eventType} does not exist.`);
    }
  }
}

export interface InvoiceStatusesResult {
  statuses: Array<InvoiceStatusDto>;
  invoices: Array<InvoiceDto>;
}
