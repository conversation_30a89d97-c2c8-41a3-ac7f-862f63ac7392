import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoiceStatusDto } from '../dtos/invoice-status.dto';
import { Injectable } from '@nestjs/common';
import { InvoiceStatusNotFoundError } from '../exceptions/invoice-status-not-found.error';
import { PrismaService } from '../../prisma/prisma.service';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class InvoiceStatusesDbService extends BaseDbService<Prisma.InvoiceStatusesDelegate, InvoiceStatusDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.invoiceStatuses);
  }

  mapToDto(model: any): InvoiceStatusDto {
    return new InvoiceStatusDto(model);
  }

  throw404(id: string): Error {
    throw new InvoiceStatusNotFoundError(id);
  }

  /**
   * need to override the default because we need the ::TEXT value of
   * statusType not the key value
   * @param page
   * @param size
   * @param params
   * @param orderBy
   */
  async getAll(page: number, size: number, params: any[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceStatusDto>> {
    const filter: any = params ? this.createFilter(params) : {};
    orderBy;
    // Since we can pass in -1 (get all rows) we still want to ensure we are a positive integer
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;

    const invoicesId: string = params[0]['invoicesId'];

    // Combine the query string with the dynamic clauses
    const items: InvoiceStatusDto[] = await this.prisma.$queryRaw`
      SELECT
      i.id,
        i."createdOn",
        i."statusType"::TEXT AS "statusType",
        i."invoicesId"
      FROM public."InvoiceStatuses" i
      WHERE "invoicesId" = ${invoicesId}::uuid AND "deletedOn" IS NULL
      ORDER BY "createdOn" ASC
      LIMIT ${take} OFFSET ${skip};
      `;
    // Get the total count for pagination purposes
    const totalCount: number = await this.table.count({
      where: filter,
    });

    return new ListResultset(items.map(this.mapToDto), page, size, totalCount, Math.ceil(totalCount / size));
  }
}
