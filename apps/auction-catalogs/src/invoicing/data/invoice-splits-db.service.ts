import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoiceSplitDto } from '../dtos/invoice-split.dto';
import { InvoiceSplitNotFoundError } from '../exceptions/invoice-split-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { InvoiceSplitDetailDto } from '../dtos/invoice-split-detail.dto';

@Injectable()
export class InvoiceSplitsDbService extends BaseDbService<Prisma.InvoiceSplitsDelegate, InvoiceSplitDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.invoiceSplits, true, false, false);
  }

  throw404(id: string): never {
    throw new InvoiceSplitNotFoundError(id);
  }

  mapToDto(model: any): InvoiceSplitDto {
    return new InvoiceSplitDto(model);
  }

  /**
   * get all related invoices for a given invoice recursively going up and down the invoice splits
   * so that we can see even the cascade of splits (eg: A -> B -> C -> D, A -> E -> F)
   *
   * @param invoiceId
   */
  async getRelatedInvoices(invoiceId: string): Promise<InvoiceSplitDetailDto[]> {
    const uplineInvoices: InvoiceSplitDetailDto[] = await this.getUplineInvoices(invoiceId);
    const downlineInvoices: InvoiceSplitDetailDto[] = await this.getDownlineInvoices(invoiceId);
    const combinedInvoices: InvoiceSplitDetailDto[] = [...uplineInvoices, ...downlineInvoices];
    return combinedInvoices;
  }

  async getUplineInvoices(invoiceId: string): Promise<InvoiceSplitDetailDto[]> {
    const sql: Prisma.Sql = Prisma.sql`
            WITH RECURSIVE "InvoiceUpline" AS (
                SELECT i."id", i."number", i."auctionsId", i."isShipping", i."itemsSubtotal", i."taxesSubtotal",
                       i."buyerPremiumTotal", i."totalAmount", i."amountDue", i."invoicesApproved", i."isPaidInFull"
                FROM "Invoices" i
                WHERE i."id" = ${invoiceId}::uuid
            UNION
            SELECT i."id", i."number", i."auctionsId", i."isShipping", i."itemsSubtotal", i."taxesSubtotal",
                   i."buyerPremiumTotal", i."totalAmount", i."amountDue", i."invoicesApproved", i."isPaidInFull"
            FROM "Invoices" i
                     JOIN "InvoiceSplits" s ON i."id" = s."invoiceId"
                     JOIN "InvoiceUpline" u ON s."newInvoiceId" = u."id"
                )
            SELECT * FROM "InvoiceUpline" WHERE "id" != ${invoiceId}::uuid;
        `;
    const items: any = await this.prisma.$queryRaw<InvoiceSplitDto>(sql);
    return items.map((row: any) => new InvoiceSplitDetailDto(row));
  }

  async getDownlineInvoices(invoiceId: string): Promise<InvoiceSplitDetailDto[]> {
    const sql: Prisma.Sql = Prisma.sql`
            WITH RECURSIVE "InvoiceDownline" AS (
                SELECT i."id", i."number", i."auctionsId", i."isShipping", i."itemsSubtotal", i."taxesSubtotal",
                       i."buyerPremiumTotal", i."totalAmount", i."amountDue", i."invoicesApproved", i."isPaidInFull"
                FROM "Invoices" i
                WHERE i."id" = ${invoiceId}::uuid
            UNION
            SELECT i."id", i."number", i."auctionsId", i."isShipping", i."itemsSubtotal", i."taxesSubtotal",
                   i."buyerPremiumTotal", i."totalAmount", i."amountDue", i."invoicesApproved", i."isPaidInFull"
            FROM "Invoices" i
                     JOIN "InvoiceSplits" s ON i."id" = s."newInvoiceId"
                     JOIN "InvoiceDownline" d ON s."invoiceId" = d."id"
                )
            SELECT * FROM "InvoiceDownline" WHERE "id" != ${invoiceId}::uuid;
        `;
    const items: any = await this.prisma.$queryRaw<InvoiceSplitDto>(sql);
    return items.map((row: any) => new InvoiceSplitDetailDto(row));
  }
}
