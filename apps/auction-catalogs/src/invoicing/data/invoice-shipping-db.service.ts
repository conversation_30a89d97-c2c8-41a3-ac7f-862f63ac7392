import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { InvoiceShippingNotFoundError } from '../exceptions/invoice-shipping-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceShippingListDto } from '../dtos/shipping/invoice-shipping-list.dto';

@Injectable()
export class InvoiceShippingDbService extends BaseDbService<Prisma.InvoiceShippingDelegate, InvoiceShippingDto> {
  mapToDto(model: any): InvoiceShippingDto {
    return new InvoiceShippingDto(model);
  }

  throw404(id: string): Error {
    throw new InvoiceShippingNotFoundError(id);
  }

  constructor(private prisma: PrismaService) {
    super(prisma.invoiceShipping);
  }

  async getAllList(
    page: number,
    size: number,
    params: string[], // Accepts an array of conditions as strings
    orderBy?: Record<string, 'asc' | 'desc'> | undefined,
  ): Promise<ListResultset<InvoiceShippingListDto>> {
    // Handle pagination
    const offset: number = size > 0 ? (page - 1) * size : 0;
    const limit: string = size < 0 ? '' : `LIMIT ${size} OFFSET ${offset}`;

    // Build the dynamic WHERE clause from params
    const filter: string = params.length > 0 ? `WHERE ${params.join(' OR ')}` : '';

    // Build sorting based on orderBy parameter
    const sorting: string = orderBy
      ? `ORDER BY ${Object.entries(orderBy)
          .map(([key, value]: [string, 'asc' | 'desc']) => `${key} ${value}`)
          .join(', ')}`
      : `ORDER BY id ASC`;

    // Build the complete query using filter, pagination, and sorting
    const query: string = `
      WITH RankedShipping AS (
          SELECT
              ish.id,
              ish."createdOn",
              ish."invoicesId",
              ish.weight,
              ish.subtotal,
              ish."numPieces",
              sv."logoUrl",
              sv.name,
              sv."trackUrl",
              ib.firstname,
              ib.lastname,
              i.number AS "invoiceNumber",
              ROW_NUMBER() OVER (PARTITION BY ish.id ORDER BY ish.id ASC) AS rn
          FROM public."InvoiceShipping" ish
          JOIN public."Invoices" i ON i.id = ish."invoicesId"
          JOIN public."ShippingVendors" sv ON sv.id = ish."shippingVendorsId"
          JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = ish."invoicesId"
          ${filter}
      )
      SELECT
          id,
          "createdOn",
          "invoicesId",
          weight,
          subtotal,
          "numPieces",
          "logoUrl",
          name,
          "trackUrl",
          firstname,
          lastname,
          "invoiceNumber"
      FROM RankedShipping
      WHERE rn = 1
      ${sorting}
      ${limit};
  `;

    // Execute the query to get the filtered, sorted, and paginated results
    const items: any[] = await this.prisma.$queryRawUnsafe<InvoiceShippingListDto[]>(query);

    // Build a count query for the pagination information
    const countQuery: string = `
    SELECT COUNT(*) FROM (
        SELECT ish.id
        FROM public."InvoiceShipping" ish
             JOIN public."Invoices" i ON i.id = ish."invoicesId"
             JOIN public."ShippingVendors" sv ON sv.id = ish."shippingVendorsId"
             JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = ish."invoicesId"
        ${filter}
    ) AS subquery;
  `;
    const totalCountResult: any[] = await this.prisma.$queryRawUnsafe<{ count: bigint }[]>(countQuery);
    const totalCount: number = Number(totalCountResult[0]?.count || 0);
    const totalPages: number = Math.ceil(totalCount / size);

    // Return the result set with pagination details
    return new ListResultset<InvoiceShippingListDto>(
      items.map((item: InvoiceShippingListDto) => new InvoiceShippingListDto(item)),
      page,
      size,
      totalCount,
      totalPages,
    );
  }

  protected buildWhereClause(params: any[]): { whereClause: string; whereValues: any[] } {
    let whereClause: string = '';
    const whereValues: any[] = [];
    let paramIndex: number = 1;

    // Loop through each condition provided in the params
    params.forEach((condition: any) => {
      if (condition.OR && Array.isArray(condition.OR)) {
        // Handle OR conditions
        let orConditions: string = '';
        condition.OR.forEach((orCondition: any) => {
          const [key, value]: [string, any] = Object.entries(orCondition)[0];

          // If the value is a string, use ILIKE for case-insensitive matching with wildcards
          if (typeof value.contains === 'string') {
            orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE $${paramIndex}`;
            whereValues.push(`%${value.contains}%`);
          } else if (typeof value.contains === 'number') {
            // If the value is a number, use '=' without wildcards
            orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}`;
            whereValues.push(value.contains);
          }

          paramIndex++;
        });

        if (orConditions) {
          whereClause += `${whereClause ? ' AND ' : ''}(${orConditions})`;
        }
      } else {
        // Handle other single conditions if needed
        const [key, value]: [string, any] = Object.entries(condition)[0];
        if (typeof value === 'string') {
          // String comparison, use ILIKE with wildcards
          whereClause += `${whereClause ? ' AND ' : ''}${key} ILIKE $${paramIndex}`;
          whereValues.push(`%${value}%`);
        } else if (typeof value === 'number') {
          // Numeric comparison, use '=' without wildcards
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        }

        paramIndex++;
      }
    });

    return { whereClause, whereValues };
  }

  protected async getRelationFieldsForCurrentModel(): Promise<string[]> {
    return ['invoicesId', 'shippingVendorsId'];
  }
}
