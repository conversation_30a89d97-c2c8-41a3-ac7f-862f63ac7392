import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoiceItemDto } from '../dtos/items/invoice-item.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { InvoiceItemWithTaxDto } from '../dtos/items/invoice-item-with-tax.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceDto } from '../dtos/invoice.dto';

@Injectable()
export class InvoiceItemsDbService extends BaseDbService<Prisma.InvoiceItemsDelegate, InvoiceItemDto> {
  constructor(private readonly prisma: PrismaService) {
    super(prisma.invoiceItems);
  }
  mapToDto(model: any): InvoiceItemDto {
    return new InvoiceItemDto(model);
  }
  throw404(id: string): Error {
    throw new Error(id);
  }

  async getAllByItemMappingsIdMap(itemMappingsIdMap: Map<string, string>): Promise<Array<InvoiceItemDto>> {
    // Convert the map keys (lot IDs) to an array for use in the query
    const auctionLotIds: Array<string> = Array.from(itemMappingsIdMap.keys());

    // Generate the SQL query with placeholders
    const sql: Prisma.Sql = Prisma.sql`
    SELECT l.id, l.title, al.id as "auctionLotId"
    FROM public."Lots" l
    JOIN public."AuctionLots" al ON al."lotId" = l.id
    WHERE al.id IN (${Prisma.join(auctionLotIds)})
  `;

    // Execute the raw SQL query safely using parameterized inputs
    const items: any = await this.prisma.$queryRaw(sql);

    // Return the result list of items
    return items.map(
      (item: any) =>
        new InvoiceItemDto({
          auctionLotId: item.auctionLotId,
          lotsId: item.id,
          name: item.title,
          itemMappingsId: itemMappingsIdMap.get(item.auctionLotId),
        }),
    );
  }

  async createInvoiceItems(finalInvoiceItems: Array<InvoiceItemDto>): Promise<Array<InvoiceItemDto>> {
    const dbData: Array<Omit<InvoiceItemDto, 'auctionLotId'>> = finalInvoiceItems.map((item: InvoiceItemDto) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { auctionLotId, ...validDbFields } = item;
      return validDbFields;
    });

    const auctionLotIdMap: Map<string, string> = new Map(finalInvoiceItems.map((item: InvoiceItemDto) => [item.itemMappingsId, item.auctionLotId]));

    const result: any = await this.table.createManyAndReturn({
      data: dbData,
    });

    return result.map((dbItem: any) => {
      const dto: InvoiceItemDto = this.mapToDto(dbItem);
      dto.auctionLotId = auctionLotIdMap.get(dto.itemMappingsId) ?? '';
      return dto;
    });
  }

  async getInvoiceItemsWithTaxes(invoiceId: string): Promise<ListResultset<InvoiceItemWithTaxDto>> {
    // Generate the SQL query with placeholders
    const sql: Prisma.Sql = Prisma.sql`
      SELECT distinct(ii.id), ii."invoicesId", ii.quantity, ii.price, ii.name, ii."buyerPremiumAmount", ii."itemMappingsId",
      iit."taxAmount"
      FROM "InvoiceItems" ii
      LEFT JOIN "InvoiceItemTaxes" iit on iit."invoiceItemsId" = ii.id
      WHERE ii."invoicesId" = ${invoiceId}::uuid
    `;

    // Execute the raw SQL query safely using parameterized inputs
    const items: InvoiceItemWithTaxDto[] = await this.prisma.$queryRaw(sql);

    return new ListResultset(items, 1, -1, items.length, 1) as unknown as ListResultset<InvoiceItemWithTaxDto>;
  }

  async splitInvoice(userId: string, invoiceId: string, newInvoiceId: string, ids: string[]): Promise<ListResultset<InvoiceItemDto>> {
    const sql: Prisma.Sql = Prisma.sql`
      UPDATE public."InvoiceItems"
      SET "invoicesId" = ${newInvoiceId}::uuid
      WHERE "invoicesId"::uuid = ${invoiceId}::uuid AND id IN (${Prisma.join(ids.map((id: string) => Prisma.sql`${id}::uuid`))})
        RETURNING *;
    `;

    // Execute the query safely
    const items: any = await this.prisma.$queryRaw<InvoiceDto[]>(sql);

    // Check if the result is empty
    if (!items || items.length === 0) {
      throw new Error(`Could not update Invoice with invoiceId ${invoiceId}`);
    }

    return new ListResultset(items, 1, -1, items.length, 1) as unknown as ListResultset<InvoiceItemWithTaxDto>;
  }

  async getAllByInvoiceIdAndInvoiceItemId(invoiceId: string, invoiceItemIds: string[]): Promise<ListResultset<InvoiceItemDto>> {
    // Generate the SQL query with placeholders
    const sql: Prisma.Sql = Prisma.sql`
    SELECT ii.id, ii."invoicesId", ii.quantity, ii.price, ii.name, ii."buyerPremiumAmount", ii."itemMappingsId",
           im."proxyId" as "auctionLotId", al."lotId" as "lotsId"
    FROM "InvoiceItems" ii
    INNER JOIN "ItemMappings" im ON im.id = ii."itemMappingsId"
    INNER JOIN "AuctionLots" al ON al.id::uuid = im."proxyId"
    WHERE ii."invoicesId" = ${invoiceId}::uuid AND ii.id IN (${Prisma.join(invoiceItemIds.map((id: string) => Prisma.sql`${id}::uuid`))})
  `;

    // Execute the raw SQL query safely using parameterized inputs
    const items: any = await this.prisma.$queryRaw(sql);

    // Map the result to InvoiceItemDto objects
    const invoiceItems: InvoiceItemDto[] = items.map(
      (item: any) =>
        new InvoiceItemDto({
          id: item.id,
          invoicesId: item.invoicesId,
          lotsId: item.lotsId,
          quantity: item.quantity,
          price: item.price,
          name: item.name,
          buyerPremiumAmount: item.buyerPremiumAmount,
          auctionLotId: item.auctionLotId,
          itemMappingsId: item.itemMappingsId, //ludes(item.id) ? item.id : null,
        }),
    );

    return new ListResultset(invoiceItems, 1, -1, invoiceItems.length, 1);
  }
}
