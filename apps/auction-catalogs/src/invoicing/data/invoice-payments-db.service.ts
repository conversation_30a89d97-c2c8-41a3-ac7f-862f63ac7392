import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';
import { InvoicePaymentNotFoundError } from '../exceptions/invoice-payment-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoicePaymentExportDto } from '../dtos/invoice-payment-export.dto';
@Injectable()
export class InvoicePaymentsDbService extends BaseDbService<Prisma.InvoicePaymentsDelegate, InvoicePaymentDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.invoicePayments);
  }

  mapToDto(model: any): InvoicePaymentDto {
    return new InvoicePaymentDto(model);
  }

  throw404(id: string): Error {
    throw new InvoicePaymentNotFoundError(id);
  }

  async getAllByBidderId(page: number, size: number, params: { bidderId: string; invoiceId?: string }[]): Promise<ListResultset<InvoicePaymentDto>> {
    // Extract the bidderId and invoiceId from the params array
    const { bidderId, invoiceId } = params[0];

    // Build the WHERE clause
    let whereClause: Prisma.Sql = Prisma.sql`WHERE i."bidderId" = ${bidderId}`;
    if (invoiceId) {
      whereClause = Prisma.sql`${whereClause} AND ip."invoicesId" = ${invoiceId}`;
    }

    // Construct the main query with pagination
    const offset: number = (page - 1) * size;
    const mainSql: Prisma.Sql = Prisma.sql`
      SELECT ip.*
      FROM public."InvoicePayments" ip
             INNER JOIN public."Invoices" i ON i.id = ip."invoicesId"
        ${whereClause}
      LIMIT ${size} OFFSET ${offset}
    `;

    const items: any = await this.prisma.$queryRaw<InvoicePaymentDto[]>(mainSql);

    const countSql: Prisma.Sql = Prisma.sql`
      SELECT COUNT(*)
      FROM public."InvoicePayments" ip
             INNER JOIN public."Invoices" i ON i.id = ip."invoicesId"
        ${whereClause}
    `;

    // Execute the count query and convert BigInt to number
    const totalCountResult: any = await this.prisma.$queryRaw<{ count: bigint }[]>(countSql);
    const totalCount: number = Number(totalCountResult[0]?.count || 0); // Convert BigInt to number

    // Return the results in the ListResultset format
    return new ListResultset(
      items.map((item: any) => new InvoicePaymentDto(item)),
      page,
      size,
      totalCount,
      Math.ceil(totalCount / size),
    );
  }

  async getAllPaymentsForExport(page: number, size: number, params: any[]): Promise<ListResultset<InvoicePaymentExportDto>> {
    // Calculate the offset for pagination
    const offset: number = (page - 1) * size;

    // Extract filter parameters if any
    const filter: any = params.length > 0 ? this.createFilter(params) : {};

    // Build the WHERE clause based on provided filters
    let whereClause: Prisma.Sql = Prisma.sql`WHERE 1=1`;

    // Handle payment date range filter
    if (filter.paymentDate?.gte) {
      whereClause = Prisma.sql`${whereClause} AND ip."paymentDate" >= ${filter.paymentDate.gte}`;
    }

    if (filter.paymentDate?.lt) {
      whereClause = Prisma.sql`${whereClause} AND ip."paymentDate" < ${filter.paymentDate.lt}`;
    }

    // Handle invoice ID filter with explicit UUID casting
    if (filter.invoiceId) {
      whereClause = Prisma.sql`${whereClause} AND ip."invoicesId"::uuid = ${filter.invoiceId}::uuid`;
    }

    // Handle deleted records filter
    if (filter.deletedOn === null) {
      whereClause = Prisma.sql`${whereClause} AND ip."deletedOn" IS NULL`;
    }

    // Main query to get payment data with related information
    const mainSql: Prisma.Sql = Prisma.sql`
      SELECT
        	ip.id,
          ip."invoicesId",
          ip."paymentTypes",
          ip."isPartial",
          ip."amountPaid",
          ip."currentOwing",
          ip."transactionCode",
          ip."createdOn",
          ip."createdBy",
          ip."paymentDate",
          ip.location,
          i."itemsSubtotal",
          i."shippingSubtotal",
          i."buyerPremiumTotal",
          i."taxesSubtotal",
          i."totalAmount",
          i."number" AS "invoiceNumber",
          ii."quantity",
          ii."price",
          ii."name",
          tt."name" AS "taxType",
          tt."percent" AS "taxPercent",
          i."bidderId" AS "accountMappingsId"
      FROM public."InvoicePayments" ip
      LEFT JOIN public."Invoices" i ON i.id = ip."invoicesId"
      LEFT JOIN public."InvoiceItems" ii ON ii."invoicesId" = i.id
      LEFT JOIN public."InvoiceItemTaxes" iit ON iit."invoiceItemsId" = ii.id
      LEFT JOIN public."TaxTypes" tt ON tt.id = iit."taxTypesId"
      ${whereClause}
      ORDER BY ip."createdOn" DESC
      LIMIT ${size} OFFSET ${offset}
    `;

    const items: any = await this.prisma.$queryRaw(mainSql);

    // Count query to get total number of records
    const countSql: Prisma.Sql = Prisma.sql`
      SELECT COUNT(*)
      FROM public."InvoicePayments" ip
      ${whereClause}
    `;

    const totalCountResult: any = await this.prisma.$queryRaw<{ count: bigint }[]>(countSql);
    const totalCount: number = Number(totalCountResult[0]?.count ?? 0);

    return new ListResultset(
      items.map((item: any) => new InvoicePaymentExportDto(item)),
      page,
      size,
      totalCount,
      Math.ceil(totalCount / size),
    );
  }

  async getBidderPayment(accountMappingsId: string, id: string): Promise<InvoicePaymentDto> {
    // Construct the WHERE clause with explicit casting to UUID
    const whereClause: Prisma.Sql = Prisma.sql`i."bidderId"::uuid = ${accountMappingsId}::uuid AND ip."id"::uuid = ${id}::uuid`;

    const mainSql: Prisma.Sql = Prisma.sql`
      SELECT ip.*
      FROM public."InvoicePayments" ip
      INNER JOIN public."Invoices" i ON i.id = ip."invoicesId"
      WHERE ${whereClause}
      LIMIT 1
    `;

    const result: any = await this.prisma.$queryRaw<InvoicePaymentDto[]>(mainSql);

    if (!result || result.length === 0) {
      this.throw404(id);
    }

    return this.mapToDto(result[0]);
  }
}
