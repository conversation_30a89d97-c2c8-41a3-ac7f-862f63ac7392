import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoiceAddressDto } from '../dtos/buyers/invoice-address.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

@Injectable()
export class InvoicesShippingAddressesDbService extends BaseDbService<Prisma.InvoiceShippingAddressesDelegate, InvoiceAddressDto> {
  mapToDto(model: any): InvoiceAddressDto {
    return new InvoiceAddressDto(model);
  }
  throw404(id: string): Error {
    throw new Error(id);
  }
  constructor(prisma: PrismaService) {
    super(prisma.invoiceShippingAddresses);
  }
}
