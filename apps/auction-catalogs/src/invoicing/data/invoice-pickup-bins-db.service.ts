import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoicePickupBinDto } from '../dtos/pickup-bins/invoice-pickup-bin.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { InvoicePickupBinNotFoundError } from '../exceptions/invoice-pickup-bin-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class InvoicePickupBinsDbService extends BaseDbService<Prisma.InvoicePickupBinsDelegate, InvoicePickupBinDto> {
  constructor(prisma: PrismaService) {
    super(prisma.invoicePickupBins);
  }

  async setBinsForInvoice(userId: string, invoicesId: string, pickupBins: string[]): Promise<void> {
    await this.table.updateMany({
      where: {
        invoicesId,
      },
      data: {
        deletedBy: userId,
        deletedOn: new Date(),
      },
    });

    await this.table.createMany({
      data: pickupBins.map((bin: string) => ({
        invoicesId,
        pickupBin: bin,
        createdBy: userId,
      })),
    });
  }

  async getPickupBinByName(pickupBin: string): Promise<InvoicePickupBinDto> {
    const row: any = await this.table.findFirst({
      where: {
        deletedOn: null,
        pickupBin: pickupBin,
      },
      orderBy: {
        createdOn: 'desc',
      },
    });

    if (!row) {
      this.throw404(pickupBin);
    }

    return this.mapToDto(row);
  }

  mapToDto(model: any): InvoicePickupBinDto {
    return new InvoicePickupBinDto(model);
  }

  throw404(id: string): Error {
    throw new InvoicePickupBinNotFoundError(id);
  }

  createFilter(params: any[]): any {
    const invoicesId: string = params.find((r: any) => r.hasOwnProperty('invoicesId'))?.invoicesId;

    return { invoicesId };
  }
}
