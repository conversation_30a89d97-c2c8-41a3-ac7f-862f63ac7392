import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoicePullerDto } from '../dtos/invoice-puller.dto';
import { InvoicePullerNotFoundError } from '../exceptions/invoice-pullers-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

@Injectable()
export class InvoicePullersDbService extends BaseDbService<Prisma.InvoicePullersDelegate, InvoicePullerDto> {
  constructor(prisma: PrismaService) {
    super(prisma.invoicePullers);
  }

  mapToDto(model: any): InvoicePullerDto {
    return new InvoicePullerDto(model);
  }

  throw404(id: string): Error {
    throw new InvoicePullerNotFoundError(id);
  }
}
