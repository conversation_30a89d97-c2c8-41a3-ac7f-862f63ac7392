import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { TrackingNumberDto } from '../dtos/tracking-number.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TrackingNumberNotFoundError } from '../exceptions/tracking-numbers-not-found-error';

@Injectable()
export class TrackingNumbersDbService extends BaseDbService<Prisma.TrackingNumbersDelegate, TrackingNumberDto> {
  constructor(prisma: PrismaService) {
    super(prisma.trackingNumbers);
  }

  mapToDto(model: any): TrackingNumberDto {
    return new TrackingNumberDto(model);
  }

  throw404(id: string): Error {
    throw new TrackingNumberNotFoundError(id);
  }

  async get(trackingNumber: string): Promise<TrackingNumberDto> {
    let whereClause: any = {};

    whereClause = {
      number: trackingNumber,
      deletedOn: null,
    };

    const item: TrackingNumberDto | null = await this.table.findFirst({
      where: whereClause,
    });

    if (!item) {
      this.throw404(trackingNumber);
    }
    return this.mapToDto(item);
  }
}
