import { BaseDbService } from '@mvc/data/base-db.service';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

@Injectable()
export class InvoiceBuyersDbService extends BaseDbService<Prisma.InvoiceBuyersDelegate, InvoiceBuyerDto> {
  mapToDto(model: any): InvoiceBuyerDto {
    return new InvoiceBuyerDto(model);
  }
  throw404(id: string): Error {
    throw new Error(id);
  }

  constructor(private prisma: PrismaService) {
    super(prisma.invoiceBuyers);
  }

  async splitInvoice(userId: string, invoiceId: string, newInvoiceId: string): Promise<InvoiceBuyerDto> {
    const sql: Prisma.Sql = Prisma.sql`
      INSERT INTO public."InvoiceBuyers" (id, firstname, lastname, email, telephone, companyName, accountMappingsId, invoicesId)
      SELECT uuid_generate_v4(), firstname, lastname, email, telephone, companyName, accountMappingsId, ${newInvoiceId}
      FROM public."InvoiceBuyers"
      WHERE invoicesId = ${invoiceId}
        RETURNING *;
    `;

    // Execute the query safely
    const result: any = await this.prisma.$queryRaw(sql);

    // Check if the result is empty
    if (!result || result.length === 0) {
      throw new Error(`Could not copy InvoiceBuyer with invoiceId ${invoiceId}`);
    }

    // Map the result to InvoiceBuyerDto and return
    return this.mapToDto(result[0]);
  }
}
