import { BaseDbService } from '@mvc/data/base-db.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { AdminTimeslotListDto } from '../dtos/timeslots/admin-timeslot-list.dto';
import { TimeslotAvailabilityListDto } from '../dtos/timeslots/timeslot-availability-list.dto';
import { TimeslotDto } from '../dtos/timeslots/timeslot.dto';
import { TimeslotNotFoundError } from '../exceptions/timeslots-not-found-error';

@Injectable()
export class TimeslotsDbService extends BaseDbService<Prisma.TimeslotsDelegate, TimeslotDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.timeslots);
  }

  mapToDto(model: any): TimeslotDto {
    return new TimeslotDto(model);
  }

  throw404(id: string): Error {
    throw new TimeslotNotFoundError(id);
  }

  async updateStatus(userId: string, id: string, status: string): Promise<TimeslotDto> {
    try {
      // Fetch the existing item from the database
      const item: TimeslotDto = await this.get(id);

      // Initialize updateData object
      const updateData: any = {
        status: status,
        ...(this.hasUpdatedBy
          ? {
              updatedBy: userId,
              updatedOn: new Date(), // Prisma will handle Date objects
            }
          : {}),
      };

      // Perform the update in the database
      const result: any = await this.table.update({
        where: { id: item.id },
        data: updateData,
      });

      return this.mapToDto(result);
    } catch (error) {
      throw new Error(`Unable to update item with id ${id}`);
    }
  }

  async getAdminBookings(params: any[]): Promise<ListResultset<AdminTimeslotListDto>> {
    const fromDate: string = params[0]['fromDate'];
    const toDate: string = params[0]['toDate'];

    const query: Prisma.Sql = Prisma.sql`
      SELECT
        a.id AS "auctionsId",
        a.name AS "auctionName",
        a.code AS "auctionCode",
        i.number AS "invoiceNumber",
        t.id,
        TO_TIMESTAMP(t."pickupTime", 'YYYY-MM-DD HH24:MI') AS "pickupTime",
        t."staffId",
        t."accountMappingsId",
        t.status,
        t."isProxyPickup",
        t."proxyName",
        t."proxyEmail",
        ib.id AS "buyerId",
        ib.firstname,
        ib.lastname,
        ib.email,
        ib.telephone,
        ib."companyName"
      FROM
        public."Auctions" a
          INNER JOIN
        public."Invoices" i ON i."auctionsId" = a.id
          INNER JOIN
        public."Timeslots" t ON t."invoicesId" = i.id
          INNER JOIN
        public."InvoiceBuyers" ib ON ib."invoicesId" = i.id
      WHERE
        t."pickupTime"::timestamp >= TO_TIMESTAMP(${fromDate}, 'YYYY-MM-DD') AND t."pickupTime"::timestamp < TO_TIMESTAMP(${toDate}, 'YYYY-MM-DD') + interval '1 day'
      ORDER BY
        t."pickupTime"
    `;
    // Execute the query and handle the results
    const list: any[] = await this.prisma.$queryRaw(query);
    const totalCount: number = list.length;

    return new ListResultset(
      list.map((row: any) => new AdminTimeslotListDto(row)), // Correct DTO mapping
      1,
      -1,
      totalCount,
      1,
    );
  }

  async getBookings(params: any[]): Promise<ListResultset<TimeslotAvailabilityListDto>> {
    const fromDate: string = params[0]['fromDate'];
    const toDate: string = params[0]['toDate'];

    // Prepare raw SQL query with parameterized placeholders (?)
    const query: string = `
        WITH RECURSIVE "TimeslotIntervals" AS (
          SELECT (DATE '${fromDate}' + (i * interval '1 day') + interval '9 hour' + (j * interval '15 minute'))::timestamp AS "pickupTime"
          FROM generate_series(0, CAST(DATE_PART('day', '${toDate}'::timestamp - '${fromDate}'::timestamp) AS integer ) + 1 ) i
          CROSS JOIN generate_series(0, (60 / 15) * (18 - 9) - 1) j
        )
        SELECT TO_CHAR(t."pickupTime", 'YYYY-MM-DD HH24:MI') AS "pickupTime",
          COUNT(ts."id") AS "bookings"
        FROM "TimeslotIntervals" t
        LEFT JOIN "Timeslots" ts ON t."pickupTime" = ts."pickupTime"::timestamp
        WHERE t."pickupTime" BETWEEN '${fromDate}'::timestamp AND ('${toDate}'::timestamp + interval '1 day')
        GROUP BY t."pickupTime"
        ORDER BY t."pickupTime";
      `;

    // Execute the raw SQL query using queryRawUnsafe
    const timeslots: any = await this.prisma.$queryRawUnsafe(query);

    // Return the result as a ListResultset, mapping the raw result to DTOs
    const totalCount: number = Number(timeslots.length);
    return new ListResultset(
      timeslots.map((row: TimeslotAvailabilityListDto) => new TimeslotAvailabilityListDto(row)), // Correct DTO mapping
      1,
      -1,
      totalCount,
      1,
    );
  }
}
