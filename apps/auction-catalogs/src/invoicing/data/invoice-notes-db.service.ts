import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { InvoiceNoteDto } from '../dtos/invoice-note.dto';
import { InvoiceNoteNotFoundError } from '../exceptions/invoice-note-not-found.error';

@Injectable()
export class InvoiceNotesDbService extends BaseDbService<Prisma.InvoiceNotesDelegate, InvoiceNoteDto> {
  constructor(prisma: PrismaService) {
    super(prisma.invoiceNotes);
  }

  mapToDto(model: any): InvoiceNoteDto {
    return new InvoiceNoteDto(model);
  }

  throw404(id: string): Error {
    throw new InvoiceNoteNotFoundError(id);
  }
}
