import { BaseDbService } from '@mvc/data/base-db.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { InvoiceListDto } from '../dtos/invoice-list.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { CouldNotUpdateInvoiceStatusError } from '../exceptions/could-not-update-invoice-status.error';
import { InvoiceNotFoundError } from '../exceptions/invoice-not-found.error';
import { TrackingNumberNotFoundError } from '../exceptions/tracking-numbers-not-found-error';
import { CouldNotApproveInvoicesError } from '../exceptions/could-not-approve-invoices.error';
import { CopyInvoiceError } from '../exceptions/copy-invoice.error';

@Injectable()
export class InvoicesDbService extends BaseDbService<Prisma.InvoicesDelegate, InvoiceDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.invoices);
  }

  mapToDto(model: any): InvoiceDto {
    return new InvoiceDto(model);
  }
  throw404(id: string): Error {
    throw new InvoiceNotFoundError(id);
  }

  buildWhereClause(params: Record<string, any>[]): { whereClause: string; whereValues: any[] } {
    let whereClause: string = '';
    const whereValues: any[] = [];
    let paramIndex: number = 1;

    params.forEach((condition: Record<string, any>) => {
      // Check if condition has an OR block and it's an array
      if (condition.OR && Array.isArray(condition.OR)) {
        let orConditions: string = '';
        condition.OR.forEach((orCondition: Record<string, any>) => {
          const [key, value] = Object.entries(orCondition)[0];

          if (key === 'ist."statusType"') {
            // Handle invoiceStatusType with specific handling for enums
            orConditions += `${orConditions ? ' OR ' : ''}"ist"."statusType" = $${paramIndex}::"InvoiceStatusType"`;
            whereValues.push(value);
          } else if (key === 'i."bidderId"') {
            // Handle invoiceStatusType with specific handling for enums
            orConditions += `${orConditions ? ' OR ' : ''}"i"."bidderId" = $${paramIndex}"`;
            whereValues.push(value);
          } else if (key === 'i."auctionsId"') {
            orConditions += `${orConditions ? ' OR ' : ''}"i"."auctionsId" = $${paramIndex}"`;
            whereValues.push(value);
          } else if (isNaN(Number(value.contains))) {
            // String comparison, use ILIKE with wildcards
            orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE $${paramIndex}`;
            whereValues.push(`%${value.contains}%`);
          } else {
            // Numeric comparison, use '=' with type casting
            orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}::DECIMAL(10,2)`;
            whereValues.push(value.contains);
          }

          paramIndex++;
        });

        if (orConditions) {
          whereClause += `${whereClause ? ' AND ' : ''}(${orConditions})`;
        }
      } else {
        // Handle other conditions outside of OR blocks
        const [key, value] = Object.entries(condition)[0];

        if (key === 'ist."statusType"') {
          // Handle invoiceStatusType separately for equality comparison
          whereClause += `${whereClause ? ' AND ' : ''}"ist"."statusType" = $${paramIndex}::"InvoiceStatusType"`;
          whereValues.push(value);
        } else if (key === 'i."bidderId"') {
          // Handle bidderId separately for exact match
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        } else if (key === 'i."auctionsId"') {
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        } else if (key === 'i."isPaidInFull"') {
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        } else if (typeof value === 'string') {
          // String comparison, use ILIKE with wildcards
          whereClause += `${whereClause ? ' AND ' : ''}"${key}" ILIKE $${paramIndex}`;
          whereValues.push(`%${value}%`);
        } else if (key === 'i."invoicesApproved"') {
          whereClause += `${whereClause ? ' AND ' : ''}${key} = $${paramIndex}`;
          whereValues.push(value);
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          // Numeric comparison, use '=' without wildcards
          whereClause += `${whereClause ? ' AND ' : ''}"${key}" = $${paramIndex}`;
          whereValues.push(value);
        }

        paramIndex++;
      }
    });

    return { whereClause, whereValues };
  }

  async getAllPublic(page: number, size: number, params: Record<string, any>[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceListDto>> {
    const offset: number = size > 0 ? (page - 1) * size : 0;
    const limit: string = size > 0 ? `LIMIT ${size} OFFSET ${offset}` : '';

    // Build the dynamic WHERE clause and get the parameters
    const { whereClause, whereValues } = this.buildWhereClause(params);

    // Construct the query dynamically with placeholders ($1, $2, etc.)
    const query: string = `
      SELECT
        i.id,
        i.number,
        i."createdOn",
        i."bidderId",
        i."auctionsId",
        a."code",
        a."name" AS "auctionName",
        i."totalAmount" AS "totalAmount",
        i."amountDue" AS "amountDue",
        (i."totalAmount" - i."amountDue") as "amountPaid",
        i."isShipping",
        ARRAY_AGG(ii."name") AS "items",  -- Aggregating the item names into an array
        COALESCE(SUM(ii."buyerPremiumAmount"), 0) AS "buyerPremiumTotal",  -- Sum of buyerPremiumAmount renamed to buyerPremiumTotal
        COALESCE(SUM(ii."price" * ii."quantity"), 0) AS "itemsSubtotal",  -- Calculate the total amount of items (price * quantity)
        ib.firstname,
        ib.lastname,
        ib.email,
        ib.telephone,
        ib."companyName",
        ist."statusType",
        ist."createdOn" AS "statusCreatedOn",
        t.id as "timeslotsId",
        t."pickupTime" AS "pickupTime"
      FROM public."Invoices" i
             LEFT OUTER JOIN public."Auctions" a ON a.id = i."auctionsId"
             LEFT OUTER JOIN public."InvoiceItems" ii ON ii."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = i.id
             LEFT OUTER JOIN public."Timeslots" t ON t."invoicesId" = i.id
             LEFT JOIN (
        SELECT
          "invoicesId",
          "statusType",
          "createdOn",  -- Explicitly select createdOn here
          ROW_NUMBER() OVER (PARTITION BY "invoicesId" ORDER BY "createdOn" DESC) AS rn
        FROM public."InvoiceStatuses"
      ) ist ON ist."invoicesId" = i.id AND ist.rn = 1
        ${whereClause ? `WHERE ${whereClause}` : ''}
      GROUP BY i.id, ib.firstname, ib.lastname, ib.email, ib.telephone, ib."companyName", ist."statusType", ist."createdOn", t.id, t."pickupTime", a."code", a."name"
        ${
          orderBy
            ? `ORDER BY ${Object.entries(orderBy)
                .map(([key, value]: [string, 'asc' | 'desc']) => `"${key}" ${value}`)
                .join(', ')}`
            : ''
        }
        ${limit};
    `;

    // Execute the query to get the filtered, sorted, and paginated results
    const items: InvoiceListDto[] = await this.prismaService.$queryRawUnsafe<InvoiceListDto[]>(query, ...whereValues);

    // Get the total count of matching records for pagination purposes
    const countQuery: string = `
      SELECT COUNT(DISTINCT i.id)
      FROM public."Invoices" i
             LEFT OUTER JOIN public."InvoiceItems" ii ON ii."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = i.id
             LEFT OUTER JOIN public."Timeslots" t ON t."invoicesId" = i.id
             LEFT JOIN (
        SELECT
          "invoicesId",
          "statusType",
          "createdOn",
          ROW_NUMBER() OVER (PARTITION BY "invoicesId" ORDER BY "createdOn" DESC) AS rn
        FROM public."InvoiceStatuses"
      ) ist ON ist."invoicesId" = i.id AND ist.rn = 1
        ${whereClause ? `WHERE ${whereClause}` : ''}

    `;

    const totalCountResult: { count: bigint }[] = await this.prismaService.$queryRawUnsafe<{ count: bigint }[]>(countQuery, ...whereValues);
    const totalCount: number = Number(totalCountResult[0]?.count || 0);
    const totalRows: number = Math.ceil(totalCount / size);

    // Return the result set with pagination details
    return new ListResultset(
      items.map((row: any) => new InvoiceListDto(row)),
      page,
      size,
      totalCount,
      totalRows,
    );
  }

  async getAllAdmin(page: number, size: number, params: Record<string, any>[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceListDto>> {
    const offset: number = size > 0 ? (page - 1) * size : 0;
    const limit: string = size > 0 ? `LIMIT ${size} OFFSET ${offset}` : '';

    // Build the dynamic WHERE clause and get the parameters
    const { whereClause, whereValues } = this.buildWhereClause(params);

    // Construct the query dynamically with placeholders ($1, $2, etc.)
    const query: string = `
      SELECT
        i.id,
        i.number,
        i."createdOn",
        i."bidderId",
        i."auctionsId",
        a."code",
        a."name" AS "auctionName",
        i."totalAmount" AS "totalAmount",  -- Add buyerPremiumTotal to totalAmount
        i."amountDue" AS "amountDue",      -- Add buyerPremiumTotal to amountDue
        (i."totalAmount" - i."amountDue") as "amountPaid",
        i."taxesSubtotal",
        i."isShipping",
        ARRAY_AGG(ii."name") AS "items",  -- Aggregating the item names into an array
        COALESCE(SUM(ii."buyerPremiumAmount"), 0) AS "buyerPremiumTotal",  -- Sum of buyerPremiumAmount renamed to buyerPremiumTotal
        COALESCE(SUM(ii."price" * ii."quantity"), 0) AS "itemsSubtotal",  -- Calculate the total amount of items (price * quantity)
        ib.firstname,
        ib.lastname,
        ib.email,
        ib.telephone,
        ib."companyName",
        ist."statusType",
        ist."createdOn" AS "statusCreatedOn",  -- Adding createdOn with alias
        ip."pullerId"
      FROM public."Invoices" i
             LEFT OUTER JOIN public."Auctions" a ON a.id = i."auctionsId"
             LEFT OUTER JOIN public."InvoiceItems" ii ON ii."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoicePullers" ip ON ip."invoicesId" = i.id
             LEFT JOIN (
        SELECT
          "invoicesId",
          "statusType",
          "createdOn",  -- Explicitly select createdOn here
          ROW_NUMBER() OVER (PARTITION BY "invoicesId" ORDER BY "createdOn" DESC) AS rn
        FROM public."InvoiceStatuses"
      ) ist ON ist."invoicesId" = i.id AND ist.rn = 1
        ${whereClause ? `WHERE ${whereClause}` : ''}
      GROUP BY i.id, ib.firstname, ib.lastname, ib.email, ib.telephone, ib."companyName", ist."statusType", ist."createdOn", ip."pullerId", a."code", a."name"
        ${
          orderBy
            ? `ORDER BY ${Object.entries(orderBy)
                .map(([key, value]: [string, 'asc' | 'desc']) => `${key} ${value}`)
                .join(', ')}`
            : ''
        }
        ${limit};
    `;

    // Execute the query to get the filtered, sorted, and paginated results
    const items: InvoiceListDto[] = await this.prismaService.$queryRawUnsafe<InvoiceListDto[]>(query, ...whereValues);

    // Get the total count of matching records for pagination purposes
    const countQuery: string = `
      SELECT COUNT(DISTINCT i.id)
      FROM public."Invoices" i
             LEFT OUTER JOIN public."InvoiceItems" ii ON ii."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoiceBuyers" ib ON ib."invoicesId" = i.id
             LEFT OUTER JOIN public."InvoicePullers" ip ON ip."invoicesId" = i.id
             LEFT JOIN (
        SELECT
          "invoicesId",
          "statusType",
          "createdOn",
          ROW_NUMBER() OVER (PARTITION BY "invoicesId" ORDER BY "createdOn" DESC) AS rn
        FROM public."InvoiceStatuses"
      ) ist ON ist."invoicesId" = i.id AND ist.rn = 1
        ${whereClause ? `WHERE ${whereClause}` : ''}

    `;

    const totalCountResult: { count: bigint }[] = await this.prismaService.$queryRawUnsafe<{ count: bigint }[]>(countQuery, ...whereValues);
    const totalCount: number = Number(totalCountResult[0]?.count || 0);
    const totalRows: number = Math.ceil(totalCount / size);

    // Return the result set with pagination details
    return new ListResultset(
      items.map((row: any) => new InvoiceListDto(row)),
      page,
      size,
      totalCount,
      totalRows,
    );
  }

  async getByTrackingNumber(trackingNumber: string): Promise<InvoiceDto> {
    const sql: Prisma.Sql = Prisma.sql`
      select i.*
      from public."Invoices" i
             inner join public."InvoiceShipping" ish on ish."invoicesId" = i.id
             inner join public."TrackingNumbers" tn on tn."invoiceShippingId" = ish.id
      where tn.number = ${trackingNumber} and tn."deletedOn" IS NULL
      limit 1`;

    // Execute raw SQL query in a safe manner
    const result: any = await this.prismaService.$queryRaw<InvoiceDto[]>(sql);

    // Check if we got a result, otherwise throw a NotFoundException
    if (!result || result.length === 0) {
      throw new TrackingNumberNotFoundError(trackingNumber);
    }

    // Assuming the first result is the desired invoice
    return this.mapToDto(result[0]);
  }

  async setInvoiceStatuses(auctionId: string, status: string): Promise<number> {
    const sql: Prisma.Sql = Prisma.sql`
      WITH updated_invoices AS (
        UPDATE public."Invoices" i
        SET "invoicesApproved" = true
        WHERE i."auctionsId" = ${auctionId}
        RETURNING i.id
      )
      INSERT INTO public."InvoiceStatuses" (
        id,
        "createdOn",
        "createdBy",
        "invoicesId",
        "statusType"
      )
      SELECT
        uuid_generate_v4(),
        NOW(),
        '00000000-0000-0000-0000-000000000000', -- Adjust as needed for actual user id
        i.id,
        ${status}::"InvoiceStatusType"
      FROM updated_invoices i;
    `;

    // Execute raw SQL query in a safe manner
    const result: number = await this.prismaService.$executeRaw<number>(sql);

    // Check if we got a result, otherwise throw a NotFoundException
    if (!result) {
      throw new CouldNotUpdateInvoiceStatusError(auctionId);
    }

    return result;
  }

  async setPaidAndAmountDueAndTotal(invoiceId: string, isPaidInFull: boolean, amountDue: number, totalAmount?: number, taxesSubtotal?: number): Promise<InvoiceDto> {
    const sql: Prisma.Sql = Prisma.sql`
      UPDATE public."Invoices"
      SET "isPaidInFull" = ${isPaidInFull},
          "amountDue" = ${amountDue}
      ${totalAmount ? Prisma.raw(`, "totalAmount" = ${totalAmount}, "taxesSubtotal" = ${taxesSubtotal}`) : Prisma.empty}
      WHERE id = ${invoiceId}::uuid
      RETURNING *;
    `;

    // Execute raw SQL query in a safe manner
    const result: any = await this.prismaService.$queryRaw<InvoiceDto>(sql);

    // Check if we got a result, otherwise throw a NotFoundException
    if (!result) {
      throw new CouldNotUpdateInvoiceStatusError(result.id);
    }

    return result;
  }

  protected override getExcludedFields(): (keyof InvoiceDto)[] {
    return ['amountPaid'];
  }

  async approveAllInvoicesInAuction(auctionsId: string): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        auctionsId: auctionsId,
      },
      data: {
        invoicesApproved: true,
      },
    });

    if (!item || !item.count || item.count == 0) {
      throw new CouldNotApproveInvoicesError(auctionsId);
    }

    return item.count;
  }

  //TODO: refactor the totals to be calculated in the db
  async splitInvoice(userId: string, invoiceId: string): Promise<InvoiceDto> {
    const sql: Prisma.Sql = Prisma.sql`
      WITH copied_invoice AS (
      INSERT INTO public."Invoices" ("createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", mode, "auctionsId", "bidderId", notes, "specialInstructions", "poNumber", "dueDate", "isShipping", "invoiceTemplatesId", "currencyType", "itemsSubtotal", "shippingSubtotal", "taxesSubtotal", "totalAmount", "amountDue", "invoicesApproved", "isPaidInFull", "buyerPremiumTotal", "number")

      SELECT  "createdOn", "createdBy", "updatedOn", "updatedBy", "deletedOn", "deletedBy", mode, "auctionsId", "bidderId", notes, "specialInstructions", "poNumber", "dueDate", "isShipping", "invoiceTemplatesId", "currencyType", "itemsSubtotal", "shippingSubtotal", "taxesSubtotal", "totalAmount", "amountDue", "invoicesApproved", "isPaidInFull", "buyerPremiumTotal", "number"
      FROM public."Invoices"
      WHERE id::uuid = ${invoiceId}::uuid
        RETURNING *
    )
      SELECT * FROM copied_invoice;
    `;

    // Execute the query safely
    const result: any = await this.prismaService.$queryRaw(sql);

    // Check if the result is empty
    if (!result || result.length === 0) {
      throw new CopyInvoiceError(invoiceId);
    }

    // Map the result to InvoiceDto and return
    return this.mapToDto(result[0]);
  }

  async getByInvoiceNumber(invoiceNumber: number): Promise<InvoiceDto> {
    const item: any = await this.table.findFirst({
      where: {
        number: invoiceNumber,
        deletedOn: null,
      },
    });

    if (!item) {
      throw new InvoiceNotFoundError(invoiceNumber.toString());
    }

    return this.mapToDto(item);
  }
}
