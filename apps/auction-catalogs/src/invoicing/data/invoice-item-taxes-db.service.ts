import { BaseDbService } from '@mvc/data/base-db.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { InvoiceItemTaxDto } from '../dtos/items/invoice-item-tax.dto';

@Injectable()
export class InvoiceItemTaxesDbService extends BaseDbService<Prisma.InvoiceItemTaxesDelegate, InvoiceItemTaxDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.invoiceItemTaxes);
  }
  mapToDto(model: any): InvoiceItemTaxDto {
    return new InvoiceItemTaxDto(model);
  }

  throw404(id: string): Error {
    throw new Error(id);
  }

  async createInvoiceItemTaxes(invoiceTaxItems: Array<InvoiceItemTaxDto>): Promise<void> {
    // Save the invoice item taxes in the database
    await this.table.createMany({
      data: invoiceTaxItems,
    });
  }
}
