import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { ShippingVendorNotFoundError } from '../exceptions/shipping-vendor-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { ShippingVendorDto } from '../dtos/shipping-vendor.dto';

@Injectable()
export class ShippingVendorsDbService extends BaseDbService<Prisma.ShippingVendorsDelegate, ShippingVendorDto> {
  constructor(prisma: PrismaService) {
    super(prisma.shippingVendors, false, false, false);
  }

  mapToDto(model: any): ShippingVendorDto {
    return new ShippingVendorDto(model);
  }

  throw404(id: string): Error {
    throw new ShippingVendorNotFoundError(id);
  }

  async get(id: string): Promise<ShippingVendorDto> {
    let whereClause: any = {};
    whereClause = {
      id: parseInt(id),
    };

    const item: any = await this.table.findFirst({
      where: whereClause,
    });

    if (!item) {
      this.throw404(id);
    }
    return this.mapToDto(item);
  }
}
