import { BaseDbService } from '@mvc/data/base-db.service';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoiceShippingTaxesDto } from '../dtos/shipping/invoice-shipping-taxes.dto';

@Injectable()
export class InvoiceShippingTaxesDbService extends BaseDbService<Prisma.InvoiceShippingTaxesDelegate, InvoiceShippingTaxesDto> {
  mapToDto(model: any): InvoiceShippingTaxesDto {
    return new InvoiceShippingTaxesDto(model);
  }
  throw404(id: string): Error {
    throw new Error(id);
  }
  constructor(prisma: PrismaService) {
    super(prisma.invoiceShippingTaxes);
  }
}
