import { BaseDbService } from '@mvc/data/base-db.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { TaxTypeDto } from '../dtos/tax-type.dto';

@Injectable()
export class TaxTypeDbService extends BaseDbService<Prisma.TaxTypesDelegate, TaxTypeDto> {
  constructor(private readonly prisma: PrismaService) {
    super(prisma.taxTypes, false, false, false);
  }

  mapToDto(model: any): TaxTypeDto {
    return new TaxTypeDto(model);
  }

  throw404(id: string): Error {
    throw new Error(id);
  }

  async getByAuctionsId(auctionsId: string): Promise<TaxTypeDto> {
    const rows: any[] = await this.prisma.$queryRaw`
      SELECT tt.id, tt.name, tt.percent
      FROM public."TaxTypes" tt
        JOIN
      public."AuctionTaxes" a_t ON a_t."taxTypesId" = tt.id
      WHERE a_t."auctionsId" = ${auctionsId}
    `;

    if (rows.length !== 1) {
      this.throw404(auctionsId);
    }

    return this.mapToDto(rows[0]);
  }

  async getMapByLotIds(lotIds: string[]): Promise<{ [key: string]: TaxTypeDto }> {
    const rows: any[] = await this.prisma.$queryRaw`
      SELECT tt.id, tt.name, tt.percent, l_t."lotsId"
      FROM public."TaxTypes" tt
        JOIN
      public."LotTaxes" l_t ON l_t."taxTypesId" = tt.id
      WHERE l_t."lotsId" IN (${Prisma.join(lotIds)})
    `;

    if (rows.length != lotIds.length) {
      this.throw404(lotIds.join(','));
    }

    const result: { [key: string]: TaxTypeDto } = rows.reduce((prev: any, cur: any) => {
      prev[cur.lotsId] = this.mapToDto(cur);
      return prev;
    }, {});

    return result;
  }
}
