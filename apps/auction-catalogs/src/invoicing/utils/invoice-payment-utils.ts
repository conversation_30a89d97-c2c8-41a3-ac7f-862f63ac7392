import { PaymentType } from '@viewbid/ts-node-client';
import { TransactionType } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { InvoiceOverPaymentError } from '../exceptions/invoice-over-payment.error';

export function checkForOverPayment(invoice: InvoiceDto, invoicePayment: InvoicePaymentDto): void {
  if (invoice.amountDue < invoicePayment.amountPaid) {
    throw new InvoiceOverPaymentError(invoice, invoicePayment);
  }
}

export function getPaymentType(paymentType: PaymentType): keyof PaymentType {
  const map: any = {
    onlineCard: 'OnlineCard',
    cash: 'Cash',
    'e-transfer': 'ETransfer',
    inPersonCard: 'InPersonCard',
    paypal: 'Paypal',
  };
  return map[paymentType];
}

export function mapToTransactionType(paymentTypes: PaymentType): TransactionType {
  switch (paymentTypes) {
    case PaymentType.OnlineCard:
    case PaymentType.InPersonCard:
      return TransactionType.CreditCard;
    case PaymentType.Paypal:
      return TransactionType.PayPal;
    default:
      return TransactionType.CreditCard;
  }
}

export function mapStateToShort(province: string): string {
  const provinceMap: { [key: string]: string } = {
    Alberta: 'AB',
    'British Columbia': 'B.C.',
    Manitoba: 'MB',
    'New Brunswick': 'N.B.',
    'Newfoundland and Labrador': 'N.L.',
    'Northwest Territories': 'N.T.',
    'Nova Scotia': 'N.S.',
    Nunavut: 'NU',
    Ontario: 'ON',
    'Prince Edward Island': 'P.E.I.',
    Quebec: 'QC',
    Saskatchewan: 'SK',
    Yukon: 'YT',
  };
  return provinceMap[province];
}
