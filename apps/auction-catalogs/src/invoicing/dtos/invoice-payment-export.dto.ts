import { PaymentType } from '@vb/nest/enums/paymentType';

export class InvoicePaymentExportDto {
  id: string;
  invoicesId: string;
  paymentTypes: keyof PaymentType;
  isPartial: boolean;
  amountPaid: number;
  currentOwing: number;
  transactionCode: string;
  createdOn: string;
  createdBy: string;
  paymentDate: string;
  location: string;
  itemsSubtotal: number;
  shippingSubtotal: number;
  buyerPremiumTotal: number;
  taxesSubtotal: number;
  totalAmount: number;
  username: string;
  accountMappingsId: string;
  invoiceNumber: string;
  quantity: number;
  price: number;
  name: string;
  taxType: string;
  taxPercent: number;

  constructor(row: any) {
    this.id = row.id;
    this.invoicesId = row.invoicesId;
    this.paymentTypes = row.paymentTypes;
    this.isPartial = row.isPartial;
    this.amountPaid = Number(row.amountPaid);
    this.currentOwing = Number(row.currentOwing);
    this.transactionCode = row.transactionCode;
    this.createdOn = row.createdOn;
    this.createdBy = row.createdBy;
    this.paymentDate = row.paymentDate;
    this.location = row.location ?? 'Online';
    this.itemsSubtotal = Number(row.itemsSubtotal);
    this.shippingSubtotal = Number(row.shippingSubtotal);
    this.buyerPremiumTotal = Number(row.buyerPremiumTotal);
    this.taxesSubtotal = Number(row.taxesSubtotal);
    this.totalAmount = Number(row.totalAmount);
    this.username = row.username;
    this.accountMappingsId = row.accountMappingsId;
    this.invoiceNumber = row.invoiceNumber;
    this.quantity = Number(row.quantity);
    this.price = Number(row.price);
    this.name = row.name;
    this.taxType = row.taxType;
    this.taxPercent = Number(row.taxPercent);
  }
}
