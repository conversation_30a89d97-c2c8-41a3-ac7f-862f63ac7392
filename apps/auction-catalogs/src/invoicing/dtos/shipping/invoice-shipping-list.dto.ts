export class InvoiceShippingListDto {
  id: string;
  createdOn: Date;
  weight: number;
  subtotal: number;
  numPieces: number;
  logoUrl: string;
  vendorName: string;
  trackUrl: string;
  buyerFirstName: string;
  buyerLastName: string;
  invoicesId: string;
  invoiceNumber: string;

  constructor(row: any) {
    this.id = row.id;
    this.createdOn = new Date(row.createdOn);
    this.invoicesId = row.invoicesId;
    this.weight = parseFloat(row.weight);
    this.subtotal = parseFloat(row.subtotal);
    this.numPieces = parseInt(row.numPieces, 10);
    this.logoUrl = row.logoUrl;
    this.vendorName = row.name; // Renaming 'name' to 'vendorName' for better context.
    this.trackUrl = row.trackUrl;
    this.buyerFirstName = row.firstname;
    this.buyerLastName = row.lastname;
    this.invoiceNumber = row.invoiceNumber;
  }
}
