import { UserDto } from '@vb/nest/common-dtos/users/user.dto';

export class InvoiceShippingDto {
  id: string;
  invoicesId: string;
  weight: number;
  shippingVendorsId: number;
  subtotal: number;
  numPieces: number;
  isApproved: boolean;
  isBuyerApproved: boolean;
  createdBy: string;
  createdOn: string;
  method: string;
  staff?: StaffInfo;

  constructor(row: any) {
    this.id = row.id;
    this.invoicesId = row.invoicesId;
    this.weight = row.weight;
    this.shippingVendorsId = row.shippingVendorsId;
    this.subtotal = parseFloat(row.subtotal);
    this.numPieces = row.numPieces;
    this.isApproved = row.isApproved;
    this.isBuyerApproved = row.isBuyerApproved;
    this.createdBy = row.createdBy;
    this.createdOn = row.createdOn;
    this.method = row.method;
  }

  setStaff(user: UserDto): void {
    this.staff = {
      accountMappingsId: user.accountMappingsId,
      name: `${user.firstname} ${user.lastname}`,
    };
  }
}
interface StaffInfo {
  accountMappingsId: string;
  name: string;
}
