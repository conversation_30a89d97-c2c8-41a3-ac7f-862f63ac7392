export class TimeslotDto {
  id: string;
  pickupTime: string;
  accountMappingsId: string;
  auctionsId: string;
  staffId: string;
  status: any;
  invoicesId: string;
  index: number;
  isProxyPickup: boolean;
  proxyName: string;
  proxyEmail: string;
  staffName: string;

  constructor(row: any) {
    this.id = row.id;
    this.pickupTime = this.id ? new Date(row.pickupTime).toISOString() : row.pickupTime;
    this.accountMappingsId = row.accountMappingsId;
    this.auctionsId = row.auctionsId;
    this.staffId = row.staffId;
    this.status = row.status;
    this.invoicesId = row.invoicesId;
    this.index = row.index;
    this.isProxyPickup = row.isProxyPickup;
    this.proxyName = row.proxyName ?? '';
    this.proxyEmail = row.proxyEmail ?? '';
  }

  setStaffName(name: string): void {
    this.staffName = name;
  }
}
