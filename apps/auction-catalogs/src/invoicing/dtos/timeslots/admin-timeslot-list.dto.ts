export class AdminTimeslotListDto {
  id: string;
  buyerId: string;
  buyerName: string;
  email: string;
  telephone: string;
  companyName: string;
  auctionsId: string;
  auctionCode: string;
  auctionName: string;
  staffId: string;
  staffName: string;
  status: string;
  invoiceId: string;
  invoiceNumber: number;
  pickupTime: string;
  isProxyPickup: boolean;
  proxyName: string;
  proxyEmail: string;

  constructor(row: any) {
    this.id = row.id;
    this.buyerId = row.buyerId;
    this.buyerName = `${row.firstname} ${row.lastname}`;
    this.email = row.email;
    this.telephone = row.telephone;
    this.companyName = row.companyName;
    this.auctionsId = row.auctionsId;
    this.auctionCode = row.auctionCode;
    this.auctionName = row.auctionName;
    this.invoiceNumber = row.invoiceNumber;
    this.staffId = row.staffId;
    this.status = row.status;
    this.invoiceId = row.invoiceId;
    this.invoiceNumber = row.invoiceNumber;
    this.pickupTime = row.pickupTime;
    this.isProxyPickup = row.isProxyPickup;
    this.proxyName = row.proxyName;
    this.proxyEmail = row.proxyEmail;
  }

  setStaffName(name: string): void {
    this.staffName = name;
  }
}
