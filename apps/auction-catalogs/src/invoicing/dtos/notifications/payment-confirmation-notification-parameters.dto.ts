export class PaymentConfirmationNotificationParametersDto {
  readonly firstname: string;
  readonly lastname: string;
  readonly receiptNumber: string;
  readonly totalAmount: string;
  readonly date: string;
  readonly paymentMethod: string;
  readonly invoiceNumber: string;
  readonly transactionId: string;
  readonly invoiceLink: string;
  readonly amountDue: string;

  constructor(
    firstname: string,
    lastname: string,
    totalAmount: string,
    date: string,
    paymentMethod: string,
    invoiceNumber: string,
    transactionId: string,
    invoiceLink: string,
    amountDue: string,
  ) {
    this.firstname = firstname;
    this.lastname = lastname;
    // this.receiptNumber = receiptNumber;
    this.totalAmount = totalAmount;
    this.date = date;
    this.paymentMethod = paymentMethod;
    this.invoiceNumber = invoiceNumber;
    this.transactionId = transactionId;
    this.invoiceLink = invoiceLink;
    this.amountDue = amountDue;
  }
}
