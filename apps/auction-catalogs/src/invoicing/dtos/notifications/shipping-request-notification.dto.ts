import { UserDto } from '@vb/nest/common-dtos/users/user.dto';

export class ShippingRequestNotificationDto {
  readonly firstname: string;
  readonly lastname: string;
  readonly invoiceNumber: string;
  readonly invoiceLink: string;
  readonly requestDate: string;
  readonly requestTime: string;
  readonly timestamp: string;

  constructor(user: UserDto, requestDate: string, requestTime: string, invoiceLink: string, invoiceNumber: string) {
    this.invoiceLink = invoiceLink;
    this.requestDate = requestDate;
    this.requestTime = requestTime;
    this.firstname = user.firstname;
    this.lastname = user.lastname;
    this.invoiceNumber = invoiceNumber;
    this.timestamp = `${requestDate} ${requestTime};`;
  }
}
