import { TimeslotDto } from '../timeslots/timeslot.dto';
import { InvoiceDto } from '../invoice.dto';
import { InvoiceBuyerDto } from '../buyers/invoice-buyer.dto';

export class TimeslotCancelledNotificationParametersDto {
  buyerName: string;
  invoiceNumber: number;
  invoiceId: string;
  pickupTime: string;
  accountMappingsId: string;
  auctionsId: string;
  isProxyPickup: boolean;
  proxyName: string;
  proxyEmail: string;
  link: string;
  timestamp: string;

  constructor(buyer: InvoiceBuyerDto, timeslot: TimeslotDto, invoice: InvoiceDto, link: string, pickupTime: string, timestamp: string) {
    this.buyerName = buyer.firstname;
    this.invoiceNumber = invoice.number;
    this.invoiceId = invoice.id;
    this.pickupTime = pickupTime;
    this.accountMappingsId = buyer.accountMappingsId;
    this.auctionsId = invoice.auctionsId;
    this.isProxyPickup = timeslot.isProxyPickup;
    this.proxyName = timeslot.proxyName;
    this.proxyEmail = timeslot.proxyEmail;
    this.link = link;
    this.timestamp = timestamp;
  }
}
