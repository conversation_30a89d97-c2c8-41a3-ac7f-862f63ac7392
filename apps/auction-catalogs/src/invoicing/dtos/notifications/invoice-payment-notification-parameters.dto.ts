export class InvoicePaymentNotificationParametersDto {
  invoiceId: string;
  invoiceNumber: number;
  paymentType: string;
  paymentAmount: number;
  amountDue: number;
  paymentDate: string;
  firstname: string;
  lastname: string;
  invoiceLink: string;

  constructor(
    invoiceId: string,
    invoiceNumber: number,
    paymentType: string,
    paymentAmount: number,
    amountDue: number,
    paymentDate: string,
    firstname: string,
    lastname: string,
    invoiceLink: string,
  ) {
    this.invoiceId = invoiceId;
    this.invoiceNumber = invoiceNumber;
    this.paymentType = paymentType;
    this.paymentAmount = paymentAmount;
    this.amountDue = amountDue;
    this.paymentDate = paymentDate;
    this.firstname = firstname;
    this.lastname = lastname;
    this.invoiceLink = invoiceLink;
  }
}
