export class InvoiceListDto {
  id: string;
  auctionsId: string;
  auctionName: string;
  code: string;
  number: string;
  createdOn: string;
  totalAmount: string;
  paymentStatus: string;
  statusType: string;
  statusCreatedOn: string;
  numItems: number;
  firstname: string;
  lastname: string;
  email: string;
  telephone: string;
  companyName: string;
  amountDue: number;
  amountPaid: number;
  taxesSubtotal: number;
  buyerPremiumTotal: number;
  itemsSubtotal: number;
  isShipping: boolean;
  isPaidInFull: boolean;
  pullerId: string;
  timeslotsId: string;
  pickupTime: string;
  location: string;

  puller?: {
    id: string;
    firstname: string;
    lastname: string;
  };

  constructor(row: any) {
    this.id = row.id;
    this.auctionsId = row.auctionsId;
    this.auctionName = row.auctionName;
    this.code = row.code;
    this.number = row.number;
    this.createdOn = row.createdOn;
    this.statusType = row.statusType;
    this.totalAmount = row.totalAmount;
    this.paymentStatus = row.paymentStatus;
    this.firstname = row.firstname;
    this.lastname = row.lastname;
    this.email = row.email;
    this.telephone = row.mobile;
    this.amountDue = row.amountDue;
    this.numItems = Number(row.items.length);
    this.statusCreatedOn = row.statusCreatedOn;
    this.amountPaid = row.amountPaid;
    this.taxesSubtotal = row.taxesSubtotal;
    this.buyerPremiumTotal = row.buyerPremiumTotal;
    this.itemsSubtotal = row.itemsSubtotal;
    this.isShipping = row.isShipping;
    this.isPaidInFull = row.isPaidInFull;
    this.pullerId = row.pullerId;
    this.timeslotsId = row.timeslotsId;
    this.pickupTime = row.pickupTime;
    this.location = row.location;
  }
}
