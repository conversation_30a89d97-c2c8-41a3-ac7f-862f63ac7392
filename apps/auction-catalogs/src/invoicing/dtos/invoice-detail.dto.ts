import { InvoiceBuyerDto } from './buyers/invoice-buyer.dto';
import { InvoiceDto } from './invoice.dto';
import { InvoiceAddressDto } from './buyers/invoice-address.dto';
import { InvoiceShippingDto } from './shipping/invoice-shipping.dto';
import { TrackingNumberDto } from './tracking-number.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { ShippingVendorDto } from './shipping-vendor.dto';
import { InvoiceNoteDto } from './invoice-note.dto';
import { InvoiceStatusDto } from './invoice-status.dto';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { InvoicePaymentDto } from './invoice-payment.dto';
import { InvoiceItemWithTaxDto } from './items/invoice-item-with-tax.dto';
import { InvoiceTemplateDto } from '../../invoice-template/dtos/invoice-template.dto';

export class InvoiceDetailDto {
  buyer: InvoiceBuyerDto;
  auction: AuctionName;
  invoice: InvoiceDto;
  items: Array<Partial<InvoiceItemWithTaxDto>>;
  shippingAddress: InvoiceAddressDto | undefined;
  shipping: InvoiceShippingDto | undefined;
  trackingNumbers: Array<TrackingNumberDto> | undefined;
  vendor: ShippingVendorDto | undefined;
  invoiceNotes: Array<InvoiceNoteDto> | undefined;
  invoiceStatuses: Array<InvoiceStatusDto> | undefined;
  invoicePayments: Array<InvoicePaymentDto> | undefined;
  currentStatus?: InvoiceStatusDto;
  billingAddress?: InvoiceAddressDto | undefined;
  invoiceTemplate?: InvoiceTemplateDto | undefined;

  constructor(
    invoice: InvoiceDto,
    auction: AuctionDto,
    buyer: InvoiceBuyerDto,
    items: ListResultset<Partial<InvoiceItemWithTaxDto>>,
    shippingAddress?: InvoiceAddressDto | undefined,
    shipping?: InvoiceShippingDto | undefined,
    trackingNumbers?: ListResultset<TrackingNumberDto> | undefined,
    vendor?: ShippingVendorDto | undefined,
    invoiceNotes?: Array<InvoiceNoteDto> | undefined,
    invoiceStatuses?: ListResultset<InvoiceStatusDto> | undefined,
    invoicePayments?: ListResultset<InvoicePaymentDto> | undefined,
    billingAddress?: InvoiceAddressDto | undefined,
    invoiceTemplate?: InvoiceTemplateDto | undefined,
  ) {
    this.buyer = buyer;
    this.auction = {
      name: auction.name,
      code: auction.code,
      invoiceInfo: auction.invoiceText,
    };
    this.invoice = invoice;
    this.items = items.list;
    this.shippingAddress = shippingAddress;
    this.shipping = shipping;
    this.trackingNumbers = trackingNumbers?.list;
    this.vendor = vendor;
    this.invoiceNotes = invoiceNotes;
    this.invoiceStatuses = invoiceStatuses?.list;
    this.invoicePayments = invoicePayments?.list;
    this.currentStatus = this.invoiceStatuses?.at(-1);
    this.billingAddress = billingAddress;
    this.invoiceTemplate = invoiceTemplate;
  }
}
interface AuctionName {
  name: string;
  code: string;
  invoiceInfo: string;
}
