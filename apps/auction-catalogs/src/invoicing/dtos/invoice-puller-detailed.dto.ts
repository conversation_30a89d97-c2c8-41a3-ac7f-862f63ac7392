import { InvoicePullerDto } from './invoice-puller.dto';
import { UserDto } from '@vb/account-mappings/dtos/user.dto';

export class InvoicePullerDetailedDto {
  id: string;
  puller: admin;
  startTime: string;
  endTime: string;
  invoicesId: string;
  assigner: admin;

  constructor(puller: InvoicePullerDto, pullerUser: UserDto, assignerUser: UserDto) {
    this.id = puller.id;
    this.puller = {
      id: pullerUser.id,
      name: `${pullerUser.firstname} ${pullerUser.lastname}`,
    };
    this.startTime = puller.startTime;
    this.endTime = puller.endTime;
    this.invoicesId = puller.invoicesId;
    this.assigner = {
      id: assignerUser.id,
      name: `${assignerUser.firstname} ${assignerUser.lastname}`,
    };
  }
}
interface admin {
  id: string;
  name: string;
}
