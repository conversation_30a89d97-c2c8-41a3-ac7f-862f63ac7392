import { PaymentType } from '@vb/nest/enums/paymentType';

export class InvoicePaymentDto {
  id: string;
  invoicesId: string;
  paymentTypes: keyof PaymentType;
  isPartial: boolean;
  amountPaid: number;
  currentOwing: number;
  transactionCode: string;
  createdOn: string;
  createdBy: string;
  paymentDate: string;
  location: string;

  constructor(row: any) {
    this.id = row.id;
    this.invoicesId = row.invoicesId;
    this.paymentTypes = row.paymentTypes;
    this.isPartial = row.isPartial;
    this.amountPaid = parseFloat(row.amountPaid); // Ensuring it's a number
    this.currentOwing = parseFloat(row.currentOwing);
    this.transactionCode = row.transactionCode;
    this.createdOn = row.createdOn;
    this.createdBy = row.createdBy;
    this.paymentDate = row.paymentDate;
    this.location = row.location ?? 'Online'; // Default to 'Online' for all payments
  }
}
