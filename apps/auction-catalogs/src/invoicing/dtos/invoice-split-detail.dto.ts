export class InvoiceSplitDetailDto {
  id: string;
  number: number;
  auctionsId: string;
  isShipping: boolean;
  itemsSubtotal: number;
  taxesSubtotal: number;
  buyerPremiumTotal: number;
  totalAmount: number;
  amountDue: number;
  invoicesApproved: boolean;
  isPaidInFull: boolean;

  constructor(row: any) {
    this.id = row.id;
    this.number = row.number;
    this.auctionsId = row.auctionsId;
    this.isShipping = row.isShipping;
    this.itemsSubtotal = row.itemsSubtotal;
    this.taxesSubtotal = row.taxesSubtotal;
    this.buyerPremiumTotal = row.buyerPremiumTotal;
    this.totalAmount = row.totalAmount;
    this.amountDue = row.amountDue;
    this.invoicesApproved = row.invoicesApproved;
    this.isPaidInFull = row.isPaidInFull;
  }
}
