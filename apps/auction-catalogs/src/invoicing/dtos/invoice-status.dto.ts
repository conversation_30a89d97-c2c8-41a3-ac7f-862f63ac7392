import { InvoiceDto } from './invoice.dto';

export class InvoiceStatusDto {
  id: string;
  invoicesId: string;
  statusType: string; // Using string for enum type, replace with actual enum type if available
  createdOn: string;

  constructor(row: any) {
    this.id = row.id;
    this.invoicesId = row.invoicesId;
    this.statusType = row.statusType;
    this.createdOn = row.createdOn;
  }

  static setInvoiceStatus(invoice: InvoiceDto, status: string): InvoiceStatusDto {
    return new InvoiceStatusDto({
      invoicesId: invoice.id,
      statusType: status,
    });
  }
}
