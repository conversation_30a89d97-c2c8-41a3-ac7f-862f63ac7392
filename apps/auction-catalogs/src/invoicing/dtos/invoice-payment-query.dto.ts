import { IsOptional, IsInt, Min, IsDateString, IsUUID, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class InvoicePaymentQueryDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page: number = 1;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  size: number = 10;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsString()
  @IsUUID()
  invoiceId?: string;
}
