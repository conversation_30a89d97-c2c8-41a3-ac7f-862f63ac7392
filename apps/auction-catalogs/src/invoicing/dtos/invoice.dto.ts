import { AuctionDto } from '../../auction/dtos/auction.dto';

export class InvoiceDto {
  id: string;
  createdOn: Date;
  mode: string; // Using string for enum type, replace with actual enum type if available
  auctionsId: string;
  number: number;
  bidderId: string;
  notes: any; // Json type in TypeScript can be represented as 'any'
  specialInstructions: string;
  poNumber: string;
  dueDate: Date;
  isShipping: boolean;
  invoiceTemplatesId: string;
  currencyType: string; // Using string for enum type, replace with actual enum type if available
  itemsSubtotal: number;
  shippingSubtotal: number;
  taxesSubtotal: number;
  totalAmount: number;
  amountDue: number;
  amountPaid: number;
  buyerPremiumTotal: number;
  isPaidInFull: boolean;

  constructor(row: any, invoiceId?: string) {
    this.id = invoiceId ?? row.id;
    this.mode = row.mode;
    if (!invoiceId) {
      this.auctionsId = row.auctionsId;
      this.bidderId = row.bidderId;
      this.number = row.number;
      this.createdOn = row.createdOn;
    }
    this.notes = row.notes;
    this.specialInstructions = row.specialInstructions;
    this.poNumber = row.poNumber;
    this.dueDate = row.dueDate;
    this.isShipping = row.isShipping;
    this.invoiceTemplatesId = row.invoiceTemplatesId;
    this.currencyType = row.currencyType;
    this.itemsSubtotal = parseFloat(row.itemsSubtotal); // Ensuring it's a number
    this.shippingSubtotal = parseFloat(row.shippingSubtotal); // Ensuring it's a number
    this.taxesSubtotal = parseFloat(row.taxesSubtotal); // Ensuring it's a number
    this.totalAmount = parseFloat(row.totalAmount); // Ensuring it's a number
    this.amountDue = parseFloat(row.amountDue); // Ensuring it's a number
    this.amountPaid = this.totalAmount - this.amountDue;
    this.isPaidInFull = row.isPaidInFull;
    this.buyerPremiumTotal = parseFloat(row.buyerPremiumTotal);
  }

  static generate(
    auction: AuctionDto,
    bidderId: string,
    currencyType: string,
    itemsSubtotal: number,
    amountDue: number,
    totalAmount: number,
    taxesSubtotal: number,
    buyerPremiumSubTotal: number,
  ): InvoiceDto {
    return new InvoiceDto({
      mode: 'Draft',
      auctionsId: auction.id,
      bidderId: bidderId,
      notes: null,
      specialInstructions: '',
      poNumber: '',
      dueDate: new Date(new Date().setDate(new Date().getDate() + 30)),
      isShipping: false,
      invoiceTemplatesId: auction.invoiceTemplatesId,
      currencyType: 'CAD',
      itemsSubtotal: itemsSubtotal,
      shippingSubtotal: 0.0,
      taxesSubtotal: taxesSubtotal,
      totalAmount: totalAmount,
      amountDue: amountDue,
      buyerPremiumTotal: buyerPremiumSubTotal,
    });
  }

  setBuyerPremiumTotal(buyerPremiumTotal: number): void {
    this.buyerPremiumTotal = buyerPremiumTotal;
  }
}
