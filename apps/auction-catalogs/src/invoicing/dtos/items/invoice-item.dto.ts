export class InvoiceItemDto {
  id: string;
  invoicesId: string;
  lotsId: string;
  quantity: number;
  price: number;
  name: string;
  buyerPremiumAmount: number;
  auctionLotId: string;
  itemMappingsId: string;

  constructor(row: any, invoiceId?: string) {
    if (!invoiceId) {
      this.id = row.id;
    }
    this.lotsId = row.lotsId;
    this.invoicesId = invoiceId ?? row.invoicesId;
    this.quantity = row.quantity;
    this.price = parseFloat(row.price); // Ensuring it's a number
    this.name = row.name;
    this.buyerPremiumAmount = parseFloat(row.buyerPremiumAmount); // Ensuring it's a number
    this.auctionLotId = row.auctionLotId;
    this.itemMappingsId = row.itemMappingsId;
  }
}
