export class InvoiceBuyerDto {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  telephone: string;
  companyName: string;
  accountMappingsId: string;
  invoicesId?: string;

  constructor(row: any, invoiceId?: string) {
    if (!invoiceId) {
      this.id = row.id;
    }
    this.firstname = row.firstname;
    this.lastname = row.lastname;
    this.email = row.email;
    this.telephone = row.telephone;
    this.companyName = row.companyName ?? '';
    this.accountMappingsId = row.accountMappingsId;
    this.invoicesId = invoiceId ?? row.invoicesId;
  }
}
