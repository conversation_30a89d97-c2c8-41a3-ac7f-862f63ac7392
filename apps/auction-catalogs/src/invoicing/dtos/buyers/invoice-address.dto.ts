export class InvoiceAddressDto {
  id: string;
  address1: string;
  address2: string;
  city: string;
  province: string;
  country: string;
  postalCode: string;
  buzzer: string;
  accountAddressesId: string;
  invoicesId: string;

  constructor(row: any, invoiceId?: string) {
    if (!invoiceId) {
      this.id = row.id;
    }
    this.address1 = row.address1;
    this.address2 = row.address2;
    this.city = row.city;
    this.province = row.province;
    this.country = row.country;
    this.postalCode = row.postalCode;
    this.buzzer = row.buzzer;
    this.accountAddressesId = row.accountAddressesId;
    this.invoicesId = invoiceId ?? row.invoicesId;
  }
}
