export class Constants {
  static readonly INVALID_REQUEST_ERROR: number = 41000;
  static readonly INVOICE_NOT_FOUND_ERROR: number = 41010;
  static readonly INVOICE_STATUS_NOT_FOUND: number = 41020;
  static readonly INVOICE_SHIPPING_NOT_FOUND: number = 41030;
  static readonly INVOICE_NOTE_NOT_FOUND: number = 41040;
  static readonly INVOICE_PULLER_SCHEDULE_NOT_FOUND: number = 41050;
  static readonly TIMESLOT_UNAVAILABLE: number = 41060;
  static readonly INVOICE_ALREADY_BOOKED_ERROR: number = 41070;
  static readonly SHIPPING_INVOICE_ALREADY_EXISTS: number = 41080;
  static readonly PAYMENT_STILL_OWING_EXCEPTION: number = 41090;
  static readonly INVOICE_OVERPAYMENT_ERROR: number = 41100;
  static readonly SHIPPING_NOT_FOUND_ERROR: number = 41110;
  static readonly INOICE_IDS_DO_NOT_MATCH: number = 41120;
  static readonly MULTIPLE_PAYMENTS_STILL_OWING_ERROR: number = 41130;
  static readonly INVOICE_LIST_MISMATCH_ERROR: number = 41140;
  static readonly DOES_NOT_OWN_INVOICE: number = 41150;
  static readonly UNSUPPORTED_PAYMENT_TYPE: number = 41160;
  static readonly INVALID_MAPPING_ERROR: number = 41170;
  static readonly IMMUTABLE_UPDATE_ATTEMPT_ERROR: number = 41180;
  static readonly TAX_TYPE_NOT_FOUND: number = 41190;
  static readonly INVOICE_PICKUP_BIN_NOT_FOUND: number = 41200;
  static readonly DUPLICATE_TRACKING_NUMBER_FOUND: number = 41210;
  static readonly COULD_NOT_UPDATE_INVOICE_STATUS: number = 41220;
  static readonly COULD_NOT_APPROVE_INVOICES: number = 41230;
  static readonly SPLITTABLE_AUCTION_ITEMS_NOT_FOUND: number = 41240;
  static readonly COPY_INVOICE_ERROR: number = 41250;
  static readonly INVOICESPLIT_NOT_FOUND_ERROR: number = 41260;
}
