import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { InvoicePullersDbService } from '../data/invoice-pullers-db.service';
import { InvoicePullerDto } from '../dtos/invoice-puller.dto';

@Injectable()
export class InvoicePullersHandler extends BaseHandler<InvoicePullersDbService, InvoicePullerDto> {
  constructor(dbService: InvoicePullersDbService) {
    super(dbService);
  }
}
