import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { TrackingNumbersDbService } from '../data/tracking-numbers-db.service';
import { TrackingNumberDto } from '../dtos/tracking-number.dto';

@Injectable()
export class TrackingNumbersHandler extends BaseHandler<TrackingNumbersDbService, TrackingNumberDto> {
  constructor(dbService: TrackingNumbersDbService) {
    super(dbService);
  }
}
