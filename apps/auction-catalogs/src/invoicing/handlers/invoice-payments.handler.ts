import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { InvoicePaymentsDbService } from '../data/invoice-payments-db.service';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoicePaymentExportDto } from '../dtos/invoice-payment-export.dto';

@Injectable()
export class InvoicePaymentsHandler extends BaseHandler<InvoicePaymentsDbService, InvoicePaymentDto> {
  constructor(dbService: InvoicePaymentsDbService) {
    super(dbService);
  }

  async getAllByBidderId(page: number, size: number, params: any[]): Promise<ListResultset<InvoicePaymentDto>> {
    return await this.dbService.getAllByBidderId(page, size, params);
  }

  async getBidderPayment(accountMappingsId: string, id: string): Promise<InvoicePaymentDto> {
    return await this.dbService.getBidderPayment(accountMappingsId, id);
  }

  async getAllPaymentsForExport(page: number, size: number, params: any[]): Promise<ListResultset<InvoicePaymentExportDto>> {
    return await this.dbService.getAllPaymentsForExport(page, size, params);
  }
}
