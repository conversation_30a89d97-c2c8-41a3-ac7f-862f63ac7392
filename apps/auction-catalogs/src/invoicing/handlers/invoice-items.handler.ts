import { ListResultset } from '@mvc/data/list-resultset';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoiceItemsDbService } from '../data/invoice-items-db.service';
import { InvoiceItemWithTaxDto } from '../dtos/items/invoice-item-with-tax.dto';
import { InvoiceItemDto } from '../dtos/items/invoice-item.dto';

@Injectable()
export class InvoiceItemsHandler extends BaseHandler<InvoiceItemsDbService, InvoiceItemDto> {
  constructor(dbService: InvoiceItemsDbService) {
    super(dbService);
  }

  async getAllByItemMappingsIdMap(itemMappingsIdMap: Map<string, string>): Promise<Array<InvoiceItemDto>> {
    return await this.dbService.getAllByItemMappingsIdMap(itemMappingsIdMap);
  }

  async getAllByInvoiceId(invoiceId: string): Promise<ListResultset<InvoiceItemDto>> {
    return await this.dbService.getAllCustom(-1, -1, [{ invoicesId: invoiceId }]);
  }

  async createInvoiceItems(finalInvoiceItems: Array<InvoiceItemDto>): Promise<Array<InvoiceItemDto>> {
    return await this.dbService.createInvoiceItems(finalInvoiceItems);
  }

  async getInvoiceItemsWithTaxes(invoiceId: string): Promise<ListResultset<InvoiceItemWithTaxDto>> {
    return await this.dbService.getInvoiceItemsWithTaxes(invoiceId);
  }

  async splitInvoice(userId: string, invoiceId: string, newInvoiceId: string, ids: string[]): Promise<ListResultset<InvoiceItemDto>> {
    return await this.dbService.splitInvoice(userId, invoiceId, newInvoiceId, ids);
  }

  async getAllByInvoiceIdAndInvoiceItemId(invoiceId: string, itemMappingsId: string[]): Promise<ListResultset<InvoiceItemDto>> {
    return await this.dbService.getAllByInvoiceIdAndInvoiceItemId(invoiceId, itemMappingsId);
  }
}
