import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { TaxTypeDbService } from '../data/tax-types-db.service';
import { TaxTypeDto } from '../dtos/tax-type.dto';

@Injectable()
export class TaxTypeHandler extends BaseHandler<TaxTypeDbService, TaxTypeDto> {
  constructor(dbService: TaxTypeDbService) {
    super(dbService);
  }

  async getByAuctionsId(auctionsId: string): Promise<TaxTypeDto> {
    return await this.dbService.getByAuctionsId(auctionsId);
  }

  async getMapByLotIds(lotIds: string[]): Promise<{ [key: string]: TaxTypeDto }> {
    return this.dbService.getMapByLotIds(lotIds);
  }
}
