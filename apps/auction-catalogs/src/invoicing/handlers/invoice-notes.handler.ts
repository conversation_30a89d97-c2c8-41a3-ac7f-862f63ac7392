import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoiceNoteDto } from '../dtos/invoice-note.dto';
import { InvoiceNotesDbService } from '../data/invoice-notes-db.service';

@Injectable()
export class InvoiceNotesHandler extends BaseHandler<InvoiceNotesDbService, InvoiceNoteDto> {
  constructor(dbService: InvoiceNotesDbService) {
    super(dbService);
  }
}
