import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { InvoiceShippingDbService } from '../data/invoice-shipping-db.service';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoiceShippingListDto } from '../dtos/shipping/invoice-shipping-list.dto';

@Injectable()
export class InvoiceShippingHandler extends BaseHandler<InvoiceShippingDbService, InvoiceShippingDto> {
  constructor(dbService: InvoiceShippingDbService) {
    super(dbService);
  }

  async getAllList(page: number, size: number, params: any[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceShippingListDto>> {
    return await this.dbService.getAllList(page, size, params, orderBy);
  }
  async getByInvoiceId(invoiceId: string): Promise<InvoiceShippingDto> {
    return await this.dbService.getCustom([{ invoicesId: invoiceId }]);
  }
}
