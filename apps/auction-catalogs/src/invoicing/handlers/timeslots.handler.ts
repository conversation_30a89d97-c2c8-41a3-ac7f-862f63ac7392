import { ListResultset } from '@mvc/data/list-resultset';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { TimeslotsDbService } from '../data/timeslots-db.service';
import { AdminTimeslotListDto } from '../dtos/timeslots/admin-timeslot-list.dto';
import { TimeslotAvailabilityListDto } from '../dtos/timeslots/timeslot-availability-list.dto';
import { TimeslotDto } from '../dtos/timeslots/timeslot.dto';

@Injectable()
export class TimeslotsHandler extends BaseHandler<TimeslotsDbService, TimeslotDto> {
  constructor(dbService: TimeslotsDbService) {
    super(dbService);
  }

  async updateStatus(userId: string, id: string, status: string): Promise<TimeslotDto> {
    return await this.dbService.updateStatus(userId, id, status);
  }

  async getBookings(params: any[]): Promise<ListResultset<TimeslotAvailabilityListDto>> {
    return await this.dbService.getBookings(params);
  }

  async getAdminBookings(params: any[]): Promise<ListResultset<AdminTimeslotListDto>> {
    return await this.dbService.getAdminBookings(params);
  }
}
