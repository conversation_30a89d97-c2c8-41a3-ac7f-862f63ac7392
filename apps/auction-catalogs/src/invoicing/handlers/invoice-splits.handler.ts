import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { InvoiceSplitsDbService } from '../data/invoice-splits-db.service';
import { InvoiceSplitDto } from '../dtos/invoice-split.dto';
import { InvoiceSplitDetailDto } from '../dtos/invoice-split-detail.dto';

@Injectable()
export class InvoiceSplitsHandler extends BaseHandler<InvoiceSplitsDbService, InvoiceSplitDto> {
  constructor(dbService: InvoiceSplitsDbService) {
    super(dbService);
  }

  async getRelatedInvoices(id: string): Promise<InvoiceSplitDetailDto[]> {
    return await this.dbService.getRelatedInvoices(id);
  }
}
