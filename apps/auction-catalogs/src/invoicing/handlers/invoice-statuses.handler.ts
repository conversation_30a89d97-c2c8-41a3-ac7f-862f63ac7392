import { <PERSON>Hand<PERSON> } from '@mvc/handlers/base.handler';
import { InvoiceStatusesDbService } from '../data/invoice-statuses-db.service';
import { InvoiceStatusDto } from '../dtos/invoice-status.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class InvoiceStatusesHandler extends BaseHandler<InvoiceStatusesDbService, InvoiceStatusDto> {
  constructor(dbService: InvoiceStatusesDbService) {
    super(dbService);
  }
}
