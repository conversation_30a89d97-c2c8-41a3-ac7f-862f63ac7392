import { <PERSON>Hand<PERSON> } from '@mvc/handlers/base.handler';
import { InvoiceBuyersDbService } from '../data/invoice-buyers-db.service';
import { InvoiceBuyerDto } from '../dtos/buyers/invoice-buyer.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class InvoiceBuyersHandler extends BaseHandler<InvoiceBuyersDbService, InvoiceBuyerDto> {
  constructor(dbService: InvoiceBuyersDbService) {
    super(dbService);
  }

  async splitInvoice(userId: string, invoiceId: string, newInvoiceId: string): Promise<InvoiceBuyerDto> {
    return await this.dbService.splitInvoice(userId, invoiceId, newInvoiceId);
  }
}
