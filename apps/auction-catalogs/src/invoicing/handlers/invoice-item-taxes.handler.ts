import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoiceItemTaxesDbService } from '../data/invoice-item-taxes-db.service';
import { InvoiceItemTaxDto } from '../dtos/items/invoice-item-tax.dto';

@Injectable()
export class InvoiceItemTaxesHandler extends BaseHandler<InvoiceItemTaxesDbService, InvoiceItemTaxDto> {
  constructor(dbService: InvoiceItemTaxesDbService) {
    super(dbService);
  }

  async createInvoiceItemTaxes(finalInvoiceItemTaxes: Array<InvoiceItemTaxDto>): Promise<void> {
    await this.dbService.createInvoiceItemTaxes(finalInvoiceItemTaxes);
  }
}
