import { BaseHandler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoiceShippingTaxesDto } from '../dtos/shipping/invoice-shipping-taxes.dto';
import { InvoiceShippingTaxesDbService } from '../data/invoice-shipping-taxes-db.service';

@Injectable()
export class InvoiceShippingTaxesHandler extends BaseHandler<InvoiceShippingTaxesDbService, InvoiceShippingTaxesDto> {
  constructor(dbService: InvoiceShippingTaxesDbService) {
    super(dbService);
  }
}
