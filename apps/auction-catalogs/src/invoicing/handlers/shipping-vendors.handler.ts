import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { ShippingVendorsDbService } from '../data/shipping-vendors-db.service';
import { ShippingVendorDto } from '../dtos/shipping-vendor.dto';

@Injectable()
export class ShippingVendorsHandler extends BaseHandler<ShippingVendorsDbService, ShippingVendorDto> {
  constructor(dbService: ShippingVendorsDbService) {
    super(dbService);
  }

  async getById(id: number): Promise<ShippingVendorDto> {
    return await this.dbService.getCustom([{ id: id }]);
  }
}
