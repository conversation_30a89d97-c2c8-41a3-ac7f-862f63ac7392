import { ListResultset } from '@mvc/data/list-resultset';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoicesDbService } from '../data/invoices-db.service';
import { InvoiceListDto } from '../dtos/invoice-list.dto';
import { InvoiceDto } from '../dtos/invoice.dto';

@Injectable()
export class InvoicesHandler extends BaseHandler<InvoicesDbService, InvoiceDto> {
  constructor(dbService: InvoicesDbService) {
    super(dbService);
  }

  async getAllPublic(page: number, size: number, params: any[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceListDto>> {
    //need a different method than getAll() because the return type is different than crud signature
    return await this.dbService.getAllPublic(page, size, params, orderBy);
  }

  async getAllAdmin(page: number, size: number, params: any[], orderBy?: Record<string, 'asc' | 'desc'>): Promise<ListResultset<InvoiceListDto>> {
    //need a different method than getAll() because the return type is different than crud signature
    return await this.dbService.getAllAdmin(page, size, params, orderBy);
  }

  async getByTrackingNumber(trackingNumber: string): Promise<InvoiceDto> {
    return await this.dbService.getByTrackingNumber(trackingNumber);
  }

  async getByInvoiceNumber(invoiceNumber: number): Promise<InvoiceDto> {
    return await this.dbService.getByInvoiceNumber(invoiceNumber);
  }

  async setInvoiceStatuses(auctionId: string, status: string): Promise<number> {
    return await this.dbService.setInvoiceStatuses(auctionId, status);
  }

  async setPaidAndAmountDueAndTotal(invoiceId: string, isPaidInFull: boolean, amountDue: number, totalAmount?: number, taxesSubtotal?: number): Promise<InvoiceDto> {
    return await this.dbService.setPaidAndAmountDueAndTotal(invoiceId, isPaidInFull, amountDue, totalAmount, taxesSubtotal);
  }

  async approveAllInvoicesInAuction(auctionsId: string): Promise<number> {
    return await this.dbService.approveAllInvoicesInAuction(auctionsId);
  }

  async splitInvoice(userId: string, invoiceId: string): Promise<InvoiceDto> {
    return await this.dbService.splitInvoice(userId, invoiceId);
  }
}
