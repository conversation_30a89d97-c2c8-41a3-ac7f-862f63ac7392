import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { InvoicesShippingAddressesDbService } from '../data/invoices-shipping-addresses-db.service';
import { InvoiceAddressDto } from '../dtos/buyers/invoice-address.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class InvoicesShippingAddressesHandler extends BaseHandler<InvoicesShippingAddressesDbService, InvoiceAddressDto> {
  constructor(dbService: InvoicesShippingAddressesDbService) {
    super(dbService);
  }
}
