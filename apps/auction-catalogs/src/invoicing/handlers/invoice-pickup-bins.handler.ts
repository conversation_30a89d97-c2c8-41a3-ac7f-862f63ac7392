import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { Injectable } from '@nestjs/common';
import { InvoicePickupBinsDbService } from '../data/invoice-pickup-bins-db.service';
import { InvoicePickupBinDto } from '../dtos/pickup-bins/invoice-pickup-bin.dto';

@Injectable()
export class InvoicePickupBinsHandler extends BaseHandler<InvoicePickupBinsDbService, InvoicePickupBinDto> {
  constructor(dbService: InvoicePickupBinsDbService) {
    super(dbService);
  }

  async getPickupBinByName(pickupBin: string): Promise<InvoicePickupBinDto> {
    return await this.dbService.getPickupBinByName(pickupBin);
  }

  async setBinsForInvoice(userId: string, invoicesId: string, pickupBins: string[]): Promise<void> {
    await this.dbService.setBinsForInvoice(userId, invoicesId, pickupBins);
  }
}
