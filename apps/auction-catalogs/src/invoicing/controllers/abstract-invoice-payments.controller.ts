import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { PaymentType, UsersApi } from '@viewbid/ts-node-client';
import * as _ from 'lodash';
import { PaymentProfilesController } from '../../payment-processing/controller/payment-profiles.controller';
import { TransactionRequestsController } from '../../payment-processing/controller/transaction-requests.controller';
import { PaymentProfileDto } from '../../payment-processing/dtos/payment-profile.dto';
import { TransactionAddressDto } from '../../payment-processing/dtos/transaction-address.dto';
import { TransactionLineItemDto } from '../../payment-processing/dtos/transaction-line-item.dto';
import { TransactionRequestDto } from '../../payment-processing/dtos/transaction-request.dto';
import { TransactionShippingCostDto } from '../../payment-processing/dtos/transaction-shipping-cost.dto';
import { PaymentProfileNotFoundError } from '../../payment-processing/exceptions/payment-profiles-not-found-error';
import { ProfileTransactionRequestRequest } from '../../payment-processing/http/requests/profile-transaction-request.request';
import { CreateTransactionRequestResponse } from "../../payment-processing/http/responses/create-transaction-request.response'";
import { AddressType } from '../../prisma/generated/auction-catalogs-db-client';
import { InvoicePaymentDto } from '../dtos/invoice-payment.dto';
import { InvoiceDto } from '../dtos/invoice.dto';
import { InvoiceItemDto } from '../dtos/items/invoice-item.dto';
import { InvoiceItemsHandler } from '../handlers/invoice-items.handler';
import { InvoicePaymentsHandler } from '../handlers/invoice-payments.handler';
import { InvoicesHandler } from '../handlers/invoices.handler';
import { InvoicePaymentRequest } from '../http/requests/invoice-payment.request';
import { InvoicePaymentResponse } from '../http/responses/invoice-payment.response';
import { InvoicePaymentsListResponse } from '../http/responses/invoice-payments-list.response';
import { checkForOverPayment, getPaymentType, mapStateToShort, mapToTransactionType } from '../utils/invoice-payment-utils';
import { v4 as uuidv4 } from 'uuid';
import { InvoiceShippingHandler } from '../handlers/invoice-shipping.handler';
import { InvoiceShippingDto } from '../dtos/shipping/invoice-shipping.dto';

export abstract class AbstractInvoicePaymentsController extends PubSubController<
  InvoicePaymentsHandler,
  InvoicePaymentRequest,
  InvoicePaymentDto,
  InvoicePaymentResponse,
  InvoicePaymentsListResponse
> {
  constructor(
    public handler: InvoicePaymentsHandler,
    public invoiceShippingHandler: InvoiceShippingHandler,
    public redisService: RedisService,
    public invoicesHandler: InvoicesHandler,
    public invoiceItemsHandler: InvoiceItemsHandler,
    public transactionRequestsController: TransactionRequestsController,
    public paymentProfilePayments: PaymentProfilesController,
    public usersApi: UsersApi,
  ) {
    super(handler, redisService, 'invoice_payments');
  }

  async createPayment(invoiceId: string, body: InvoicePaymentRequest, req: VBInterceptorRequest, invoiceDto?: InvoiceDto): Promise<InvoicePaymentResponse | ErrorResponse> {
    return this.executeAndNotify(
      'create_payment',
      async () => {
        //define outside of try/catch so we have access to it on error
        let invoice: InvoiceDto | undefined = invoiceDto;
        try {
          if (!invoice) {
            invoice = await this.invoicesHandler.get(invoiceId);
          }

          const rawDto: InvoicePaymentDto = this.createDtoFromRequest(body);
          checkForOverPayment(invoice, rawDto);
          rawDto.invoicesId = invoice.id;
          rawDto.isPartial = body.amountPaid < invoice.amountDue;

          // Set location based on payment type or provided value
          rawDto.location = body.location ?? 'Online';

          if (body.paymentTypes === PaymentType.OnlineCard) {
            rawDto.id = uuidv4();
            const response: CreateTransactionRequestResponse | ErrorResponse | undefined = await this.runOnlinePayment(invoice, req, body, rawDto);
            if (response instanceof ErrorResponse) {
              return response;
            }
          }

          rawDto.currentOwing = invoice.amountDue - rawDto.amountPaid;
          const invoicePayment: InvoicePaymentDto = await this.handler.create(req.accountMappingsId, rawDto);

          invoice.isPaidInFull = invoice.amountDue - invoicePayment.amountPaid === 0;
          invoice.amountDue = invoice.amountDue - invoicePayment.amountPaid;
          invoice = await this.invoicesHandler.setPaidAndAmountDueAndTotal(invoice.id, invoice.isPaidInFull, invoice.amountDue);

          this.notify('success', 'payment_made', {
            invoice: invoice,
            invoicePayment: invoicePayment,
          });

          if (invoice.isPaidInFull) {
            this.notify('success', 'payment_complete', {
              invoice: invoice,
              invoicePayment: invoicePayment,
            });
          }

          return new InvoicePaymentResponse(invoicePayment);
        } catch (error) {
          return new ErrorResponse(error, 'createPayment');
        }
      },
      {},
    );
  }

  async getShippingCost(invoice: InvoiceDto): Promise<number> {
    if (invoice.isShipping) {
      const invoiceShippingDto: InvoiceShippingDto = await this.invoiceShippingHandler.getCustom([{ invoicesId: invoice.id }]);
      return invoiceShippingDto.subtotal;
    } else {
      return 0;
    }
  }

  async runOnlinePayment(
    invoice: InvoiceDto,
    req: VBInterceptorRequest,
    body: InvoicePaymentRequest,
    rawDto: InvoicePaymentDto,
  ): Promise<CreateTransactionRequestResponse | ErrorResponse | undefined> {
    const response: CreateTransactionRequestResponse | ErrorResponse = await this.runOnlineCreditTransaction(invoice, body, req, rawDto);
    rawDto.transactionCode = response?.data?.result?.transId ?? '';
    rawDto.paymentTypes = getPaymentType(body.paymentTypes);
    return response;
  }

  async runOnlineCreditTransaction(
    invoice: InvoiceDto,
    invoicePaymentReq: InvoicePaymentRequest,
    req: VBInterceptorRequest,
    rawDto: InvoicePaymentDto,
  ): Promise<CreateTransactionRequestResponse | ErrorResponse> {
    const request: ProfileTransactionRequestRequest = await this.generateRequestForTransactionRequest(invoice, invoicePaymentReq, req, rawDto);
    return await this.transactionRequestsController.createWithProfile(request, req);
  }

  async generateRequestForTransactionRequest(
    invoice: InvoiceDto,
    invoicePaymentReq: InvoicePaymentRequest,
    req: VBInterceptorRequest,
    rawDto: InvoicePaymentDto,
  ): Promise<ProfileTransactionRequestRequest> {
    const [transactionRequest, transactionAddresses, transactionShipping, requestLineItems] = await Promise.all([
      this.buildTransactionRequest(invoicePaymentReq, invoice, rawDto),
      this.buildTransactionAddresses(invoicePaymentReq, req),
      this.buildTransactionShippingCost(invoice),
      this.buildRequestLineItems(invoice),
    ]);

    const request: ProfileTransactionRequestRequest = new ProfileTransactionRequestRequest({
      transactionRequest: transactionRequest,
      transactionAddresses: [transactionAddresses],
      transactionLineItems: requestLineItems,
      customerPaymentProfileId: invoicePaymentReq.customerPaymentProfileId,
      transactionShipping: transactionShipping,
    });

    return request;
  }

  private async buildRequestLineItems(invoice: InvoiceDto): Promise<Array<TransactionLineItemDto>> {
    const lineItems: ListResultset<InvoiceItemDto> = await this.invoiceItemsHandler.getAllByInvoiceId(invoice.id);
    const requestLineItems: Array<TransactionLineItemDto> = _.map(lineItems.list, (item: InvoiceItemDto) => {
      return new TransactionLineItemDto({
        itemMappingsId: item.itemMappingsId,
        name: item.name,
        description: '', //TODO - same here - where does this come from?
        quantity: item.quantity,
        unitPrice: item.price,
      });
    });
    return requestLineItems;
  }

  private buildTransactionShippingCost(invoice: InvoiceDto): TransactionShippingCostDto {
    return new TransactionShippingCostDto({
      name: 'Shipping', //TODO - Where does this come from?
      amount: invoice.shippingSubtotal,
      description: '',
    });
  }

  private async buildTransactionAddresses(invoicePaymentReq: InvoicePaymentRequest, req: VBInterceptorRequest): Promise<TransactionAddressDto> {
    const profile: PaymentProfileDto = (await this.paymentProfilePayments.getProfile(invoicePaymentReq.customerPaymentProfileId, req)).data;

    if (!profile.id) {
      throw new PaymentProfileNotFoundError(invoicePaymentReq.customerPaymentProfileId);
    }

    const [userDto, billingAddress] = await Promise.all([
      this.usersApi.getAllByAccountMappingsIds(req.accountMappingsId, 1, -1),
      this.usersApi.getUserAccountAddressById(profile.billingAddressId),
    ]).then((values: any) => {
      return [values[0].data.data[0], values[1].data.data];
    });

    const transactionAddresses: TransactionAddressDto = new TransactionAddressDto({
      address: billingAddress.address1,
      firstname: userDto.firstname,
      lastname: userDto.lastname,
      companyName: '', //TODO - Where does this come from - should we look up org?
      address1: billingAddress.address1,
      address2: billingAddress.address2,
      city: billingAddress.city,
      state: mapStateToShort(billingAddress.province),
      zip: billingAddress.postalCode,
      country: billingAddress.country,
      addressType: AddressType.Billing,
    });
    return transactionAddresses;
  }

  private buildTransactionRequest(invoicePaymentReq: InvoicePaymentRequest, invoice: InvoiceDto, rawDto: InvoicePaymentDto): TransactionRequestDto {
    return new TransactionRequestDto({
      transactionType: mapToTransactionType(invoicePaymentReq.paymentTypes).valueOf(),
      amount: invoicePaymentReq.amountPaid,
      invoicesId: invoice.id,
      description: '', //TODO - Is this description required? What should go here?
      invoiceNumber: String(invoice.number),
      invoicePaymentsId: rawDto.id,
    });
  }
}
