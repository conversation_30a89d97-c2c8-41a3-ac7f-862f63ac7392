import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Post, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import * as _ from 'lodash';
import { TrackingNumberDto } from '../../dtos/tracking-number.dto';
import { DuplicateTrackingNumbersFoundError } from '../../exceptions/duplicate-tracking-numbers-found-error';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { ShippingVendorsHandler } from '../../handlers/shipping-vendors.handler';
import { TrackingNumbersHandler } from '../../handlers/tracking-numbers.handler';
import { TrackingNumberRequest } from '../../http/requests/tracking-number.request';
import { TrackingNumberResponse } from '../../http/responses/tracking-number.response';
import { TrackingNumbersListResponse } from '../../http/responses/tracking-numbers-list.response';

@Controller('admin/tracking-numbers')
export class TrackingNumbersController extends BaseController<
  TrackingNumbersHandler,
  TrackingNumberRequest,
  TrackingNumberDto,
  TrackingNumberResponse,
  TrackingNumbersListResponse
> {
  constructor(
    handler: TrackingNumbersHandler,
    private invoiceShippingHandler: InvoiceShippingHandler,
    private vendorsHandler: ShippingVendorsHandler,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: TrackingNumberRequest): TrackingNumberDto {
    return new TrackingNumberDto(request);
  }

  createResponseFromDto(dto: TrackingNumberDto): TrackingNumberResponse {
    return new TrackingNumberResponse(dto, {});
  }

  createResponseList(list: ListResultset<TrackingNumberDto>): TrackingNumbersListResponse {
    return new TrackingNumbersListResponse(list);
  }

  @Post('/:invoiceShippingId')
  async saveTrackingNumbers(
    @Param('invoiceShippingId') invoiceShippingId: string,
    @Body() body: TrackingNumberRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<TrackingNumberResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;

      // Fetch invoice shipping and vendor concurrently for efficiency
      const [invoiceShipping, vendor, currentTrackingNumbers] = await Promise.all([
        this.invoiceShippingHandler.get(invoiceShippingId),
        this.vendorsHandler.getCustom([{ id: body.shippingVendorsId }]),
        this.handler.getAll(1, -1, [{ number: body.numbers, shippingVendorsId: body.shippingVendorsId, deletedOn: null }]),
      ]);

      if (currentTrackingNumbers.totalCount > 0) {
        throw new DuplicateTrackingNumbersFoundError(_.map(currentTrackingNumbers.list, 'number').join(', '));
      }

      // Map tracking numbers to DTOs in a concise manner
      const trackingDtos: TrackingNumberDto[] = body.numbers.map(
        (number: string) => new TrackingNumberDto({ number, shippingVendorsId: vendor.id, invoiceShippingId: invoiceShipping.id }),
      );

      // Create tracking numbers concurrently
      const createdItems: TrackingNumberDto[] = await Promise.all(trackingDtos.map((dto: TrackingNumberDto) => this.handler.create(userId, dto)));

      // Return the response with created tracking numbers
      return new TrackingNumberResponse(createdItems);
    } catch (error) {
      return new ErrorResponse(error, 'saveTrackingNumbers');
    }
  }

  @Patch('/:invoiceShippingId')
  async update(
    @Param('invoiceShippingId') invoiceShippingId: string,
    @Body() body: TrackingNumberRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<TrackingNumberResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;

      // Fetch invoice shipping and vendor concurrently
      const [invoiceShipping, vendor, currentTrackingNumbers] = await Promise.all([
        this.invoiceShippingHandler.get(invoiceShippingId),
        this.vendorsHandler.getCustom([{ id: body.shippingVendorsId }]),
        this.handler.getAll(1, -1, [{ number: body.numbers, shippingVendorsId: body.shippingVendorsId, invoiceShippingId: { not: invoiceShippingId }, deletedOn: null }]),
      ]);

      if (currentTrackingNumbers.totalCount > 0) {
        throw new DuplicateTrackingNumbersFoundError(_.map(currentTrackingNumbers.list, 'number').join(', '));
      }

      // First, delete the existing rows
      await this.handler.deleteBulk(userId, { invoiceShippingId: invoiceShipping.id });

      // Then, create the new tracking numbers
      const createdItems: ListResultset<TrackingNumberDto> = await this.handler.createOrReplaceBulk(
        body.numbers.map(
          (trackingNumber: any) =>
            new TrackingNumberDto({
              number: trackingNumber,
              shippingVendorsId: vendor.id,
              invoiceShippingId: invoiceShipping.id,
            }),
        ),
      );
      // Return response with created tracking numbers
      return new TrackingNumberResponse(createdItems.list);
    } catch (error) {
      return new ErrorResponse(error, 'saveTrackingNumbers');
    }
  }

  @Get('/:trackingNumber')
  async get(@Param('trackingNumber') trackingNumber: string, @Req() req: VBInterceptorRequest): Promise<TrackingNumberResponse | ErrorResponse> {
    req;
    try {
      const item: TrackingNumberDto = await this.handler.get(trackingNumber);

      return this.createResponseFromDto(item);
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }
}
