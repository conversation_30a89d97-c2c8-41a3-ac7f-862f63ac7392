import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { SuccessResponse } from '@mvc/http/responses/success.response';
import { Body, Controller, Get, OnModuleInit, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { UserDto } from '@vb/nest/common-dtos/users/user.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { PdfService } from '@vb/pdf';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { GetAllByAccountMappingsIds200Response, UsersApi } from '@viewbid/ts-node-client';
import { InvoiceTemplateNotFoundError } from 'apps/auction-catalogs/src/invoice-template/exceptions/invoice-template-not-found-error';
import * as axios from 'axios';
import { ConfigService } from 'libs/config-service/src/config.service';
import { UserBillingDetailDto } from '../../../../../users/src/users/dtos/user-billing-detail.dto';
import { AuctionDto } from '../../../auction/dtos/auction.dto';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { S3Service } from '../../../aws/s3/data/s3.service';
import { InvoiceTemplateDto } from '../../../invoice-template/dtos/invoice-template.dto';
import { InvoiceTemplatesHandler } from '../../../invoice-template/handler/invoice-templates.handler';
import { MappingType, Mode, InvoiceStatusType } from '../../../prisma/generated/auction-catalogs-db-client';
import { InvoiceAddressDto } from '../../dtos/buyers/invoice-address.dto';
import { InvoiceBuyerDto } from '../../dtos/buyers/invoice-buyer.dto';
import { InvoiceDetailDto } from '../../dtos/invoice-detail.dto';
import { InvoiceListDto } from '../../dtos/invoice-list.dto';
import { InvoiceNoteDto } from '../../dtos/invoice-note.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoiceItemTaxDto } from '../../dtos/items/invoice-item-tax.dto';
import { InvoiceItemDto } from '../../dtos/items/invoice-item.dto';
import { ShippingVendorDto } from '../../dtos/shipping-vendor.dto';
import { InvoiceShippingDto } from '../../dtos/shipping/invoice-shipping.dto';
import { TrackingNumberDto } from '../../dtos/tracking-number.dto';
import { InvalidRequestError } from '../../exceptions/invalid-request.error';
import { InvoiceListMismatchError } from '../../exceptions/invoice-list-mismatch.error';
import { MultiplePaymentsOwingError } from '../../exceptions/multiple-payments-owing.error';
import { PaymentOwingError } from '../../exceptions/payment-owing.error';
import { SplittableAuctionItemsNotFoundError } from '../../exceptions/splittable-auction-items-not-found.error';
import { InvoiceFactory } from '../../factories/invoice.factory';
import { InvoiceBuyersHandler } from '../../handlers/invoice-buyers.handler';
import { InvoiceItemsHandler } from '../../handlers/invoice-items.handler';
import { InvoiceNotesHandler } from '../../handlers/invoice-notes.handler';
import { InvoicePaymentsHandler } from '../../handlers/invoice-payments.handler';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { InvoiceStatusesHandler } from '../../handlers/invoice-statuses.handler';
import { InvoicesShippingAddressesHandler } from '../../handlers/invoices-shipping-addresses.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { ShippingVendorsHandler } from '../../handlers/shipping-vendors.handler';
import { TrackingNumbersHandler } from '../../handlers/tracking-numbers.handler';
import { InvoiceIdListRequest } from '../../http/requests/invoice-id-list.request';
import { InvoiceRequest } from '../../http/requests/invoice.request';
import { SetCompleteInvoiceRequest } from '../../http/requests/set-complete-invoice.request';
import { SplitInvoiceRequest } from '../../http/requests/split-invoice.request';
import { InvoiceItemResponse } from '../../http/responses/invoice-item.response';
import { InvoiceListResponse } from '../../http/responses/invoice-list.response';
import { InvoiceNotFoundResponse } from '../../http/responses/invoice-not-found.response';
import { InvoiceResponse } from '../../http/responses/invoice.response';
import { InvoiceSplitDetailDto } from '../../dtos/invoice-split-detail.dto';
import { InvoiceSplitsHandler } from '../../handlers/invoice-splits.handler';
import { ApplicationPreferencesHandler } from 'apps/auction-catalogs/src/application-preferences/handler/app-preferences.handler';
import { TaxTypeHandler } from '../../handlers/tax-type.handler';
import { TaxTypeDto } from '../../dtos/tax-type.dto';
import { ListResponse } from '@mvc/http/responses/list-response';
import { InvoiceItemWithTaxDto } from '../../dtos/items/invoice-item-with-tax.dto';
import { InvoiceLineItemRequest } from '../../http/requests/invoice-line-item.request';
import { LotAttributesHandler } from '../../../lot-attributes/handler/lot-attributes.handler';
import { LotAttributeDto } from '../../../lot-attributes/dtos/lot-attribute.dto';
import { ItemMappingsHandler } from '../../../item-mappings/handler/item-mappings.handler';
import { ItemMappingDto } from '../../../item-mappings/dtos/item-mapping.dto';

@Controller('admin/invoices')
@UseGuards(IsAdmin)
export class InvoicesController extends PubSubController<InvoicesHandler, InvoiceRequest, InvoiceDto, InvoiceResponse, InvoiceListResponse> implements OnModuleInit {
  private bucketName: string = '';

  createDtoFromRequest(request: InvoiceRequest): InvoiceDto {
    return new InvoiceDto(request.invoice);
  }
  createResponseFromDto(dto: InvoiceDto): InvoiceResponse {
    return new InvoiceResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceDto>): InvoiceListResponse {
    return new InvoiceListResponse(list);
  }

  constructor(
    handler: InvoicesHandler,
    redis: RedisService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
    private readonly invoiceItemsHandler: InvoiceItemsHandler,
    private readonly taxTypeHandler: TaxTypeHandler,
    private readonly invoiceShippingAddressesHandler: InvoicesShippingAddressesHandler,
    private readonly invoiceShippingHandler: InvoiceShippingHandler,
    private readonly trackingNumbersHandler: TrackingNumbersHandler,
    private readonly shippingVendorsHandler: ShippingVendorsHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly invoiceFactory: InvoiceFactory,
    private readonly usersApi: UsersApi,
    private readonly invoiceNotesHandler: InvoiceNotesHandler,
    private readonly invoiceStatusesHandler: InvoiceStatusesHandler,
    private readonly invoicePaymentsHandler: InvoicePaymentsHandler,
    private readonly configService: ConfigService,
    private readonly pdfService: PdfService,
    private readonly invoiceTemplatesHandler: InvoiceTemplatesHandler,
    private readonly s3Service: S3Service,
    private readonly invoiceSplitsHandler: InvoiceSplitsHandler,
    private readonly appPreferencesHandler: ApplicationPreferencesHandler,
    private readonly lotAttributesHandler: LotAttributesHandler,
    private readonly itemMappingsHandler: ItemMappingsHandler,
  ) {
    super(handler, redis, 'invoices');
  }

  async onModuleInit(): Promise<void> {
    const templateBucketName: string | undefined = await this.configService.getSetting('TEMPLATE_BUCKET_NAME');

    if (!templateBucketName) {
      this.logger.error('Auction Catalog Service was not provided with the required env variable: TEMPLATE_BUCKET_NAME');
      process.kill(process.pid, 'SIGTERM');
    }

    this.bucketName = templateBucketName;
  }

  @Get('tax-types/all')
  async getAllTaxTypes(): Promise<ListResponse | ErrorResponse> {
    try {
      const taxTypes: ListResultset<TaxTypeDto> = await this.taxTypeHandler.getAll(1, -1, [], { percent: 'desc' });

      return new ListResponse(taxTypes, 'getAllTaxTypes');
    } catch (error) {
      return new ErrorResponse(error, 'getAllTaxTypes');
    }
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') _query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const items: ListResultset<InvoiceListDto> = await this.handler.getAllAdmin(page, size, params, { 'i.number': 'desc' });
      await this.getPullers(items);

      return new InvoiceListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAllInvoicesAdmin');
    }
  }

  private async getPullers(items: ListResultset<InvoiceListDto>): Promise<void> {
    // Extract accountMappingsIds from items.list.pullerId
    const accountMappingsIds: string[] = items.list.map((item: InvoiceListDto) => item.pullerId);

    // Make an API call to get user details
    const userResponse: axios.AxiosResponse<GetAllByAccountMappingsIds200Response> = await this.usersApi.getAllByAccountMappingsIds(accountMappingsIds.join(','), 1, -1);

    // Create a mapping of accountMappingsId to user details
    const users: { [key: string]: any } = userResponse.data.data.reduce((acc: { [key: string]: any }, user: any) => {
      acc[user.accountMappingsId] = {
        id: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
      };
      return acc;
    }, {});

    // Update each item in items.list with the corresponding puller details
    items.list.forEach((item: InvoiceListDto) => {
      if (item.pullerId && users[item.pullerId]) {
        item.puller = users[item.pullerId];
      }
    });
  }

  protected convertQueryToArray(uri: string, additionalParams?: { [key: string]: any }): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    // Extract 'page' and 'size' parameters as they're not used for searching
    searchParams.delete('page');
    searchParams.delete('size');

    // Extract the statusType parameter for filtering
    const statusType: string | null = searchParams.get('statusType');
    if (statusType) {
      searchParams.delete('statusType');
    }
    // Extract the bidderId parameter for filtering
    const bidderId: string | null = searchParams.get('bidderId');
    if (bidderId) {
      searchParams.delete('bidderId');
    }

    const auctionsId: string | null = searchParams.get('auctionsId');
    if (auctionsId) {
      searchParams.delete('auctionsId');
    }

    const unpaidInvoices: string | null = searchParams.get('unpaidInvoices');
    if (unpaidInvoices) {
      searchParams.delete('unpaidInvoices');
    }

    // If additionalParams is provided, add or override the parameters in the searchParams
    if (additionalParams) {
      for (const [key, value] of Object.entries(additionalParams)) {
        searchParams.set(key, value.toString()); // Use .set to add or override a parameter
      }
    }

    const array: any[] = [];
    // Extract the query parameter for searching
    const query: string | null = searchParams.get('query');
    searchParams.delete('query');
    if (query) {
      if (isNaN(Number(query))) {
        // Prepare the list of conditions for string search
        const orConditions: Record<string, any>[] = [
          { 'ib.firstname': { contains: query, mode: 'insensitive' } },
          { 'ib.lastname': { contains: query, mode: 'insensitive' } },
          { 'ib.email': { contains: query, mode: 'insensitive' } },
          { 'ib.telephone': { contains: query, mode: 'insensitive' } },
          { 'ib."companyName"': { contains: query, mode: 'insensitive' } },
          { 'ii.name': { contains: query, mode: 'insensitive' } },
        ];

        array.push({ OR: orConditions });
      } else {
        // Numeric search
        array.push({
          OR: [{ 'i.number': { contains: query, mode: 'insensitive' } }, { 'i."totalAmount"': { contains: query, mode: 'insensitive' } }],
        });
      }
    }

    // Convert remaining search parameters to array
    searchParams.forEach((value: string, key: string) => {
      array.push({ [key]: value });
    });

    // Handle statusType separately and add only if it matches a valid enum value
    if (statusType && Object.keys(InvoiceStatusType).includes(statusType)) {
      // Convert the enum key to its corresponding value
      const statusTypeValue: string = InvoiceStatusType[statusType as keyof typeof InvoiceStatusType].toLowerCase().replace(/_/g, ' ');
      array.push({ 'ist."statusType"': statusTypeValue });
    }
    if (bidderId) {
      array.push({ 'i."bidderId"': bidderId });
    }
    if (auctionsId) {
      array.push({ 'i."auctionsId"': auctionsId });
    }

    if (unpaidInvoices && unpaidInvoices !== 'false') {
      array.push({ 'i."isPaidInFull"': unpaidInvoices !== 'true' });
    }

    return array;
  }

  @Post('/')
  async create(@Body() body: InvoiceRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;

      const invoice: InvoiceDto = await this.handler.create(userId, this.createDtoFromRequest(body));
      const auction: AuctionDto = await this.auctionsHandler.get(invoice.id);
      // Start all async operations concurrently
      const [buyer, items, shippingAddress] = await Promise.all([
        this.invoiceBuyersHandler.create(userId, new InvoiceBuyerDto(body.buyer, invoice.id)),
        this.invoiceItemsHandler.createOrReplaceBulk(body.items.map((item: any) => new InvoiceItemDto(item, invoice.id))),
        body.shipping ? this.invoiceShippingAddressesHandler.create(userId, new InvoiceAddressDto(body.shipping, invoice.id)) : Promise.resolve(undefined), // Use a resolved promise if shipping is not provided
      ]);
      invoice.setBuyerPremiumTotal(this.getBuyerPremiumTotal(items));

      return new InvoiceResponse(new InvoiceDetailDto(invoice, auction, buyer, items, shippingAddress, undefined, undefined, undefined, undefined, undefined));
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Req() _req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.handler.get(id);
      const auction: AuctionDto = await this.auctionsHandler.get(invoice.auctionsId);
      const invoiceSplits: InvoiceSplitDetailDto[] = await this.invoiceSplitsHandler.getRelatedInvoices(invoice.id);
      const invoiceDetail: InvoiceDetailDto = await this.loadInvoiceDetails(invoice, auction);
      const shippingVendors: ListResultset<ShippingVendorDto> = await this.shippingVendorsHandler.getAll(1, -1, []);

      return new InvoiceResponse(invoiceDetail, shippingVendors, invoiceSplits);
    } catch (error) {
      // Return an error response if any error occurs.
      return new InvoiceNotFoundResponse(error);
    }
  }

  private async getInvoiceDetails(invoiceId: string): Promise<InvoiceDetailDto> {
    const invoice: InvoiceDto = await this.handler.get(invoiceId);
    const auction: AuctionDto = await this.auctionsHandler.get(invoice.auctionsId);
    return await this.loadInvoiceDetails(invoice, auction);
  }

  @Patch('/:id')
  async update(@Param('id') invoiceId: string, @Body() body: InvoiceRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const originalInvoice: InvoiceDto = await this.handler.get(invoiceId);
      const auction: AuctionDto = await this.auctionsHandler.get(originalInvoice.id);

      const [invoice, originalBuyer, originalShipping] = await Promise.all([
        await this.handler.update(userId, new InvoiceDto(body.invoice, originalInvoice.id), originalInvoice.id),
        await this.invoiceBuyersHandler.getCustom([{ invoicesId: originalInvoice.id }]),
        await this.invoiceShippingAddressesHandler.getCustom([{ invoicesId: originalInvoice.id }]),
      ]);

      // Start all async operations concurrently
      const [buyer, items, shippingInvoice] = await Promise.all([
        this.invoiceBuyersHandler.update(userId, new InvoiceBuyerDto(body.buyer, invoice.id), originalBuyer.id),
        this.invoiceItemsHandler.createOrReplaceBulk(body.items.map((item: any) => new InvoiceItemDto(item, invoice.id))),
        body.shipping ? this.invoiceShippingHandler.update(userId, new InvoiceShippingDto(body.shipping), originalShipping.id) : Promise.resolve(undefined), // Use a resolved promise if shipping is not provided
      ]);
      invoice.setBuyerPremiumTotal(this.getBuyerPremiumTotal(items));

      return new InvoiceResponse(new InvoiceDetailDto(invoice, auction, buyer, items, originalShipping, shippingInvoice, undefined, undefined, undefined));
    } catch (error) {
      return new ErrorResponse(error, 'update');
    }
  }

  @Post('generateInvoices/:auctionId')
  async generateInvoices(@Param('auctionId') auctionId: string, @Body() body: any, @Req() req: VBInterceptorRequest): Promise<void> {
    this.executeAndNotify(
      'generate',
      async () => {
        if (!Array.isArray(req.body)) {
          throw new InvalidRequestError();
        }
        const data: Array<{
          auctionId: string;
          accountMappingId: string;
          bids: Array<{ auctionLotsId: string; amount: number }>;
        }> = req.body;

        // Validate that data exists - this is a background process so no response to send
        if (!data || !Array.isArray(data)) {
          this.logger.error(`Invalid request body for auctionId: ${auctionId}`);
          return { message: 'Invalid request body', success: false };
        }

        // DISTRIBUTED LOCK: Prevent concurrent invoice generation for the same auction
        const lockKey = `invoice_generation_lock:${auctionId}`;
        const lockValue = `${Date.now()}_${Math.random()}`;
        const lockTTL = 300000; // 5 minutes lock timeout in milliseconds

        // Try to acquire lock
        const lockAcquired = await this.redisService.acquireLock(lockKey, lockValue, lockTTL);
        if (!lockAcquired) {
          this.logger.warn(`Invoice generation already in progress for auction ${auctionId}. Skipping to prevent duplicates.`);
          return { message: 'Invoice generation already in progress for this auction', success: false };
        }

        try {
          // IDEMPOTENCY CHECK: Prevent duplicate invoice generation
          const existingInvoices = await this.handler.getAll(1, 1, [{ auctionsId: auctionId }]);
          if (existingInvoices.list.length > 0) {
            this.logger.warn(`Invoices already exist for auction ${auctionId}. Skipping generation to prevent duplicates.`);
            return { message: 'Invoices already exist for this auction', success: true, invoices: existingInvoices.list };
          }

        try {
          const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
          const accountMappingIds: string[] = data.map((item: { accountMappingId: string }) => item.accountMappingId);
          const userBillingDetails: Array<UserBillingDetailDto> = await this.getUserBillingDetails(accountMappingIds);

          // Execute all promises in parallel
          const promises: Promise<InvoiceDto>[] = [];
          for (const accountData of data) {
            const billingDetail: UserBillingDetailDto | undefined = userBillingDetails.find(
              (billingDetail: UserBillingDetailDto) => billingDetail.accountMappingsId === accountData.accountMappingId,
            );
            if (billingDetail) {
              // Generate invoice by passing auction, accountData, and the matching billing detail
              const invoicePromise: Promise<InvoiceDto> = this.invoiceFactory.generateInvoice(auction, accountData, billingDetail);
              promises.push(invoicePromise);
            } else {
              this.logger.warn(`No billing detail found for accountMappingId: ${accountData.accountMappingId}`);
              //TODO: add subscriber handling that notifies admin when we can't find the buyer - different ticket
              this.notify('error', 'generate_invoices', { accountMappingsId: accountData.accountMappingId, applicationSection: 'generateInvoices' });
            }
          }

          const results: PromiseSettledResult<InvoiceDto>[] = await Promise.allSettled(promises);

          const generatedInvoices: InvoiceDto[] = results
            .filter((result: PromiseSettledResult<InvoiceDto>): result is PromiseFulfilledResult<InvoiceDto> => result.status === 'fulfilled') // Keep only fulfilled promises
            .map((result: PromiseFulfilledResult<InvoiceDto>) => result.value); // Extract the value (invoice)

          // This is actually a background process - will be used by the subscriptions. It will use invoices.
          return { message: 'Invoices generated successfully', success: true, invoices: generatedInvoices };
        } catch (error) {
          //TODO: add subscriber handling that notifies admin when we can't find the buyer - different ticket
          this.logger.error(`Error in generateInvoices for auctionId ${auctionId}:`, error);
          return { message: 'Failed to generate invoices', success: false };
        } finally {
          // Always release the distributed lock
          try {
            await this.redisService.releaseLock(lockKey, lockValue);
            this.logger.debug(`Released distributed lock for auction ${auctionId}`);
          } catch (lockError) {
            this.logger.error(`Error releasing distributed lock for auction ${auctionId}:`, lockError);
          }
        }
        } catch (outerError) {
          this.logger.error(`Outer error in generateInvoices for auctionId ${auctionId}:`, outerError);
          return { message: 'Failed to generate invoices', success: false };
        }
      },
      {},
    );
  }

  private async getUserBillingDetails(accountMappingsIds: string[]): Promise<UserBillingDetailDto[]> {
    const userResponse: axios.AxiosResponse<GetAllByAccountMappingsIds200Response> = await this.usersApi.getAllBillingDetailsByAccountMappingIds(accountMappingsIds.join(','));
    return userResponse.data.data as Array<UserBillingDetailDto>;
  }

  @Get('trackingNumber/:trackingNumber')
  async getByTrackingNumber(@Param('trackingNumber') trackingNumber: string, @Req() _req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.handler.getByTrackingNumber(trackingNumber);
      const auction: AuctionDto = await this.auctionsHandler.get(invoice.auctionsId);
      const invoiceDetail: InvoiceDetailDto = await this.loadInvoiceDetails(invoice, auction);
      const shippingVendors: ListResultset<ShippingVendorDto> = await this.shippingVendorsHandler.getAll(1, -1, []);

      return new InvoiceResponse(invoiceDetail, shippingVendors);
    } catch (error) {
      // Return an error response if any error occurs.
      return new ErrorResponse(error, 'getByTrackingNumber');
    }
  }

  private async loadInvoiceDetails(invoice: InvoiceDto, auction: AuctionDto): Promise<InvoiceDetailDto> {
    const [buyer, items, shippingAddress, invoiceShippingData, invoiceNotes, invoiceStatuses, invoicePayments, billingAddress, invoiceTemplate] = await Promise.all([
      this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]),
      this.invoiceItemsHandler.getInvoiceItemsWithTaxes(invoice.id) as unknown as ListResultset<InvoiceItemTaxDto>,

      // Load shipping address and handle potential error gracefully
      this.invoiceShippingAddressesHandler.getCustom([{ invoicesId: invoice.id }]).catch(() => undefined),

      // Load shipping-related data with type annotations
      (async (): Promise<{ invoiceShipping?: InvoiceShippingDto; trackingNumbers?: ListResultset<TrackingNumberDto>; vendor?: ShippingVendorDto }> => {
        try {
          const invoiceShipping: InvoiceShippingDto = await this.invoiceShippingHandler.getByInvoiceId(invoice.id);
          const invoiceShippingStaff: UserDto = await this.getUserInfo(invoiceShipping.createdBy);
          invoiceShipping.setStaff(invoiceShippingStaff);

          // Only try to load vendor if tracking numbers are present
          const vendor: ShippingVendorDto = await this.shippingVendorsHandler.get(invoiceShipping.shippingVendorsId.toString());
          const trackingNumbers: ListResultset<TrackingNumberDto> = await this.trackingNumbersHandler.getAll(1, -1, [{ invoiceShippingId: invoiceShipping.id }]);

          return { invoiceShipping, trackingNumbers, vendor };
        } catch {
          // Return undefined values if there's an error in loading shipping data
          return { invoiceShipping: undefined, trackingNumbers: undefined, vendor: undefined };
        }
      })(),
      this.invoiceNotesHandler.getAllCustom(1, -1, [{ invoicesId: invoice.id }], InvoiceNoteDto) as unknown as ListResultset<InvoiceNoteDto>,
      this.invoiceStatusesHandler.getAll(1, -1, [{ invoicesId: invoice.id }], { createdOn: 'asc' }),
      this.invoicePaymentsHandler.getAll(1, -1, [{ invoicesId: invoice.id }], { createdOn: 'asc' }),
      this.getBillingAddress(invoice.bidderId),
      await this.getInvoiceTemplateDetails(invoice.invoiceTemplatesId),
    ]);

    // Load lot attributes for invoice items
    await this.enrichItemsWithLotAttributes(items);

    // Destructure the shipping-related data
    const { invoiceShipping, trackingNumbers, vendor } = invoiceShippingData;
    const buyerPremiumTotal: number = this.getBuyerPremiumTotal(items);
    invoice.setBuyerPremiumTotal(buyerPremiumTotal);
    if (!auction.invoiceText) {
      auction.invoiceText = (await this.appPreferencesHandler.getByKey('DEFAULT_INVOICE_TEXT')).value;
    }
    return new InvoiceDetailDto(
      invoice,
      auction,
      buyer,
      items,
      shippingAddress,
      invoiceShipping,
      trackingNumbers,
      vendor,
      invoiceNotes.list,
      invoiceStatuses,
      invoicePayments,
      billingAddress,
      invoiceTemplate,
    );
  }

  private async getUserInfo(accountMappingsId: string): Promise<UserDto> {
    const response: any = await this.usersApi.getByAccountMappingId(accountMappingsId);
    return new UserDto(response.data.data.user);
  }
  private getBuyerPremiumTotal(items: ListResultset<Partial<InvoiceItemDto>>): number {
    return items.list.reduce((total: number, item: InvoiceItemDto) => {
      return Number(total) + Number(item.buyerPremiumAmount);
    }, 0);
  }
  private async getBillingAddress(accountMappingsId: string): Promise<InvoiceAddressDto> {
    const response: any = await this.usersApi.getAllBillingDetailsByAccountMappingIds(accountMappingsId);
    return new InvoiceAddressDto(response.data?.data?.[0]);
  }

  /**
   * once invoices are generated, they still need admin approval before going to buyers
   *
   * @param auctionId
   * @param req
   */
  @Post('approve/:auctionId')
  async approveAuctionInvoices(@Param('auctionId') auctionId: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    const result: ErrorResponse | AuctionDto = await this.executeAndNotify(
      'approve',
      async () => {
        const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
        auction.invoicesApproved = true;
        await this.auctionsHandler.update(req.accountMappingsId, auction, auction.id);
        await this.handler.approveAllInvoicesInAuction(auctionId);

        return auction;
      },
      {},
    );
    if (result instanceof ErrorResponse) {
      return result;
    }
  }

  @Post(':invoiceId/complete')
  async setInvoiceComplete(
    @Param('invoiceId') invoiceId: string,
    @Body() body: SetCompleteInvoiceRequest,
    @Req() req: VBInterceptorRequest,
    @Query('forceComplete') forceComplete: boolean = false,
  ): Promise<InvoiceResponse | ErrorResponse> {
    return this.executeAndNotify(
      'set_complete',
      async () => {
        try {
          const invoice: InvoiceDto = await this.handler.get(invoiceId);

          // Check if payment is complete
          if (!forceComplete && (!body.isPaidInFull || invoice.amountDue > 0)) {
            throw new PaymentOwingError(invoice.number);
          }

          invoice.mode = Mode.Complete;

          // Update invoice mode
          await this.handler.update(req.accountMappingsId, invoice, invoice.id);

          // Notify subscribers about status update
          await this.notify('success', 'set_complete', {
            invoices: [invoice],
          });

          return new InvoiceItemResponse(invoice);
        } catch (error) {
          return new ErrorResponse(error, 'setInvoiceComplete');
        }
      },
      body,
    );
  }

  @Post('complete-all')
  async setInvoicesComplete(
    @Body() body: InvoiceIdListRequest,
    @Req() req: VBInterceptorRequest,
    @Query('forceComplete') forceComplete: boolean = false,
  ): Promise<SuccessResponse | ErrorResponse> {
    try {
      const invoiceList: ListResultset<InvoiceDto> = await this.handler.getAll(1, -1, [{ id: { in: body.invoiceIds }, mode: { not: 'Complete' } }]);
      const invoices: InvoiceDto[] = invoiceList.list;
      if (body.invoiceIds.length !== invoices.length) {
        throw new InvoiceListMismatchError();
      }

      if (!forceComplete) {
        const invoiceNumbersWithAmountDue: number[] = invoices
          .filter((invoice: InvoiceDto) => invoice.amountDue > 0) // Filter invoices for those with amountDue > 0
          .map((invoice: InvoiceDto) => invoice.number); // Map the filtered invoices to their numbers

        if (invoiceNumbersWithAmountDue.length > 0) {
          throw new MultiplePaymentsOwingError(invoiceNumbersWithAmountDue);
        }
      }

      // Update invoice modes
      await this.handler.updateMany(
        req.accountMappingsId,
        { mode: Mode.Complete },
        invoices.map((invoice: InvoiceDto) => invoice.id),
      );

      // Notify subscribers about status update
      await this.notify('success', 'set_complete', {
        invoices,
      });

      return new SuccessResponse('complete', 'closeInvoices');
    } catch (error) {
      return new ErrorResponse(error, 'setInvoicesComplete');
    }
  }

  @Post(':invoiceId/puller-status/:status')
  async setInvoicePullerStatus(@Param('invoiceId') invoiceId: string, @Param('status') status: string, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    if (status !== 'assembled' && status !== 'validated') {
      return new ErrorResponse(new InvalidRequestError(), 'setInvoicePullerStatus');
    }
    return this.executeAndNotify(
      'set_' + status,
      async () => {
        try {
          const invoice: InvoiceDto = await this.handler.get(invoiceId);

          return new InvoiceResponse({
            invoice: invoice,
            status: status,
            accountMappingsId: req.accountMappingsId,
          });
        } catch (error) {
          return new ErrorResponse(error, 'setInvoicePullerStatus');
        }
      },
      {},
    );
  }

  @Post('split/:invoiceId')
  async splitInvoice(@Param('invoiceId') invoiceId: string, @Body() body: SplitInvoiceRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'split',
      async () => {
        try {
          const userId: string = req.accountMappingsId;
          const originalInvoice: InvoiceDto = await this.handler.get(invoiceId);
          const auction: AuctionDto = await this.auctionsHandler.get(originalInvoice.auctionsId);
          const buyer: InvoiceBuyerDto = await this.invoiceBuyersHandler.getCustom([{ invoicesId: originalInvoice.id }]);
          const userBillingDetail: UserBillingDetailDto = await this.getUserBillingDetails([originalInvoice.bidderId]).then((data: UserBillingDetailDto[]) => data[0]);
          const items: ListResultset<InvoiceItemDto> = await this.invoiceItemsHandler.getAllByInvoiceIdAndInvoiceItemId(originalInvoice.id, body.id);

          if (items.list.length === 0) {
            throw new SplittableAuctionItemsNotFoundError(invoiceId);
          }
          const newInvoiceDetail: InvoiceDetailDto = await this.invoiceFactory.splitInvoice(userId, originalInvoice.id, auction, userBillingDetail, items, buyer);

          return new InvoiceResponse(newInvoiceDetail);
        } catch (error) {
          this.logger.error(error);
          return new ErrorResponse(error, 'split');
        }
      },
      body,
    );
  }

  @Post('archive/:invoiceId')
  async archiveInvoice(@Param('invoiceId') invoiceId: string): Promise<SuccessResponse | ErrorResponse> {
    try {
      const invoiceDetails: InvoiceDetailDto = await this.getInvoiceDetails(invoiceId);
      const template: InvoiceTemplateDto = await this.invoiceTemplatesHandler.get(invoiceDetails.invoice.invoiceTemplatesId);

      if (!template) {
        throw new InvoiceTemplateNotFoundError(invoiceDetails.invoice.invoiceTemplatesId);
      }

      const pdfBuffer: Uint8Array = await this.pdfService.render(template.html, invoiceDetails);
      await this.s3Service.uploadToS3(this.bucketName, `invoices/${invoiceDetails.invoice.number}.pdf`, pdfBuffer, 'application/pdf');

      return new SuccessResponse([], invoiceId);
    } catch (error: any) {
      return new ErrorResponse(error, 'archiveInvoice');
    }
  }

  private async getInvoiceTemplateDetails(templateId: string): Promise<InvoiceTemplateDto | undefined> {
    try {
      return await this.invoiceTemplatesHandler.get(templateId);
    } catch {
      return undefined;
    }
  }

  @Post(':invoiceId/line-items')
  async addLineItem(@Param('invoiceId') invoiceId: string, @Body() body: InvoiceLineItemRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    return this.executeAndNotify(
      'add_line_item',
      async () => {
        try {
          const userId: string = req.accountMappingsId;
          const newItem: InvoiceItemDto = new InvoiceItemDto({ ...body }, invoiceId);
          const createdItem: InvoiceItemDto = await this.invoiceItemsHandler.create(userId, newItem);

          if (body.serialNumber && body.serialNumber.trim() !== '' && createdItem.id) {
            try {
              const itemMapping: ItemMappingDto = new ItemMappingDto({
                mappingType: MappingType.LineItems,
                proxyId: createdItem.id,
                createdBy: userId,
              });

              const createdItemMapping: ItemMappingDto = await this.itemMappingsHandler.create(userId, itemMapping);

              const serialNumberAttribute: LotAttributeDto = new LotAttributeDto({
                lotId: createdItemMapping.proxyId,
                name: 'Serial Number',
                attributeKey: 'SERIAL_NUMBER',
                value: body.serialNumber.trim(),
                createdBy: userId,
              });

              await Promise.all([
                this.lotAttributesHandler.create(userId, serialNumberAttribute),
                this.invoiceItemsHandler.update(userId, { ...createdItem, itemMappingsId: createdItemMapping.id }, createdItem.id),
              ]);
            } catch (error) {
              this.logger.error(`Failed to create serial number attribute for item ${createdItem.id}: ${error.message}`);
            }
          }

          await this.recalculateInvoiceTotals(invoiceId, userId);

          const updatedInvoice: InvoiceResponse | ErrorResponse = await this.get(invoiceId, req);
          return updatedInvoice;
        } catch (error) {
          return new ErrorResponse(error, 'addLineItem');
        }
      },
      body,
    );
  }

  private async recalculateInvoiceTotals(invoiceId: string, userId: string): Promise<void> {
    try {
      const [invoice, items] = await Promise.all([this.handler.get(invoiceId), this.invoiceItemsHandler.getInvoiceItemsWithTaxes(invoiceId)]);
      const calculatedTotals: any = this.calculateInvoiceTotals(items.list, invoice);
      Object.assign(invoice, calculatedTotals);

      await this.handler.update(userId, invoice, invoiceId);
    } catch (error) {
      this.logger.error(`Failed to recalculate invoice totals for invoice ${invoiceId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate invoice totals from items list
   * @param items List of invoice items with tax information
   * @param invoice Current invoice data for shipping costs
   * @returns Calculated totals for the invoice
   */
  private calculateInvoiceTotals(items: InvoiceItemWithTaxDto[], invoice: InvoiceDto): any {
    const totals: any = items.reduce(
      (acc: { itemsSubtotal: number; buyerPremiumTotal: number; taxesSubtotal: number }, item: InvoiceItemWithTaxDto) => {
        const quantity: number = Number(item.quantity) || 0;
        const price: number = Number(item.price) || 0;
        const itemTotal: number = quantity * price;
        const buyerPremium: number = Number(item.buyerPremiumAmount) || 0;
        const taxAmount: number = Number(item.taxAmount) || 0;

        return {
          itemsSubtotal: acc.itemsSubtotal + itemTotal,
          buyerPremiumTotal: acc.buyerPremiumTotal + buyerPremium,
          taxesSubtotal: acc.taxesSubtotal + taxAmount,
        };
      },
      { itemsSubtotal: 0, buyerPremiumTotal: 0, taxesSubtotal: 0 },
    );

    const shippingSubtotal: number = Number(invoice.shippingSubtotal) || 0;
    const amountPaid: number = Number(invoice.amountPaid) || 0;
    const totalAmount: number = totals.itemsSubtotal + totals.buyerPremiumTotal + totals.taxesSubtotal + shippingSubtotal;
    const amountDue: number = totalAmount - amountPaid;

    return {
      ...totals,
      totalAmount,
      amountDue,
      isPaidInFull: amountDue <= 0,
    };
  }

  private async enrichItemsWithLotAttributes(items: ListResultset<InvoiceItemTaxDto>): Promise<void> {
    try {
      if (!items?.list?.length) {
        return;
      }

      const itemMappingIds: Set<string> = this.extractItemMappingIds(items);
      if (itemMappingIds.size === 0) {
        return;
      }

      const itemMappings: ItemMappingDto[] = await this.fetchItemMappings(itemMappingIds);
      if (itemMappings.length === 0) {
        return;
      }

      const lotIds: Set<string> = this.extractLotIds(itemMappings);
      if (lotIds.size === 0) {
        return;
      }

      const lotAttributes: LotAttributeDto[] = await this.fetchLotAttributes(lotIds);
      if (!lotAttributes?.length) {
        return;
      }

      const attributeMaps: { lotAttributesMap: Map<string, LotAttributeDto[]>; itemMappingToAttributesMap: Map<string, LotAttributeDto[]> } = this.createAttributeLookupMaps(
        lotAttributes,
        itemMappings,
      );
      this.applyAttributesToItems(items, attributeMaps.itemMappingToAttributesMap);
    } catch (error: any) {
      this.logger.error(`Failed to enrich items with lot attributes: ${error?.message ?? 'Unknown error'}`);
    }
  }

  private extractItemMappingIds(items: ListResultset<InvoiceItemTaxDto>): Set<string> {
    const itemMappingIds: string[] = items.list.map((item: InvoiceItemTaxDto) => {
      return (item as any).itemMappingsId;
    });
    const validIds: string[] = itemMappingIds.filter((id: unknown): id is string => Boolean(id));

    return new Set(validIds);
  }

  private async fetchItemMappings(itemMappingIds: Set<string>): Promise<ItemMappingDto[]> {
    const itemMappingsResults: PromiseSettledResult<ItemMappingDto>[] = await Promise.allSettled(Array.from(itemMappingIds).map((id: string) => this.itemMappingsHandler.get(id)));

    return itemMappingsResults
      .filter((result: PromiseSettledResult<ItemMappingDto>): result is PromiseFulfilledResult<ItemMappingDto> => {
        if (result.status === 'rejected') {
          this.logger.warn(`Failed to get item mapping: ${result.reason?.message ?? 'Unknown error'}`);
          return false;
        }
        return true;
      })
      .map((result: PromiseFulfilledResult<ItemMappingDto>) => result.value);
  }

  private extractLotIds(itemMappings: ItemMappingDto[]): Set<string> {
    const proxyIds: string[] = itemMappings.map((mapping: ItemMappingDto) => mapping.proxyId);
    const validProxyIds: string[] = proxyIds.filter((id: unknown): id is string => Boolean(id));
    return new Set(validProxyIds);
  }

  private async fetchLotAttributes(lotIds: Set<string>): Promise<LotAttributeDto[]> {
    const lotAttributesResult: ListResultset<LotAttributeDto> = await this.lotAttributesHandler.getAll(1, -1, [{ lotId: { in: Array.from(lotIds) } }]);
    return lotAttributesResult?.list || [];
  }

  private createAttributeLookupMaps(
    lotAttributes: LotAttributeDto[],
    itemMappings: ItemMappingDto[],
  ): {
    lotAttributesMap: Map<string, LotAttributeDto[]>;
    itemMappingToAttributesMap: Map<string, LotAttributeDto[]>;
  } {
    const lotAttributesMap: Map<string, LotAttributeDto[]> = lotAttributes.reduce((map: Map<string, LotAttributeDto[]>, attr: LotAttributeDto) => {
      const lotId: string = attr.lotId;
      if (!map.has(lotId)) {
        map.set(lotId, []);
      }
      map.get(lotId)!.push(attr);
      return map;
    }, new Map<string, LotAttributeDto[]>());

    const itemMappingToAttributesMap: Map<string, LotAttributeDto[]> = itemMappings.reduce((map: Map<string, LotAttributeDto[]>, mapping: ItemMappingDto) => {
      if (mapping.proxyId && lotAttributesMap.has(mapping.proxyId)) {
        map.set(mapping.id, lotAttributesMap.get(mapping.proxyId)!);
      }
      return map;
    }, new Map<string, LotAttributeDto[]>());

    return { lotAttributesMap, itemMappingToAttributesMap };
  }

  private applyAttributesToItems(items: ListResultset<InvoiceItemTaxDto>, itemMappingToAttributesMap: Map<string, LotAttributeDto[]>): void {
    items.list.forEach((item: InvoiceItemTaxDto) => {
      const itemMappingsId: string = (item as any).itemMappingsId;
      if (itemMappingsId && itemMappingToAttributesMap.has(itemMappingsId)) {
        (item as any).lotAttributes = itemMappingToAttributesMap.get(itemMappingsId);
      }
    });
  }
}
