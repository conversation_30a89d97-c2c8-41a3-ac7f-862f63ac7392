import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { AuctionLotsHandler } from '../../../auction-lots/handlers/auction-lots.handler';
import { ItemMappingDto } from '../../../item-mappings/dtos/item-mapping.dto';
import { ItemMappingsHandler } from '../../../item-mappings/handler/item-mappings.handler';
import { InvoiceItemDto } from '../../dtos/items/invoice-item.dto';
import { InvoiceItemsHandler } from '../../handlers/invoice-items.handler';
import { InvoiceItemsRequest } from '../../http/requests/invoice-items.request';
import { InvoiceItemResponse } from '../../http/responses/invoice-item.response';
import { InvoiceItemsListResponse } from '../../http/responses/invoice-items-list.response';

@Controller('admin/invoice-items')
@UseGuards(IsAdmin)
export class InvoiceItemsController extends BaseController<InvoiceItemsHandler, InvoiceItemsRequest, InvoiceItemDto, InvoiceItemResponse, InvoiceItemsListResponse> {
  constructor(
    handler: InvoiceItemsHandler,
    private readonly itemMappingsHandler: ItemMappingsHandler,
    private readonly auctionLotsHandler: AuctionLotsHandler,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: InvoiceItemsRequest): InvoiceItemDto {
    return new InvoiceItemDto(request);
  }
  createResponseFromDto(dto: InvoiceItemDto): InvoiceItemResponse {
    return new InvoiceItemResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceItemDto>): InvoiceItemsListResponse {
    return new InvoiceItemsListResponse(list);
  }

  @Get(':id')
  async get(@Param('id') id: string): Promise<InvoiceItemResponse> {
    try {
      const itemMapping: ItemMappingDto = await this.itemMappingsHandler.get(id);
      return new InvoiceItemResponse(await this.auctionLotsHandler.getFlattenedById(itemMapping.proxyId));
    } catch (error) {
      return new ErrorResponse(error, 'getInvoiceItem');
    }
  }
}
