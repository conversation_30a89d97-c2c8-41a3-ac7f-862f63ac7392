import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoicePickupBinDto } from '../../dtos/pickup-bins/invoice-pickup-bin.dto';
import { InvoicePickupBinsHandler } from '../../handlers/invoice-pickup-bins.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { InvoicePickupBinRequest } from '../../http/requests/invoice-pickup-bin.request';
import { SetInvoicePickupBinsRequest } from '../../http/requests/set-invoice-pickup-bins.request';
import { InvoicePickupBinListResponse } from '../../http/responses/invoice-pickup-bin-list.response';
import { InvoicePickupBinResponse } from '../../http/responses/invoice-pickup-bin-response';

@Controller('/admin/invoice-pickup-bins')
@UseGuards(IsAdmin)
export class InvoicePickupBinsController extends BaseController<
  InvoicePickupBinsHandler,
  InvoicePickupBinRequest,
  InvoicePickupBinDto,
  InvoicePickupBinResponse,
  InvoicePickupBinListResponse
> {
  constructor(
    handler: InvoicePickupBinsHandler,
    private invoicesHandler: InvoicesHandler,
  ) {
    super(handler);
  }

  @Get('/bin/:pickupBin')
  async getPickupBinByName(@Param('pickupBin') pickupBin: string, @Req() req: VBInterceptorRequest): Promise<InvoicePickupBinResponse | ErrorResponse> {
    // return the most recently created bin row with the given name
    req;
    try {
      const dto: InvoicePickupBinDto = await this.handler.getPickupBinByName(pickupBin);

      return this.createResponseFromDto(dto);
    } catch (error) {
      throw new ErrorResponse(error, 'getActiveBinByName');
    }
  }

  @Post('/invoice/:invoicesId')
  async setPickupBinsForInvoice(
    @Param('invoicesId') invoicesId: string,
    @Body() body: SetInvoicePickupBinsRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoicePickupBinListResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.get(invoicesId);

      await this.handler.setBinsForInvoice(req.accountMappingsId, invoice.id, body.pickupBins);

      const listResult: ListResultset<InvoicePickupBinDto> = await this.handler.getAll(1, -1, [{ invoicesId: invoice.id, deletedOn: null }]);

      return this.createResponseList(listResult);
    } catch (error) {
      return new ErrorResponse(error, 'setPickupBinsForInvoice');
    }
  }

  @Get('/invoice/:invoicesId')
  async getByInvoiceId(@Param('invoicesId') invoicesId: string, @Req() req: VBInterceptorRequest): Promise<InvoicePickupBinListResponse | ErrorResponse> {
    req;
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.get(invoicesId);

      const listResult: ListResultset<InvoicePickupBinDto> = await this.handler.getAll(1, -1, [{ invoicesId: invoice.id, deletedOn: null }]);

      return this.createResponseList(listResult);
    } catch (error) {
      return new ErrorResponse(error, 'getByInvoiceId');
    }
  }

  createDtoFromRequest(request: InvoicePickupBinRequest): InvoicePickupBinDto {
    return new InvoicePickupBinDto({
      ...request,
      pickedUpOn: request.isPickedUp ? new Date() : null,
    });
  }

  createResponseFromDto(dto: InvoicePickupBinDto): InvoicePickupBinResponse {
    return new InvoicePickupBinResponse(dto);
  }

  createResponseList(list: ListResultset<InvoicePickupBinDto>): InvoicePickupBinListResponse {
    return new InvoicePickupBinListResponse(list);
  }
}
