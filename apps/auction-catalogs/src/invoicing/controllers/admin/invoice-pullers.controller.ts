import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Param, Post, Req } from '@nestjs/common';
import { InvoicePullersHandler } from '../../handlers/invoice-pullers.handler';
import { InvoicePullerRequest } from '../../http/requests/invoice-puller.request';
import { InvoicePullerDto } from '../../dtos/invoice-puller.dto';
import { InvoicePullersListResponse } from '../../http/responses/invoice-pullers-list.response';
import { InvoicePullerResponse } from '../../http/responses/invoice-puller.response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { UsersApi } from '@viewbid/ts-node-client';
import { UserDto } from '@vb/account-mappings/dtos/user.dto';
import { InvoicePullerDetailedDto } from '../../dtos/invoice-puller-detailed.dto';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { RedisService } from '@vb/redis';

@Controller('admin/invoice-pullers')
export class InvoicePullersController extends PubSubController<InvoicePullersHandler, InvoicePullerRequest, InvoicePullerDto, InvoicePullerResponse, InvoicePullersListResponse> {
  constructor(
    handler: InvoicePullersHandler,
    private invoicesHandler: InvoicesHandler,
    private usersApi: UsersApi,
    redisService: RedisService,
  ) {
    super(handler, redisService, 'pullers');
  }

  createDtoFromRequest(request: InvoicePullerRequest): InvoicePullerDto {
    return new InvoicePullerDto(request);
  }

  createResponseFromDto(dto: InvoicePullerDto): InvoicePullerResponse {
    return new InvoicePullerResponse(dto, {});
  }

  createResponseList(list: ListResultset<InvoicePullerDto>): InvoicePullersListResponse {
    return new InvoicePullersListResponse(list);
  }

  @Post(':invoiceId')
  async addPuller(@Param('invoiceId') invoiceId: string, @Body() body: InvoicePullerRequest, @Req() req: VBInterceptorRequest): Promise<InvoicePullerResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'addpuller',
      async () => {
        try {
          const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
          const rawDto: InvoicePullerDto = this.createDtoFromRequest(body);
          rawDto.invoicesId = invoice.id;
          rawDto.assignerId = req.accountMappingsId;

          const puller: InvoicePullerDto = await this.handler.create(req.accountMappingsId, rawDto);
          const [pullerUser, assignerUser] = await Promise.all([this.getUser(puller.pullerId), this.getUser(puller.assignerId)]);
          return new InvoicePullerResponse(new InvoicePullerDetailedDto(puller, pullerUser, assignerUser));
        } catch (error) {
          return new ErrorResponse(error, 'addPuller');
        }
      },
      body,
    );
  }

  private async getUser(accountMappingsId: string): Promise<UserDto> {
    const result: any = await this.usersApi.getByAccountMappingId(accountMappingsId);
    return new UserDto(result.data.data.user);
  }
}
