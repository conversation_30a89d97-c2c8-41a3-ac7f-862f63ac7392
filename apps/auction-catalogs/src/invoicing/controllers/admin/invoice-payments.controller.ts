import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Post, Query, Req, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { DateFilterService } from '@vb/nest/services/date-filter.service';
import { RedisService } from '@vb/redis';
import { UsersApi } from '@viewbid/ts-node-client';
import { PaymentProfilesController } from 'apps/auction-catalogs/src/payment-processing/controller/payment-profiles.controller';
import { TransactionRequestsController } from 'apps/auction-catalogs/src/payment-processing/controller/transaction-requests.controller';
import { InvoicePaymentDto } from '../../dtos/invoice-payment.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoiceItemsHandler } from '../../handlers/invoice-items.handler';
import { InvoicePaymentsHandler } from '../../handlers/invoice-payments.handler';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { InvoicePaymentRequest } from '../../http/requests/invoice-payment.request';
import { InvoicePaymentResponse } from '../../http/responses/invoice-payment.response';
import { InvoicePaymentsListResponse } from '../../http/responses/invoice-payments-list.response';
import { AbstractInvoicePaymentsController } from '../abstract-invoice-payments.controller';
import { SuccessResponse } from '@mvc/http/responses/success.response';
import { InvoicePaymentQueryDto } from '../../dtos/invoice-payment-query.dto';
import { InvoicePaymentExportDto } from '../../dtos/invoice-payment-export.dto';
import { InvoicePaymentsExportListResponse } from '../../http/responses/invoice-payments-export-list.response';
import * as _ from 'lodash';

@Controller('admin/invoice-payments')
@UseGuards(IsAdmin)
export class InvoicePaymentsController extends AbstractInvoicePaymentsController {
  constructor(
    handler: InvoicePaymentsHandler,
    redisService: RedisService,
    invoicesHandler: InvoicesHandler,
    invoiceItemsHandler: InvoiceItemsHandler,
    invoiceShippingHandler: InvoiceShippingHandler,
    transactionRequestsController: TransactionRequestsController,
    paymentsProfileController: PaymentProfilesController,
    usersApi: UsersApi,
    private readonly dateFilterService: DateFilterService,
  ) {
    super(handler, invoiceShippingHandler, redisService, invoicesHandler, invoiceItemsHandler, transactionRequestsController, paymentsProfileController, usersApi);
  }

  createDtoFromRequest(request: InvoicePaymentRequest): InvoicePaymentDto {
    return new InvoicePaymentDto(request);
  }

  createResponseFromDto(dto: InvoicePaymentDto): InvoicePaymentResponse {
    return new InvoicePaymentResponse(dto, {});
  }

  createResponseList(list: ListResultset<InvoicePaymentDto>): InvoicePaymentsListResponse {
    return new InvoicePaymentsListResponse(list);
  }

  @Get('all')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAllInvoicePayments(@Query() query: InvoicePaymentQueryDto): Promise<InvoicePaymentsExportListResponse | ErrorResponse> {
    try {
      // Use the date filter service to create date range filters
      const filters: any[] = this.dateFilterService.createDateRangeFilter('paymentDate', query.startDate, query.endDate);
      if (query.invoiceId) {
        filters.push({ invoiceId: query.invoiceId });
      }

      // Get payments with filters
      const payments: ListResultset<InvoicePaymentExportDto> = (await this.handler.getAllPaymentsForExport(
        query.page,
        query.size,
        filters,
      )) as unknown as ListResultset<InvoicePaymentExportDto>;
      await this.mapUsernames(payments);

      return new InvoicePaymentsExportListResponse(payments);
    } catch (error) {
      return new ErrorResponse(error, 'getAllInvoicePayments');
    }
  }

  @Get('invoice/:invoiceId')
  async listInvoicePayments(@Param('invoiceId') invoiceId: string): Promise<any> {
    try {
      const payments: ListResultset<InvoicePaymentDto> = (await this.handler.getAllCustom(1, -1, [{ invoicesId: invoiceId }], InvoicePaymentDto, {
        createdOn: 'desc',
      })) as unknown as ListResultset<InvoicePaymentDto>;
      return this.createResponseList(payments);
    } catch (error) {
      return new ErrorResponse(error, 'listInvoicePayments');
    }
  }

  @Get('invoice-number/:invoiceNumber')
  async listInvoicePaymentsByNumber(@Param('invoiceNumber') invoiceNumber: number): Promise<SuccessResponse | InvoicePaymentsListResponse | ErrorResponse> {
    if (typeof invoiceNumber !== 'number' || isNaN(invoiceNumber)) {
      return new SuccessResponse([], 'listInvoicePaymentsByNumber');
    }
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.getByInvoiceNumber(invoiceNumber);

      return await this.listInvoicePayments(invoice.id);
    } catch (error) {
      return new ErrorResponse(error, 'listInvoicePaymentsByNumber');
    }
  }

  @Post('invoice/:invoiceId')
  async adminCreatePayment(
    @Param('invoiceId') invoiceId: string,
    @Body() body: InvoicePaymentRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoicePaymentResponse | ErrorResponse> {
    //Pass thru to the absract version.
    return await this.createPayment(invoiceId, body, req);
  }

  private async mapUsernames(items: ListResultset<InvoicePaymentExportDto>): Promise<void> {
    const accountMappingIds: string[] = items.list.map((item: InvoicePaymentExportDto) => item.accountMappingsId);
    const userResponse: any = await this.usersApi.getAllByAccountMappingsIds(accountMappingIds.join(','), 1, -1);
    const usersResult: any = userResponse.data.data;
    const userMap: any = _.keyBy(usersResult, (value: any) => value.accountMappingsId);

    _.forEach(items.list, (value: any) => {
      value.username = userMap[value.accountMappingsId]?.username;
    });
  }
}
