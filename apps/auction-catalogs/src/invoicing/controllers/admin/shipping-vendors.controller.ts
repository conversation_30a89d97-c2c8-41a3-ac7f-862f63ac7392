import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { Controller } from '@nestjs/common';
import { ShippingVendorsHandler } from '../../handlers/shipping-vendors.handler';
import { ShippingVendorRequest } from '../../http/requests/shipping-vendor.request';
import { ShippingVendorDto } from '../../dtos/shipping-vendor.dto';
import { ShippingVendorsListResponse } from '../../http/responses/shipping-vendors-list.response';
import { ShippingVendorResponse } from '../../http/responses/shipping-vendor.response';

@Controller('admin/shipping-vendors')
export class ShippingVendorsController extends BaseController<
  ShippingVendorsHandler,
  ShippingVendorRequest,
  ShippingVendorDto,
  ShippingVendorResponse,
  ShippingVendorsListResponse
> {
  constructor(handler: ShippingVendorsHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ShippingVendorRequest): ShippingVendorDto {
    return new ShippingVendorDto(request);
  }

  createResponseFromDto(dto: ShippingVendorDto): ShippingVendorResponse {
    return new ShippingVendorResponse(dto, {});
  }

  createResponseList(list: ListResultset<ShippingVendorDto>): ShippingVendorsListResponse {
    return new ShippingVendorsListResponse(list);
  }
}
