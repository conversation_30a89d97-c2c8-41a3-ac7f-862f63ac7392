import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { TaxType } from '@viewbid/ts-node-client';
import { InvalidParameterError } from '../../../../../users/src/account-mappings/exceptions/invalid-parameter.error';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoiceShippingListDto } from '../../dtos/shipping/invoice-shipping-list.dto';
import { InvoiceShippingDto } from '../../dtos/shipping/invoice-shipping.dto';
import { TaxTypeDto } from '../../dtos/tax-type.dto';
import { ImmutableUpdateAttemptError } from '../../exceptions/immutable-update-attempt.error';
import { InvalidMappingError } from '../../exceptions/invalid-mapping.error';
import { ShippingInvoiceAlreadyExistsError } from '../../exceptions/shipping-invoice-already-exists.error';
import { TaxTypeNotFoundError } from '../../exceptions/tax-type-not-found.error';
import { InvoiceShippingTaxesHandler } from '../../handlers/invoice-shipping-taxes.handler';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { ShippingVendorsHandler } from '../../handlers/shipping-vendors.handler';
import { TaxTypeHandler } from '../../handlers/tax-type.handler';
import { InvoiceShippingRequest } from '../../http/requests/invoice-shipping.request';
import { InvoiceShippingListResponse } from '../../http/responses/invoice-shipping-list.response';
import { InvoiceShippingResponse } from '../../http/responses/invoice-shipping.response';
import { InvoiceFactory } from '../../factories/invoice.factory';

@Controller('admin/invoices-shipping')
@UseGuards(IsAdmin)
export class InvoiceShippingController extends PubSubController<
  InvoiceShippingHandler,
  InvoiceShippingRequest,
  InvoiceShippingDto,
  InvoiceShippingResponse,
  InvoiceShippingListResponse
> {
  constructor(
    handler: InvoiceShippingHandler,
    redisService: RedisService,
    private readonly invoicesHandler: InvoicesHandler,
    private readonly taxTypeHandler: TaxTypeHandler,
    private readonly shippingVendorsHandler: ShippingVendorsHandler,
    private readonly invoiceShippingTaxesHandler: InvoiceShippingTaxesHandler,
    private readonly invoiceFactory: InvoiceFactory,
  ) {
    super(handler, redisService, 'invoice_shipping');
  }

  createDtoFromRequest(request: InvoiceShippingRequest): InvoiceShippingDto {
    return new InvoiceShippingDto(request);
  }

  createResponseFromDto(dto: InvoiceShippingDto): InvoiceShippingResponse {
    return new InvoiceShippingResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceShippingDto>): InvoiceShippingListResponse {
    return new InvoiceShippingListResponse(list);
  }

  @Post('invoices/:invoiceId')
  async createShipping(
    @Param('invoiceId') invoiceId: string,
    @Body() body: InvoiceShippingRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceShippingResponse | ErrorResponse> {
    return this.executeAndNotify(
      'create',
      async () => {
        try {
          const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
          const userId: string = req.accountMappingsId;
          const dto: InvoiceShippingDto = this.createDtoFromRequest(body);
          if (await this.checkShippingInvoiceAlreadyExists(invoice)) {
            throw new ShippingInvoiceAlreadyExistsError(invoice);
          }
          //verify vendor exists
          await this.shippingVendorsHandler.getById(dto.shippingVendorsId);
          dto.invoicesId = invoice.id;
          const item: InvoiceShippingDto = await this.handler.create(userId, dto);

          return this.createResponseFromDto(item);
        } catch (error) {
          return new ErrorResponse(error, 'create');
        }
      },
      {},
    );
  }

  private async checkShippingInvoiceAlreadyExists(invoice: InvoiceDto): Promise<boolean> {
    try {
      await this.handler.getByInvoiceId(invoice.id);
      return true;
    } catch (error) {
      return false;
    }
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceShippingListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      const items: ListResultset<InvoiceShippingListDto> = await this.handler.getAllList(page, size, params, { '"invoiceNumber"': 'desc' });
      return new InvoiceShippingListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  convertQueryToArray(uri: string): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    searchParams.delete('page');
    searchParams.delete('size');

    const array: any[] = [];

    let query: string;
    if (searchParams.has('query') && (query = searchParams.get('query')!.toString()) !== '') {
      // Add conditions for searching within JSONB fields
      const queryFields: Array<string> = ['ib.firstname', 'ib.lastname', 'ib."companyName"'];

      queryFields.forEach((field: string) => {
        // Embed the query directly in the SQL condition
        array.push(`${field} ILIKE '%${query}%'`);
      });
      searchParams.delete('query');
    }

    searchParams.forEach((value: string, key: string) => {
      array.push({ [key]: value });
    });

    return array;
  }

  @Post(':shippingId/status/:status')
  async approve(@Param('shippingId') shippingId: string, @Param('status') status: string, @Req() req: VBInterceptorRequest): Promise<InvoiceShippingResponse | ErrorResponse> {
    if (status !== 'approve' && status !== 'reject') {
      return new ErrorResponse(new InvalidParameterError(), 'approveShippingQuote');
    }

    const invoiceShipping: InvoiceShippingDto = await this.handler.get(shippingId);
    const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceShipping.invoicesId);
    invoiceShipping.isApproved = status === 'approve';

    await this.handler.update(req.accountMappingsId, invoiceShipping, invoiceShipping.id);

    if (invoiceShipping.isApproved && invoiceShipping.subtotal > 0) {
      const taxDto: TaxTypeDto = await this.retrieveTaxes(TaxType.Hst.toString());
      const totalSubTotal: number = Number(invoice.itemsSubtotal) + Number(invoice.buyerPremiumTotal) + Number(invoiceShipping.subtotal);
      const taxesSubtotal: number = invoice.taxesSubtotal + Number(invoiceShipping.subtotal) * taxDto.percent;
      const totalAmount: number = totalSubTotal + taxesSubtotal;
      invoice.shippingSubtotal = invoiceShipping.subtotal;
      await this.invoicesHandler.update(req.accountMappingsId, invoice, invoice.id);
      await this.invoicesHandler.setPaidAndAmountDueAndTotal(invoice.id, false, invoiceShipping.subtotal * (1 + taxDto.percent), totalAmount, taxesSubtotal);
    }

    this.notify('success', `admin_${status}`, {
      data: {
        invoice: invoice,
        invoiceShipping: invoiceShipping,
      },
    });
    return new InvoiceShippingResponse(invoiceShipping);
  }

  @Patch('invoices/:invoiceId/shippingInvoice/:shippingInvoiceId')
  async updateShippingInvoice(
    @Param('invoiceId') invoiceId: string,
    @Param('shippingInvoiceId') shippingInvoiceId: string,
    @Body() body: InvoiceShippingRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceShippingResponse | ErrorResponse> {
    return this.executeAndNotify(
      'update',
      async () => {
        try {
          const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
          const invoiceShipping: InvoiceShippingDto = await this.handler.get(shippingInvoiceId);
          if (invoiceShipping.invoicesId !== invoice.id) {
            throw new InvalidMappingError(invoice.id, invoiceShipping.id);
          }
          if (invoiceShipping.isBuyerApproved) {
            throw new ImmutableUpdateAttemptError();
          }
          const dto: InvoiceShippingDto = this.createDtoFromRequest(body);
          //verify vendor exists
          await this.shippingVendorsHandler.getById(dto.shippingVendorsId);
          const userId: string = req.accountMappingsId;

          dto.invoicesId = invoice.id;
          dto.id = invoiceShipping.id;
          const item: InvoiceShippingDto = await this.handler.update(userId, dto, dto.id);

          return this.createResponseFromDto(item);
        } catch (error) {
          return new ErrorResponse(error, 'create');
        }
      },
      {},
    );
  }

  @Patch('invoices/:invoiceId/quote/:invoiceShippingId')
  async updateShipping(
    @Param('invoiceId') invoiceId: string,
    @Param('invoiceShippingId') invoiceShippingId: string,
    @Body() body: InvoiceShippingRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceShippingResponse | ErrorResponse> {
    return this.executeAndNotify(
      'update',
      async () => {
        try {
          const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
          const invoiceShipping: InvoiceShippingDto = await this.handler.get(invoiceShippingId);
          if (invoiceShipping.invoicesId !== invoice.id) {
            throw new InvalidMappingError(invoice.id, invoiceShipping.id);
          }
          if (invoiceShipping.isBuyerApproved) {
            throw new ImmutableUpdateAttemptError();
          }
          const dto: InvoiceShippingDto = this.createDtoFromRequest(body);
          //verify vendor exists
          await this.shippingVendorsHandler.getById(dto.shippingVendorsId);
          const userId: string = req.accountMappingsId;

          dto.invoicesId = invoice.id;
          dto.id = invoiceShipping.id;
          const item: InvoiceShippingDto = await this.handler.update(userId, dto, dto.id);

          return this.createResponseFromDto(item);
        } catch (error) {
          return new ErrorResponse(error, 'create');
        }
      },
      {},
    );
  }

  async retrieveTaxes(taxName: string): Promise<TaxTypeDto> {
    const types: ListResultset<TaxTypeDto> = await this.taxTypeHandler.getAll(1, -1, [{ name: taxName }]);

    // Return the first one that matches
    if (types.totalCount > 0) {
      return types.list[0];
    }
    throw new TaxTypeNotFoundError(taxName);
  }

  @Delete('/:invoiceId/:shippingId')
  async deleteInvoiceShipping(
    @Param('invoiceId') invoiceId: string,
    @Param('shippingId') invoiceShippingId: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<void | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
      const invoiceShipping: InvoiceShippingDto = await this.handler.get(invoiceShippingId);
      const userId: string = req.accountMappingsId;
      if (invoiceShipping.invoicesId !== invoice.id) {
        throw new InvalidMappingError(invoice.id, invoiceShipping.id);
      }
      this.handler.delete(userId, invoiceShipping.id);
      return;
    } catch (error) {
      this.logger.error("Error caught in DELETE 'deleteInvoiceShipping': " + error);

      return new ErrorResponse(error, 'deleteInvoiceShipping');
    }
  }
}
