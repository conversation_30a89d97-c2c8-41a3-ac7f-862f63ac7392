import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { BadRequestException, Controller, Delete, Get, Param, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { UsersApi } from '@viewbid/ts-node-client';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { AdminTimeslotListDto } from '../../dtos/timeslots/admin-timeslot-list.dto';
import { TimeslotDto } from '../../dtos/timeslots/timeslot.dto';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { TimeslotsHandler } from '../../handlers/timeslots.handler';
import { TimeslotRequest } from '../../http/requests/timeslot.request';
import { TimeslotResponse } from '../../http/responses/timeslot.response';
import { TimeslotsListResponse } from '../../http/responses/timeslots-list.response';

@Controller('admin/timeslots')
export class TimeslotsController extends PubSubController<TimeslotsHandler, TimeslotRequest, TimeslotDto, TimeslotResponse, TimeslotsListResponse> {
  constructor(
    handler: TimeslotsHandler,
    redisService: RedisService,
    private usersApi: UsersApi,
    private readonly invoicesHandler: InvoicesHandler,
  ) {
    super(handler, redisService, 'timeslots');
  }

  createDtoFromRequest(request: TimeslotRequest): TimeslotDto {
    return new TimeslotDto(request);
  }

  createResponseFromDto(dto: TimeslotDto): TimeslotResponse {
    return new TimeslotResponse(dto, {});
  }

  createResponseList(list: ListResultset<TimeslotDto>): TimeslotsListResponse {
    return new TimeslotsListResponse(list);
  }

  @Get('getAllBookings')
  async getAllBookings(@Query('fromDate') fromDate: string, @Query('toDate') toDate: string): Promise<TimeslotsListResponse | ErrorResponse> {
    try {
      // Check if both dates are provided
      if (!fromDate || !toDate) {
        throw new BadRequestException('Both fromDate and toDate are required.');
      }

      // Validate if fromDate and toDate are valid dates
      if (!this.isValidDate(fromDate) || !this.isValidDate(toDate)) {
        throw new BadRequestException('Invalid date format. Use YYYY-MM-DD.');
      }

      // Check if toDate is not earlier than fromDate
      if (new Date(toDate) < new Date(fromDate)) {
        throw new BadRequestException('toDate cannot be earlier than fromDate.');
      }
      const items: ListResultset<AdminTimeslotListDto> = await this.handler.getAdminBookings([{ fromDate: fromDate, toDate: toDate }]);
      await this.getAllStaff(items);
      const response: TimeslotsListResponse = new TimeslotsListResponse(items);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  private isValidDate(dateStr: string): boolean {
    const date: Date = new Date(dateStr);
    return !isNaN(date.getTime());
  }

  private async getAllStaff(items: ListResultset<AdminTimeslotListDto | TimeslotDto>): Promise<void> {
    const staffIds: string = items.list.map((item: AdminTimeslotListDto) => item.staffId).join(',');
    const result: any = await this.usersApi.getAllByAccountMappingsIds(staffIds, 1, -1);

    const users: any = result.data.data;
    const staffMap: Record<string, string> = users.reduce((map: Record<string, string>, user: Staff) => {
      map[user.id] = `${user.firstname} ${user.lastname}`;
      return map;
    }, {});

    items.list.forEach((item: AdminTimeslotListDto | TimeslotDto) => {
      const staffName: string | undefined = staffMap[item.staffId];
      if (staffName) {
        item.setStaffName(staffName);
      }
    });
  }
  private async getStaff(item: TimeslotDto): Promise<void> {
    const result: any = await this.usersApi.getAllByAccountMappingsIds(item.staffId, 1, 1);
    const user: any = result.data.data.pop();

    item.setStaffName(`${user.firstname} ${user.lastname}`);
  }

  @Get(':id')
  async get(@Param('id') id: string): Promise<TimeslotResponse | ErrorResponse> {
    const item: TimeslotDto = await this.handler.get(id);
    if (item.staffId) {
      await this.getStaff(item);
    }

    return new TimeslotResponse(item);
  }

  @Delete('/:id')
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const timeslot: TimeslotDto = await this.handler.get(id);
      const invoice: InvoiceDto = await this.invoicesHandler.get(timeslot.invoicesId);

      await this.handler.updateStatus(req.accountMappingsId, timeslot.id, 'Cancelled');

      await this.handler.delete(userId, timeslot.id);
      this.notify('success', 'delete', { invoice: invoice, timeslot: timeslot });
    } catch (error) {
      return new ErrorResponse(error, 'delete');
    }
  }
}
// Define the type of user data returned by the API
type Staff = {
  id: string;
  firstname: string;
  lastname: string;
};
