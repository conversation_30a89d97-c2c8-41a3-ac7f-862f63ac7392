import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { Controller } from '@nestjs/common';
import { InvoiceNoteDto } from '../../dtos/invoice-note.dto';
import { InvoiceNotesHandler } from '../../handlers/invoice-notes.handler';
import { InvoiceNoteRequest } from '../../http/requests/invoice-note.request';
import { InvoiceNoteListResponse } from '../../http/responses/invoice-note-list.response';
import { InvoiceNoteResponse } from '../../http/responses/invoice-note.response';

@Controller('admin/invoice-notes')
export class AdminInvoiceNotesController extends BaseController<InvoiceNotesHandler, InvoiceNoteRequest, InvoiceNoteDto, InvoiceNoteResponse, InvoiceNoteListResponse> {
  constructor(handler: InvoiceNotesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: InvoiceNoteRequest): InvoiceNoteDto {
    return new InvoiceNoteDto(request);
  }

  createResponseFromDto(dto: InvoiceNoteDto): InvoiceNoteResponse {
    return new InvoiceNoteResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceNoteDto>): InvoiceNoteListResponse {
    return new InvoiceNoteListResponse(list);
  }
}
