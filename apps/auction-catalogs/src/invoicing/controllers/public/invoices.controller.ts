import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Query, Req } from '@nestjs/common';
import { Transactional } from '@transactional/core';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { AuctionDto } from '../../../auction/dtos/auction.dto';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { InvoiceAddressDto } from '../../dtos/buyers/invoice-address.dto';
import { InvoiceDetailDto } from '../../dtos/invoice-detail.dto';
import { InvoiceListDto } from '../../dtos/invoice-list.dto';
import { InvoiceNoteDto } from '../../dtos/invoice-note.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoiceItemTaxDto } from '../../dtos/items/invoice-item-tax.dto';
import { InvoiceItemWithTaxDto } from '../../dtos/items/invoice-item-with-tax.dto';
import { InvoiceItemDto } from '../../dtos/items/invoice-item.dto';
import { ShippingVendorDto } from '../../dtos/shipping-vendor.dto';
import { InvoiceShippingDto } from '../../dtos/shipping/invoice-shipping.dto';
import { TrackingNumberDto } from '../../dtos/tracking-number.dto';
import { InvoiceNotFoundError } from '../../exceptions/invoice-not-found.error';
import { InvoiceBuyersHandler } from '../../handlers/invoice-buyers.handler';
import { InvoiceItemsHandler } from '../../handlers/invoice-items.handler';
import { InvoiceNotesHandler } from '../../handlers/invoice-notes.handler';
import { InvoicePaymentsHandler } from '../../handlers/invoice-payments.handler';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { InvoiceStatusesHandler } from '../../handlers/invoice-statuses.handler';
import { InvoicesShippingAddressesHandler } from '../../handlers/invoices-shipping-addresses.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { ShippingVendorsHandler } from '../../handlers/shipping-vendors.handler';
import { TrackingNumbersHandler } from '../../handlers/tracking-numbers.handler';
import { InvoiceShippingAddressRequest } from '../../http/requests/invoice-shipping-address.request';
import { InvoiceRequest } from '../../http/requests/invoice.request';
import { InvoiceListResponse } from '../../http/responses/invoice-list.response';
import { InvoiceNotFoundResponse } from '../../http/responses/invoice-not-found.response';
import { InvoiceResponse } from '../../http/responses/invoice.response';
import { InvoiceStatusType } from '../../../prisma/generated/auction-catalogs-db-client';
import { UsersApi } from '@viewbid/ts-node-client';
import { ApplicationPreferencesHandler } from 'apps/auction-catalogs/src/application-preferences/handler/app-preferences.handler';

@Controller('invoices')
export class InvoicesController extends PubSubController<InvoicesHandler, InvoiceRequest, InvoiceDto, InvoiceResponse, InvoiceListResponse> {
  createDtoFromRequest(request: InvoiceRequest): InvoiceDto {
    return new InvoiceDto(request);
  }
  createResponseFromDto(dto: InvoiceDto): InvoiceResponse {
    return new InvoiceResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceDto>): InvoiceListResponse {
    return new InvoiceListResponse(list);
  }

  constructor(
    handler: InvoicesHandler,
    redis: RedisService,
    private readonly invoiceBuyersHandler: InvoiceBuyersHandler,
    private readonly invoiceItemsHandler: InvoiceItemsHandler,
    private readonly invoiceShippingHandler: InvoiceShippingHandler,
    private readonly invoiceShippingAddressesHandler: InvoicesShippingAddressesHandler,
    private readonly trackingNumbersHandler: TrackingNumbersHandler,
    private readonly shippingVendorsHandler: ShippingVendorsHandler,
    private readonly invoiceNotesHandler: InvoiceNotesHandler,
    private readonly invoiceStatusesHandler: InvoiceStatusesHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly invoicePaymentsHandler: InvoicePaymentsHandler,
    private readonly usersApi: UsersApi,
    private readonly appPreferencesHandler: ApplicationPreferencesHandler,
  ) {
    super(handler, redis, 'invoices');
  }
  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceListResponse | ErrorResponse> {
    try {
      // Check if the call is from dashboard module via custom header
      const isFromDashboard: boolean = (req.headers as any)['x-source-module'] === 'dashboard';

      let accountMappingsId: string = req.accountMappingsId;
      if (isFromDashboard && query) {
        const queryParams: URLSearchParams = new URLSearchParams(query);
        const queryAccountMappingsId: string | null = queryParams.get('accountMappingsId');
        accountMappingsId = queryAccountMappingsId ?? req.accountMappingsId;
      }

      const params: any[] = this.convertQueryToArray(req.url);
      params.push({ ['bidderId']: accountMappingsId });
      params.push({ ['i."invoicesApproved"']: true });

      const items: ListResultset<InvoiceListDto> = await this.handler.getAllPublic(page, size, params, { createdOn: 'desc' });

      return new InvoiceListResponse(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAllInvoices');
    }
  }

  protected convertQueryToArray(uri: string): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    // Extract 'page' and 'size' parameters as they're not used for searching
    searchParams.delete('page');
    searchParams.delete('size');

    // Extract the query parameter for searching
    const query: string | null = searchParams.get('query');
    if (query) {
      searchParams.delete('query');
    }

    // Extract the statusType parameter for filtering
    const statusType: string | null = searchParams.get('statusType');
    if (statusType) {
      searchParams.delete('statusType');
    }
    const array: any[] = [];

    // If a query parameter exists, add the condition for searching across multiple columns
    if (query) {
      if (isNaN(Number(query))) {
      } else {
        // Numeric search
        array.push({
          OR: [{ 'i.number': { contains: query, mode: 'insensitive' } }, { 'i."totalAmount"': { contains: query, mode: 'insensitive' } }],
        });
      }
    }

    // Handle statusType separately and add only if it matches a valid enum value
    if (statusType && Object.keys(InvoiceStatusType).includes(statusType)) {
      // Convert the enum key to its corresponding value
      const statusTypeValue: string = InvoiceStatusType[statusType as keyof typeof InvoiceStatusType].toLowerCase().replace(/_/g, ' ');
      array.push({ 'ist."statusType"': statusTypeValue });
    }

    return array;
  }
  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.handler.getCustom([{ bidderId: req.accountMappingsId, id: id }]);

      const invoiceDetail: InvoiceDetailDto = await this.loadInvoiceDetails(invoice);

      return new InvoiceResponse(invoiceDetail);
    } catch (error) {
      // Return an error response if any error occurs.
      return new InvoiceNotFoundResponse(new InvoiceNotFoundError(id));
    }
  }

  @Patch(':invoiceId/request-shipping')
  async requestShipping(@Param('invoiceId') id: string, @Body() body: InvoiceShippingAddressRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceResponse | ErrorResponse> {
    return await this.executeAndNotify(
      'request_shipping',
      async () => {
        try {
          const [invoice, buyer, items] = await Promise.all([
            this.handler.getCustom([{ bidderId: req.accountMappingsId, id: id }]),
            this.invoiceBuyersHandler.getCustom([{ invoicesId: id }]),
            this.invoiceItemsHandler.getAllCustom(1, -1, [{ invoicesId: id }], InvoiceItemDto) as unknown as ListResultset<InvoiceItemDto>,
          ]);
          const auction: AuctionDto = await this.auctionsHandler.get(invoice.auctionsId);
          const shippingAddress: InvoiceAddressDto = await this.addShippingInformation(invoice, body, req);

          return new InvoiceResponse(new InvoiceDetailDto(invoice, auction, buyer, items, shippingAddress, undefined, undefined, undefined, undefined));
        } catch (error) {
          this.logger.log((error as Error).stack);
          return new ErrorResponse(error, 'requestShipping');
        }
      },
      {},
    );
  }

  @Transactional()
  async addShippingInformation(invoice: InvoiceDto, body: InvoiceShippingAddressRequest, req: VBInterceptorRequest): Promise<InvoiceAddressDto> {
    invoice.isShipping = true;
    await this.handler.update(req.accountMappingsId, invoice, invoice.id);
    return await this.invoiceShippingAddressesHandler.create(req.accountMappingsId, new InvoiceAddressDto(body, invoice.id));
  }

  private async loadInvoiceDetails(invoice: InvoiceDto): Promise<InvoiceDetailDto> {
    const [buyer, auction, items, shippingAddress, invoiceShippingData, invoiceNotes, invoiceStatuses, invoicePayments, billingAddress] = await Promise.all([
      this.invoiceBuyersHandler.getCustom([{ invoicesId: invoice.id }]),
      (async (): Promise<AuctionDto> => {
        const auction: AuctionDto = await this.auctionsHandler.get(invoice.auctionsId);
        if (!auction.invoiceText) {
          auction.invoiceText = (await this.appPreferencesHandler.getByKey('DEFAULT_INVOICE_TEXT')).value;
        }
        return auction;
      })(),
      this.invoiceItemsHandler.getInvoiceItemsWithTaxes(invoice.id) as unknown as ListResultset<InvoiceItemTaxDto>,

      // Load shipping address and handle potential error gracefully
      this.invoiceShippingAddressesHandler.getCustom([{ invoicesId: invoice.id }]).catch(() => undefined),

      // Load shipping-related data with type annotations
      (async (): Promise<{ invoiceShipping?: InvoiceShippingDto; trackingNumbers?: ListResultset<TrackingNumberDto>; vendor?: ShippingVendorDto }> => {
        try {
          const invoiceShipping: InvoiceShippingDto = await this.invoiceShippingHandler.getByInvoiceId(invoice.id);
          const trackingNumbers: ListResultset<TrackingNumberDto> = await this.trackingNumbersHandler.getAll(1, -1, [{ invoiceShippingId: invoiceShipping.id }]);

          // Only try to load vendor if tracking numbers are present
          const vendor: ShippingVendorDto | undefined =
            trackingNumbers.list.length > 0 ? await this.shippingVendorsHandler.get(trackingNumbers.list[0].shippingVendorsId.toString()) : undefined;

          return { invoiceShipping, trackingNumbers, vendor };
        } catch {
          // Return undefined values if there's an error in loading shipping data
          return { invoiceShipping: undefined, trackingNumbers: undefined, vendor: undefined };
        }
      })(),
      this.invoiceNotesHandler.getAllCustom(1, -1, [{ invoicesId: invoice.id }, { isAdmin: false }], InvoiceNoteDto) as unknown as ListResultset<InvoiceNoteDto>,
      this.invoiceStatusesHandler.getAll(1, -1, [{ invoicesId: invoice.id }], { createdOn: 'asc' }),
      this.invoicePaymentsHandler.getAll(1, -1, [{ invoicesId: invoice.id }], { createdOn: 'asc' }),
      this.getBillingAddress(invoice.bidderId),
    ]);

    // Destructure the shipping-related data
    const { invoiceShipping, trackingNumbers, vendor } = invoiceShippingData;
    invoice.setBuyerPremiumTotal(this.getBuyerPremiumTotal(items));

    return new InvoiceDetailDto(
      invoice,
      auction,
      buyer,
      items,
      shippingAddress,
      invoiceShipping,
      trackingNumbers,
      vendor,
      invoiceNotes.list,
      invoiceStatuses,
      invoicePayments,
      billingAddress,
    );
  }

  private getBuyerPremiumTotal(items: ListResultset<Partial<InvoiceItemWithTaxDto>>): number {
    return items.list.reduce((total: number, item: InvoiceItemDto) => {
      return Number(total) + Number(item.buyerPremiumAmount);
    }, 0);
  }

  private async getBillingAddress(accountMappingsId: string): Promise<InvoiceAddressDto> {
    const response: any = await this.usersApi.getAllBillingDetailsByAccountMappingIds(accountMappingsId);
    return new InvoiceAddressDto(response.data?.data?.[0]);
  }
}
