import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { Controller, Param, Post, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { InvoiceShippingResponse } from '../../http/responses/invoice-shipping.response';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { InvalidParameterError } from '../../../../../users/src/account-mappings/exceptions/invalid-parameter.error';
import { InvoiceShippingDto } from '../../dtos/shipping/invoice-shipping.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';
import { InvoiceShippingRequest } from '../../http/requests/invoice-shipping.request';
import { InvoiceShippingListResponse } from '../../http/responses/invoice-shipping-list.response';
import { ListResultset } from '@mvc/data/list-resultset';
import { RedisService } from '@vb/redis';
import { InvoicesHandler } from '../../handlers/invoices.handler';

@Controller('invoices-shipping')
export class InvoiceShippingController extends PubSubController<
  InvoiceShippingHandler,
  InvoiceShippingRequest,
  InvoiceShippingDto,
  InvoiceShippingResponse,
  InvoiceShippingListResponse
> {
  constructor(
    invoiceShippingHandler: InvoiceShippingHandler,
    redisService: RedisService,
    private invoicesHandler: InvoicesHandler,
  ) {
    super(invoiceShippingHandler, redisService, 'invoice_shipping');
  }

  createDtoFromRequest(request: InvoiceShippingRequest): InvoiceShippingDto {
    return new InvoiceShippingDto(request);
  }

  createResponseFromDto(dto: InvoiceShippingDto): InvoiceShippingResponse {
    return new InvoiceShippingResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceShippingDto>): InvoiceShippingListResponse {
    return new InvoiceShippingListResponse(list);
  }

  @Post(':shippingId/status/:status')
  async approve(@Param('shippingId') shippingId: string, @Param('status') status: string, @Req() req: VBInterceptorRequest): Promise<InvoiceShippingResponse | ErrorResponse> {
    if (status !== 'approve' && status !== 'reject') {
      return new ErrorResponse(new InvalidParameterError(), 'approveShippingQuote');
    }

    const invoiceShipping: InvoiceShippingDto = await this.handler.get(shippingId);
    const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceShipping.invoicesId);
    invoiceShipping.isBuyerApproved = status === 'approve';

    await this.handler.update(req.accountMappingsId, invoiceShipping, invoiceShipping.id);
    this.notify('success', `buyer_${status}`, {
      data: {
        invoice: invoice,
        invoiceShipping: invoiceShipping,
      },
    });
    return new InvoiceShippingResponse(invoiceShipping);
  }
}
