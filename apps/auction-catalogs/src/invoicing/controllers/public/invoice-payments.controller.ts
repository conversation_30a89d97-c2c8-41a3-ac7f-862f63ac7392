import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PaymentType, UsersApi } from '@viewbid/ts-node-client';
import { PaymentProfilesController } from 'apps/auction-catalogs/src/payment-processing/controller/payment-profiles.controller';
import { TransactionRequestsController } from 'apps/auction-catalogs/src/payment-processing/controller/transaction-requests.controller';
import { InvoicePaymentDto } from '../../dtos/invoice-payment.dto';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { DoesNotOwnInvoiceError } from '../../exceptions/does-not-own-invoice.error';
import { UnsupportedPaymentTypeError } from '../../exceptions/unsupported-payment-type.error';
import { InvoiceItemsHandler } from '../../handlers/invoice-items.handler';
import { InvoicePaymentsHandler } from '../../handlers/invoice-payments.handler';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { InvoicePaymentRequest } from '../../http/requests/invoice-payment.request';
import { InvoicePaymentResponse } from '../../http/responses/invoice-payment.response';
import { InvoicePaymentsListResponse } from '../../http/responses/invoice-payments-list.response';
import { AbstractInvoicePaymentsController } from '../abstract-invoice-payments.controller';
import { InvoiceShippingHandler } from '../../handlers/invoice-shipping.handler';

@Controller('invoice-payments')
export class InvoicePaymentsController extends AbstractInvoicePaymentsController {
  constructor(
    handler: InvoicePaymentsHandler,
    invoicesHandler: InvoicesHandler,
    invoiceItemsHandler: InvoiceItemsHandler,
    invoiceShippingHandler: InvoiceShippingHandler,
    redisService: RedisService,
    transactionRequestsController: TransactionRequestsController,
    paymentsProfileController: PaymentProfilesController,
    usersApi: UsersApi,
  ) {
    super(handler, invoiceShippingHandler, redisService, invoicesHandler, invoiceItemsHandler, transactionRequestsController, paymentsProfileController, usersApi);
  }

  createDtoFromRequest(request: InvoicePaymentRequest): InvoicePaymentDto {
    return new InvoicePaymentDto(request);
  }

  createResponseFromDto(dto: InvoicePaymentDto): InvoicePaymentResponse {
    return new InvoicePaymentResponse(dto, {});
  }

  createResponseList(list: ListResultset<InvoicePaymentDto>): InvoicePaymentsListResponse {
    return new InvoicePaymentsListResponse(list);
  }

  /**
   * jail the results to the user
   * @param invoiceId
   * @param req
   */
  @Get('invoice/:invoiceId')
  async listInvoicePayments(@Param('invoiceId') invoiceId: string, @Req() req: VBInterceptorRequest): Promise<any> {
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.getCustom([
        {
          id: invoiceId,
          bidderId: req.accountMappingsId,
        },
      ]);

      const payments: ListResultset<InvoicePaymentDto> = (await this.handler.getAllCustom(1, -1, [{ invoicesId: invoice.id }], InvoicePaymentDto, {
        createdOn: 'desc',
      })) as unknown as ListResultset<InvoicePaymentDto>;
      return this.createResponseList(payments);
    } catch (error) {
      return new ErrorResponse(error, 'listInvoicePayments');
    }
  }

  @Get('invoice-number/:invoiceNumber')
  async listInvoicePaymentsByNumber(@Param('invoiceNumber') invoiceNumber: number, @Req() req: VBInterceptorRequest): Promise<InvoicePaymentsListResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.getByInvoiceNumber(invoiceNumber);

      return await this.listInvoicePayments(invoice.id, req);
    } catch (error) {
      return new ErrorResponse(error, 'listInvoicePaymentsByNumber');
    }
  }

  /**
   * jail the results to the user
   * @param id
   * @param req
   */
  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<InvoicePaymentResponse | ErrorResponse> {
    try {
      const item: InvoicePaymentDto = await this.handler.getBidderPayment(req.accountMappingsId, id);

      return this.createResponseFromDto(item);
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }

  /**
   * list all payments a user has ever made - jail the results to the user
   * @param page
   * @param size
   * @param query
   * @param req
   */
  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoicePaymentsListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      params.push({ bidderId: req.accountMappingsId });
      const items: ListResultset<InvoicePaymentDto> = await this.handler.getAllByBidderId(page, size, params);

      return this.createResponseList(items);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  /**
   * don't allow a user to update anything
   * @param id
   * @param body
   * @param req
   */
  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: InvoicePaymentRequest, @Req() req: VBInterceptorRequest): Promise<InvoicePaymentResponse | ErrorResponse> {
    id;
    body;
    req;
    return this.blockedEndpoint();
  }

  @Post('invoice/:invoiceId')
  async publicCreatePayment(
    @Param('invoiceId') invoiceId: string,
    @Body() body: InvoicePaymentRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoicePaymentResponse | ErrorResponse> {
    try {
      const invoice: InvoiceDto = await this.invoicesHandler.get(invoiceId);
      if (invoice.bidderId != req.accountMappingsId) {
        throw new DoesNotOwnInvoiceError(invoice.id, req.accountMappingsId);
      }

      //Currently we only allow credit card publically, everything else will be utilizing the admin endpoint
      if (body.paymentTypes !== PaymentType.OnlineCard) {
        throw new UnsupportedPaymentTypeError(body.paymentTypes);
      }

      return await this.createPayment(invoice.id, body, req, invoice);
    } catch (error) {
      return new ErrorResponse(error, 'createPayment');
    }
  }
}
