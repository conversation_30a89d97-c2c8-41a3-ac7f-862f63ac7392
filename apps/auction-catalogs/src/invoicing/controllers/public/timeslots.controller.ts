import { ListResultset } from '@mvc/data/list-resultset';
import { BadRequestException, Body, Controller, Get, Param, Post, Query, Req } from '@nestjs/common';
import { TimeslotsHandler } from '../../handlers/timeslots.handler';
import { TimeslotRequest } from '../../http/requests/timeslot.request';
import { TimeslotDto } from '../../dtos/timeslots/timeslot.dto';
import { TimeslotsListResponse } from '../../http/responses/timeslots-list.response';
import { TimeslotResponse } from '../../http/responses/timeslot.response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { InvoicesHandler } from '../../handlers/invoices.handler';
import { InvoiceDto } from '../../dtos/invoice.dto';
import { TimeslotUnavailableError } from '../../exceptions/timeslot-unavailable.error';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { RedisService } from '@vb/redis';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { AuctionDto } from '../../../auction/dtos/auction.dto';
import { InvoiceAlreadyBookedError } from '../../exceptions/invoice-already-booked.error';
import { TimeslotAvailabilityListDto } from '../../dtos/timeslots/timeslot-availability-list.dto';

enum TimeslotStatus {
  Pending = 'Pending',
  InProgress = 'In-Progress',
  Ready = 'Ready',
}

@Controller('timeslots')
export class TimeslotsController extends PubSubController<TimeslotsHandler, TimeslotRequest, TimeslotDto, TimeslotResponse, TimeslotsListResponse> {
  constructor(
    handler: TimeslotsHandler,
    redisService: RedisService,
    private auctionsHandler: AuctionsHandler,
    private invoicesHandler: InvoicesHandler,
  ) {
    super(handler, redisService, 'timeslots');
  }

  createDtoFromRequest(request: TimeslotRequest): TimeslotDto {
    return new TimeslotDto(request);
  }

  createResponseFromDto(dto: TimeslotDto): TimeslotResponse {
    return new TimeslotResponse(dto, {});
  }

  createResponseList(list: ListResultset<TimeslotDto>): TimeslotsListResponse {
    return new TimeslotsListResponse(list);
  }

  @Post('auctions/:auctionId/invoices/:invoiceId')
  async createTimeslot(
    @Param('auctionId') auctionId: string,
    @Param('invoiceId') invoiceId: string,
    @Body() body: TimeslotRequest,
    @Req() req: VBInterceptorRequest,
  ): Promise<TimeslotResponse | ErrorResponse> {
    return this.executeAndNotify(
      'booked',
      async () => {
        try {
          const [auction, invoice]: [AuctionDto, InvoiceDto] = await Promise.all([this.auctionsHandler.get(auctionId), this.invoicesHandler.get(invoiceId)]);

          const item: TimeslotDto = this.createDtoFromRequest(body);
          item.auctionsId = auction.id;
          item.invoicesId = invoice.id;
          item.accountMappingsId = req.accountMappingsId;
          item.status = TimeslotStatus.Pending;
          const existingBooking: TimeslotDto | undefined = await this.checkInvoiceBooked(invoice);
          if (existingBooking) {
            throw new InvoiceAlreadyBookedError(invoice, existingBooking);
          }
          const pickupTime: string = new Date(item.pickupTime).toISOString();
          const timeslots: ListResultset<TimeslotDto> = (await this.handler.getAllCustom(
            1,
            4, // Fetch up to 4 timeslots for checking
            [{ pickupTime: pickupTime }],
            TimeslotDto,
          )) as unknown as ListResultset<TimeslotDto>;

          if (timeslots.totalCount >= 3) {
            throw new TimeslotUnavailableError(item.pickupTime.toString());
          }

          item.index = timeslots.totalCount + 1;
          const timeslot: TimeslotDto = await this.handler.create(req.accountMappingsId, item);

          return new TimeslotResponse(timeslot);
        } catch (error) {
          return new ErrorResponse(error, 'createTimeslot');
        }
      },
      {},
    );
  }

  protected async checkInvoiceBooked(invoice: InvoiceDto): Promise<TimeslotDto | undefined> {
    try {
      return await this.handler.getCustom([{ invoicesId: invoice.id }]);
    } catch (error) {}
  }

  @Get('getAllBookings')
  async getAllBookings(@Query('fromDate') fromDate: string, @Query('toDate') toDate: string): Promise<TimeslotsListResponse | ErrorResponse> {
    try {
      // Check if both dates are provided
      if (!fromDate || !toDate) {
        throw new BadRequestException('Both fromDate and toDate are required.');
      }

      // Validate if fromDate and toDate are valid dates
      if (!this.isValidDate(fromDate) || !this.isValidDate(toDate)) {
        throw new BadRequestException('Invalid date format. Use YYYY-MM-DD.');
      }

      // Check if toDate is not earlier than fromDate
      if (new Date(toDate) < new Date(fromDate)) {
        throw new BadRequestException('toDate cannot be earlier than fromDate.');
      }
      const items: ListResultset<TimeslotAvailabilityListDto> = await this.handler.getBookings([{ fromDate: fromDate, toDate: toDate }]);
      const response: TimeslotsListResponse = new TimeslotsListResponse(items);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  private isValidDate(dateStr: string): boolean {
    const date: Date = new Date(dateStr);
    return !isNaN(date.getTime());
  }
}
