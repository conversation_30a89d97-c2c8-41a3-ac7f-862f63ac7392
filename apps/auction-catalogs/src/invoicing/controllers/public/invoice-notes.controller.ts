import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Delete, Get, Param, Patch, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { InvoiceNoteDto } from '../../dtos/invoice-note.dto';
import { InvoiceNotesHandler } from '../../handlers/invoice-notes.handler';
import { InvoiceNoteRequest } from '../../http/requests/invoice-note.request';
import { InvoiceNoteListResponse } from '../../http/responses/invoice-note-list.response';
import { InvoiceNoteResponse } from '../../http/responses/invoice-note.response';

@Controller('invoice-notes')
export class PublicInvoiceNotesController extends BaseController<InvoiceNotesHandler, InvoiceNoteRequest, InvoiceNoteDto, InvoiceNoteResponse, InvoiceNoteListResponse> {
  constructor(handler: InvoiceNotesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: InvoiceNoteRequest): InvoiceNoteDto {
    return new InvoiceNoteDto(request);
  }

  createResponseFromDto(dto: InvoiceNoteDto): InvoiceNoteResponse {
    return new InvoiceNoteResponse(dto);
  }
  createResponseList(list: ListResultset<InvoiceNoteDto>): InvoiceNoteListResponse {
    return new InvoiceNoteListResponse(list);
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: InvoiceNoteRequest, @Req() req: VBInterceptorRequest): Promise<InvoiceNoteResponse | ErrorResponse> {
    req;
    return this.blockedEndpoint();
  }

  @Delete('/:id')
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    req;
    id;
    return this.blockedEndpoint();
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<InvoiceNoteResponse | ErrorResponse> {
    id;
    req;
    return this.blockedEndpoint();
  }

  @Patch('/restore/:id')
  async restore(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<InvoiceNoteResponse | ErrorResponse> {
    req;
    id;
    return this.blockedEndpoint();
  }

  @Get('')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<InvoiceNoteListResponse | ErrorResponse> {
    page;
    size;
    query;
    req;
    return this.blockedEndpoint();
  }
}
