import { IsInt, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, ValidateIf } from 'class-validator';

export class PurchaseOrderItemRequest {
  @IsUUID()
  productsId: string;
  @IsUUID()
  purchaseOrdersId: string;
  @IsInt()
  @Min(0)
  @ValidateIf((object: any, value: any) => value !== null)
  quantityOrdered: number | null;
  @IsInt()
  @Min(0)
  @ValidateIf((object: any, value: any) => value !== null)
  quantityDelivered: number | null;
  @IsString()
  comments: string;
  @IsString()
  barcode: string;
  @IsUUID(undefined, { each: true })
  lotIds: string[];
}
