import { BaseController } from '@mvc/controllers/base.controller';
import { PurchaseOrderItemsHandler } from '../../handler/purchase-order-items.handler';
import { PurchaseOrderItemRequest } from '../../http/requests/purchase-order-item.request';
import { PurchaseOrderItemDto } from '../../dtos/purchase-order-item.dto';
import { PurchaseOrderItemResponse } from '../../http/responses/purchase-order-item.response';
import { PurchaseOrderItemListResponse } from '../../http/responses/purchase-order-item-list.response';
import { Body, Controller, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { PurchaseOrdersHandler } from '../../../purchase-orders/handler/purchase-orders.handler';
import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ProductsHandler } from '../../../products/handler/products.handler';
import { LotsHandler } from '../../../lots/handlers/lots.handler';
import { ExtendedPurchaseOrderItemDto } from '../../dtos/extended-purchase-order-item.dto';

@Controller('admin/purchase-order-items')
export class PurchaseOrderItemsController extends BaseController<
  PurchaseOrderItemsHandler,
  PurchaseOrderItemRequest,
  PurchaseOrderItemDto,
  PurchaseOrderItemResponse,
  PurchaseOrderItemListResponse
> {
  constructor(
    handler: PurchaseOrderItemsHandler,
    private productsHandler: ProductsHandler,
    private lotsHandler: LotsHandler,
    private purchaseOrdersHandler: PurchaseOrdersHandler,
  ) {
    super(handler);
  }

  @Post('/')
  async create(@Body() body: PurchaseOrderItemRequest, @Req() req: VBInterceptorRequest): Promise<PurchaseOrderItemResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      await this.purchaseOrdersHandler.get(body.purchaseOrdersId);
      await this.productsHandler.get(body.productsId);

      const result: PurchaseOrderItemDto = await this.handler.create(userId, this.createDtoFromRequest(body));

      return this.createResponseFromDto(result);
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Get('/:id')
  async get(@Param('id') id: string, @Param('req') req: VBInterceptorRequest): Promise<PurchaseOrderItemResponse | ErrorResponse> {
    id;
    req;
    return this.blockedEndpoint();
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: PurchaseOrderItemRequest, @Req() req: VBInterceptorRequest): Promise<PurchaseOrderItemResponse | ErrorResponse> {
    try {
      const userId: string = req.accountMappingsId;
      await this.purchaseOrdersHandler.get(body.purchaseOrdersId);
      await this.productsHandler.get(body.productsId);

      const result: PurchaseOrderItemDto = await this.handler.update(userId, this.createDtoFromRequest(body), id);

      return this.createResponseFromDto(result);
    } catch (error) {
      return new ErrorResponse(error, 'update');
    }
  }

  @Get('/')
  async getAll(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<PurchaseOrderItemListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);

      const productsId: string | undefined = params.find((r: any) => r.hasOwnProperty('productsId'))?.productsId;
      const purchaseOrdersId: string | undefined = params.find((r: any) => r.hasOwnProperty('purchaseOrdersId'))?.purchaseOrdersId;
      const lotsId: string | undefined = params.find((r: any) => r.hasOwnProperty('lotsId'))?.lotsId;

      if (productsId) {
        await this.productsHandler.get(productsId);
      }
      if (purchaseOrdersId) {
        await this.purchaseOrdersHandler.get(purchaseOrdersId);
      }
      if (lotsId) {
        await this.lotsHandler.get(lotsId);
      }

      const result: ListResultset<PurchaseOrderItemDto> = await this.handler.getAll(page, size, params);

      return this.createResponseList(result);
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  createDtoFromRequest(request: PurchaseOrderItemRequest): ExtendedPurchaseOrderItemDto {
    return new ExtendedPurchaseOrderItemDto(request);
  }

  createResponseFromDto(dto: PurchaseOrderItemDto): PurchaseOrderItemResponse {
    return new PurchaseOrderItemResponse(dto);
  }

  createResponseList(list: ListResultset<PurchaseOrderItemDto>): PurchaseOrderItemListResponse {
    return new PurchaseOrderItemListResponse(list);
  }
}
