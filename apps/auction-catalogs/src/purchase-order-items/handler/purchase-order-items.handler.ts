import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { PurchaseOrderItemDto } from '../dtos/purchase-order-item.dto';
import { PurchaseOrderItemsDbService } from '../data/purchase-order-items-db.service';
import { Injectable } from '@nestjs/common';
import { LotPurchaseOrderItemsDbService } from '../data/lot-purchase-order-items-db.service';
import { ExtendedPurchaseOrderItemDto } from '../dtos/extended-purchase-order-item.dto';
import { LotPurchaseOrderItemDto } from '../dtos/lot-purchase-order-item.dto';

@Injectable()
export class PurchaseOrderItemsHandler extends BaseHandler<PurchaseOrderItemsDbService, PurchaseOrderItemDto> {
  constructor(
    dbService: PurchaseOrderItemsDbService,
    private lotPurchaseOrderItemsDbService: LotPurchaseOrderItemsDbService,
  ) {
    super(dbService);
  }

  async create(userId: string, request: ExtendedPurchaseOrderItemDto): Promise<ExtendedPurchaseOrderItemDto> {
    const item: PurchaseOrderItemDto = await this.dbService.create(userId, new PurchaseOrderItemDto(request));

    await this.upsertLotIds(item.id, request.lotIds);

    return new ExtendedPurchaseOrderItemDto({
      ...item,
      lotIds: request.lotIds,
    });
  }

  async get(id: string): Promise<ExtendedPurchaseOrderItemDto> {
    const item: PurchaseOrderItemDto = await this.dbService.get(id);

    const lotPoItems: LotPurchaseOrderItemDto[] = (await this.lotPurchaseOrderItemsDbService.getAll(1, -1, [{ purchaseOrderItemsId: id }])).list;

    return new ExtendedPurchaseOrderItemDto({
      ...item,
      lotIds: lotPoItems.map((lotPoItem: LotPurchaseOrderItemDto) => lotPoItem.lotsId),
    });
  }

  async update(userId: string, request: ExtendedPurchaseOrderItemDto, id: string): Promise<PurchaseOrderItemDto> {
    const item: PurchaseOrderItemDto = await this.dbService.update(userId, new PurchaseOrderItemDto(request), id);

    await this.upsertLotIds(item.id, request.lotIds);

    return new ExtendedPurchaseOrderItemDto({
      ...item,
      lotIds: request.lotIds,
    });
  }

  async upsertLotIds(purchaseOrderItemsId: string, lotIds: string[]): Promise<void> {
    const dtos: LotPurchaseOrderItemDto[] = lotIds.map(
      (lotsId: string) =>
        new LotPurchaseOrderItemDto({
          lotsId,
          purchaseOrderItemsId,
        }),
    );

    await this.lotPurchaseOrderItemsDbService.createOrReplaceBulk(dtos, { purchaseOrderItemsId });
  }
}
