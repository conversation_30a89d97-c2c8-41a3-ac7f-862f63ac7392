import { forwardRef, Module } from '@nestjs/common';
import { PurchaseOrderItemsController } from './controller/admin/purchase-order-items.controller';
import { PurchaseOrderItemsDbService } from './data/purchase-order-items-db.service';
import { PurchaseOrderItemsHandler } from './handler/purchase-order-items.handler';
import { LotPurchaseOrderItemsDbService } from './data/lot-purchase-order-items-db.service';
import { ProductsModule } from '../products/products.module';
import { LotsModule } from '../lots/lots.module';
import { PurchaseOrdersModule } from '../purchase-orders/purchase-orders.module';

@Module({
  controllers: [PurchaseOrderItemsController],
  providers: [PurchaseOrderItemsDbService, PurchaseOrderItemsHandler, LotPurchaseOrderItemsDbService],
  imports: [forwardRef(() => ProductsModule), forwardRef(() => LotsModule), forwardRef(() => PurchaseOrdersModule)],
  exports: [],
})
export class PurchaseOrderItemsModule {}
