export class PurchaseOrderItemDto {
  id: string;
  productsId: string;
  purchaseOrdersId: string;
  quantityOrdered: number | null;
  quantityDelivered: number | null;
  comments: string;
  barcode: string;

  constructor(row: any) {
    this.id = row.id;
    this.productsId = row.productsId;
    this.purchaseOrdersId = row.purchaseOrdersId;
    this.quantityDelivered = row.quantityDelivered;
    this.quantityOrdered = row.quantityOrdered;
    this.comments = row.comments;
    this.barcode = row.barcode;
  }
}
