import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { LotPurchaseOrderItemDto } from '../dtos/lot-purchase-order-item.dto';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { LotPurchaseOrderItemNotFoundError } from '../exceptions/lot-purchase-order-item-not-found.error';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotPurchaseOrderItemsDbService extends BaseDbService<Prisma.LotPurchaseOrderItemsDelegate<DefaultArgs>, LotPurchaseOrderItemDto> {
  constructor(prisma: PrismaService) {
    super(prisma.lotPurchaseOrderItems, true, false, true);
  }

  throw404(id: string): Error {
    throw new LotPurchaseOrderItemNotFoundError(id);
  }

  mapToDto(model: any): LotPurchaseOrderItemDto {
    return new LotPurchaseOrderItemDto(model);
  }

  createFilter(params: any[]): any {
    const purchaseOrderItemsId: string | undefined = params.find((r: any) => r.hasOwnProperty('purchaseOrderItemsId'))?.purchaseOrderItemsId;
    const lotsId: string | undefined = params.find((r: any) => r.hasOwnProperty('lotsId'))?.lotsId;

    return { purchaseOrderItemsId, lotsId };
  }
}
