import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { PurchaseOrderItemDto } from '../dtos/purchase-order-item.dto';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { PurchaseOrderItemNotFoundError } from '../exceptions/purchase-order-item-not-found.error';
import { Injectable } from '@nestjs/common';
import { ExtendedPurchaseOrderItemDto } from '../dtos/extended-purchase-order-item.dto';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class PurchaseOrderItemsDbService extends BaseDbService<Prisma.PurchaseOrderItemsDelegate<DefaultArgs>, PurchaseOrderItemDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.purchaseOrderItems);
  }

  throw404(id: string): Error {
    throw new PurchaseOrderItemNotFoundError(id);
  }

  mapToDto(model: any): PurchaseOrderItemDto {
    return new PurchaseOrderItemDto(model);
  }

  mapToExtendedDto(model: any): ExtendedPurchaseOrderItemDto {
    return new ExtendedPurchaseOrderItemDto({
      ...model.purchaseOrderItem,
      lotIds: model.lotIds,
    });
  }

  async getAll(page: number, size: number, params: any[]): Promise<ListResultset<ExtendedPurchaseOrderItemDto>> {
    const filter: Prisma.Sql = this.createFilter(params);

    const skip: number = size > 0 ? (page - 1) * size : 0;
    const limit: number | Prisma.Sql = size > 0 ? size : Prisma.sql`ALL`;

    const purchaseOrderItems: ExtendedPurchaseOrderItemDto[] = (
      await this.prisma.$queryRaw<any[]>`
    SELECT ROW_TO_JSON(poi.*) as "purchaseOrderItem",
           ARRAY_AGG(lpoi."lotsId") FILTER (WHERE lpoi.id IS NOT NULL
                                            AND lpoi."deletedOn" IS NULL
                                            AND lot."deletedOn" IS NULL) as "lotIds"
    FROM public."PurchaseOrderItems" poi
    LEFT JOIN public."LotPurchaseOrderItems" lpoi ON poi.id = lpoi."purchaseOrderItemsId"
    LEFT JOIN public."Lots" lot ON lot.id = lpoi."lotsId"
    WHERE poi."deletedOn" IS NULL
    ${filter} OFFSET ${skip} LIMIT ${limit};`
    ).map((row: any) => this.mapToExtendedDto(row));

    const totalCount: number = (
      await this.prisma.$queryRaw<any[]>`
      SELECT SUM("count")::int AS "totalCount" FROM (
        SELECT COUNT(DISTINCT poi.id) as "count"
        FROM public."PurchaseOrderItems" poi
        LEFT JOIN public."LotPurchaseOrderItems" lpoi ON poi.id = lpoi."purchaseOrderItemsId"
        LEFT JOIN public."Lots" lot ON lot.id = lpoi."lotsId"
        WHERE poi."deletedOn" IS NULL ${filter});`
    )[0].totalCount;

    return new ListResultset(purchaseOrderItems, page, size, totalCount, Math.ceil(totalCount / size));
  }

  createFilter(params: any[]): any {
    const productsId: string | undefined = params.find((r: any) => r.hasOwnProperty('productsId'))?.productsId;
    const purchaseOrdersId: string | undefined = params.find((r: any) => r.hasOwnProperty('purchaseOrdersId'))?.purchaseOrdersId;
    const lotsId: string | undefined = params.find((r: any) => r.hasOwnProperty('lotsId'))?.lotsId;

    const productsWhere: Prisma.Sql = productsId ? Prisma.sql`AND poi."productsId" = ${productsId}::uuid` : Prisma.empty;
    const purchaseOrdersWhere: Prisma.Sql = purchaseOrdersId ? Prisma.sql`AND poi."purchaseOrdersId" = ${purchaseOrdersId}::uuid` : Prisma.empty;
    const lotsWhere: Prisma.Sql = lotsId ? Prisma.sql`AND ${lotsId}=ANY(ARRAY_AGG(lot.id))` : Prisma.empty;

    return Prisma.sql`${productsWhere} ${purchaseOrdersWhere} GROUP BY poi.id HAVING TRUE ${lotsWhere}`;
  }
}
