import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { Injectable, Logger } from '@nestjs/common';
import { LotsHandler } from '../handlers/lots.handler';
import { LotEditHistoryHandler } from '../handlers/lot-edit-history.handler';
import { LotDto } from '../dtos/lot.dto';
import { LotEditHistoryDto } from '../dtos/lot-edit-history.dto';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { AuctionDto } from '../../auction/dtos/auction.dto';

@Injectable()
export class SaveLotEditHistorySubscriber implements SubscriberInterface {
  private logger = new Logger(SaveLotEditHistorySubscriber.name);
  private ignoredFields: (keyof LotDto)[] = ['createdBy', 'id', 'inputMethod', 'inputDevice', 'tags', 'code'];

  constructor(
    private lotsHandler: <PERSON><PERSON>and<PERSON>,
    private lotEditHistoryHandler: LotEditHistoryHandler,
    private auctionsHandler: AuctionsHandler,
  ) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const accountMappingsId: string = eventPayload.userId ?? '********-0000-0000-0000-************';
    const lotId: string = eventPayload.id;
    let lot: LotDto;

    try {
      lot = await this.lotsHandler.get(lotId);
    } catch (error) {
      this.logger.error(`Lot with ID ${lotId} not found: ${error.message}`);
      // Optionally, you can return or perform other actions here
      return;
    }

    try {
      const auction: AuctionDto = await this.auctionsHandler.getMostRecentAuctionByLotId(lotId);
      if (!this.isTrackable(auction)) {
        return;
      }
    } catch (error) {
      // No auction found, no need to track the edit
      this.logger.log(`No recent auction found for lot ID ${lotId}, no tracking needed.`);
      return;
    }

    const newLot: LotDto = new LotDto(eventPayload.items);
    const lotEditHistory: LotEditHistoryDto | null = this.createSingleEditHistoryEntry(lot, newLot, accountMappingsId);
    if (lotEditHistory) {
      lotEditHistory.reason = eventPayload.items.reason;
      await this.lotEditHistoryHandler.create(accountMappingsId, lotEditHistory);
      this.logger.log(`Edit to lot ${lotId} has been tracked in history`);
    }
  }

  createSingleEditHistoryEntry(oldLot: LotDto, newLot: LotDto, editedBy: string): LotEditHistoryDto | null {
    const changes: Record<string, { oldValue: string; newValue: string }> = {};
    const fields: any = Object.keys(oldLot) as (keyof LotDto)[];

    fields.forEach((field: any) => {
      if (this.ignoredFields.includes(field)) {
        return; // Skip ignored fields
      }

      const oldValue: any = oldLot[field as keyof LotDto];
      const newValue: any = newLot[field as keyof LotDto];

      const normalizedOldValue: string = this.normalizeValue(oldValue);
      const normalizedNewValue: string = this.normalizeValue(newValue);

      if (normalizedOldValue !== normalizedNewValue) {
        changes[field] = {
          oldValue: oldValue !== undefined && oldValue !== null ? oldValue.toString() : 'null',
          newValue: newValue !== undefined && newValue !== null ? newValue.toString() : 'null',
        };
      }
    });
    if (Object.keys(changes).length === 0) {
      return null; // No changes
    }
    return new LotEditHistoryDto({
      lotId: oldLot.id,
      editedOn: new Date(),
      editedBy,
      changes,
    });
  }

  normalizeValue(value: any): string {
    if (value === undefined || value === null) {
      return 'null';
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    if (typeof value === 'string' && value.trim() === '') {
      return 'null';
    }
    return value.toString().trim();
  }

  /**
   * don't track edits when an auction hasn't been published yet.
   *
   * @param auction
   * @private
   */
  private isTrackable(auction: AuctionDto): boolean {
    return auction.status !== 'DRAFT';
  }
}
