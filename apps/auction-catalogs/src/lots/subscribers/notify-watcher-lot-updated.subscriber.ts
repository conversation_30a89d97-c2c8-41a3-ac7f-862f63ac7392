import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { LotDto } from '../dtos/lot.dto';
import { AuctionLotsHandler } from '../../auction-lots/handlers/auction-lots.handler';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { LotUpdateNotificationParametersDto } from '../dtos/lot-update-notification-parameters.dto';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { ConfigService } from '@vb/config/src/config.service';
import { AuctionLotsStatus } from '@viewbid/ts-node-client';
import { SharedService } from '../../shared/shared.service';
import { ResourceType } from '../../uploads/entities/uploads.interfaces';

@Injectable()
export class NotifyWatcherLotUpdatedSubscriber implements SubscriberInterface, OnModuleInit {
  private readonly logger = new Logger(NotifyWatcherLotUpdatedSubscriber.name);
  private baseUrl: string;
  private cdnURL: string;

  constructor(
    private readonly auctionLotHandler: AuctionLotsHandler,
    private readonly sqsService: AwsSqsService,
    private readonly configService: ConfigService,
    private readonly sharedService: SharedService,
  ) {}
  async onModuleInit(): Promise<void> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
    this.cdnURL = await this.configService.getSetting('CDN_URL', '');
  }

  /**
   * when a lot item I'm watching is edited after auction opens"
   *
   * @param eventType
   * @param payload
   */
  async handleEvent(_eventType: EventType, payload: EventPayload): Promise<void> {
    try {
      const eventKey: string = 'LOT_UPDATE_NOTIFICATION';

      const lotDto: LotDto = new LotDto(payload.items);
      const auctionLotsDto: AuctionLotDto = await this.auctionLotHandler.getByLotId(lotDto.id);
      const images: Array<{
        [p: string]: any;
        id?: string;
      }> = await this.sharedService.getResourceURLById(lotDto.id, ResourceType.LOT_ITEM_IMAGES);

      if (auctionLotsDto && auctionLotsDto.status?.toUpperCase() === AuctionLotsStatus.Published) {
        let imageURL: string = '';
        if (images && images.length > 0) {
          imageURL = images?.[0]?.url ?? '';
        } else {
          imageURL = `${this.cdnURL}/no-image-lot.svg`;
        }

        this.logger.log(`Sending LOT_UPDATE_NOTIFICATION event for Lot Id: ${lotDto.id}`);

        const message: Message = MessageFactory.build(
          eventKey,
          new AccountMappingCommonListDto([]),
          new LotUpdateNotificationParametersDto(
            lotDto.title,
            `${this.baseUrl}/auctions/${auctionLotsDto.auctionId}/lot/${lotDto.id}`,
            auctionLotsDto.id,
            auctionLotsDto.number ?? 0,
            imageURL,
          ),
        );

        this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
      }
    } catch (error) {
      this.logger.error(`Error sending lot update notification: ${error}`);
    }
  }
}
