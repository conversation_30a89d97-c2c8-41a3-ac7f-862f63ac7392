import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { Injectable, Logger } from '@nestjs/common';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { Lots<PERSON>and<PERSON> } from '../handlers/lots.handler';

@Injectable()
export class SetThumbnailSubscriber implements SubscriberInterface {
  private logger = new Logger(SetThumbnailSubscriber.name);

  constructor(private lotsHandler: LotsHandler) {}

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    try {
      const files: Array<any> = eventPayload.items.files;
      const uploadId: string = files[0].uploadId;

      await this.lotsHandler.updateThumbnail(uploadId);
      this.logger.log('thumbnail updated for lot item successfully');
    } catch (error) {
      this.logger.error('error occurred while updating thumbnail for lot item');
      this.logger.error(error.toString());
    }
  }
}
