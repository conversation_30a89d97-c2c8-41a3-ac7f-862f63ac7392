import { Response } from '@mvc/http/responses/response';
import { LotDto } from '../../dtos/lot.dto';
import { AuctionLotDto } from '../../../auction-lots/dto/auction-lot.dto';
import { LotAttributeViewDto } from '../../../lot-attributes/dtos/lot-attribute-view.dto';
import { LotEditHistoryDto } from '../../dtos/lot-edit-history.dto';

/**
 * contains attributes but no bidding data
 */
export class LotDetailedAdminResponse extends Response {
  constructor(
    lot: LotDto,
    auctionLot: AuctionLotDto,
    images: {
      [p: string]: any;
      id?: string | undefined;
    }[],
    documents: { [p: string]: any; id?: string | undefined }[],
    attributes: Array<LotAttributeViewDto>,
    editHistory?: LotEditHistoryDto[],
  ) {
    super(
      {
        id: lot.id,
        title: lot.title,
        description: lot.description,
        status: lot.status,
        code: lot.code,
        reserveAmount: lot.reserveAmount,
        tags: lot.tags,
        condition: lot.condition,
        category: lot.category,
        region: lot.region,
        thumbnailId: lot.thumbnailId,
        subtitle: lot.subtitle,
        consignorNumber: lot.consignorNumber,
        excludedRegions: lot.excludedRegions,
        isRegionSpecific: lot.isRegionSpecific,
        inputDevice: lot.inputDevice,
        inputMethod: lot.inputMethod,
        number: auctionLot?.number,
        suffix: auctionLot?.suffix,
        displayOrder: auctionLot?.displayOrder,
        auctionsId: auctionLot?.auctionId,
        toDatetime: auctionLot?.toDatetime,
        pickupLocation: lot.pickupLocation,
        images: images,
        documents: documents,
        attributes: attributes,
        buyNowPrice: auctionLot?.buyNowPrice,
        editHistory: editHistory,
        startAmount: lot.startAmount,
        isAcceptOffers: lot.isAcceptOffers,
        nextBidMeetsReserve: auctionLot.nextBidMeetsReserve,
        taxPercentage: lot.taxPercentage,
      },
      'getLot',
      lot.id,
    );
  }
}
