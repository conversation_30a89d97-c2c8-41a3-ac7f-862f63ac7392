import { Response } from '@mvc/http/responses/response';
import { LotAttributeDto } from '../../../lot-attributes/dtos/lot-attribute.dto';
import { LotDetailedDto } from '../../dtos/lot-detailed.dto';
import { ListResultset } from '@mvc/data/list-resultset';

export class LotDetailedResponse extends Response {
  constructor(lot: LotDetailedDto, attributes?: ListResultset<LotAttributeDto>) {
    super(
      {
        id: lot.id,
        title: lot.title,
        description: lot.description,
        status: lot.status,
        code: lot.code,
        reserveAmount: lot.reserveAmount,
        tags: lot.tags,
        condition: lot.condition,
        category: lot.category,
        region: lot.region,
        thumbnailId: lot.thumbnailId,
        subtitle: lot.subtitle,
        consignorNumber: lot.consignorNumber,
        excludedRegions: lot.excludedRegions,
        isRegionSpecific: lot.isRegionSpecific,
        inputDevice: lot.inputDevice,
        inputMethod: lot.inputMethod,
        number: lot.number,
        suffix: lot.suffix,
        displayOrder: lot.displayOrder,
        auctionsId: lot.auctionsId,
        toDatetime: lot.toDatetime,
        pickupLocation: lot.pickupLocation,
        attributes: attributes?.list,
        startAmount: lot.startAmount,
        isAcceptOffers: lot.isAcceptOffers,
        nextBidMeetsReserve: lot.nextBidMeetsReserve,
        taxPercentage: lot.taxPercentage,
      },
      'getLot',
      lot.id,
    );
  }
}
