import { ListResultset } from '@mvc/data/list-resultset';
import { ListResponse } from '@mvc/http/responses/list-response';
import { LotDto } from '../../dtos/lot.dto';
import { LotDetailedDto } from '../../dtos/lot-detailed.dto';

export class LotsListGetResponse extends ListResponse {
  constructor(listResultSet: ListResultset<LotDto>) {
    super(listResultSet, 'listLots');
    this.data = listResultSet.list.map((item: LotDto) => new LotDetailedDto(item));
  }
}
