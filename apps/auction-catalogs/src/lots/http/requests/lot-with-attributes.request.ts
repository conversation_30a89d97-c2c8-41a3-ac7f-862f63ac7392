import { Is<PERSON><PERSON>y, IsBoolean, IsDefined, IsEnum, IsInt, IsNotEmpty, IsNumber, IsString, Length, Min, ValidateIf } from 'class-validator';
import { LotStatus } from '@vb/nest/enums/lotStatus';
import { isValidFloatNumber } from '@vb/nest/validators/isValidFloatNumber';
import { LotCondition } from '@vb/nest/enums/lotCondition';
import { InputDevice } from '@vb/nest/enums/inputDevice';
import { InputMethod } from '@vb/nest/enums/inputMethod';

export class LotWithAttributesRequest {
  @IsNotEmpty()
  @IsString()
  @Length(1, 300)
  title: string;
  @IsDefined()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsEnum(LotStatus)
  status: LotStatus;

  @IsNotEmpty()
  @IsString()
  @Length(1, 15)
  code: string;

  @IsNotEmpty()
  @IsNumber()
  @isValidFloatNumber({
    message: (): string => 'Invalid float number. Two digits after decimal accepted',
  })
  reserveAmount: number;
  @IsDefined()
  @IsString()
  tags: string;

  @IsNotEmpty()
  @IsEnum(LotCondition)
  condition: LotCondition;

  @IsNotEmpty()
  @IsString()
  @Length(0, 50)
  category: string;

  @IsNotEmpty()
  @IsString()
  @Length(0, 25)
  region: string;

  @IsNotEmpty()
  @IsNumber()
  number: number;
  suffix: string;

  @IsNotEmpty()
  @IsNumber()
  displayOrder: number;
  auctionId: string;
  pickupLocation: string;
  @IsDefined()
  @IsString()
  @Length(0, 300)
  subtitle: string;

  @IsString()
  @Length(0, 36)
  consignorNumber: string;
  @IsArray()
  @IsString({ each: true })
  excludedRegions: string[];

  @IsNotEmpty()
  @IsBoolean()
  isRegionSpecific: boolean;

  @IsNotEmpty()
  @IsEnum(InputDevice)
  inputDevice: InputDevice;

  @IsNotEmpty()
  @IsEnum(InputMethod)
  inputMethod: InputMethod;
  buyNowPrice: number;
  attributes: any[];
  reason: string;

  @IsNotEmpty()
  @IsNumber()
  @isValidFloatNumber({
    message: (): string => 'Invalid float number. Two digits after decimal accepted',
  })
  startAmount: number;

  @IsNotEmpty()
  @IsBoolean()
  isAcceptOffers: boolean;

  @IsNotEmpty()
  @IsBoolean()
  nextBidMeetsReserve: boolean;

  @ValidateIf((o: any) => o.taxTypesId !== null && o.taxTypesId !== undefined)
  @IsInt()
  @Min(1)
  taxTypesId: number | null;
}
