import { IsDefined, IsNotEmpty, IsString, IsEnum, IsNumber, IsBoolean, Length, IsArray, Min, IsInt, ValidateIf } from 'class-validator';
import { LotStatus } from '@vb/nest/enums/lotStatus';
import { LotCondition } from '@vb/nest/enums/lotCondition';
import { isValidFloatNumber } from '@vb/nest/validators/isValidFloatNumber';
import { InputDevice } from '@vb/nest/enums/inputDevice';
import { InputMethod } from '@vb/nest/enums/inputMethod';

export class LotsRequest {
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  @Length(1, 300)
  title: string;
  @IsDefined()
  @IsString()
  description: string;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(LotStatus)
  status: LotStatus;
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  @Length(1, 15)
  code: string;
  @IsDefined()
  @IsNotEmpty()
  @IsNumber()
  @isValidFloatNumber({
    message: (): string => 'Invalid float number. Two digits after decimal accepted',
  })
  reserveAmount: number;
  @IsDefined()
  @IsString()
  tags: string;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(LotCondition)
  condition: LotCondition;
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  @Length(0, 25)
  category: string;
  @IsDefined()
  @IsNotEmpty()
  @IsString()
  @Length(0, 25)
  region: string;
  @IsDefined()
  @IsNotEmpty()
  @IsNumber()
  number: number;
  @IsDefined()
  @IsNotEmpty()
  @IsNumber()
  displayOrder: number;
  auctionId: string;
  pickupLocation: string;
  @IsDefined()
  @IsString()
  @Length(0, 300)
  subtitle: string;
  @IsDefined()
  @IsString()
  @Length(0, 36)
  consignorNumber: string;
  @IsArray()
  @IsString({ each: true })
  excludedRegions: string[];
  @IsDefined()
  @IsNotEmpty()
  @IsBoolean()
  isRegionSpecific: boolean;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(InputDevice)
  inputDevice: InputDevice;
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(InputMethod)
  inputMethod: InputMethod;
  buyNowPrice: number;
  reason: string;
  @IsString()
  @Length(0, 6)
  suffix: string;
  @IsDefined()
  @IsNotEmpty()
  @IsNumber()
  @isValidFloatNumber({
    message: (): string => 'Invalid float number. Two digits after decimal accepted',
  })
  startAmount: number;
  @IsDefined()
  @IsNotEmpty()
  @IsBoolean()
  isAcceptOffers: boolean;

  @ValidateIf((o: any) => o.taxTypesId !== null && o.taxTypesId !== undefined)
  @IsInt()
  @Min(1)
  taxTypesId: number | null;
}
