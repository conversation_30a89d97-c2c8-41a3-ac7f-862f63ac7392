import { BaseDbService } from '@mvc/data/base-db.service';
import { LotEditHistoryDto } from '../dtos/lot-edit-history.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { Injectable } from '@nestjs/common';
import { LotEditHistoryNotFoundError } from '../exceptions/lot-edit-history-not-found.error';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class LotEditHistoryDbService extends BaseDbService<Prisma.LotEditHistoryDelegate, LotEditHistoryDto> {
  constructor(prisma: PrismaService) {
    super(prisma.lotEditHistory, false, false, false);
  }

  getOrderBy(): any {
    return {
      editedOn: 'desc',
    };
  }

  mapToDto(model: any): LotEditHistoryDto {
    return new LotEditHistoryDto(model);
  }

  throw404(id: string): Error {
    throw new LotEditHistoryNotFoundError(id);
  }
}
