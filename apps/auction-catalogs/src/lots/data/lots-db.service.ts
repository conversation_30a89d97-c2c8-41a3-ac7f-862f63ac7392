import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { DefaultArgs } from '../../prisma/generated/auction-catalogs-db-client/runtime/library';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { LotDto } from '../dtos/lot.dto';
import { LotNotFoundError } from '../exceptions/lot-not-found.error';
import { LotStatus } from '@viewbid/ts-node-client';
import { ParamType, BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class LotsDbService extends BaseDbService<Prisma.LotsDelegate<DefaultArgs>, LotDto> {
  constructor(private prismaService: PrismaService) {
    super(prismaService.lots);
  }

  createFilter(params: any[]): any {
    const queryParam: ParamType | undefined = params.find((param: ParamType) => param.hasOwnProperty('query'));

    const query: string = queryParam?.query ?? '';
    return {
      AND: [
        {
          description: {
            contains: query,
          },
        },
        {
          deletedOn: null,
        },
      ],
    };
  }

  protected getExcludedFields(): (keyof LotDto)[] {
    return ['images', 'reason', 'documents', 'nextBidMeetsReserve', 'taxPercentage'];
  }

  async addTaxesToLots(lots: any[]): Promise<void> {
    if (!lots || lots.length === 0) {
      return;
    }

    const lotIds: string[] = lots.map((item: any) => item.id);

    const taxTypeInfo: any[] = await this.prismaService.$queryRaw`
      SELECT l_t."lotsId", tt.percent
      FROM public."LotTaxes" l_t
      JOIN public."TaxTypes" tt ON tt.id = l_t."taxTypesId"
      WHERE l_t."lotsId" IN (${Prisma.join(lotIds)});
    `;

    const taxMap: { [key: string]: number } = taxTypeInfo.reduce((prev: any, cur: any) => {
      prev[cur.lotsId] = cur.percent;
      return prev;
    }, {});

    lots.forEach((lot: any) => {
      lot.taxPercentage = taxMap[lot.id];
    });
  }

  async updateStatuses(lotIds: string[], lotStatus: LotStatus): Promise<number> {
    const item: { count: number } = await this.table.updateMany({
      where: {
        id: {
          in: lotIds,
        },
      },
      data: {
        status: lotStatus,
      },
    });

    return item.count;
  }

  mapToDto(model: any): LotDto {
    return new LotDto(model);
  }

  throw404(id: string): Error {
    throw new LotNotFoundError(id);
  }

  async updateThumbnail(uploadId: string): Promise<void> {
    const query: Prisma.Sql = Prisma.sql`
      UPDATE public."Lots"
      SET "thumbnailId" = subquery."id"
        FROM (
               SELECT ui."id"
               FROM public."Upload" u
                      JOIN public."Lots" l ON l.id = u."proxyId" AND u.id = ${uploadId}
                      JOIN public."UploadItems" ui ON ui."uploadsId" = u.id::uuid
                      WHERE ui."deletedOn" IS NULL
                      ORDER BY ui."createdOn" ASC
               LIMIT 1
             ) AS subquery
        WHERE public."Lots".id = (
            SELECT l.id
            FROM public."Upload" u
            JOIN public."Lots" l ON l.id = u."proxyId" AND u.id = ${uploadId}
             LIMIT 1
               );
    `;
    //it's safe to use unsafe because this is a backend service updating
    await this.prismaService.$executeRaw(query);
  }

  async clone(userId: string, auctionId: string, lotId: string): Promise<LotDto> {
    const lot: LotDto[] = await this.prismaService.$queryRaw<LotDto[]>`
      INSERT INTO "Lots" (
        "id", "createdOn", "createdBy",
        "title", "description", "status", "code", "reserveAmount", "startAmount", "tags", "condition",
        "category", "region", "thumbnailId", "pickupLocation", "subtitle", "consignorNumber",
        "excludedRegions", "isRegionSpecific", "inputDevice", "inputMethod", "isAcceptOffers"
      )
        SELECT
          uuid_generate_v4(),
          NOW(),
          ${userId},
          "title", "description", "status", "code", "reserveAmount", "startAmount", "tags", "condition",
          "category", "region", "thumbnailId", "pickupLocation", "subtitle", "consignorNumber",
          "excludedRegions", "isRegionSpecific", "inputDevice", "inputMethod", "isAcceptOffers"
        FROM "Lots"
        WHERE "id" = ${lotId}
          RETURNING *;
    `;

    await this.prismaService.$queryRaw`
      INSERT INTO public."LotTaxes" ("id", "lotsId", "taxTypesId")
        SELECT
          uuid_generate_v4(),
          ${lot[0].id},
          lt."taxTypesId"
        FROM public."LotTaxes" lt
        WHERE lt."lotsId" = ${lotId}
    `;

    await this.addTaxesToLots(lot);

    // Return the first result from the array
    return lot[0];
  }

  async cloneFromTemplate(userId: string, auctionId: string, templateId: string): Promise<LotDto> {
    // Step 1: Fetch the template from the Templates table
    const template: any = await this.prismaService.$queryRaw<any>`
      SELECT "body"
      FROM public."Templates"
      WHERE "id" = ${templateId}::uuid
    `;

    if (!template || template.length === 0) {
      throw new Error('Template not found');
    }

    const templateBody: any = template[0].body; // Assuming 'body' is a JSON field in the template

    // Step 2: Extract values from the template body JSON
    const {
      title,
      description,
      status,
      code,
      reserveAmount,
      tags,
      condition,
      category,
      region,
      thumbnailId,
      pickupLocation,
      subtitle,
      consignorNumber,
      excludedRegions,
      isRegionSpecific,
      inputDevice,
      inputMethod,
    } = templateBody;

    // Step 3: Insert a new row in the Lots table
    const lot: LotDto[] = await this.prismaService.$queryRaw<LotDto[]>`
    INSERT INTO "Lots" (
      "id", "createdOn", "createdBy",
      "title", "description", "status", "code", "reserveAmount", "tags", "condition",
      "category", "region", "thumbnailId", "pickupLocation", "subtitle", "consignorNumber",
      "excludedRegions", "isRegionSpecific", "inputDevice", "inputMethod"
    )
    VALUES (
      uuid_generate_v4(),
      NOW(),
      ${userId},
      ${title}, ${description}, ${status}, ${code},  ${reserveAmount}::DECIMAL(10,2), ${tags}, ${condition},
      ${category}, ${region}, ${thumbnailId}, ${pickupLocation}, ${subtitle}, ${consignorNumber},
      ${excludedRegions}, ${isRegionSpecific}, ${inputDevice}, ${inputMethod}
    )
    RETURNING *;
  `;

    await this.addTaxesToLots(lot);

    // Return the first result from the array (the newly inserted lot)
    return lot[0];
  }
}
