import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { LotTaxesDto } from '../dtos/lot-taxes.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotTaxesDbService extends BaseDbService<Prisma.LotTaxesDelegate, LotTaxesDto> {
  constructor(prisma: PrismaService) {
    super(prisma.lotTaxes, false, false, false);
  }

  mapToDto(model: any): LotTaxesDto {
    return new LotTaxesDto(model);
  }

  throw404(id: string): Error {
    throw new Error();
  }

  async getByLotId(lotsId: string): Promise<LotTaxesDto> {
    const row: any = this.table.findFirst({
      where: {
        lotsId: lotsId,
      },
    });

    if (!row) this.throw404(lotsId);

    return this.mapToDto(row);
  }
}
