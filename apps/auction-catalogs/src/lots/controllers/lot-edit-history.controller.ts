import { BaseController } from '@mvc/controllers/base.controller';
import { LotEditHistoryHandler } from '../handlers/lot-edit-history.handler';
import { LotEditHistoryDto } from '../dtos/lot-edit-history.dto';
import { LotEditHistoryRequest } from '../http/requests/lot-edit-history.request';
import { LotEditHistoryListResponse } from '../http/responses/lot-edit-history-list.response';
import { Controller } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { LotEditHistoryResponse } from '../http/responses/lot-edit-history.response';

@Controller('lot-edit-history')
export class LotEditHistoryController extends BaseController<LotEditHistoryHandler, LotEditHistoryRequest, LotEditHistoryDto, LotEditHistoryResponse, LotEditHistoryListResponse> {
  constructor(handler: LotEditHistoryHand<PERSON>) {
    super(handler);
  }

  createDtoFromRequest(request: LotEditHistoryRequest): LotEditHistoryDto {
    return new LotEditHistoryDto(request);
  }

  createResponseFromDto(dto: LotEditHistoryDto): LotEditHistoryResponse {
    return new LotEditHistoryResponse(dto);
  }

  createResponseList(list: ListResultset<LotEditHistoryDto>): LotEditHistoryListResponse {
    return new LotEditHistoryListResponse(list);
  }
}
