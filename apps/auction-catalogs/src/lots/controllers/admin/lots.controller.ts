import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Delete, Get, HttpCode, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { Transactional } from '@transactional/core';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { AllAccountMappingsDataInner, AuctionLotsStatus, AuctionStatus, LotStatus, ProfilesApi, Response } from '@viewbid/ts-node-client';
import { GroupDto } from 'apps/auction-catalogs/src/groups/dtos/group.dto';
import { GroupsHandler } from 'apps/auction-catalogs/src/groups/handlers/groups.handler';
import * as axios from 'axios';
import { AuctionLotDto } from '../../../auction-lots/dto/auction-lot.dto';
import { InvalidAuctionLotError } from '../../../auction-lots/exceptions/invalid-auction-lot.error';
import { AuctionLotsHandler } from '../../../auction-lots/handlers/auction-lots.handler';
import { AuctionDto } from '../../../auction/dtos/auction.dto';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { LotAttributeViewDto } from '../../../lot-attributes/dtos/lot-attribute-view.dto';
import { LotAttributeDto } from '../../../lot-attributes/dtos/lot-attribute.dto';
import { LotAttributesHandler } from '../../../lot-attributes/handler/lot-attributes.handler';
import { SharedService } from '../../../shared/shared.service';
import { ResourceType } from '../../../uploads/entities/uploads.interfaces';
import { LotDetailedDto } from '../../dtos/lot-detailed.dto';
import { LotEditHistoryDto } from '../../dtos/lot-edit-history.dto';
import { LotDto } from '../../dtos/lot.dto';
import { LotNumberAlreadyExistsError, LotNumberAlreadyExistsWithSuffixError } from '../../exceptions/lot-number-already-exists.error';
import { StartAmountGreaterThanReserveError } from '../../exceptions/start-amount-greater-than-reserve.error';
import { LotEditHistoryHandler } from '../../handlers/lot-edit-history.handler';
import { LotsHandler } from '../../handlers/lots.handler';
import { LotWithAttributesRequest } from '../../http/requests/lot-with-attributes.request';
import { LotsRequest } from '../../http/requests/lots-request';
import { LotDetailedAdminResponse } from '../../http/responses/lot-detailed-admin.response';
import { LotDetailedResponse } from '../../http/responses/lot-detailed-response';
import { LotResponse } from '../../http/responses/lot-response';
import { LotsListGetResponse } from '../../http/responses/lots-list-get-response';
import { LotsListResponse } from '../../http/responses/lots-list-response';
import { AuctionTaxesHandler } from 'apps/auction-catalogs/src/auction/handlers/auction-taxes.handler';
import { LotTaxesHandler } from '../../handlers/lot-taxes.handler';
import { LotTaxesDto } from '../../dtos/lot-taxes.dto';
import { AuctionTaxesDto } from 'apps/auction-catalogs/src/auction/dtos/auction-taxes.dto';
import { AuctionLotsCachingService } from 'apps/auction-catalogs/src/services/auction-lots-caching.service';
import { AuctionsCachingService } from 'apps/auction-catalogs/src/services/auctions-caching.service';
import { LotsCachingService } from 'apps/auction-catalogs/src/services/lots-caching.service';

@Controller('admin/lots')
export class LotsController extends PubSubController<LotsHandler, LotWithAttributesRequest, LotDto, LotDetailedResponse, LotsListResponse> {
  constructor(
    handler: LotsHandler,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly groupsHandler: GroupsHandler,
    private readonly sharedService: SharedService,
    private readonly lotEditHistoryHandler: LotEditHistoryHandler,
    private readonly lotAttributesHandler: LotAttributesHandler,
    redisService: RedisService,
    private readonly profilesApi: ProfilesApi,
    private readonly auctionTaxesHandler: AuctionTaxesHandler,
    private readonly lotTaxesHandler: LotTaxesHandler,
  ) {
    super(handler, redisService, 'admin_lots');
  }

  createDtoFromRequest(request: LotWithAttributesRequest): LotDto {
    return new LotDto(request);
  }

  createResponseFromDto(dto: LotDto): LotResponse {
    return new LotResponse(dto, {});
  }
  createResponseList(list: ListResultset<LotDto>): LotsListResponse {
    return new LotsListResponse(list);
  }

  @Post('/')
  async create(@Body() body: LotWithAttributesRequest, @Req() req: VBInterceptorRequest): Promise<LotDetailedResponse | ErrorResponse> {
    return this.executeAndNotify(
      'create',
      async () => {
        try {
          let auction: AuctionDto | undefined = undefined;

          if (body.auctionId) {
            auction = await this.auctionsHandler.get(body.auctionId);

            await this.validateLotNumber(body); // Validate lot number and suffix for new lot
          }

          if (!auction && !body.taxTypesId) {
            throw new Error('Tax information missing on lot');
          }

          const lot: LotDto = this.createDtoFromRequest(body);
          lot.status = auction?.isViewable && auction?.status == AuctionStatus.Open ? LotStatus.Published : body.status;

          if (auction) {
            lot.startAmount = await this.setStartAmount(auction, lot);
          }

          const lotDetailed: LotDetailedDto = await this.saveLotValues(lot, body, req.accountMappingsId, auction);

          const lotAttributes: ListResultset<LotAttributeDto> = await this.updateLotAttributes(body, lotDetailed, req.accountMappingsId);

          let taxTypesId: number;
          if (body.taxTypesId) {
            taxTypesId = body.taxTypesId;
          } else {
            const auctionTaxes: AuctionTaxesDto = await this.auctionTaxesHandler.getCustom([{ auctionsId: auction!.id }]);
            taxTypesId = auctionTaxes.taxTypesId;
          }
          await this.lotTaxesHandler.create(
            req.accountMappingsId,
            new LotTaxesDto({
              lotsId: lotDetailed.id,
              taxTypesId: taxTypesId,
            }),
          );
          await this.handler.addTaxesToLots([lotDetailed]);

          return new LotDetailedResponse(lotDetailed, lotAttributes);
        } catch (error) {
          this.logger.error("Error caught in POST 'createLot': " + error);
          return new ErrorResponse(error, 'createLot');
        }
      },
      {},
    );
  }

  public async setStartAmount(auction: AuctionDto, lot: LotDto): Promise<number> {
    const startAmount: number = lot.startAmount;
    const reserveAmount: number = lot.reserveAmount;
    const isReserveMinBid: number = auction.isReserveMinBid;

    if (reserveAmount && startAmount > reserveAmount) {
      throw new StartAmountGreaterThanReserveError();
    }

    if (reserveAmount && isReserveMinBid) {
      return reserveAmount;
    }

    return startAmount ? startAmount : 0;
  }

  @Transactional()
  private async saveLotValues(lot: LotDto, @Body() body: LotsRequest, accountMappingsId: string, auction?: AuctionDto): Promise<LotDetailedDto> {
    const savedLot: LotDto = await this.handler.create(accountMappingsId, lot);
    let lotDetailed: LotDetailedDto;
    if (auction) {
      let group: GroupDto | undefined = undefined;
      if (!auction.isLiveAuction) {
        group = await this.groupsHandler.create(accountMappingsId, new GroupDto({ name: body.title, auctionsId: auction.id }));
      }
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.create(accountMappingsId, AuctionLotDto.buildFromLot(body, savedLot, auction, group));
      lotDetailed = new LotDetailedDto(savedLot, auctionLot);
    } else {
      lotDetailed = new LotDetailedDto(savedLot);
    }
    await this.handler.addTaxesToLots([lotDetailed]);
    return lotDetailed;
  }

  async updateLotAttributes(@Body() body: LotWithAttributesRequest, lot: LotDetailedDto, accountMappingsId: string): Promise<ListResultset<LotAttributeDto>> {
    if (body.attributes) {
      const dtos: Array<LotAttributeDto> = this.createDtoArray(body.attributes, LotAttributeDto, { lotId: lot.id, createdBy: accountMappingsId });

      // Set createdBy for each dto
      const updatedDtos: LotAttributeDto[] = dtos.map((dto: LotAttributeDto) => {
        dto.createdBy = accountMappingsId;
        return dto;
      });

      return await this.lotAttributesHandler.createOrReplaceBulk(updatedDtos, { lotId: lot.id });
    }
    const params: Record<string, any>[] = [{ lotId: lot.id, deletedOn: null }];

    return await this.lotAttributesHandler.getAll(1, -1, params);
  }

  @Anonymous()
  @Get('/:id')
  async get(@Param('id') id: string, @Req() _req: VBInterceptorRequest): Promise<LotDetailedAdminResponse | ErrorResponse> {
    try {
      const lot: LotDto = await this.handler.get(id);
      await this.handler.addTaxesToLots([lot]);
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(id);
      //fail-fast check
      await this.auctionsHandler.get(auctionLot.auctionId);

      const images: { [key: string]: any; id?: string }[] = await this.sharedService.getResourceURLById(id, ResourceType.LOT_ITEM_IMAGES);
      const documents: { [key: string]: any; id?: string }[] = await this.sharedService.getResourceURLById(id, ResourceType.LOT_ITEM_DOCUMENTS);

      const editHistory: ListResultset<LotEditHistoryDto> = await this.getLotEditHistory(lot.id);
      const lotAttributes: ListResultset<LotAttributeDto> = await this.lotAttributesHandler.getAllByLotId(lot.id);
      return new LotDetailedAdminResponse(
        lot,
        auctionLot,
        images,
        documents,
        lotAttributes.list.map((item: LotAttributeDto) => new LotAttributeViewDto(item), editHistory.list),
        editHistory.list,
      );
    } catch (error) {
      this.logger.error("Error caught in GET 'getLotById': " + error);

      return new ErrorResponse(error, 'getLotById');
    }
  }

  @Anonymous()
  @Get('getAllLots')
  async getAllLots(@Query('page') page: number, @Query('size') size: number, @Query('query') query: string): Promise<LotsListResponse | ErrorResponse> {
    try {
      const params: any[] = ['query', query];
      const items: ListResultset<LotDto> = await this.handler.getAll(page, size, params);
      await this.handler.addTaxesToLots(items.list);

      return new LotsListGetResponse(items);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAllLots': " + error);

      return new ErrorResponse(error, 'getAllLots');
    }
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: LotWithAttributesRequest, @Req() req: VBInterceptorRequest): Promise<LotDetailedResponse | ErrorResponse> {
    try {
      let lotDetailed: LotDetailedDto;

      if (!body.taxTypesId) {
        throw new Error('Missing tax types ID');
      }

      try {
        const lotTaxes: LotTaxesDto = await this.lotTaxesHandler.getCustom([{ lotsId: id }]);
        lotTaxes.taxTypesId = body.taxTypesId;
        await this.lotTaxesHandler.update(req.accountMappingsId, lotTaxes, lotTaxes.id);
      } catch (error) {
        const newLotTaxes: LotTaxesDto = new LotTaxesDto({
          lotsId: id,
          taxTypesId: body.taxTypesId,
        });
        await this.lotTaxesHandler.create(req.accountMappingsId, newLotTaxes);
      }

      if (body.auctionId) {
        const existingAuctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotIdAndAuctionId(id, body.auctionId);
        await this.validateLotNumber(body, existingAuctionLot);

        lotDetailed = await this.updateLotValues(id, body, existingAuctionLot, req.accountMappingsId);
      } else {
        const updatedLot: LotDto = await this.handler.update(req.accountMappingsId, new LotDto(body), id);
        lotDetailed = new LotDetailedDto(updatedLot);
      }
      await this.handler.addTaxesToLots([lotDetailed]);

      const lotAttributes: ListResultset<LotAttributeDto> = await this.updateLotAttributes(body, lotDetailed, req.accountMappingsId);
      return new LotDetailedResponse(lotDetailed, lotAttributes);
    } catch (error) {
      this.logger.error("Error caught in PUT 'putLotById': " + error);
      return new ErrorResponse(error, 'putLot');
    }
  }

  @Transactional()
  private async updateLotValues(id: string, @Body() body: LotsRequest, existingAuctionLot: AuctionLotDto, accountMappingsId: string): Promise<LotDetailedDto> {
    const updatedLot: LotDto = await this.handler.update(accountMappingsId, new LotDto(body), id);
    const updatedAuctionLot: AuctionLotDto = await this.auctionLotsHandler.update(accountMappingsId, AuctionLotDto.buildFromLot(body, updatedLot), existingAuctionLot.id);

    return new LotDetailedDto(updatedLot, updatedAuctionLot);
  }

  private async validateAuctionLotNumber(body: LotWithAttributesRequest): Promise<void> {
    const params: Record<string, any>[] = [{ auctionId: body.auctionId, lotNumber: body.number, suffix: body.suffix }];
    await this.auctionLotsHandler.getAuctionLotByLotNumber(params);
    throw new LotNumberAlreadyExistsError(body.auctionId, body.number);
  }

  @Delete('/:id')
  @HttpCode(204)
  @Transactional()
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    try {
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(id);
      await Promise.all([this.handler.delete(req.accountMappingsId, id, auctionLot.auctionId), this.tryDeleteAuctionLot(req.accountMappingsId, auctionLot.id)]);
    } catch (error) {
      this.logger.error("Error caught in DELETE 'deleteLotById': " + error);
      return new ErrorResponse(error, 'deleteLotById');
    }
  }

  private async tryDeleteAuctionLot(accountMappingsId: string, auctionLotId: string): Promise<void> {
    try {
      await this.auctionLotsHandler.delete(accountMappingsId, auctionLotId);
    } catch (error) {
      if (!(error instanceof InvalidAuctionLotError)) {
        throw error;
      }
    }
  }

  private async validateLotNumber(body: LotsRequest, existingAuctionLot?: AuctionLotDto): Promise<void> {
    if (existingAuctionLot && this.checkLotNumberMaintained(existingAuctionLot, body)) {
      return;
    }

    let auctionLotByNumber: AuctionLotDto | undefined;
    try {
      const bodyNumberWithSuffix: string = `${body.number}${body.suffix}`;
      const param: Record<string, any>[] = [{ lotNumber: bodyNumberWithSuffix, auctionId: body.auctionId }];
      auctionLotByNumber = await this.auctionLotsHandler.getAuctionLotByLotNumber(param);
    } catch (error) {
      // Catch the 404 - it doesn't exist and is safe to proceed
      this.logger.error('Error caught in validateLotNumber: ' + error);
    }

    if (auctionLotByNumber) {
      throw new LotNumberAlreadyExistsWithSuffixError(body.auctionId, body.number, body.suffix);
    }
  }

  private checkLotNumberMaintained(existingAuctionLot: AuctionLotDto, body: LotsRequest): boolean {
    const existingNumberWithSuffix: string = `${existingAuctionLot.number}${existingAuctionLot.suffix || ''}`;
    const bodyNumberWithSuffix: string = `${body.number}${body.suffix || ''}`;
    return existingNumberWithSuffix === bodyNumberWithSuffix;
  }

  async getLotEditHistory(lotId: string): Promise<ListResultset<LotEditHistoryDto>> {
    const lotEditHistory: ListResultset<LotEditHistoryDto> = await this.lotEditHistoryHandler.getAll(1, -1, [{ lotId }]);
    if (lotEditHistory.list.length > 0) {
      const userIds: string[] = Array.from(new Set(lotEditHistory.list.map((history: LotEditHistoryDto) => history.editedBy)));
      const userResponses: axios.AxiosResponse<Response> = await this.profilesApi.allAccountMappings({ ids: userIds });
      const userMap: Map<string, string> = new Map();

      userResponses.data.data.forEach((response: AllAccountMappingsDataInner) => {
        userMap.set(response.id, `${response.firstname} ${response.lastname}`);
      });

      lotEditHistory.list.forEach((history: LotEditHistoryDto) => {
        history.editedBy = userMap.get(history.editedBy) ?? '';
      });
    }
    return lotEditHistory;
  }

  @Patch('/:id/archive')
  @HttpCode(200)
  @Transactional()
  async archiveLot(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotDto | ErrorResponse> {
    try {
      return await this.updateLotAndAuctionLotStatus(req.accountMappingsId, id, LotStatus.Archived, AuctionLotsStatus.Archived, 'Lot archived by admin');
    } catch (error) {
      this.logger.error(`Error caught in PATCH 'archiveLot' for lot ${id}: ${error.message}`);
      return new ErrorResponse(error, 'archiveLot');
    }
  }

  @Patch('/:id/unarchive')
  @HttpCode(200)
  @Transactional()
  async unarchiveLot(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotDto | ErrorResponse> {
    try {
      return await this.updateLotAndAuctionLotStatus(req.accountMappingsId, id, LotStatus.Draft, AuctionLotsStatus.Draft, 'Lot unarchived by admin');
    } catch (error) {
      this.logger.error(`Error caught in PATCH 'unarchiveLot' for lot ${id}: ${error.message}`);
      return new ErrorResponse(error, 'unarchiveLot');
    }
  }

  async updateLotAndAuctionLotStatus(
    accountMappingsId: string,
    lotId: string,
    lotStatus: LotStatus,
    auctionLotStatus: AuctionLotsStatus,
    reason: string,
  ): Promise<LotDto | ErrorResponse> {
    return await this.executeAndNotify(
      'update',
      async () => {
        const lot: LotDto = await this.handler.updateLotStatus(accountMappingsId, lotId, lotStatus, reason);
        const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(lotId);

        try {
          await this.auctionLotsHandler.updateStatus(auctionLot.id, auctionLotStatus);

          // Add auctionId to the lot object so it's available in the notification payload
          (lot as any).auctionId = auctionLot.auctionId;
          (lot as any).auctionLotId = auctionLot.id;
        } catch (error) {
          if (!(error instanceof InvalidAuctionLotError)) {
            throw error;
          }
        }

        // Directly invalidate caches before returning and notifying
        try {
          await LotsCachingService.deleteCache(this.redisService, lotId);

          if (auctionLot) {
            await AuctionsCachingService.deleteCache(this.redisService, auctionLot.auctionId, true);
            await AuctionLotsCachingService.deleteCache(this.redisService, auctionLot.id, true);
          }
        } catch (cacheError) {
          this.logger.error(`Error during direct cache invalidation for lot ${lotId}: ${cacheError}`);
        }

        return lot;
      },
      {},
    );
  }
}
