import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { LotDto } from '../../dtos/lot.dto';
import { LotsHandler } from '../../handlers/lots.handler';
import { LotResponse } from '../../http/responses/lot-response';
import { LotsListResponse } from '../../http/responses/lots-list-response';

import { BaseController } from '@mvc/controllers/base.controller';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';
import { BiddingApi, LotBiddingData, LotRequest } from '@viewbid/ts-node-client';
import { AuctionDto } from 'apps/auction-catalogs/src/auction/dtos/auction.dto';
import { WatcherDto } from '../../../../../bidding/src/watchers/dtos/watcher.dto';
import { AuctionLotDto } from '../../../auction-lots/dto/auction-lot.dto';
import { AuctionLotsHandler } from '../../../auction-lots/handlers/auction-lots.handler';
import { AuctionsHandler } from '../../../auction/handlers/auctions.handler';
import { BidIncrementDto } from '../../../bid-increments/dtos/bid-increment.dto';
import { BidIncrementFactory } from '../../../bid-increments/factory/bid-increment-factory';
import { SharedService } from '../../../shared/shared.service';
import { ResourceType } from '../../../uploads/entities/uploads.interfaces';
import { LotBiddingDataDto } from '../../dtos/lot-bidding-data.dto';
import { LotDetailedDto } from '../../dtos/lot-detailed.dto';
import { LotEditHistoryDto } from '../../dtos/lot-edit-history.dto';
import { LotBiddingNotFoundError } from '../../exceptions/lot-bidding-not-found.error';
import { LotEditHistoryHandler } from '../../handlers/lot-edit-history.handler';
import { LotDetailedResponse } from '../../http/responses/lot-detailed-response';
import { LotsListGetResponse } from '../../http/responses/lots-list-get-response';

@Controller('lots')
export class LotsController extends BaseController<LotsHandler, LotRequest, LotDto, LotResponse, LotsListResponse> {
  constructor(
    handler: LotsHandler,
    private readonly auctionLotsHandler: AuctionLotsHandler,
    private readonly auctionsHandler: AuctionsHandler,
    private readonly biddingApi: BiddingApi,
    private readonly sharedService: SharedService,
    private readonly lotEditHistoryHandler: LotEditHistoryHandler,
    private readonly bidIncrementFactory: BidIncrementFactory,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: LotRequest): LotDto {
    return new LotDto(request);
  }

  createResponseFromDto(dto: LotDto): LotResponse {
    return new LotResponse(dto, {});
  }
  createResponseList(list: ListResultset<LotDto>): LotsListResponse {
    return new LotsListResponse(list);
  }

  @Post('/')
  async create(@Body() body: LotRequest, @Req() req: VBInterceptorRequest): Promise<LotDetailedResponse | ErrorResponse> {
    req;
    body;
    return this.blockedEndpoint();
  }

  @Anonymous()
  @Get('/:id')
  async get(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<LotResponse | ErrorResponse> {
    try {
      req;
      const lot: LotDto = await this.handler.get(id);
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.getByLotId(id);
      //fail fast
      const auction: AuctionDto = await this.auctionsHandler.get(auctionLot.auctionId);

      const images: { [key: string]: any; id?: string | undefined }[] = await this.sharedService.getResourceURLById(id, ResourceType.LOT_ITEM_IMAGES);
      const documents: { [key: string]: any; id?: string | undefined }[] = await this.sharedService.getResourceURLById(id, ResourceType.LOT_ITEM_DOCUMENTS);

      const biddingData: LotBiddingData[] = (await this.biddingApi.getLotBiddingDataByAuctionLotIds(auctionLot.id)).data.data as LotBiddingData[];
      if (!biddingData) {
        throw new LotBiddingNotFoundError(id);
      }
      const editHistory: ListResultset<LotEditHistoryDto> = await this.lotEditHistoryHandler.getAll(1, -1, [{ lotId: lot.id }]);
      const bidIncrement: BidIncrementDto = await this.bidIncrementFactory.getNextIncrement(auctionLot.auctionId, auctionLot.currentBid);
      auctionLot.minBidAmount = this.sharedService.calculateMinimumBidAmount(auctionLot.currentBid, bidIncrement.increment, lot, auction);
      const lotBiddingDataDto: LotBiddingDataDto = await this.sharedService.createLotsBiddingDataDto(lot, biddingData, auctionLot, auction);
      const watcher: WatcherDto | undefined = await this.mapWatchedItem(auctionLot, req.accountMappingsId);
      const lotDetailed: LotDetailedDto = new LotDetailedDto(lot, auctionLot, images, documents, watcher, editHistory.list, lotBiddingDataDto, auction);
      await this.handler.addTaxesToLots([lotDetailed]);

      return new LotResponse(lotDetailed);
    } catch (error) {
      this.logger.error("Error caught in GET 'getLotById': " + error);

      return new ErrorResponse(error, 'getLotById');
    }
  }

  private async mapWatchedItem(auctionLot: AuctionLotDto, accountMappingsId: string): Promise<WatcherDto | undefined> {
    const watchedItems: Array<WatcherDto> = await this.getWatchedItems(accountMappingsId);

    return watchedItems.find((watcher: WatcherDto) => watcher.auctionLotsid === auctionLot.id);
  }

  private async getWatchedItems(accountMappingsId: string): Promise<Array<WatcherDto> | []> {
    try {
      const watchedItems: any = await this.biddingApi.watchedItemsByUser(accountMappingsId, 1, -1);
      if (!watchedItems.data.data) {
        //nothing to watch
        return [];
      }

      return watchedItems.data.data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`The following error occurred while getting watched items: ${error.message}`);
      }
      return [];
    }
  }

  @Anonymous()
  @Get('getAllLots')
  async getAllLots(@Query('page') page: number, @Query('size') size: number, @Query('query') query: string): Promise<LotsListResponse | ErrorResponse> {
    try {
      const params: any[] = ['query', query];
      const items: ListResultset<LotDto> = await this.handler.getAll(page, size, params);

      await this.handler.addTaxesToLots(items.list);

      return new LotsListGetResponse(items);
    } catch (error) {
      this.logger.error("Error caught in GET 'getAllLots': " + error);

      return new ErrorResponse(error, 'getAllLots');
    }
  }

  @Patch('/:id')
  async update(@Param('id') id: string, @Body() body: LotRequest, @Req() req: VBInterceptorRequest): Promise<LotDetailedResponse | ErrorResponse> {
    id;
    body;
    req;
    return this.blockedEndpoint();
  }

  @Delete('/:id')
  async delete(@Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    req;
    id;
    return this.blockedEndpoint();
  }
}
