import { Lo<PERSON>, Module, forwardRef } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { ApplicationPreferencesModule } from '../application-preferences/app-preferences.module';
import { AuctionLotsModule } from '../auction-lots/auction-lots.module';
import { AuctionModule } from '../auction/auction.module';
import { S3Module } from '../aws/s3/s3.module';
import { BidIncrementsDbService } from '../bid-increments/data/bid-increments-db.service';
import { BidIncrementFactory } from '../bid-increments/factory/bid-increment-factory';
import { BidIncrementsHandler } from '../bid-increments/handler/bid-increments.handler';
import { GroupsModule } from '../groups/groups.module';
import { LotAttributesDbService } from '../lot-attributes/data/lot-attributes-db.service';
import { LotAttributesHandler } from '../lot-attributes/handler/lot-attributes.handler';
import { ResourcesModule } from '../resources/resources.module';
import { SharedService } from '../shared/shared.service';
import { UploadsModule } from '../uploads/uploads.module';
import { LotsController as AdminLotsController } from './controllers/admin/lots.controller';
import { LotEditHistoryController } from './controllers/lot-edit-history.controller';
import { LotsController } from './controllers/public/lots.controller';
import { LotEditHistoryDbService } from './data/lot-edit-history-db.service';
import { LotsDbService } from './data/lots-db.service';
import { LotEditHistoryHandler } from './handlers/lot-edit-history.handler';
import { LotsHandler } from './handlers/lots.handler';
import { NotifyWatcherLotUpdatedSubscriber } from './subscribers/notify-watcher-lot-updated.subscriber';
import { LotTaxesDbService } from './data/lot-taxes-db.service';
import { LotTaxesHandler } from './handlers/lot-taxes.handler';

@Module({
  controllers: [LotsController, LotEditHistoryController, AdminLotsController],
  providers: [
    BidIncrementsDbService,
    BidIncrementFactory,
    BidIncrementsHandler,
    Logger,
    LotAttributesDbService,
    LotAttributesHandler,
    LotEditHistoryDbService,
    LotEditHistoryHandler,
    LotsController,
    LotsDbService,
    LotsHandler,
    RedisService,
    SharedService,
    NotifyWatcherLotUpdatedSubscriber,
    LotTaxesDbService,
    LotTaxesHandler,
  ],
  imports: [
    forwardRef(() => UploadsModule),
    ApplicationPreferencesModule,
    forwardRef(() => AuctionLotsModule),
    forwardRef(() => AuctionModule),
    ResourcesModule,
    S3Module,
    GroupsModule,
  ],
  exports: [LotsController, LotsHandler, LotTaxesHandler],
})
export class LotsModule {}
