import { LotCondition } from '@vb/nest/enums/lotCondition';
import { LotStatus } from '@vb/nest/enums/lotStatus';
import { InputDevice, InputMethod } from '@viewbid/ts-node-client';
import { Asset } from '../../uploads/entities/uploads.interfaces';

export class LotDto {
  id: string;
  createdBy: string;
  title: string;
  description: string;
  status: LotStatus;
  code: string;
  reserveAmount: number;
  tags: string;
  condition: LotCondition;
  category: string;
  region: string;
  thumbnailId?: string;
  pickupLocation: string;
  subtitle: string;
  consignorNumber: string;
  excludedRegions: string[];
  isRegionSpecific: number;
  inputDevice: InputDevice;
  inputMethod: InputMethod;
  reason: string;
  images: Asset[];
  documents: Asset[];
  startAmount: number;
  isAcceptOffers: boolean;
  nextBidMeetsReserve: boolean;
  taxPercentage: number;

  constructor(item: any, images: Asset[] = [], documents: Asset[] = []) {
    this.id = item.id;
    this.createdBy = item.createdBy;
    this.taxPercentage = item.taxPercentage;
    this.title = item.title;
    this.description = item.description;
    this.status = item.status;
    this.code = item.code;
    this.reserveAmount = item.reserveAmount;
    this.tags = item.tags;
    this.condition = item.condition;
    this.category = item.category;
    this.region = item.region;
    this.thumbnailId = item.thumbnailId;
    this.pickupLocation = item.pickupLocation;
    this.subtitle = item.subtitle;
    this.consignorNumber = item.consignorNumber;
    this.excludedRegions = item.excludedRegions;
    this.isRegionSpecific = typeof item.isRegionSpecific === 'boolean' ? Number(item.isRegionSpecific) : item.isRegionSpecific;
    this.inputDevice = item.inputDevice;
    this.inputMethod = item.inputMethod;
    this.images = images;
    this.documents = documents;
    this.reason = item.reason;
    this.startAmount = item.startAmount;
    this.isAcceptOffers = item.isAcceptOffers;
    this.nextBidMeetsReserve = item.nextBidMeetsReserve;
  }
}
