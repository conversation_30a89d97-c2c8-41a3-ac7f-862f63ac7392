export class LotEditHistoryDto {
  readonly id: string;
  readonly lotId: string;
  readonly editedOn: Date;
  editedBy: string;
  readonly changes: Record<string, { oldValue: string; newValue: string }>;
  reason: string;

  constructor(row: any) {
    this.id = row.id;
    this.lotId = row.lotId;
    this.editedOn = row.editedOn;
    this.editedBy = row.editedBy;
    this.changes = row.changes;
    this.reason = row.reason;
  }
}
