export class LotBiddingDataDto {
  minBidAmount: number;
  bidCount: number;
  numberOfWatchers: number;
  currentPrice: number;
  highBidderId: string;
  highBidder: string;
  bidIncrement: number;
  isProxy: number;

  constructor(
    minBidAmount: number,
    bidCount: number,
    numberOfWatchers: number,
    currentPrice: number,
    highBidderId: string,
    highBidder: string,
    bidIncrement: number,
    isProxy: number = 0,
  ) {
    this.minBidAmount = minBidAmount;
    this.bidCount = bidCount;
    this.numberOfWatchers = numberOfWatchers;
    this.currentPrice = currentPrice;
    this.highBidderId = highBidderId;
    this.highBidder = highBidder;
    this.bidIncrement = bidIncrement;
    this.isProxy = isProxy;
  }
}
