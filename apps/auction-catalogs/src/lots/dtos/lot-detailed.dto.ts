import { InputDevice, InputMethod, LotCondition, LotStatus } from '@viewbid/ts-node-client';
import { AuctionLotDto } from '../../auction-lots/dto/auction-lot.dto';
import { LotDto } from './lot.dto';
import { WatcherDto } from '../../../../bidding/src/watchers/dtos/watcher.dto';
import { LotEditHistoryDto } from './lot-edit-history.dto';
import { LotBiddingDataDto } from './lot-bidding-data.dto';
import { AuctionDto } from '../../auction/dtos/auction.dto';

export class LotDetailedDto {
  id: string;
  title: string;
  taxPercentage: number;
  description: string;
  status: LotStatus;
  code: string;
  reserveAmount: number;
  tags: string;
  condition: LotCondition;
  category: string;
  region: string;
  thumbnailId?: string;
  subtitle: string;
  consignorNumber: string;
  excludedRegions: string[];
  isRegionSpecific: boolean;
  inputDevice: InputDevice;
  inputMethod: InputMethod;
  createdOn: string;
  createdBy: string;
  updatedOn?: string;
  updatedBy?: string;
  pickupLocation: string;
  number?: number | null;
  suffix?: string;
  displayOrder?: number | null;
  auctionsId?: string | null;
  images: object[];
  documents: object[];
  toDatetime?: string | null;
  auctionLotsId?: string;
  minBidAmount?: number;
  bidCount?: number;
  numberOfWatchers?: number;
  highBidder?: string;
  currentPrice?: number;
  highBidderId?: string;
  bidIncrement?: number;
  buyNowPrice?: number;
  watcherId?: string;
  editHistory?: LotEditHistoryDto[];
  isHighestBidAProxy?: number;
  startAmount: number;
  isAcceptOffers: boolean;
  nextBidMeetsReserve: boolean;
  bidSnipeWindow: number;

  constructor(
    lot: LotDto,
    auctionLot?: AuctionLotDto,
    images?: { [p: string]: any; id?: string | undefined }[],
    documents?: { [p: string]: any; id?: string | undefined }[],
    watcher?: WatcherDto | undefined,
    reasons?: LotEditHistoryDto[],
    biddingData?: LotBiddingDataDto,
    auction?: AuctionDto,
  ) {
    this.id = lot.id;
    this.title = lot.title;
    this.description = lot.description;
    this.taxPercentage = lot.taxPercentage;
    this.status = lot.status;
    this.code = lot.code;
    this.reserveAmount = lot.reserveAmount;
    this.tags = lot.tags;
    this.condition = lot.condition;
    this.category = lot.category;
    this.region = lot.region;
    this.thumbnailId = lot.thumbnailId;
    this.subtitle = lot.subtitle;
    this.consignorNumber = lot.consignorNumber;
    this.excludedRegions = lot.excludedRegions;
    this.isRegionSpecific = Boolean(lot.isRegionSpecific);
    this.inputDevice = lot.inputDevice;
    this.inputMethod = lot.inputMethod;
    this.pickupLocation = lot.pickupLocation;
    this.minBidAmount = auctionLot?.minBidAmount;
    this.number = auctionLot?.number;
    this.suffix = auctionLot?.suffix;
    this.displayOrder = auctionLot?.displayOrder;
    this.images = images ? images : [];
    this.documents = documents ? documents : [];
    this.toDatetime = auctionLot?.toDatetime ?? '';
    this.auctionsId = auctionLot?.auctionId;
    this.auctionLotsId = auctionLot?.id;
    this.buyNowPrice = auctionLot?.buyNowPrice ?? 0;
    this.watcherId = watcher?.id;
    this.editHistory = reasons;
    this.currentPrice = auctionLot?.currentBid;
    this.bidCount = biddingData?.bidCount;
    this.numberOfWatchers = biddingData?.numberOfWatchers;
    this.highBidder = biddingData?.highBidder;
    this.highBidderId = biddingData?.highBidderId;
    this.isHighestBidAProxy = biddingData?.isProxy;
    this.startAmount = lot.startAmount;
    this.isAcceptOffers = lot.isAcceptOffers;
    this.nextBidMeetsReserve = auctionLot?.nextBidMeetsReserve ?? false;
    this.bidSnipeWindow = auction?.bidSnipeWindow ?? 0;
  }
}
