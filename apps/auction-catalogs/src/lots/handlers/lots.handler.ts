import { Injectable } from '@nestjs/common';
import { LotDto } from '../dtos/lot.dto';
import { LotsDbService } from '../data/lots-db.service';
import { LotStatus } from '@viewbid/ts-node-client';
import { PubSubHandler } from '@vb/redis/handlers/pub-sub.handler';
import { RedisService } from '@vb/redis';

@Injectable()
export class LotsHandler extends PubSubHandler<LotsDbService, LotDto> {
  constructor(dbService: LotsDbService, redisService: RedisService) {
    super(dbService, redisService, 'lots');
  }

  async updateStatuses(lotIds: string[], lotStatus: LotStatus): Promise<number> {
    return await this.dbService.updateStatuses(lotIds, lotStatus);
  }

  async updateThumbnail(uploadId: string): Promise<void> {
    return await this.dbService.updateThumbnail(uploadId);
  }

  async clone(userId: string, auctionId: string, lotId: string): Promise<LotDto> {
    return await this.dbService.clone(userId, auctionId, lotId);
  }

  async cloneFromTemplate(userId: string, auctionId: string, templateId: string): Promise<LotDto> {
    return await this.dbService.cloneFromTemplate(userId, auctionId, templateId);
  }

  async addTaxesToLots(lots: any[]): Promise<void> {
    await this.dbService.addTaxesToLots(lots);
  }

  async updateLotStatus(accountMappingsId: string, lotId: string, status: LotStatus, reason: string): Promise<LotDto> {
    const lot: LotDto = await this.get(lotId);
    lot.status = status;
    lot.reason = reason;

    return await this.update(accountMappingsId, lot, lotId);
  }
}
