import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { LotTaxesDbService } from '../data/lot-taxes-db.service';
import { LotTaxesDto } from '../dtos/lot-taxes.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotTaxesHandler extends BaseHandler<LotTaxesDbService, LotTaxesDto> {
  constructor(dbService: LotTaxesDbService) {
    super(dbService);
  }

  async getByLotId(lotsId: string): Promise<LotTaxesDto> {
    return this.dbService.getByLotId(lotsId);
  }
}
