import { <PERSON>Hand<PERSON> } from '@mvc/handlers/base.handler';
import { LotEditHistoryDbService } from '../data/lot-edit-history-db.service';
import { LotEditHistoryDto } from '../dtos/lot-edit-history.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LotEditHistoryHandler extends BaseHandler<LotEditHistoryDbService, LotEditHistoryDto> {
  constructor(dbService: LotEditHistoryDbService) {
    super(dbService);
  }
}
