import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';

export class LotNumberAlreadyExistsError extends ViewbidError {
  constructor(id: string, number: number) {
    super('Lots number already exists in this auction with number: ' + number, Constants.LOT_NUMBER_EXISTS, id);
  }
}

export class LotNumberAlreadyExistsWithSuffixError extends ViewbidError {
  constructor(id: string, number: number, suffix: string) {
    super('Lots number already exists in this auction with number: ' + number + ' and with suffix: ' + suffix, Constants.LOT_NUMBER_EXISTS, id);
  }
}
