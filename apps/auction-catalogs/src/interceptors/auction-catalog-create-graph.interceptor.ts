import { Inject, Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { transactional } from '@transactional/core';
import { CasbinInitConfig } from '@vb/authorization/config/init-auth';
import { CreateGraphIntercepter } from '@vb/authorization/interceptors/create-graph-interceptor';
import { UsersApi } from '@viewbid/ts-node-client';
import * as rt from 'route-trie';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AuctionCatalogCreateGraphInterceptor extends CreateGraphIntercepter {
  constructor(
    moduleRef: ModuleRef,
    usersApi: UsersApi,
    @Inject('ROUTE_TREE') routeTree: [rt.Trie, CasbinInitConfig],
    private readonly prismaService: PrismaService,
  ) {
    super(moduleRef, usersApi, routeTree);
  }

  public async runPrismaTransaction(callback: any): Promise<any> {
    return await transactional(callback)();
  }
}
