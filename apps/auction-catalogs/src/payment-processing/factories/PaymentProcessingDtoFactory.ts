import { TransactionRequestRequest } from '../http/requests/transaction-request.request';
import { TransactionRequestDto } from '../dtos/transaction-request.dto';
import { TransactionAddressDto } from '../dtos/transaction-address.dto';
import { TransactionShippingCostDto } from '../dtos/transaction-shipping-cost.dto';
import { TransactionCreditCardDto } from '../dtos/transaction-credit-card.dto';
import { TransactionLineItemDto } from '../dtos/transaction-line-item.dto';
import { ProfileTransactionRequestRequest } from '../http/requests/profile-transaction-request.request';
import { BaseTransactionRequest } from '../http/requests/base-transaction-request.request';

export class PaymentProcessingDtoFactory {
  static createTransactionRequestDtoFromRequest(request: TransactionRequestRequest): TransactionRequestDto {
    return new TransactionRequestDto(request.transactionRequest);
  }

  static createTransactionAddressDtosFromRequest(request: TransactionRequestRequest, item: TransactionRequestDto): Array<TransactionAddressDto> {
    return request.transactionAddresses.map((address: any) => {
      return new TransactionAddressDto({ ...address, transactionRequestsId: item.id });
    });
  }

  static createTransactionShippingDtoFromRequest(request: TransactionRequestRequest, item: TransactionRequestDto): TransactionShippingCostDto {
    return new TransactionShippingCostDto({ ...request.transactionShipping, transactionRequestsId: item.id });
  }

  static createTransactionCreditCardDtoFromRequest(request: TransactionRequestRequest, item: TransactionRequestDto): TransactionCreditCardDto {
    return new TransactionCreditCardDto({
      transactionRequestsId: item.id,
      last4: request.transactionCreditCard.number.slice(-4),
      expirationDate: request.transactionCreditCard.expirationDate,
      cardType: request.transactionCreditCard.cardType,
      number: request.transactionCreditCard.number,
    });
  }

  static createTransactionLineItemDtosFromRequest(request: TransactionRequestRequest, item: TransactionRequestDto): Array<TransactionLineItemDto> {
    return request.transactionLineItems.map((lineItem: TransactionLineItemDto) => new TransactionLineItemDto({ ...lineItem, transactionRequestsId: item.id }));
  }

  /// Profile
  static createTransactionRequestDtoFromProfileRequest(request: ProfileTransactionRequestRequest): TransactionRequestDto {
    return new TransactionRequestDto(request.transactionRequest);
  }

  static createTransactionLineItemDtosFromProfileRequest(request: ProfileTransactionRequestRequest, item: TransactionRequestDto): Array<TransactionLineItemDto> {
    return request.transactionLineItems.map((lineItem: TransactionLineItemDto) => new TransactionLineItemDto({ ...lineItem, transactionRequestsId: item.id }));
  }

  static createTransactionShippingDtoFromProfileRequest(request: ProfileTransactionRequestRequest, item: TransactionRequestDto): TransactionShippingCostDto {
    return new TransactionShippingCostDto({ ...request.transactionShipping, transactionRequestsId: item.id });
  }

  static createTransactionAddressDtosFromProfileRequest(request: ProfileTransactionRequestRequest, item: TransactionRequestDto): Array<TransactionAddressDto> {
    return request.transactionAddresses.map((address: any) => {
      return new TransactionAddressDto({ ...address, transactionRequestsId: item.id });
    });
  }

  static createTransactionRequestDtoFromBaseRequest(request: BaseTransactionRequest): TransactionRequestDto {
    return new TransactionRequestDto(request.transactionRequest);
  }

  static createTransactionLineItemDtosFromBaseRequest(request: BaseTransactionRequest, item: TransactionRequestDto): Array<TransactionLineItemDto> {
    return request.transactionLineItems.map((lineItem: TransactionLineItemDto) => new TransactionLineItemDto({ ...lineItem, transactionRequestsId: item.id }));
  }

  static createTransactionShippingDtoFromBaseRequest(request: BaseTransactionRequest, item: TransactionRequestDto): TransactionShippingCostDto {
    return new TransactionShippingCostDto({ ...request.transactionShipping, transactionRequestsId: item.id });
  }

  static createTransactionAddressDtosFromBaseRequest(request: BaseTransactionRequest, item: TransactionRequestDto): Array<TransactionAddressDto> {
    return request.transactionAddresses.map((address: any) => {
      return new TransactionAddressDto({ ...address, transactionRequestsId: item.id });
    });
  }
}
