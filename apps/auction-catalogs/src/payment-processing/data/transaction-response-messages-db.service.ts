import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TransactionResponseMessageDto } from '../dtos/transaction-response-message.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { TransactionResponseMessageNotFoundError } from '../exceptions/transaction-response-message-not-found-error';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionResponseMessagesDbService extends BaseDbService<Prisma.TransactionResponseMessagesDelegate, TransactionResponseMessageDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.transactionResponseMessages, false, false, false);
  }

  createFilter(params: any[]): any {
    return {
      transactionResponsesId: params,
    };
  }

  mapToDto(model: any): TransactionResponseMessageDto {
    return new TransactionResponseMessageDto(model);
  }

  throw404(id: string): Error {
    return new TransactionResponseMessageNotFoundError(id);
  }
}
