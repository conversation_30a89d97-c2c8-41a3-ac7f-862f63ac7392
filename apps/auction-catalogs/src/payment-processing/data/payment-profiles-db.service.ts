import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseDbService } from '@mvc/data/base-db.service';
import { PaymentProfileDto } from '../dtos/payment-profile.dto';
import { PaymentProfileNotFoundError } from '../exceptions/payment-profiles-not-found-error';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

@Injectable()
export class PaymentProfilesDbService extends BaseDbService<Prisma.PaymentProfilesDelegate, PaymentProfileDto> {
  constructor(prisma: PrismaService) {
    super(prisma.paymentProfiles, true, false, true);
  }

  mapToDto(model: any): PaymentProfileDto {
    return new PaymentProfileDto(model);
  }

  throw404(id: string): Error {
    throw new PaymentProfileNotFoundError(id);
  }
}
