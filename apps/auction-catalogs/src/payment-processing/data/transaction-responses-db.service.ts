import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { TransactionResponseDto } from '../dtos/transaction-response.dto';
import { TransactionResponseNotFoundError } from '../exceptions/transaction-response-not-found-error';
import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionResponsesDbService extends BaseDbService<Prisma.TransactionResponsesDelegate, TransactionResponseDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.transactionResponses);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionResponseDto {
    return new TransactionResponseDto(model);
  }

  throw404(id: string): Error {
    return new TransactionResponseNotFoundError(id);
  }
}
