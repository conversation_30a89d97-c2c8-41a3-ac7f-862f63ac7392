import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TransactionAddressDto } from '../dtos/transaction-address.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { TransactionAddressNotFoundError } from '../exceptions/transaction-address-not-found-error';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionAddressesDbService extends BaseDbService<Prisma.TransactionAddressesDelegate, TransactionAddressDto> {
  constructor(prisma: PrismaService) {
    super(prisma.transactionAddresses);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionAddressDto {
    return new TransactionAddressDto(model);
  }

  throw404(id: string): Error {
    return new TransactionAddressNotFoundError(id);
  }
}
