import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TransactionShippingCostDto } from '../dtos/transaction-shipping-cost.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { TransactionShippingNotFoundError } from '../exceptions/transaction-shipping-not-found-error';
import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionShippingCostsDbService extends BaseDbService<Prisma.TransactionShippingDelegate, TransactionShippingCostDto> {
  constructor(prisma: PrismaService) {
    super(prisma.transactionShipping, false, false, false);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionShippingCostDto {
    return new TransactionShippingCostDto(model);
  }

  throw404(id: string): Error {
    return new TransactionShippingNotFoundError(id);
  }
}
