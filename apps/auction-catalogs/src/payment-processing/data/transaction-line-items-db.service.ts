import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TransactionLineItemDto } from '../dtos/transaction-line-item.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { TransactionLineItemNotFoundError } from '../exceptions/transaction-line-item-not-found-error';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionLineItemsDbService extends BaseDbService<Prisma.TransactionLineItemsDelegate, TransactionLineItemDto> {
  constructor(prisma: PrismaService) {
    super(prisma.transactionLineItems, false, false, false);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionLineItemDto {
    return new TransactionLineItemDto(model);
  }

  throw404(id: string): Error {
    return new TransactionLineItemNotFoundError(id);
  }
}
