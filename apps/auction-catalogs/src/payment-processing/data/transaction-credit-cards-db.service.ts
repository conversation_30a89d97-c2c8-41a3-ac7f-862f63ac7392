import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { TransactionCreditCardDto } from '../dtos/transaction-credit-card.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { CreditCardNotFoundError } from '../exceptions/credit-card-not-found-error';
import { Injectable } from '@nestjs/common';
import { BaseDbService } from '@mvc/data/base-db.service';

@Injectable()
export class TransactionCreditCardsDbService extends BaseDbService<Prisma.TransactionCreditCardsDelegate, TransactionCreditCardDto> {
  constructor(prisma: PrismaService) {
    super(prisma.transactionCreditCards);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionCreditCardDto {
    return new TransactionCreditCardDto(model);
  }

  throw404(id: string): Error {
    return new CreditCardNotFoundError(id);
  }

  /**
   * since we aren't serializing all the DTO fields we need to create a custom override method
   *
   * @param userId
   * @param request
   */
  async create(userId: string, request: TransactionCreditCardDto): Promise<TransactionCreditCardDto> {
    const data: any = {
      transactionRequestsId: request.transactionRequestsId,
      last4: request.last4,
      expirationDate: request.expirationDate,
      cardType: request.cardType,
    };

    const createdItem: any = await this.table.create({ data });
    return this.mapToDto(createdItem);
  }
}
