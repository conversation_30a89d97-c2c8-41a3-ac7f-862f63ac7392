import { BaseDbService } from '@mvc/data/base-db.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { TransactionDetailDto } from '../dtos/transaction-details.dto';
import { TransactionRequestDto } from '../dtos/transaction-request.dto';
import { TransactionRequestNotFoundError } from '../exceptions/transaction-request-not-found-error';

@Injectable()
export class TransactionRequestsDbService extends BaseDbService<Prisma.TransactionRequestsDelegate, TransactionRequestDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.transactionRequests);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): TransactionRequestDto {
    return new TransactionRequestDto(model);
  }

  throw404(id: string): Error {
    return new TransactionRequestNotFoundError(id);
  }

  async report(page: number, size: number, params: Record<string, any>[]): Promise<ListResultset<TransactionDetailDto>> {
    const offset: number = size > 0 ? (page - 1) * size : 0;
    const limit: string = size > 0 ? `LIMIT ${size} OFFSET ${offset}` : '';

    // Build dynamic filters (whereClause and whereValues)
    const { whereClause, whereValues } = this.buildWhereClause(params);

    // Construct main query
    const query: string = `
      SELECT 
        ip.id, 
        tr."invoicesId", 
        ip."currentOwing", 
        tr."transactionType", 
        tr."amount", 
        tr."invoiceNumber",
        ts."name",
        ta."firstname", 
        ta."lastname", 
        ta."address1", 
        ta."city", 
        ta."state", 
        ta."zip", 
        ta."country", 
        CASE
          WHEN tresp."responseCode" = '1' THEN 'Approved'
          WHEN tresp."responseCode" = '2' THEN 'Declined'
          WHEN tresp."responseCode" = '3' THEN 'Error'
          WHEN tresp."responseCode" = '4' THEN 'Held for Review'
          ELSE 'Unknown'
        END AS "responseStatus",
        tresp."authCode", 
        tresp."transId", 
        tresp."accountNumber", 
        tresp."accountType", 
        tresp."networkTransId", 
        tresp."createdOn" AS "paymentDate", 
        trm."code", 
        trm."description",
        ts."amount" AS "shippingAmount",
        a."name" AS "auctionName"
      FROM "InvoicePayments" ip
        LEFT JOIN "TransactionRequests" tr ON tr."invoicePaymentsId" = ip."id"
        LEFT JOIN "TransactionAddresses" ta ON ta."transactionRequestsId" = tr.id 
        LEFT JOIN "TransactionResponses" tresp ON tresp."transactionRequestsId"::uuid = tr.id 
        LEFT JOIN "TransactionResponseMessages" trm ON trm."transactionResponsesId"::uuid = tresp.id 
        LEFT JOIN "TransactionShipping" ts ON ts."transactionRequestsId"::uuid = tr.id
        LEFT JOIN "Invoices" i ON ip."invoicesId" = i.id 
        LEFT JOIN "Auctions" a ON i."auctionsId" = a.id
        ${whereClause ? `WHERE ${whereClause}` : ''}
        ORDER BY tr."createdOn" DESC
        ${limit};
    `;

    // Construct count query
    const countQuery: string = `
      SELECT COUNT(*)
      FROM "InvoicePayments" ip
        LEFT JOIN "Invoices" i ON ip."invoicesId" = i.id
        LEFT JOIN "Auctions" a ON i."auctionsId" = a.id
        LEFT JOIN "TransactionRequests" tr ON tr."invoicePaymentsId" = ip."id"
        LEFT JOIN "TransactionAddresses" ta ON ta."transactionRequestsId" = tr.id
        LEFT JOIN "TransactionResponses" tresp ON tresp."transactionRequestsId"::uuid = tr.id 
      ${whereClause ? `WHERE ${whereClause}` : ''}
    `;

    // Execute queries
    const [results, countResult] = await Promise.all<any[]>([this.prisma.$queryRawUnsafe(query, ...whereValues), this.prisma.$queryRawUnsafe(countQuery, ...whereValues)]);

    const count: number = Number(countResult[0]?.count || 0);

    return new ListResultset(
      results.map((row: any) => new TransactionDetailDto(row)),
      page,
      size,
      count,
      Math.ceil(count / size),
    );
  }

  buildWhereClause(params: Record<string, any>[]): { whereClause: string; whereValues: any[] } {
    let whereClause: string = '';
    const whereValues: any[] = [];
    let paramIndex: number = 1;

    params.forEach((condition: Record<string, any>) => {
      // Check if condition has an OR block and it's an array
      if (condition.OR && Array.isArray(condition.OR)) {
        let orConditions: string = '';
        condition.OR.forEach((orCondition: Record<string, any>) => {
          const [key, value] = Object.entries(orCondition)[0];

          if (isNaN(Number(value.contains))) {
            // String comparison, use ILIKE with wildcards
            orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE $${paramIndex}`;
            whereValues.push(`%${value.contains}%`);
          } else {
            // Numeric comparison, use '=' with type casting
            if (key === 'tresp."transId"') {
              orConditions += `${orConditions ? ' OR ' : ''}${key} ILIKE  $${paramIndex}`;
              whereValues.push(`%${value.contains}%`);
            } else {
              orConditions += `${orConditions ? ' OR ' : ''}${key} = $${paramIndex}::DECIMAL(18,2)`;
              whereValues.push(value.contains);
            }
          }

          paramIndex++;
        });

        if (orConditions) {
          whereClause += `${whereClause ? ' AND ' : ''}(${orConditions})`;
        }
      } else {
        // Handle other conditions outside of OR blocks
        const [key, value] = Object.entries(condition)[0];
        if (key === 'tr."transactionType"') {
          whereClause += `${whereClause ? ' AND ' : ''} ${key} = $${paramIndex}::"TransactionType"`;
          whereValues.push(value); // No wildcard needed for ENUMs
        } else if (key === 'a.id') {
          whereClause += `${whereClause ? ' AND ' : ''} ${key}::UUID = $${paramIndex}::UUID`;
          whereValues.push(value);
        } else if (typeof value === 'string') {
          // String comparison, use ILIKE with wildcards
          whereClause += `${whereClause ? ' AND ' : ''} ${key} ILIKE $${paramIndex}`;
          whereValues.push(`%${value}%`);
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          // Numeric comparison, use '=' without wildcards
          whereClause += `${whereClause ? ' AND ' : ''} ${key} = $${paramIndex}`;
          whereValues.push(value);
        }

        paramIndex++;
      }
    });
    return { whereClause, whereValues };
  }
}
