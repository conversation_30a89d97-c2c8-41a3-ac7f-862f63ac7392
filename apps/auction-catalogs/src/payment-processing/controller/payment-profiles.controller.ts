import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Delete, Get, OnModuleInit, Param, Post, Req } from '@nestjs/common';
import { ConfigService } from '@vb/config/src/config.service';
import { AccountAddressDto } from '@vb/nest/common-dtos/users/account-address.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { uuidToBase62 } from '@vb/nest/test-utils/utils/utils';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { UsersApi } from '@viewbid/ts-node-client';
import { APIContracts } from 'authorizenet';
import { PaymentProfileCardDto } from '../dtos/payment-profile-card.dto';
import { PaymentProfileDto } from '../dtos/payment-profile.dto';
import { CouldNotDeletePaymentProfileError } from '../exceptions/could-not-delete-payment-profile-error';
import { PaymentProfileNotFoundError } from '../exceptions/payment-profiles-not-found-error';
import { PaymentProfilesHandler } from '../handler/payment-profiles.handler';
import { AddCardRequest } from '../http/requests/add-card.request';
import { PaymentProfileRequest } from '../http/requests/payment-profile.request';
import { PaymentProfileCardsResponse } from '../http/responses/payment-profile-cards.response';
import { PaymentProfileResponse } from '../http/responses/payment-profile.response';
import { PaymentProfilesListResponse } from '../http/responses/payment-profiles-list.response';
import { AddCardCommand } from '../service/authorize-net-commands/add-card-command';
import { AuthorizeNetDto, isSuccessful, SANDBOX_API_TRANSACTION_KEY, SANDBOX_LOGIN_KEY } from '../service/authorize-net-commands/authorize-net-common';
import { DeleteCardCommand } from '../service/authorize-net-commands/delete-card-command';
import { ListCardsCommand } from '../service/authorize-net-commands/list-cards-command';
import { CardCommand, CardManagementService } from '../service/card-management.service';

@Controller('payment/profiles')
export class PaymentProfilesController
  extends PubSubController<PaymentProfilesHandler, PaymentProfileRequest, PaymentProfileDto, PaymentProfileResponse, PaymentProfilesListResponse>
  implements OnModuleInit
{
  private loginKey: string;
  private transactionKey: string;

  constructor(
    handler: PaymentProfilesHandler,
    private configService: ConfigService,
    private cardManagementService: CardManagementService,
    private usersApi: UsersApi,
    redisService: RedisService,
  ) {
    super(handler, redisService, 'payment_profiles');
  }

  createDtoFromRequest(request: PaymentProfileRequest): PaymentProfileDto {
    return new PaymentProfileDto(request);
  }

  createResponseFromDto(dto: PaymentProfileDto): PaymentProfileResponse {
    return new PaymentProfileResponse(dto, {});
  }

  createResponseList(list: ListResultset<PaymentProfileDto>): PaymentProfilesListResponse {
    return new PaymentProfilesListResponse(list);
  }

  async onModuleInit(): Promise<void> {
    //Constructors can't do async - so doing it after module is initialized.
    this.loginKey = await this.configService.getSetting('AUTHORIZE_NET_API_LOGIN_KEY', SANDBOX_LOGIN_KEY);
    this.transactionKey = await this.configService.getSetting('AUTHORIZE_NET_API_TRANSACTION_KEY', SANDBOX_API_TRANSACTION_KEY);
  }

  @Post('/addCard')
  async addCard(@Body() body: AddCardRequest, @Req() req: VBInterceptorRequest): Promise<PaymentProfileResponse | ErrorResponse> {
    return this.executeAndNotify(
      'add_card',
      async () => {
        try {
          const profile: PaymentProfileDto = await this.findFirstPaymentProfile(req.accountMappingsId, true);

          //Create a new merchantId - since this is the first time, and customer doesn't exist.
          if (profile.merchantCustomerId == undefined || profile.merchantCustomerId?.length <= 0) {
            profile.merchantCustomerId = uuidToBase62(req.accountMappingsId);
          }

          const address: AccountAddressDto = (await this.usersApi.getUserAccountAddressById(body.addressId)).data.data as AccountAddressDto;
          const authorizeNetDto: AuthorizeNetDto = {
            customerInfo: body.customerInformation,
            opaqueData: body.opaqueData,
            profile: profile,
            address: address,
            userId: req.accountMappingsId,
          };

          const addCardCommand: AddCardCommand = this.cardManagementService.getCommand(CardCommand.ADD_CARD) as AddCardCommand;
          const response: APIContracts.CreateCustomerProfileResponse | APIContracts.CreateCustomerPaymentProfileResponse | ErrorResponse =
            await addCardCommand.addCard(authorizeNetDto);
          const newPaymentProfile: PaymentProfileDto = await addCardCommand.mapTo(response, authorizeNetDto, address.id);
          return new PaymentProfileResponse(newPaymentProfile);
        } catch (error) {
          if (error instanceof ErrorResponse) {
            return error;
          }
          return new ErrorResponse(error, 'paymentProfiles');
        }
      },
      {},
    );
  }

  @Get('/byCustomerPaymentProfileId/:customerPaymentProfileId')
  async getProfile(@Param('customerPaymentProfileId') customerPaymentProfileId: string, @Req() req: VBInterceptorRequest): Promise<PaymentProfileCardsResponse | ErrorResponse> {
    try {
      const profile: PaymentProfileDto = await this.findFirstPaymentProfile(req.accountMappingsId, false, [
        { accountMappingsId: req.accountMappingsId, deletedOn: null, customerPaymentProfileId: customerPaymentProfileId },
      ]);
      if (!profile.id) {
        throw new PaymentProfileNotFoundError(customerPaymentProfileId);
      }
      return new PaymentProfileResponse(profile);
    } catch (error) {
      this.logger.error("Error caught in GET 'byCustomerPaymentProfileId': " + JSON.stringify(error));
      return new ErrorResponse(error, 'byCustomerPaymentProfileId');
    }
  }

  @Get('/listCards')
  async listCards(@Req() req: VBInterceptorRequest): Promise<PaymentProfileCardsResponse | ErrorResponse> {
    try {
      const paymentCards: PaymentProfileCardDto[] = await this.getCardList(req.accountMappingsId);
      return new PaymentProfileCardsResponse(paymentCards);
    } catch (error) {
      if (error instanceof ErrorResponse) {
        return error;
      }
      this.logger.error("Error caught in POST 'listCards': " + JSON.stringify(error));
      return new ErrorResponse(error, 'listCards');
    }
  }

  private async getCardList(accountMappingsId: string): Promise<PaymentProfileCardDto[]> {
    const profile: PaymentProfileDto = await this.findFirstPaymentProfile(accountMappingsId);

    if (!profile.customerProfileId) {
      return [];
    }

    const authorizeNetDto: AuthorizeNetDto = {
      profile: profile,
      userId: accountMappingsId,
    };

    const listCardCommand: ListCardsCommand = this.cardManagementService.getCommand(CardCommand.LIST_CARDS) as ListCardsCommand;
    const response: APIContracts.GetCustomerProfileResponse | ErrorResponse = await listCardCommand.listCards(authorizeNetDto);

    return await listCardCommand.mapTo(response, authorizeNetDto, profile.billingAddressId);
  }

  @Delete('/deleteCard/:customerPaymentProfileId')
  async deleteCard(@Param('customerPaymentProfileId') customerPaymentProfileId: string, @Req() req: VBInterceptorRequest): Promise<void | ErrorResponse> {
    const profile: PaymentProfileDto = await this.findFirstPaymentProfile(req.accountMappingsId, false, [
      { accountMappingsId: req.accountMappingsId, deletedOn: null, customerPaymentProfileId: customerPaymentProfileId },
    ]);
    const authorizeNetDto: AuthorizeNetDto = {
      profile: profile,
      userId: req.accountMappingsId,
    };

    try {
      const deleteCardCommand: DeleteCardCommand = this.cardManagementService.getCommand(CardCommand.DELETE_CARD) as DeleteCardCommand;
      const response: APIContracts.DeleteCustomerPaymentProfileResponse | ErrorResponse = await deleteCardCommand.deleteCard(authorizeNetDto);
      if (isSuccessful(response)) {
        await this.handler.deleteBulk(req.accountMappingsId, {
          accountMappingsId: authorizeNetDto.userId,
          customerPaymentProfileId: authorizeNetDto.profile?.customerPaymentProfileId,
          customerProfileId: authorizeNetDto.profile?.customerProfileId,
        });
        const cardList: PaymentProfileCardDto[] = await this.getCardList(req.accountMappingsId);

        if (cardList.length === 0) {
          this.notify('success', 'remove_all_cards', { accountMappingsId: req.accountMappingsId });
        }
      } else {
        throw new CouldNotDeletePaymentProfileError(profile.customerPaymentProfileId);
      }
    } catch (error) {
      if (error instanceof ErrorResponse) {
        return error;
      }
      this.logger.error("Error caught in POST 'deleteCard': " + JSON.stringify(error));
      return new ErrorResponse(error, 'deleteCard');
    }

    //Don't want to throw an error really, because don't want the user knowing if it's a valid customerProfileId or not. Return an empty list
    return;
  }

  async findFirstPaymentProfile(userId: string, showDeleted = false, newParams: [any] | undefined = undefined): Promise<PaymentProfileDto> {
    let params: any = showDeleted ? [{ accountMappingsId: userId, deletedOn: undefined }] : [{ accountMappingsId: userId }];
    params = newParams == undefined ? params : newParams;
    const paymentProfileDtos: ListResultset<PaymentProfileDto> = (await this.handler.getAllCustom(
      -1,
      -1,
      params,
      PaymentProfileDto,
    )) as unknown as ListResultset<PaymentProfileDto>;

    return paymentProfileDtos.totalCount > 0 && paymentProfileDtos.list.length > 0 ? paymentProfileDtos.list[0] : new PaymentProfileDto({});
  }
}
