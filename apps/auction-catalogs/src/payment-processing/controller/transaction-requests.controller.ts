import { <PERSON><PERSON><PERSON>roller } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Post, Req } from '@nestjs/common';
import { TransactionRequestDto } from '../dtos/transaction-request.dto';
import { TransactionAddressesHandler } from '../handler/transaction-addresses.handler';
import { TransactionRequestsHandler } from '../handler/transaction-requests.handler';
import { TransactionRequestRequest } from '../http/requests/transaction-request.request';
import { TransactionRequestListResponse } from '../http/responses/transaction-request-list.response';
import { TransactionRequestResponse } from '../http/responses/transaction-request.response';

import { ErrorResponse } from '@mvc/http/responses/error-response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { APIContracts } from 'authorizenet';
import { ItemMappingDto } from '../../item-mappings/dtos/item-mapping.dto';
import { ItemMappingsHandler } from '../../item-mappings/handler/item-mappings.handler';
import { TransactionAddressDto } from '../dtos/transaction-address.dto';
import { TransactionCreditCardDto } from '../dtos/transaction-credit-card.dto';
import { TransactionLineItemDto } from '../dtos/transaction-line-item.dto';
import { TransactionRequestDetailResponseDto } from '../dtos/transaction-request-detail-response.dto';
import { TransactionResponseDetailDto } from '../dtos/transaction-response-detail.dto';
import { TransactionResponseMessageDto } from '../dtos/transaction-response-message.dto';
import { TransactionResponseDto } from '../dtos/transaction-response.dto';
import { TransactionShippingCostDto } from '../dtos/transaction-shipping-cost.dto';
import { AuthorizeNetError } from '../exceptions/authorize-net-error';
import { DebitCreditCardError } from '../exceptions/debit-credit-card-error';
import { ItemsNotFoundError } from '../exceptions/items-not-found-error';
import { PaymentProfileNotFoundError } from '../exceptions/payment-profiles-not-found-error';
import { PaymentProcessingDtoFactory } from '../factories/PaymentProcessingDtoFactory';
import { TransactionCreditCardsHandler } from '../handler/transaction-credit-cards.handler';
import { TransactionLineItemsHandler } from '../handler/transaction-line-items.handler';
import { TransactionResponseMessagesHandler } from '../handler/transaction-response-messages.handler';
import { TransactionResponsesHandler } from '../handler/transaction-responses.handler';
import { TransactionShippingCostsHandler } from '../handler/transaction-shipping-costs.handler';
import { ProfileTransactionRequestRequest } from '../http/requests/profile-transaction-request.request';
import { CreateTransactionRequestResponse } from "../http/responses/create-transaction-request.response'";
import { PaymentProfileCardsResponse } from '../http/responses/payment-profile-cards.response';
import { mapTransactionToAuthNetError } from '../service/authorize-net-commands/authorize-net-common';
import { ChargeCreditCardService, CreateTransactionResponse } from '../service/charge-credit-card.service';
import { PaymentProfilesController } from './payment-profiles.controller';
import { BaseTransactionRequest } from '../http/requests/base-transaction-request.request';

@Controller('paymentProcessing/transactionRequests')
export class TransactionRequestsController extends BaseController<
  TransactionRequestsHandler,
  TransactionRequestRequest,
  TransactionRequestDto,
  TransactionRequestResponse,
  TransactionRequestListResponse
> {
  constructor(
    handler: TransactionRequestsHandler,
    private addressesHandler: TransactionAddressesHandler,
    private shippingCostsHandler: TransactionShippingCostsHandler,
    private creditCardHandler: TransactionCreditCardsHandler,
    private lineItemsHandler: TransactionLineItemsHandler,
    private itemMappingsHandler: ItemMappingsHandler,
    private creditCardService: ChargeCreditCardService,
    private transactionResponsesHandler: TransactionResponsesHandler,
    private transactionResponseMessagesHandler: TransactionResponseMessagesHandler,
    private paymentProfileController: PaymentProfilesController,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: TransactionRequestRequest): TransactionRequestDto {
    return PaymentProcessingDtoFactory.createTransactionRequestDtoFromRequest(request);
  }

  createResponseFromDto(dto: TransactionRequestDto): TransactionRequestResponse {
    return new TransactionRequestResponse(dto);
  }

  createResponseList(list: ListResultset<TransactionRequestDto>): TransactionRequestListResponse {
    return new TransactionRequestListResponse(list);
  }

  /**
   * do not make this transactional since we want to maintain a complete record
   * of all attempts so that we can see a proper forensic if things go wrong.
   *
   * @param body
   * @param req
   * @param res
   */
  @Post('/')
  async create(@Body() body: TransactionRequestRequest, @Req() req: VBInterceptorRequest): Promise<CreateTransactionRequestResponse | ErrorResponse> {
    try {
      const chargeDto: ChargeDto = await this.buildChargeDto(req, body);
      const creditCardRawDto: TransactionCreditCardDto = PaymentProcessingDtoFactory.createTransactionCreditCardDtoFromRequest(body, chargeDto.transactionRequest);
      await this.creditCardHandler.create(chargeDto.userId, creditCardRawDto);

      let result: any;
      try {
        result = await this.creditCardService.charge(chargeDto, creditCardRawDto);
        await this.serializeResponse(chargeDto.userId, result, chargeDto.transactionRequest, TransactionResponseDto.createNewFromResponse(result, chargeDto.transactionRequest));
      } catch (error) {
        await this.serializeResponse(chargeDto.userId, error, chargeDto.transactionRequest, TransactionResponseDto.createNewFromResponse(error, chargeDto.transactionRequest));
        throw new DebitCreditCardError(chargeDto.transactionRequest);
      }

      return new CreateTransactionRequestResponse(
        new TransactionRequestDetailResponseDto(chargeDto.transactionRequest, new TransactionResponseDetailDto(result.getTransactionResponse())),
      );
    } catch (error) {
      return new ErrorResponse(error, 'createTransactionRequest', error.id);
    }
  }

  @Post('/withProfile')
  async createWithProfile(@Body() body: ProfileTransactionRequestRequest, @Req() req: VBInterceptorRequest): Promise<CreateTransactionRequestResponse | ErrorResponse> {
    try {
      const chargeDto: ChargeDto = await this.buildChargeDto(req, body);

      const paymentProfile: PaymentProfileCardsResponse | ErrorResponse = await this.paymentProfileController.getProfile(body.customerPaymentProfileId, req);
      if (paymentProfile instanceof ErrorResponse) {
        return paymentProfile;
      }

      let result: any;
      try {
        result = await this.creditCardService.chargeProfile(chargeDto, paymentProfile.data);
        const transactionResponse: TransactionResponseDto = TransactionResponseDto.createNewFromResponse(result, chargeDto.transactionRequest);
        await this.serializeResponse(chargeDto.userId, result, chargeDto.transactionRequest, transactionResponse);
      } catch (error) {
        if (error instanceof PaymentProfileNotFoundError) {
          return new ErrorResponse(error, 'paymentProfileNotFound');
        }

        await this.serializeResponse(chargeDto.userId, error, chargeDto.transactionRequest, TransactionResponseDto.createNewFromResponse(error, chargeDto.transactionRequest));
        if (error instanceof APIContracts.CreateTransactionResponse) {
          return new ErrorResponse(
            new AuthorizeNetError(chargeDto.transactionRequest.id),
            chargeDto.transactionRequest.id,
            chargeDto.transactionRequest.id,
            mapTransactionToAuthNetError(error),
          );
        }

        throw new DebitCreditCardError(chargeDto.transactionRequest);
      }

      return new CreateTransactionRequestResponse(
        new TransactionRequestDetailResponseDto(chargeDto.transactionRequest, new TransactionResponseDetailDto(result.getTransactionResponse())),
      );
    } catch (error) {
      return new ErrorResponse(error, 'createTransactionRequest', error.id);
    }
  }

  private async buildChargeDto(req: VBInterceptorRequest, body: BaseTransactionRequest): Promise<ChargeDto> {
    const userId: string = req.accountMappingsId;
    const dto: TransactionRequestDto = PaymentProcessingDtoFactory.createTransactionRequestDtoFromBaseRequest(body);
    const transactionRequest: TransactionRequestDto = await this.handler.create(userId, dto);

    //get item dtos from the request
    const lineItemRawDtos: Array<TransactionLineItemDto> = PaymentProcessingDtoFactory.createTransactionLineItemDtosFromBaseRequest(body, transactionRequest);

    //throws exception if invalid
    await this.validateItemMappings(lineItemRawDtos);
    const addressRawDtos: Array<TransactionAddressDto> = PaymentProcessingDtoFactory.createTransactionAddressDtosFromBaseRequest(body, transactionRequest);
    const shippingCostRawDto: TransactionShippingCostDto = PaymentProcessingDtoFactory.createTransactionShippingDtoFromBaseRequest(body, transactionRequest);

    //save to the db
    const [addresses, shippingCost, lineItems] = await Promise.all([
      this.addressesHandler.createOrReplaceBulk(addressRawDtos),
      this.shippingCostsHandler.create(userId, shippingCostRawDto),
      this.lineItemsHandler.createOrReplaceBulk(lineItemRawDtos),
    ]);

    return { transactionRequest, userId, shippingCost, addresses, lineItems };
  }

  async serializeResponse(
    userId: string,
    response: CreateTransactionResponse,
    transactionRequestDto: TransactionRequestDto,
    transactionResponseRaw: TransactionResponseDto,
  ): Promise<void> {
    const transactionResponse: TransactionResponseDto = await this.transactionResponsesHandler.create(userId, transactionResponseRaw);
    await this.transactionResponseMessagesHandler.create(userId, TransactionResponseMessageDto.createNewFromResponse(response, transactionResponse));
  }

  private async validateItemMappings(lineItemRawDtos: Array<TransactionLineItemDto>): Promise<Array<ItemMappingDto>> {
    const items: ListResultset<ItemMappingDto> = await this.itemMappingsHandler.getAll(
      1,
      -1,
      lineItemRawDtos.map((item: TransactionLineItemDto) => item.itemMappingsId),
    );
    const itemsNotInArray: Array<TransactionLineItemDto> = lineItemRawDtos.filter(
      (item1: TransactionLineItemDto) => !items.list.some((item2: ItemMappingDto): boolean => item2.id === item1.itemMappingsId),
    );

    if (itemsNotInArray.length > 0) {
      const missingItemIds: Array<string> = itemsNotInArray.map((item: TransactionLineItemDto) => {
        return item.itemMappingsId;
      });
      throw new ItemsNotFoundError(missingItemIds);
    }

    return items.list;
  }
}

export interface ChargeDto {
  transactionRequest: TransactionRequestDto;
  userId: string;
  shippingCost: TransactionShippingCostDto;
  addresses: ListResultset<TransactionAddressDto>;
  lineItems: ListResultset<TransactionLineItemDto>;
}
