import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { TransactionRequestsHandler } from '../handler/transaction-requests.handler';

import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { TransactionDetailDto } from '../dtos/transaction-details.dto';
import { TransactionReportResponse } from '../http/responses/transaction-report.response';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { IsAdmin } from '@vb/authorization/guards/isAdmin.guard';

@Controller('admin/paymentProcessing/transactionRequests')
export class AdminTransactionRequestsController {
  constructor(public handler: TransactionRequestsHandler) {}

  private readonly responseCodeMap: Record<string, ResponseCodeEnum> = {
    APPROVED: ResponseCodeEnum.Approved,
    DECLINED: ResponseCodeEnum.Declined,
    ERROR: ResponseCodeEnum.Error,
    HELD_FOR_REVIEW: ResponseCodeEnum.HeldForReview,
  };

  @Get('/report')
  @UseGuards(IsAdmin)
  async getReport(@Req() req: VBInterceptorRequest, @Query('page') page: number, @Query('size') size: number): Promise<TransactionReportResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url, { 'tr."transactionType"': 'CreditCard' });
      const transactionDetails: ListResultset<TransactionDetailDto> = await this.handler.getReport(page, size, params);
      return new TransactionReportResponse(transactionDetails);
    } catch (error) {
      return new ErrorResponse(error, 'Transactions Report');
    }
  }

  protected convertQueryToArray(uri: string, additionalParams?: { [key: string]: any }): Array<Record<string, any>> {
    const searchParams: URLSearchParams = new URLSearchParams(uri.split('?')[1]);

    // Extract 'page' and 'size' parameters as they're not used for searching
    searchParams.delete('page');
    searchParams.delete('size');

    const statusType: string | null = searchParams.get('statusType');
    if (statusType) {
      searchParams.delete('statusType');
    }

    const auctionsId: string | null = searchParams.get('auctionsId');
    if (auctionsId || auctionsId === '') {
      searchParams.delete('auctionsId');
    }

    // If additionalParams is provided, add or override the parameters in the searchParams
    if (additionalParams) {
      for (const [key, value] of Object.entries(additionalParams)) {
        searchParams.set(key, value.toString()); // Use .set to add or override a parameter
      }
    }

    const array: any[] = [];
    // Extract the query parameter for searching
    const query: string | null = searchParams.get('query');
    searchParams.delete('query');
    if (query) {
      if (isNaN(Number(query))) {
        const orConditions: Record<string, any>[] = [
          { 'ta.firstname': { contains: query, mode: 'insensitive' } },
          { 'ta.lastname': { contains: query, mode: 'insensitive' } },
          { 'tresp."transId"': { contains: query, mode: 'insensitive' } },
        ];

        array.push({ OR: orConditions });
      } else {
        // Numeric search
        const orConditions: Record<string, any>[] = [
          { 'tresp."transId"': { contains: query, mode: 'insensitive' } },
          { 'tr.amount': { contains: query, mode: 'insensitive' } },
          { 'ip."currentOwing"': { contains: query, mode: 'insensitive' } },
          { 'i.number': { contains: query, mode: 'insensitive' } },
        ];

        array.push({ OR: orConditions });
      }
    }

    // Convert remaining search parameters to array
    searchParams.forEach((value: string, key: string) => {
      array.push({ [key]: value });
    });

    if (statusType) {
      array.push({ 'tresp."responseCode"': this.responseCodeMap[statusType] });
    }

    if (auctionsId && auctionsId.trim().length !== 0) {
      array.push({ 'a.id': auctionsId });
    }

    return array;
  }
}

export enum ResponseCodeEnum {
  Approved = '1',
  Declined = '2',
  Error = '3',
  HeldForReview = '4',
}
