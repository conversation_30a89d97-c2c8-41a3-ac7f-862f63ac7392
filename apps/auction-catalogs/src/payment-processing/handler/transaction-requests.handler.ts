import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { TransactionRequestsDbService } from '../data/transaction-requests-db.service';
import { TransactionRequestDto } from '../dtos/transaction-request.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { TransactionDetailDto } from '../dtos/transaction-details.dto';

@Injectable()
export class TransactionRequestsHandler extends BaseHandler<TransactionRequestsDbService, TransactionRequestDto> {
  constructor(dbService: TransactionRequestsDbService) {
    super(dbService);
  }

  async getReport(page: number, size: number, params: Record<string, any>[]): Promise<ListResultset<TransactionDetailDto>> {
    return this.dbService.report(page, size, params);
  }
}
