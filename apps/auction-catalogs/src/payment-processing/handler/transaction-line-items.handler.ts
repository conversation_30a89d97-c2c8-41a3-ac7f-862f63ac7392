import { <PERSON>Handler } from '@mvc/handlers/base.handler';
import { TransactionLineItemsDbService } from '../data/transaction-line-items-db.service';
import { TransactionLineItemDto } from '../dtos/transaction-line-item.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionLineItemsHandler extends BaseHandler<TransactionLineItemsDbService, TransactionLineItemDto> {
  constructor(dbService: TransactionLineItemsDbService) {
    super(dbService);
  }
}
