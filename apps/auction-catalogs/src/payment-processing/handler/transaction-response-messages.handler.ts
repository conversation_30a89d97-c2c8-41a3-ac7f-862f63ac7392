import { BaseHandler } from '@mvc/handlers/base.handler';
import { TransactionResponseMessageDto } from '../dtos/transaction-response-message.dto';
import { Injectable } from '@nestjs/common';
import { TransactionResponseMessagesDbService } from '../data/transaction-response-messages-db.service';

@Injectable()
export class TransactionResponseMessagesHandler extends BaseHandler<TransactionResponseMessagesDbService, TransactionResponseMessageDto> {
  constructor(dbService: TransactionResponseMessagesDbService) {
    super(dbService);
  }
}
