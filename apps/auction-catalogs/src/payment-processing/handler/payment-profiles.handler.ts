import { Injectable } from '@nestjs/common';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { PaymentProfilesDbService } from '../data/payment-profiles-db.service';
import { PaymentProfileDto } from '../dtos/payment-profile.dto';

@Injectable()
export class PaymentProfilesHandler extends BaseHandler<PaymentProfilesDbService, PaymentProfileDto> {
  constructor(dbService: PaymentProfilesDbService) {
    super(dbService);
  }
}
