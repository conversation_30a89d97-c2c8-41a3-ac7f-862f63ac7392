import { BaseHandler } from '@mvc/handlers/base.handler';
import { TransactionResponsesDbService } from '../data/transaction-responses-db.service';
import { TransactionResponseDto } from '../dtos/transaction-response.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionResponsesHandler extends BaseHandler<TransactionResponsesDbService, TransactionResponseDto> {
  constructor(dbService: TransactionResponsesDbService) {
    super(dbService);
  }
}
