import { BaseHandler } from '@mvc/handlers/base.handler';
import { TransactionCreditCardsDbService } from '../data/transaction-credit-cards-db.service';
import { TransactionCreditCardDto } from '../dtos/transaction-credit-card.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionCreditCardsHandler extends BaseHandler<TransactionCreditCardsDbService, TransactionCreditCardDto> {
  constructor(dbService: TransactionCreditCardsDbService) {
    super(dbService);
  }
}
