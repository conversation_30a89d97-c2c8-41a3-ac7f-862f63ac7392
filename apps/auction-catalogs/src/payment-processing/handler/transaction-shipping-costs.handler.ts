import { BaseHandler } from '@mvc/handlers/base.handler';
import { TransactionShippingCostsDbService } from '../data/transaction-shipping-costs-db.service';
import { TransactionShippingCostDto } from '../dtos/transaction-shipping-cost.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionShippingCostsHandler extends BaseHandler<TransactionShippingCostsDbService, TransactionShippingCostDto> {
  constructor(dbService: TransactionShippingCostsDbService) {
    super(dbService);
  }
}
