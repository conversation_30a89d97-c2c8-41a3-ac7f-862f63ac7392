import { <PERSON><PERSON>and<PERSON> } from '@mvc/handlers/base.handler';
import { TransactionAddressesDbService } from '../data/transaction-addresses-db.service';
import { TransactionAddressDto } from '../dtos/transaction-address.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionAddressesHandler extends BaseHandler<TransactionAddressesDbService, TransactionAddressDto> {
  constructor(dbService: TransactionAddressesDbService) {
    super(dbService);
  }
}
