import { Injectable } from '@nestjs/common';
import { AddCardCommand } from './authorize-net-commands/add-card-command';
import { BaseCardCommand } from './authorize-net-commands/base-card-command';
import { ListCardsCommand } from './authorize-net-commands/list-cards-command';
import { DeleteCardCommand } from './authorize-net-commands/delete-card-command';

export enum CardCommand {
  ADD_CARD,
  DELETE_CARD,
  LIST_CARDS,
}

@Injectable()
export class CardManagementService {
  constructor(
    public addCardCommand: AddCardCommand,
    public listCardsCommand: ListCardsCommand,
    public deleteCardCommand: DeleteCardCommand,
  ) {}

  public getCommand(card: CardCommand): BaseCardCommand {
    switch (card) {
      case CardCommand.ADD_CARD:
        return this.addCardCommand;
      case CardCommand.LIST_CARDS:
        return this.listCardsCommand;
      case CardCommand.DELETE_CARD:
        return this.deleteCardCommand;
      default:
        return this.addCardCommand;
    }
  }
}
