import { TransactionRequestDto } from '../dtos/transaction-request.dto';
import { TransactionCreditCardDto } from '../dtos/transaction-credit-card.dto';
import { TransactionShippingCostDto } from '../dtos/transaction-shipping-cost.dto';
import { TransactionAddressDto } from '../dtos/transaction-address.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { TransactionLineItemDto } from '../dtos/transaction-line-item.dto';
import { Injectable, Logger } from '@nestjs/common';
import * as AuthorizeNet from 'authorizenet';
import { NullResponseReceivedError } from '@auth-net/authorizenet/exceptions/null-response-received-error';
import { ConfigService } from '@nestjs/config';
import { SANDBOX_API_TRANSACTION_KEY, SANDBOX_LOGIN_KEY } from './authorize-net-commands/authorize-net-common';
import { PaymentProfileDto } from '../dtos/payment-profile.dto';
import { ChargeDto } from '../controller/transaction-requests.controller';

const ApiContracts: any = AuthorizeNet.APIContracts;
const ApiControllers: any = AuthorizeNet.APIControllers;
@Injectable()
export class ChargeCreditCardService {
  private readonly logger = new Logger(ChargeCreditCardService.name);
  private loginKey: string;
  private transactionKey: string;
  private billingType: string;
  private billingTypeValue: string;
  private windowSetting: string;
  private windowSize: string;

  constructor(private configService: ConfigService) {
    this.loginKey = this.configService.get('AUTHORIZE_NET_API_LOGIN_KEY', SANDBOX_LOGIN_KEY);
    this.transactionKey = this.configService.get('AUTHORIZE_NET_API_TRANSACTION_KEY', SANDBOX_API_TRANSACTION_KEY);
    this.billingType = this.configService.get('AUTHORIZE_NET_BILLING_TYPE', 'recurringBilling');
    this.billingTypeValue = this.configService.get('AUTHORIZE_NET_BILLING_TYPE_VALUE', 'false');
    this.windowSetting = this.configService.get('AUTHORIZE_NET_WINDOW_SETTING', 'duplicateWindow');
    // this.windowSize = this.configService.get('AUTHORIZE_NET_WINDOW_SIZE', '120');
    this.windowSize = this.configService.get('AUTHORIZE_NET_WINDOW_SIZE', '10');
    if (SANDBOX_API_TRANSACTION_KEY == this.transactionKey) {
      this.logger.log('******** ChargeCreditCardService using sandbox credentials ******');
    }
  }

  async charge(chargeDto: ChargeDto, transactionCreditCard: TransactionCreditCardDto): Promise<CreateTransactionResponse> {
    const creditCard: any = this.buildCreditCard(transactionCreditCard);
    const paymentType: any = new ApiContracts.PaymentType();
    paymentType.setCreditCard(creditCard);

    const billTo: any = this.buildShippingAddress(this.getAddress(chargeDto.addresses, AddressType.Billing));

    const createRequest: any = this.buildRequest(chargeDto);
    createRequest.transactionRequestType.setPayment(paymentType);
    createRequest.transactionRequestType.setBillTo(billTo);

    return this.runAuthorizeNetRequest(createRequest, chargeDto);
  }

  async chargeProfile(chargeDto: ChargeDto, paymentProfile: PaymentProfileDto): Promise<CreateTransactionResponse> {
    const profileToCharge: AuthorizeNet.APIContracts.CustomerProfilePaymentType = new ApiContracts.CustomerProfilePaymentType();
    profileToCharge.setCustomerProfileId(paymentProfile.customerProfileId);

    const paymentProfileAuthNet: AuthorizeNet.APIContracts.PaymentProfile = new ApiContracts.PaymentProfile();
    paymentProfileAuthNet.setPaymentProfileId(paymentProfile.customerPaymentProfileId);
    profileToCharge.setPaymentProfile(paymentProfileAuthNet);

    const createRequest: AuthorizeNet.APIContracts.CreateTransactionRequest = await this.buildRequest(chargeDto);
    createRequest.getTransactionRequest().setProfile(profileToCharge);

    return this.runAuthorizeNetRequest(createRequest, chargeDto);
  }

  private runAuthorizeNetRequest(createRequest: any, chargeDto: ChargeDto): Promise<CreateTransactionResponse> {
    const ctrl: any = new ApiControllers.CreateTransactionController(createRequest.getJSON());
    //Defaults to sandbox
    //ctrl.setEnvironment(SDKConstants.endpoint.production);
    return new Promise<CreateTransactionResponse>((resolve: any, reject: any): void => {
      ctrl.execute(function (): void {
        const apiResponse: any = ctrl.getResponse();
        const response: any = new ApiContracts.CreateTransactionResponse(apiResponse);

        if (response != null) {
          if (response.getMessages().getResultCode() == ApiContracts.MessageTypeEnum.OK && response.getTransactionResponse().getMessages() != null) {
            resolve(response);
          } else {
            reject(response);
          }
        } else {
          throw new NullResponseReceivedError(chargeDto.transactionRequest.id);
        }
      });
    });
  }

  private buildRequest(chargeDto: ChargeDto): Promise<AuthorizeNet.APIContracts.CreateTransactionRequest> {
    const merchantAuthenticationType: any = new ApiContracts.MerchantAuthenticationType();
    merchantAuthenticationType.setName(this.loginKey);
    merchantAuthenticationType.setTransactionKey(this.transactionKey);

    const orderDetails: any = this.buildOrderDetails(chargeDto.transactionRequest);

    const shipping: any = this.buildShippingCost(chargeDto.shippingCost);
    const billTo: any = this.buildShippingAddress(this.getAddress(chargeDto.addresses, AddressType.Billing));
    const lineItemsContainer: any = this.buildLineItems(chargeDto.lineItems);

    const transactionSetting1: any = new ApiContracts.SettingType();

    transactionSetting1.setSettingName(this.windowSetting);
    transactionSetting1.setSettingValue(this.windowSize);

    const transactionSetting2: any = new ApiContracts.SettingType();
    transactionSetting2.setSettingName(this.billingType);
    transactionSetting2.setSettingValue(this.billingTypeValue);

    const transactionSettingList: any[] = [];
    transactionSettingList.push(transactionSetting1);
    transactionSettingList.push(transactionSetting2);

    const transactionSettings: any = new ApiContracts.ArrayOfSetting();
    transactionSettings.setSetting(transactionSettingList);

    const transactionRequestType: any = new ApiContracts.TransactionRequestType();
    transactionRequestType.setTransactionType(ApiContracts.TransactionTypeEnum.AUTHCAPTURETRANSACTION);
    transactionRequestType.setAmount(chargeDto.transactionRequest.amount);
    transactionRequestType.setLineItems(lineItemsContainer);
    transactionRequestType.setOrder(orderDetails);
    transactionRequestType.setShipping(shipping);
    transactionRequestType.setShipTo(billTo);
    transactionRequestType.setTransactionSettings(transactionSettings);

    const createRequest: any = new ApiContracts.CreateTransactionRequest();
    createRequest.setMerchantAuthentication(merchantAuthenticationType);
    createRequest.setTransactionRequest(transactionRequestType);
    return createRequest;
  }

  buildCreditCard(transactionCreditCard: TransactionCreditCardDto): any {
    const creditCard: any = new ApiContracts.CreditCardType();
    creditCard.setCardNumber(transactionCreditCard.number);
    creditCard.setExpirationDate(transactionCreditCard.expirationDate);
    creditCard.setCardCode(transactionCreditCard.cvv);

    return creditCard;
  }

  buildOrderDetails(transactionRequest: TransactionRequestDto): any {
    const orderDetails: any = new ApiContracts.OrderType();
    orderDetails.setInvoiceNumber(transactionRequest.invoiceNumber);
    orderDetails.setDescription(transactionRequest.description);

    return orderDetails;
  }

  buildShippingCost(shippingCost: TransactionShippingCostDto): any {
    const shipping: any = new ApiContracts.ExtendedAmountType();
    shipping.setAmount(shippingCost.amount);
    shipping.setName(shippingCost.name);
    shipping.setDescription(shippingCost.description);

    return shipping;
  }

  buildShippingAddress(address: TransactionAddressDto): any {
    const retval: any = new ApiContracts.CustomerAddressType();
    retval.setFirstName(address.firstname);
    retval.setLastName(address.lastname);
    retval.setCompany(address.companyName);
    retval.setAddress(address.address1);
    retval.setCity(address.city);
    retval.setState(address.state);
    retval.setZip(address.zip);
    retval.setCountry(address.country);

    return retval;
  }

  getAddress(transactionAddresses: ListResultset<TransactionAddressDto>, addressType: string): TransactionAddressDto {
    // Attempt to find the address in the list
    const address: TransactionAddressDto | undefined = transactionAddresses.list.find((address: TransactionAddressDto) => address.addressType === addressType);

    // Return the found address or indicate that it was not found
    if (!address) {
      return new TransactionAddressDto({});
    }

    return address;
  }
  buildLineItems(lineItems: ListResultset<TransactionLineItemDto>): any {
    const lineItemList: any[] = [];
    lineItems.list.map((item: TransactionLineItemDto): void => {
      const lineItem: any = new ApiContracts.LineItemType();
      lineItem.setItemId(this.getMiddle31Chars(item.itemMappingsId));
      lineItem.setName(item.name.substring(0, 31));
      lineItem.setDescription(item.description);
      lineItem.setQuantity(item.quantity);
      lineItem.setUnitPrice(item.unitPrice);

      lineItemList.push(lineItem);
    });

    const lineItemsContainer: any = new ApiContracts.ArrayOfLineItem();
    lineItemsContainer.setLineItem(lineItemList);

    return lineItemsContainer;
  }
  private getMiddle31Chars(guid: string): string {
    // Calculate starting position to extract the middle 31 characters
    const startPosition: number = Math.floor((guid.length - 31) / 2);

    // Extract and return the middle 31 characters
    return guid.substring(startPosition, startPosition + 31);
  }
}

enum AddressType {
  Shipping = 'Shipping',
  Billing = 'Billing',
}
export interface CreateTransactionResponse {
  getTransactionResponse(): { getErrors: () => Errors | null } | null;
  getMessages(): { getMessage: () => Array<{ getCode: () => string; getText: () => string }> };
}
export interface Errors {
  getError: () => Array<{ getErrorCode: () => string; getErrorText: () => string }>;
}
