import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Injectable } from '@nestjs/common';
import { APIContracts, APIControllers } from 'authorizenet';
import { AuthorizeNetError } from '../../exceptions/authorize-net-error';
import { PaymentProfilesHandler } from '../../handler/payment-profiles.handler';
import { AuthorizeNetDto, buildMerchantAuthentication, isSuccessful, mapToAuthNetError } from './authorize-net-common';
import { BaseCardCommand } from './base-card-command';
import { CardManage } from './card-manage.interface';

@Injectable()
export class DeleteCardCommand extends BaseCardCommand implements CardManage<void> {
  constructor(public handler: PaymentProfilesHandler) {
    super();
  }

  public async deleteCard(authorizeNetDto: AuthorizeNetDto): Promise<APIContracts.DeleteCustomerPaymentProfileResponse | ErrorResponse> {
    const merchantAuthentication: APIContracts.MerchantAuthenticationType = buildMerchantAuthentication();
    const request: APIContracts.DeleteCustomerPaymentProfileRequest = this.buildDeleteCustomerProfile(
      authorizeNetDto.profile!.customerProfileId!,
      authorizeNetDto.profile!.customerPaymentProfileId,
      merchantAuthentication,
    );
    return await this.deletePaymentProfile(request, authorizeNetDto.userId!);
  }

  async mapTo(response: APIContracts.ANetApiResponse | ErrorResponse, authorizeNetDto: AuthorizeNetDto, addressId: string): Promise<void> {
    response;
    authorizeNetDto;
    addressId;
  }

  buildDeleteCustomerProfile(
    customerProfileId: string,
    customerPaymentProfileId: string,
    merchantAuthentication: APIContracts.MerchantAuthenticationType,
  ): APIContracts.DeleteCustomerPaymentProfileRequest {
    const createRequest: APIContracts.DeleteCustomerPaymentProfileRequest = new APIContracts.DeleteCustomerPaymentProfileRequest();
    createRequest.setCustomerProfileId(customerProfileId);
    createRequest.setCustomerPaymentProfileId(customerPaymentProfileId);
    createRequest.setMerchantAuthentication(merchantAuthentication);
    return createRequest;
  }

  async deletePaymentProfile(
    request: APIContracts.DeleteCustomerPaymentProfileRequest,
    accountMappingsId: string,
  ): Promise<APIContracts.DeleteCustomerPaymentProfileResponse | ErrorResponse> {
    const ctrl: APIControllers.DeleteCustomerPaymentProfileController = new APIControllers.DeleteCustomerPaymentProfileController(request.getJSON());
    return new Promise<APIContracts.DeleteCustomerPaymentProfileResponse | ErrorResponse>((resolve: any, reject: any): void => {
      ctrl.execute(() => {
        const apiResponse: any = ctrl.getResponse();
        const response: APIContracts.DeleteCustomerPaymentProfileResponse = new APIContracts.DeleteCustomerPaymentProfileResponse(apiResponse);
        if (response != null) {
          if (isSuccessful(response)) {
            resolve(response);
          } else {
            //TODO return the error message from authorize.net
            reject(new ErrorResponse(new AuthorizeNetError(accountMappingsId), accountMappingsId, accountMappingsId, mapToAuthNetError(response)));
          }
        }
      });
    });
  }
}
