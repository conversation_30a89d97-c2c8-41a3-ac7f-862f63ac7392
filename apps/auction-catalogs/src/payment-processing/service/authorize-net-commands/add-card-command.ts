import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Injectable } from '@nestjs/common';
import { APIContracts, APIControllers } from 'authorizenet';
import { PaymentProfileDto } from '../../dtos/payment-profile.dto';
import { AuthorizeNetError } from '../../exceptions/authorize-net-error';
import { PaymentProfilesHandler } from '../../handler/payment-profiles.handler';
import {
  AuthorizeNetDto,
  buildCustomerAddress,
  buildCustomerPaymentProfile,
  buildCustomerType,
  buildMerchantAuthentication,
  mapToAuthNetError,
  parseValidationDirectResponse,
  ValidationDirectResponseKey,
} from './authorize-net-common';
import { BaseCardCommand } from './base-card-command';
import { CardManage } from './card-manage.interface';

@Injectable()
export class AddCardCommand extends BaseCardCommand implements CardManage<PaymentProfileDto> {
  constructor(public handler: PaymentProfilesHandler) {
    super();
  }

  public async addCard(authorizeNetDto: AuthorizeNetDto): Promise<APIContracts.CreateCustomerProfileResponse | APIContracts.CreateCustomerPaymentProfileResponse | ErrorResponse> {
    const merchantAuthentication: APIContracts.MerchantAuthenticationType = buildMerchantAuthentication();
    const customerAddress: APIContracts.CustomerAddressType = buildCustomerAddress(authorizeNetDto.customerInfo!, authorizeNetDto.address!);
    const customerPaymentProfileType: APIContracts.CustomerPaymentProfileType = buildCustomerPaymentProfile(authorizeNetDto.opaqueData!, customerAddress);

    if (authorizeNetDto.profile!.customerProfileId == null) {
      const customerType: APIContracts.CustomerProfileType = buildCustomerType(authorizeNetDto.profile!.merchantCustomerId, customerPaymentProfileType);
      const createRequest: APIContracts.CreateCustomerProfileRequest = this.buildCreateCustomerProfileRequest(customerType, merchantAuthentication);
      return await this.createNewCustomerProfile(createRequest, authorizeNetDto.userId!);
    } else {
      const createRequest: APIContracts.CreateCustomerPaymentProfileRequest = this.buildCreateCustomerPaymentProfileRequest(
        authorizeNetDto.profile!.customerProfileId,
        customerPaymentProfileType,
        merchantAuthentication,
      );

      return await this.createNewPaymentProfile(createRequest, authorizeNetDto.userId!);
    }
  }

  async mapTo(response: APIContracts.ANetApiResponse | ErrorResponse, authorizeNetDto: AuthorizeNetDto, addressId: string): Promise<PaymentProfileDto> {
    const newPaymentProfileDto: PaymentProfileDto = this.buildPaymentProfileDtoFromResponse(
      response as APIContracts.CreateCustomerProfileResponse | APIContracts.CreateCustomerPaymentProfileResponse,
      authorizeNetDto.userId!,
      authorizeNetDto.profile!.merchantCustomerId,
      addressId,
    );
    return await this.handler.create(authorizeNetDto.userId!, newPaymentProfileDto);
  }

  private buildCreateCustomerPaymentProfileRequest(
    customerProfileId: string,
    customerPaymentProfile: APIContracts.CustomerPaymentProfileType,
    merchantAuthentication: APIContracts.MerchantAuthenticationType,
  ): APIContracts.CreateCustomerPaymentProfileRequest {
    const createRequest: APIContracts.CreateCustomerPaymentProfileRequest = new APIContracts.CreateCustomerPaymentProfileRequest();
    createRequest.setCustomerProfileId(customerProfileId);
    createRequest.setValidationMode(APIContracts.ValidationModeEnum.LIVEMODE);
    createRequest.setPaymentProfile(customerPaymentProfile);
    createRequest.setMerchantAuthentication(merchantAuthentication);
    return createRequest;
  }

  private buildCreateCustomerProfileRequest(
    customerProfileType: APIContracts.CustomerProfileType,
    merchantAuthentication: APIContracts.MerchantAuthenticationType,
  ): APIContracts.CreateCustomerProfileRequest {
    const createRequest: APIContracts.CreateCustomerProfileRequest = new APIContracts.CreateCustomerProfileRequest();
    createRequest.setProfile(customerProfileType);
    createRequest.setValidationMode(APIContracts.ValidationModeEnum.LIVEMODE);
    createRequest.setMerchantAuthentication(merchantAuthentication);
    return createRequest;
  }

  private buildPaymentProfileDtoFromResponse(
    response: APIContracts.CreateCustomerProfileResponse | APIContracts.CreateCustomerPaymentProfileResponse,
    accountMappingsId: string,
    merchantCustomerId: string,
    addressId: string,
  ): PaymentProfileDto {
    let customerPaymentProfileId: string = '';
    let networkTransId: string = '';
    if (response instanceof APIContracts.CreateCustomerProfileResponse) {
      customerPaymentProfileId = response?.getCustomerPaymentProfileIdList().numericString[0].toString();
      networkTransId = parseValidationDirectResponse(response.getValidationDirectResponseList().string[0].toString(), ValidationDirectResponseKey.NETWORK_TRANSACTION_ID) ?? '';
    } else {
      customerPaymentProfileId = response.getCustomerPaymentProfileId();
      networkTransId = parseValidationDirectResponse(response.getValidationDirectResponse().toString(), ValidationDirectResponseKey.NETWORK_TRANSACTION_ID) ?? '';
    }

    return new PaymentProfileDto({
      accountMappingsId: accountMappingsId,
      merchantCustomerId: merchantCustomerId,
      customerProfileId: response.customerProfileId,
      customerPaymentProfileId: customerPaymentProfileId,
      originalNetworkTransId: networkTransId,
      billingAddressId: addressId,
    });
  }

  private async createNewCustomerProfile(
    request: APIContracts.CreateCustomerProfileRequest,
    accountMappingsId: string,
  ): Promise<APIContracts.CreateCustomerProfileResponse | ErrorResponse> {
    const ctrl: APIControllers.CreateCustomerProfileController = new APIControllers.CreateCustomerProfileController(request.getJSON());
    return new Promise<APIContracts.CreateCustomerProfileResponse | ErrorResponse>((resolve: any, reject: any): void => {
      ctrl.execute(() => {
        const apiResponse: any = ctrl.getResponse();
        const response: APIContracts.CreateCustomerProfileResponse = new APIContracts.CreateCustomerProfileResponse(apiResponse);
        if (response != null) {
          if (response.getMessages().getResultCode() == APIContracts.MessageTypeEnum.OK) {
            resolve(response);
          } else {
            reject(new ErrorResponse(new AuthorizeNetError(accountMappingsId), accountMappingsId, accountMappingsId, mapToAuthNetError(response)));
          }
        }
      });
    });
  }

  private async createNewPaymentProfile(
    request: APIContracts.CreateCustomerPaymentProfileRequest,
    accountMappingsId: string,
  ): Promise<APIContracts.CreateCustomerPaymentProfileResponse | ErrorResponse> {
    const ctrl: APIControllers.CreateCustomerPaymentProfileController = new APIControllers.CreateCustomerPaymentProfileController(request.getJSON());
    return new Promise<APIContracts.CreateCustomerPaymentProfileResponse | ErrorResponse>((resolve: any, reject: any): void => {
      ctrl.execute(() => {
        const apiResponse: any = ctrl.getResponse();
        const response: APIContracts.CreateCustomerPaymentProfileResponse = new APIContracts.CreateCustomerPaymentProfileResponse(apiResponse);
        if (response != null) {
          if (response.getMessages().getResultCode() == APIContracts.MessageTypeEnum.OK) {
            resolve(response);
          } else {
            reject(new ErrorResponse(new AuthorizeNetError(accountMappingsId), accountMappingsId, accountMappingsId, mapToAuthNetError(response)));
          }
        }
      });
    });
  }
}
