import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Injectable } from '@nestjs/common';
import { APIContracts, APIControllers } from 'authorizenet';
import * as _ from 'lodash';
import { PaymentProfileCardDto } from '../../dtos/payment-profile-card.dto';
import { AuthorizeNetError } from '../../exceptions/authorize-net-error';
import { PaymentProfilesHandler } from '../../handler/payment-profiles.handler';
import { AuthorizeNetDto, buildMerchantAuthentication, isSuccessful, mapToAuthNetError } from './authorize-net-common';
import { BaseCardCommand } from './base-card-command';
import { CardManage } from './card-manage.interface';

@Injectable()
export class ListCardsCommand extends BaseCardCommand implements CardManage<PaymentProfileCardDto[]> {
  constructor(public handler: PaymentProfilesHandler) {
    super();
  }

  public async listCards(authorizeNetDto: AuthorizeNetDto): Promise<APIContracts.GetCustomerProfileResponse | ErrorResponse> {
    const merchantAuthentication: APIContracts.MerchantAuthenticationType = buildMerchantAuthentication();
    const request: APIContracts.GetCustomerProfileRequest = this.buildGetCustomerProfileRequest(authorizeNetDto.profile!.customerProfileId, merchantAuthentication);
    return await this.getPaymentProfile(request, authorizeNetDto.userId!);
  }

  async mapTo(response: APIContracts.ANetApiResponse | ErrorResponse, authorizeNetDto: AuthorizeNetDto, addressId: string): Promise<PaymentProfileCardDto[]> {
    authorizeNetDto;
    addressId;
    if (response instanceof APIContracts.GetCustomerProfileResponse) {
      const paymentCards: PaymentProfileCardDto[] = _.map(response.getProfile().getPaymentProfiles(), (value: APIContracts.CustomerPaymentProfileMaskedType) => {
        return new PaymentProfileCardDto(value.customerPaymentProfileId, value.payment.creditCard);
      });
      return paymentCards;
    }
    return [] as PaymentProfileCardDto[];
  }

  buildGetCustomerProfileRequest(customerProfileId: string, merchantAuthentication: APIContracts.MerchantAuthenticationType): APIContracts.GetCustomerProfileRequest {
    const createRequest: APIContracts.GetCustomerProfileRequest = new APIContracts.GetCustomerProfileRequest();
    createRequest.setCustomerProfileId(customerProfileId);
    createRequest.setMerchantAuthentication(merchantAuthentication);
    createRequest.setUnmaskExpirationDate(true);
    return createRequest;
  }

  async getPaymentProfile(request: APIContracts.GetCustomerProfileRequest, accountMappingsId: string): Promise<APIContracts.GetCustomerProfileResponse | ErrorResponse> {
    const ctrl: APIControllers.CreateCustomerProfileController = new APIControllers.CreateCustomerProfileController(request.getJSON());
    return new Promise<APIContracts.GetCustomerProfileResponse | ErrorResponse>((resolve: any, reject: any): void => {
      ctrl.execute(() => {
        const apiResponse: any = ctrl.getResponse();
        const response: APIContracts.GetCustomerProfileResponse = new APIContracts.GetCustomerProfileResponse(apiResponse);
        if (response != null) {
          if (isSuccessful(response)) {
            resolve(response);
          } else {
            reject(new ErrorResponse(new AuthorizeNetError(accountMappingsId), accountMappingsId, accountMappingsId, mapToAuthNetError(response)));
          }
        }
      });
    });
  }
}
