import { AccountAddressDto } from '@vb/nest/common-dtos/users/account-address.dto';
import { APIContracts } from 'authorizenet';
import * as _ from 'lodash';
import { PaymentProfileDto } from '../../dtos/payment-profile.dto';

export type OpaqueData = {
  dataValue: string;
  dataDescriptor: string;
};

export type CustomerInformation = {
  firstName: string;
  lastName: string;
};

export type AuthorizeNetDto = {
  customerInfo?: CustomerInformation;
  opaqueData?: OpaqueData;
  address?: AccountAddressDto;
  userId?: string;
  profile?: PaymentProfileDto;
};

export const SANDBOX_LOGIN_KEY: string = '5eRsk5k8XZ';
export const SANDBOX_API_TRANSACTION_KEY: string = '85mX9pZ9Aa5Rt4a4';

export const enum ValidationDirectResponseKey {
  NETWORK_TRANSACTION_ID = 58,
  AUTH_CODE = 4,
  AUTH_AMOUNT = 9,
}

export function buildMerchantAuthentication(): APIContracts.MerchantAuthenticationType {
  const merchantAuthentication: APIContracts.MerchantAuthenticationType = new APIContracts.MerchantAuthenticationType();
  merchantAuthentication.setName(SANDBOX_LOGIN_KEY);
  merchantAuthentication.setTransactionKey(SANDBOX_API_TRANSACTION_KEY);
  return merchantAuthentication;
}

export function buildCustomerPaymentProfile(opaqueData: OpaqueData, address: APIContracts.CustomerAddressType): APIContracts.CustomerPaymentProfileType {
  const paymentType: APIContracts.PaymentType = new APIContracts.PaymentType();
  paymentType.setOpaqueData(opaqueData);

  const customerPaymentProfileType: APIContracts.CustomerPaymentProfileType = new APIContracts.CustomerPaymentProfileType();
  customerPaymentProfileType.setBillTo(address);
  customerPaymentProfileType.setPayment(paymentType);
  customerPaymentProfileType.setCustomerType(APIContracts.CustomerTypeEnum.INDIVIDUAL);
  return customerPaymentProfileType;
}

export function buildCustomerType(merchantCustomerId: string, customerPaymentProfile: APIContracts.CustomerPaymentProfileType): APIContracts.CustomerProfileType {
  const customerProfileType: APIContracts.CustomerProfileType = new APIContracts.CustomerProfileType();
  customerProfileType.setMerchantCustomerId(merchantCustomerId);
  customerProfileType.setPaymentProfiles(customerPaymentProfile);
  return customerProfileType;
}

export function buildCustomerAddress(customerInfo: CustomerInformation, address: AccountAddressDto): APIContracts.CustomerAddressType {
  const customerAddress: APIContracts.CustomerAddressType = new APIContracts.CustomerAddressType();
  customerAddress.setFirstName(customerInfo.firstName);
  customerAddress.setLastName(customerInfo.lastName);
  customerAddress.setAddress(address.address1);
  customerAddress.setCity(address.city);
  customerAddress.setState(address.province);
  customerAddress.setCountry(address.country);
  customerAddress.setZip(address.postalCode);
  return customerAddress;
}

export function isSuccessful(response: APIContracts.ANetApiResponse | any): boolean {
  if (!(response instanceof APIContracts.ANetApiResponse)) {
    return false;
  }
  return response.getMessages().getResultCode() == APIContracts.MessageTypeEnum.OK;
}

export function mapToAuthNetError(response: APIContracts.ANetApiResponse | any): { errors: string[] } | undefined {
  if (!(response instanceof APIContracts.ANetApiResponse)) {
    return undefined;
  }
  const errorResponse: { errors: string[] } = { errors: [] };
  _.forEach(response.getMessages().getMessage(), (msg: any) => {
    errorResponse.errors.push(msg.text);
  });
  return errorResponse;
}

export function mapTransactionToAuthNetError(response: APIContracts.CreateTransactionResponse | any): { errors: string[] } | undefined {
  const errorResponse: { errors: string[] } = { errors: [] };
  _.forEach(response.getTransactionResponse().getErrors().getError(), (msg: any) => {
    errorResponse.errors.push(msg.errorText);
  });
  return errorResponse;
}

export function parseValidationDirectResponse(validationDirectResponse: string, key: ValidationDirectResponseKey): string | undefined {
  if (!validationDirectResponse || !key) {
    return undefined;
  }

  return validationDirectResponse.split(',')[key.valueOf()];
}
