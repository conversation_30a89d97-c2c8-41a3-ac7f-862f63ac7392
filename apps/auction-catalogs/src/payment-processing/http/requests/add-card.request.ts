import { AddCardRequestCustomerInformation, AddCardRequestEncryptedCardData, AddCardRequestMessages, AddCardRequestOpaqueData } from '@viewbid/ts-node-client';
import { IsDefined, IsString } from 'class-validator';

export class AddCardRequest {
  @IsDefined()
  customerInformation: AddCardRequestCustomerInformation;

  @IsDefined()
  encryptedCardData: AddCardRequestEncryptedCardData;

  @IsDefined()
  messages: AddCardRequestMessages;

  @IsDefined()
  opaqueData: AddCardRequestOpaqueData;

  @IsString()
  @IsDefined()
  addressId: string;
}
