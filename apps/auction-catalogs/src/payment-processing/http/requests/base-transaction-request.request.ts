import { TransactionAddressDto } from '../../dtos/transaction-address.dto';
import { TransactionLineItemDto } from '../../dtos/transaction-line-item.dto';
import { TransactionRequestDto } from '../../dtos/transaction-request.dto';
import { TransactionShippingCostDto } from '../../dtos/transaction-shipping-cost.dto';

export class BaseTransactionRequest {
  transactionRequest: TransactionRequestDto;
  transactionAddresses: Array<TransactionAddressDto>;
  transactionShipping?: TransactionShippingCostDto;
  transactionLineItems: Array<TransactionLineItemDto>;

  constructor(row: any) {
    this.transactionRequest = row.transactionRequest;
    this.transactionAddresses = row.transactionAddresses;
    this.transactionShipping = row.transactionShipping;
    this.transactionLineItems = row.transactionLineItems;
  }
}
