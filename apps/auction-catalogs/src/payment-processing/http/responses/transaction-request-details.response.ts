import { Response } from '@mvc/http/responses/response';
import { TransactionRequestDto } from '../../dtos/transaction-request.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { TransactionAddressDto } from '../../dtos/transaction-address.dto';
import { TransactionShippingCostDto } from '../../dtos/transaction-shipping-cost.dto';
import { TransactionCreditCardDto } from '../../dtos/transaction-credit-card.dto';
import { TransactionLineItemDto } from '../../dtos/transaction-line-item.dto';

export class TransactionRequestDetailsResponse extends Response {
  constructor(
    transactionRequest: TransactionRequestDto,
    transactionAddresses: ListResultset<TransactionAddressDto>,
    transactionShippingCost: TransactionShippingCostDto,
    transactionCreditCard: TransactionCreditCardDto,
    transactionLineItems: ListResultset<TransactionLineItemDto>,
  ) {
    super(
      {
        transactionRequest: transactionRequest,
        transactionAddresses: transactionAddresses,
        transactionShippingCost: transactionShippingCost,
        transactionCreditCard: transactionCreditCard,
        transactionLineItems: transactionLineItems,
      },
      'transactionRequest',
      transactionRequest.id,
      {},
    );
  }
}
