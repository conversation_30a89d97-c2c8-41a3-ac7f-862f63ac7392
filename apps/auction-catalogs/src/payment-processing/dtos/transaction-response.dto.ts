import { TransactionRequestDto } from './transaction-request.dto';

export class TransactionResponseDto {
  id: string;
  responseCode?: string;
  authCode: string;
  avsResultCode: string;
  cvvResultCode: string;
  cavvResultCode: string;
  transId: string;
  accountNumber: string;
  accountType: string;
  transHashSha2: string;
  networkTransId: string;
  transactionRequestsId: string;

  constructor(row: any) {
    this.id = row.id;
    this.responseCode = row.responseCode;
    this.authCode = row.authCode;
    this.avsResultCode = row.avsResultCode;
    this.cvvResultCode = row.cvvResultCode;
    this.cavvResultCode = row.cavvResultCode;
    this.transId = row.transId;
    this.accountNumber = row.accountNumber;
    this.accountType = row.accountType;
    this.transHashSha2 = row.transHashSha2;
    this.networkTransId = row.networkTransId;
    this.transactionRequestsId = row.transactionRequestsId;
  }

  static createNewFromResponse(response: any, transactionRequest: TransactionRequestDto): TransactionResponseDto {
    return new TransactionResponseDto({
      responseCode: response.getTransactionResponse().getResponseCode(),
      authCode: response.getTransactionResponse().getAuthCode(),
      avsResultCode: response.getTransactionResponse().getAvsResultCode(),
      cvvResultCode: response.getTransactionResponse().getCvvResultCode(),
      cavvResultCode: response.getTransactionResponse().getCavvResultCode(),
      transId: response.getTransactionResponse().getTransId(),
      testRequest: response.getTransactionResponse().getTestRequest(),
      accountNumber: response.getTransactionResponse().getAccountNumber(),
      accountType: response.getTransactionResponse().getAccountType(),
      messages: response.getMessages(),
      userFields: response.getTransactionResponse().getUserFields(),
      transHashSha2: response.getTransactionResponse().getTransHashSha2(),
      transactionRequestsId: transactionRequest.id,
      networkTransId: response.getTransactionResponse().getNetworkTransId() ?? '',
    });
  }
}
