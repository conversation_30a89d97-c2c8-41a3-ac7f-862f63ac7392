export class TransactionCreditCardDto {
  id: string;
  transactionRequestsId: string;
  last4: string;
  expirationDate: string;
  cardType: string;
  number: string;
  cvv: string;
  constructor(row: any) {
    this.id = row.id;
    this.transactionRequestsId = row.transactionRequestsId;
    this.last4 = row.last4;
    this.expirationDate = row.expirationDate;
    this.cardType = row.cardType;
    this.number = row.number;
    this.cvv = row.cvv;
  }
}
