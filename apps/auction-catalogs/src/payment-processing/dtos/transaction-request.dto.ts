import { IsDecimal, IsString, IsUUID } from 'class-validator';

export class TransactionRequestDto {
  readonly id: string;

  @IsString()
  readonly transactionType: string;

  @IsDecimal()
  readonly amount: number;

  @IsUUID()
  readonly invoicesId: string;

  @IsString()
  readonly description: string;

  @IsString()
  readonly invoiceNumber: string;

  @IsUUID()
  readonly invoicePaymentsId: string;

  constructor(row: any) {
    this.id = row?.id ?? undefined;
    this.transactionType = row.transactionType;
    this.amount = row.amount;
    this.invoicesId = row.invoicesId;
    this.description = row.description;
    this.invoiceNumber = row.invoiceNumber;
    this.invoicePaymentsId = row.invoicePaymentsId;
  }
}
