import { MessageDto } from '@auth-net/authorizenet/dtos/message.dto';
import { TransactionResponseDto } from './transaction-response.dto';

export class TransactionResponseDetailDto {
  responseCode: string;
  authCode: string;
  transId: string;
  accountNumber: string;
  accountType: string;
  message: MessageDto;

  constructor(transactionResponse: TransactionResponseDto) {
    this.responseCode = transactionResponse.responseCode ?? '';
    this.authCode = transactionResponse.authCode;
    this.transId = transactionResponse.transId;
    this.accountNumber = transactionResponse.accountNumber;
    this.accountType = transactionResponse.accountType;
  }
}
