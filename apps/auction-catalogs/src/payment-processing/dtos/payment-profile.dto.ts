export class PaymentProfileDto {
  id: string;
  accountMappingsId: string;
  merchantCustomerId: string;
  customerProfileId: string;
  customerPaymentProfileId: string;
  originalNetworkTransId: string;
  billingAddressId: string;

  constructor(row: any) {
    this.id = row.id;
    this.accountMappingsId = row.accountMappingsId;
    this.merchantCustomerId = row.merchantCustomerId;
    this.customerProfileId = row.customerProfileId;
    this.customerPaymentProfileId = row.customerPaymentProfileId;
    this.originalNetworkTransId = row.originalNetworkTransId;
    this.billingAddressId = row.billingAddressId;
  }
}
