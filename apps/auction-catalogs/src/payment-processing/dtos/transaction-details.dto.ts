export class TransactionDetailDto {
  readonly id: string;
  readonly invoicesId: string;
  readonly transactionType: string;
  readonly amount: number;
  readonly currentOwing: number;
  readonly invoiceNumber: string;
  readonly firstname: string;
  readonly lastname: string;
  readonly address1: string;
  readonly city: string;
  readonly state: string;
  readonly zip: string;
  readonly country: string;
  readonly responseStatus: string;
  readonly authCode: string;
  readonly transId: string;
  readonly accountNumber: string;
  readonly accountType: string;
  readonly networkTransId: string;
  readonly paymentDate: string;
  readonly code: string;
  readonly description: string;
  readonly shippingAmount: string;
  readonly auctionName: string;

  constructor(row: any) {
    this.id = row?.id ?? undefined;
    this.invoicesId = row.invoicesId;
    this.transactionType = row.transactionType;
    this.amount = row.amount;
    this.currentOwing = row.currentOwing;
    this.invoiceNumber = row.invoiceNumber;
    this.firstname = row.firstname;
    this.lastname = row.lastname;
    this.address1 = row.address1;
    this.city = row.city;
    this.state = row.state;
    this.zip = row.zip;
    this.country = row.country;
    this.responseStatus = row.responseStatus;
    this.authCode = row.authCode;
    this.transId = row.transId;
    this.accountNumber = row.accountNumber;
    this.accountType = row.accountType;
    this.networkTransId = row.networkTransId;
    this.paymentDate = row.paymentDate;
    this.code = row.code;
    this.description = row.description;
    this.shippingAmount = row.shippingAmount;
    this.auctionName = row.auctionName;
  }
}
