import { CreateTransactionResponse } from '../service/charge-credit-card.service';

export class TransactionResponseMessageDto {
  id: string;
  transactionResponsesId: string;
  code: string;
  description: string;

  constructor(row: any) {
    this.id = row.id;
    this.transactionResponsesId = row.transactionResponsesId;
    this.code = row.code;
    this.description = row.description;
  }

  static createNewFromResponse(response: CreateTransactionResponse, transactionResponse: { id: string }): TransactionResponseMessageDto {
    let code: string, description: string;

    const errors: any = response.getTransactionResponse()?.getErrors()?.getError();
    if (errors && errors.length > 0) {
      code = errors[0].getErrorCode();
      description = errors[0].getErrorText();
    } else {
      const messages: any[] = response.getMessages().getMessage();
      code = messages[0].getCode();
      description = messages[0].getText();
    }

    return new TransactionResponseMessageDto({
      transactionResponsesId: transactionResponse.id,
      code,
      description,
    });
  }
}
