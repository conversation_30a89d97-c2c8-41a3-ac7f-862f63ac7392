export class TransactionLineItemDto {
  id: string;
  transactionRequestsId: string;
  itemMappingsId: string;
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;

  constructor(row: any) {
    this.id = row.id;
    this.transactionRequestsId = row.transactionRequestsId;
    this.itemMappingsId = row.itemMappingsId;
    this.name = row.name;
    this.description = row.description;
    this.quantity = row.quantity;
    this.unitPrice = row.unitPrice;
  }
}
