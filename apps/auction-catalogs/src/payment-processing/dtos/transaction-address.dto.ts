export class TransactionAddressDto {
  readonly id: string;
  readonly transactionRequestsId: string;
  readonly firstname: string;
  readonly lastname: string;
  readonly companyName: string;
  readonly address1: string;
  readonly address2: string;
  readonly city: string;
  readonly state: string;
  readonly zip: string;
  readonly country: string;
  readonly addressType: string;

  constructor(row: any) {
    this.id = row.id;
    this.transactionRequestsId = row.transactionRequestsId;
    this.firstname = row.firstname;
    this.lastname = row.lastname;
    this.companyName = row.companyName;
    this.address1 = row.address1;
    this.address2 = row.address2;
    this.city = row.city;
    this.state = row.state;
    this.zip = row.zip;
    this.country = row.country;
    this.addressType = row.addressType;
  }
}
