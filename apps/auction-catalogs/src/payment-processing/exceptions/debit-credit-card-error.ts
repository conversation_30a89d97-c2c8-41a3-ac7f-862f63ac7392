import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { TransactionRequestDto } from '../dtos/transaction-request.dto';

export class DebitCreditCardError extends ViewbidError {
  constructor(transactionRequest: TransactionRequestDto) {
    super('Unable to process payment for transaction request id ' + transactionRequest.id, 8000, transactionRequest.id, '/payment-processing#debit-card-error');
  }
}
