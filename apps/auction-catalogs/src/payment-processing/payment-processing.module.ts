import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ItemMappingsDbService } from '../item-mappings/data/item-mappings-db.service';
import { ItemMappingsHandler } from '../item-mappings/handler/item-mappings.handler';
import { PaymentProfilesController } from './controller/payment-profiles.controller';
import { TransactionRequestsController } from './controller/transaction-requests.controller';
import { PaymentProfilesDbService } from './data/payment-profiles-db.service';
import { TransactionAddressesDbService } from './data/transaction-addresses-db.service';
import { TransactionCreditCardsDbService } from './data/transaction-credit-cards-db.service';
import { TransactionLineItemsDbService } from './data/transaction-line-items-db.service';
import { TransactionRequestsDbService } from './data/transaction-requests-db.service';
import { TransactionResponseMessagesDbService } from './data/transaction-response-messages-db.service';
import { TransactionResponsesDbService } from './data/transaction-responses-db.service';
import { TransactionShippingCostsDbService } from './data/transaction-shipping-costs-db.service';
import { PaymentProfilesHandler } from './handler/payment-profiles.handler';
import { TransactionAddressesHandler } from './handler/transaction-addresses.handler';
import { TransactionCreditCardsHandler } from './handler/transaction-credit-cards.handler';
import { TransactionLineItemsHandler } from './handler/transaction-line-items.handler';
import { TransactionRequestsHandler } from './handler/transaction-requests.handler';
import { TransactionResponseMessagesHandler } from './handler/transaction-response-messages.handler';
import { TransactionResponsesHandler } from './handler/transaction-responses.handler';
import { TransactionShippingCostsHandler } from './handler/transaction-shipping-costs.handler';
import { AddCardCommand } from './service/authorize-net-commands/add-card-command';
import { DeleteCardCommand } from './service/authorize-net-commands/delete-card-command';
import { ListCardsCommand } from './service/authorize-net-commands/list-cards-command';
import { CardManagementService } from './service/card-management.service';
import { ChargeCreditCardService } from './service/charge-credit-card.service';
import { AdminTransactionRequestsController } from './controller/transaction-admin.controller';
@Module({
  controllers: [TransactionRequestsController, PaymentProfilesController, AdminTransactionRequestsController],
  providers: [
    AddCardCommand,
    ListCardsCommand,
    DeleteCardCommand,
    ChargeCreditCardService,
    CardManagementService,
    ConfigService,
    ItemMappingsDbService,
    ItemMappingsHandler,
    TransactionAddressesHandler,
    TransactionAddressesDbService,
    TransactionCreditCardsHandler,
    TransactionCreditCardsDbService,
    TransactionLineItemsHandler,
    TransactionLineItemsDbService,
    TransactionResponseMessagesDbService,
    TransactionResponseMessagesHandler,
    TransactionRequestsController,
    TransactionRequestsHandler,
    TransactionRequestsDbService,
    TransactionResponsesDbService,
    TransactionResponsesHandler,
    TransactionShippingCostsHandler,
    TransactionShippingCostsDbService,
    PaymentProfilesHandler,
    PaymentProfilesDbService,
    PaymentProfilesController,
  ],
  exports: [TransactionRequestsController, PaymentProfilesController],
  imports: [],
})
export class PaymentProcessingModule {}
