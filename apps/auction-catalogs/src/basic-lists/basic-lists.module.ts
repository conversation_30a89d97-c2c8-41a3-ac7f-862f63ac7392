import { Modu<PERSON> } from '@nestjs/common';
import { BasicListsController } from './controller/basic-lists.controller';
import { BasicListsHandler } from './handler/basic-lists.handler';
import { BasicListsDbService } from './data/basic-lists-db.service';
@Module({
  controllers: [BasicListsController],
  providers: [BasicListsController, BasicListsDbService, BasicListsHandler],
})
export class BasicListsModule {}
