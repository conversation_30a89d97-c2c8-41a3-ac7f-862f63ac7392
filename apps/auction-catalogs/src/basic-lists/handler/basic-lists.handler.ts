import { BasicListsDbService } from '../data/basic-lists-db.service';
import { BasicListDto } from '../dtos/basic-list.dto';
import { Injectable } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { Caching<PERSON>andler } from '@vb/caching/handlers/caching-handler';
import { RedisService } from '@vb/redis';
import { SECONDS_IN_A_WEEK } from '@vb/nest/constants/time.constants';

@Injectable()
export class BasicListsHandler extends CachingHandler<BasicListsDbService, BasicListDto> {
  constructor(dbService: BasicListsDbService, redisService: RedisService) {
    super(dbService, redisService, SECONDS_IN_A_WEEK, 'basiclists');
  }

  async getAllByGroupName(groupName: string): Promise<ListResultset<BasicListDto>> {
    return await this.getListResultsetFromCache([groupName], this._getAllByGroupNameFromDb.bind(this));
  }

  private async _getAllByGroupNameFromDb(groupName: string[]): Promise<ListResultset<BasicListDto>> {
    return await this.dbService.getAllByGroupName(groupName[0]);
  }

  async getAllByBasicKey(groupName: string, basicKey: string): Promise<ListResultset<BasicListDto>> {
    return await this.getListResultsetFromCache([groupName, basicKey], this._getAllByBasicKeyFromDb.bind(this));
  }

  private async _getAllByBasicKeyFromDb(params: Array<string>): Promise<ListResultset<BasicListDto>> {
    return await this.dbService.getAllByBasicKey(params[0], params[1]);
  }
}
