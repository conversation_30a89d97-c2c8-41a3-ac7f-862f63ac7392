import { BaseController } from '@mvc/controllers/base.controller';
import { BasicListsHandler } from '../handler/basic-lists.handler';
import { BasicListRequest } from '../http/requests/basic-list.request';
import { BasicListDto } from '../dtos/basic-list.dto';
import { BasicListResponse } from '../http/responses/basic-list.response';
import { BasicListListResponse } from '../http/responses/basic-list-list.response';
import { ListResultset } from '@mvc/data/list-resultset';
import { Controller, Get, Param } from '@nestjs/common';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Constants } from '../config/constants';
import { InvalidGroupNameBasicListParameterError } from '../exceptions/invalid-group-name-basic-list-parameter.error';
import { InvalidGroupNameParameterError } from '../exceptions/invalid-group-name-parameter.error';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';

@Controller('basicLists')
export class BasicListsController extends BaseController<BasicListsHandler, BasicListRequest, BasicListDto, BasicListResponse, BasicListListResponse> {
  constructor(handler: BasicListsHandler) {
    super(handler);
  }

  createDtoFromRequest(request: BasicListRequest): BasicListDto {
    return new BasicListDto(request);
  }

  createResponseFromDto(dto: BasicListDto): BasicListResponse {
    return new BasicListResponse(dto);
  }

  createResponseList(list: ListResultset<BasicListDto>): BasicListListResponse {
    return new BasicListListResponse(list, 'basicLists');
  }

  private static readonly validTypes = [
    { type: 'regions', groupName: Constants.REGIONS },
    { type: 'categories', groupName: Constants.AUCTION_CATEGORIES },
    { type: 'locations', groupName: Constants.LOCATIONS },
    { type: 'vehicle-makes', groupName: Constants.VEHICLE_MAKES },
    { type: 'vehicle-models', groupName: Constants.VEHICLE_MODELS },
    { type: 'reminders', groupName: Constants.AUCTION_REMINDER },
    { type: 'lot-categories', groupName: Constants.LOT_CATEGORIES },
  ];

  @Anonymous()
  @Get(':groupName')
  async get(@Param('groupName') groupName: string): Promise<BasicListListResponse | ErrorResponse> {
    const validType: any = BasicListsController.validTypes.find((vt: { groupName: string; type: string }) => vt.type === groupName);

    if (!validType) {
      return new ErrorResponse(new InvalidGroupNameParameterError(groupName), 'getBasicList');
    }

    return this.getBasicListByGroupName(validType.groupName);
  }

  @Get(':parentGroupName/:parentBasicKey/:basicKey')
  async getBasicListWithKey(
    @Param('parentGroupName') parentGroupName: string,
    @Param('parentBasicKey') parentBasicKey: string,
    @Param('basicKey') basicKey: string,
  ): Promise<BasicListListResponse | ErrorResponse> {
    const validParentGroupType: any = BasicListsController.validTypes.find((vt: { groupName: string; type: string }) => vt.type === parentGroupName);
    const validBasicKey: any = BasicListsController.validTypes.find((vt: { groupName: string; type: string }) => vt.type === basicKey);
    if (!validParentGroupType || !validBasicKey) {
      return new ErrorResponse(new InvalidGroupNameBasicListParameterError(parentGroupName, basicKey), 'getBasicListWithKey');
    }

    return this.getBasicListByBasicKey(validBasicKey.groupName, parentBasicKey);
  }

  private async getBasicListByBasicKey(groupName: string, basicKey: string): Promise<BasicListListResponse | ErrorResponse> {
    try {
      const list: ListResultset<BasicListDto> = await this.handler.getAllByBasicKey(groupName, basicKey);
      return new BasicListListResponse(list, basicKey);
    } catch (error) {
      return new ErrorResponse(error, 'getBasicListByBasicKey');
    }
  }

  private async getBasicListByGroupName(groupName: string): Promise<BasicListListResponse | ErrorResponse> {
    try {
      const list: ListResultset<BasicListDto> = await this.handler.getAllByGroupName(groupName);
      return new BasicListListResponse(list, groupName);
    } catch (error) {
      return new ErrorResponse(error, 'getBasicListByGroupName');
    }
  }
}
