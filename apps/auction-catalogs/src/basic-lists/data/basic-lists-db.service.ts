import { BaseDbService } from '@mvc/data/base-db.service';
import { BasicListDto } from '../dtos/basic-list.dto';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';
import { BasicListNotFoundError } from '../exceptions/basic-list-not-found-error';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class BasicListsDbService extends BaseDbService<Prisma.BasicListsDelegate, BasicListDto> {
  constructor(prisma: PrismaService) {
    super(prisma.basicLists);
  }

  createFilter(params: any[]): any {
    params;
  }

  mapToDto(model: any): BasicListDto {
    return new BasicListDto(model);
  }

  throw404(id: string): Error {
    return new BasicListNotFoundError(id);
  }

  async getAllByGroupName(groupName: string): Promise<ListResultset<BasicListDto>> {
    const filter: any = {
      groupName: groupName,
    };
    const [items, totalCount] = await Promise.all([
      this.table.findMany({
        where: filter,
        orderBy: {
          displayOrder: 'asc',
        },
      }),
      this.table.count({
        where: filter,
      }),
    ]);

    return new ListResultset(items.map(this.mapToDto), 1, totalCount, totalCount, 1);
  }

  async getAllByBasicKey(groupName: string, basicKey: string): Promise<ListResultset<BasicListDto>> {
    const filter: any = {
      groupName: groupName,
      basicKey: basicKey,
    };
    const [items, totalCount] = await Promise.all([
      this.table.findMany({
        where: filter,
        orderBy: {
          displayOrder: 'asc',
        },
      }),
      this.table.count({
        where: filter,
      }),
    ]);

    return new ListResultset(items.map(this.mapToDto), 1, totalCount, totalCount, 1);
  }
}
