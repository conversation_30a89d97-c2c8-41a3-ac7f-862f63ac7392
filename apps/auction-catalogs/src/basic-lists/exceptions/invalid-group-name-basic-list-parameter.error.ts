import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Constants } from '../config/constants';

export class InvalidGroupNameBasicListParameterError extends ViewbidError {
  constructor(groupName: string, basicKey: string) {
    super(`Invalid group name ${groupName} and basicKey ${basicKey} specified`, Constants.INVALID_GROUP_NAME_BASIC_LIST_ERROR, groupName, 'basic-lists#invalid-group-name');
  }
}
