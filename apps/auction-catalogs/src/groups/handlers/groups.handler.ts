import { BaseHandler } from '@mvc/handlers/base.handler';
import { GroupsDbService } from '../data/groups-db.service';
import { GroupDto } from '../dtos/group.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GroupsHandler extends BaseHandler<GroupsDbService, GroupDto> {
  constructor(dbService: GroupsDbService) {
    super(dbService);
  }

  async deleteAllGroupsInAuction(auctionId: string): Promise<void> {
    await this.dbService.deleteMany('', { auctionsId: auctionId });
  }
}
