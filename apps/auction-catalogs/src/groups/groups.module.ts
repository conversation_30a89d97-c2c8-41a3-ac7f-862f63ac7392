import { forwardR<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>le } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { AuctionModule } from '../auction/auction.module';
import { GroupsController } from './controller/groups.controller';
import { GroupsDbService } from './data/groups-db.service';
import { GroupsHandler } from './handlers/groups.handler';

@Module({
  controllers: [GroupsController],
  providers: [GroupsDbService, GroupsHandler, Logger, RedisService],
  imports: [forwardRef(() => AuctionModule)],
  exports: [GroupsHandler],
})
export class GroupsModule {}
