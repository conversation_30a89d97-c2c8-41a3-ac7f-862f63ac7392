import { BaseDbService } from '@mvc/data/base-db.service';
import { Prisma } from '../../prisma/generated/auction-catalogs-db-client';

import { GroupDto } from '../dtos/group.dto';
import { Injectable } from '@nestjs/common';
import { GroupNotFoundError } from '../exceptions/group-not-found.error';
import { PrismaService } from '../../prisma/prisma.service';
import GroupsDelegate = Prisma.GroupsDelegate;

@Injectable()
export class GroupsDbService extends BaseDbService<GroupsDelegate, GroupDto> {
  constructor(table: PrismaService) {
    super(table.groups, false, false, false);
  }

  mapToDto(model: any): GroupDto {
    return new GroupDto(model);
  }

  throw404(id: string): Error {
    return new GroupNotFoundError(id);
  }
}
