import { BaseController } from '@mvc/controllers/base.controller';
import { GroupsHandler } from '../handlers/groups.handler';
import { GroupRequest } from '../http/requests/group.request';
import { GroupDto } from '../dtos/group.dto';
import { GroupResponse } from '../http/responses/group.response';
import { GroupListResponse } from '../http/responses/group-list.response';
import { Body, Controller, Get, Param, Post, Query, Req } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { AuctionsHandler } from '../../auction/handlers/auctions.handler';
import { AuctionDto } from '../../auction/dtos/auction.dto';
import { LiveAuctionGroupCreateError } from '../exceptions/live-auction-group-create-error';

@Controller('admin/')
export class GroupsController extends BaseController<GroupsHandler, GroupRequest, GroupDto, GroupResponse, GroupListResponse> {
  constructor(
    handler: GroupsHandler,
    private auctionsHandler: AuctionsHandler,
  ) {
    super(handler);
  }

  createDtoFromRequest(request: GroupRequest): GroupDto {
    return new GroupDto(request);
  }

  createResponseFromDto(dto: GroupDto): GroupResponse {
    return new GroupResponse(dto);
  }

  createResponseList(list: ListResultset<GroupDto>): GroupListResponse {
    return new GroupListResponse(list);
  }

  @Post(':auctionId/groups')
  async createGroup(@Param('auctionId') auctionId: string, @Body() body: GroupRequest, @Req() req: VBInterceptorRequest): Promise<GroupResponse | ErrorResponse> {
    try {
      const auction: AuctionDto = await this.auctionsHandler.get(auctionId);
      if (auction.isLiveAuction) {
        throw new LiveAuctionGroupCreateError();
      }
      const userId: string = req.accountMappingsId;
      const dto: GroupDto = this.createDtoFromRequest(body);
      dto.auctionsId = auction.id;

      const item: GroupDto = await this.handler.create(userId, dto);
      const response: GroupResponse = this.createResponseFromDto(item);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Get(':auctionId/groups')
  async getAllGroups(
    @Param('auctionId') auctionId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest,
  ): Promise<GroupListResponse | ErrorResponse> {
    try {
      const params: any[] = this.convertQueryToArray(req.url);
      params.push({ auctionsId: auctionId });

      const items: ListResultset<GroupDto> = await this.handler.getAll(page, size, params);
      const response: GroupListResponse = this.createResponseList(items);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  @Get(':auctionId/groups/:id')
  async getGroup(@Param('auctionId') auctionId: string, @Param('id') id: string, @Req() req: VBInterceptorRequest): Promise<GroupResponse | ErrorResponse> {
    req;
    try {
      await this.auctionsHandler.get(auctionId);
      const item: GroupDto = await this.handler.get(id);
      const response: GroupResponse = this.createResponseFromDto(item);

      return response;
    } catch (error) {
      return new ErrorResponse(error, 'get');
    }
  }
}
