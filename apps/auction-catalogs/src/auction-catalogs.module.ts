import { AccessTokenGuard } from '@http/authentication/guards/access-token.guard';
import { AccessTokenInterceptor } from '@http/authentication/interceptors/access-token.interceptor';
import { createRequestAuditInterceptor } from '@mvc/interceptors/request-audit.interceptor';
import { Logger, Module, OnApplicationShutdown, OnModuleInit } from '@nestjs/common';
import { ConfigModule as NestJsConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, ModuleRef } from '@nestjs/core';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { AuthorizationModule } from '@vb/authorization';
import { CasbinInitConfig } from '@vb/authorization/config/init-auth';
import { CasbinGuard } from '@vb/authorization/guards/casbin.guard';
import { CachingModule } from '@vb/caching';
import { HealthModule } from '@vb/health/health.module';
import { getLogger } from '@vb/nest';
import { EventManager } from '@vb/nest/events/event.manager';
import { LoggingSubscriber } from '@vb/nest/events/logging.subscriber';
import { ErrorLoggingInterceptor } from '@vb/nest/intercepters/error-logging.interceptor';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { openTelemetry } from '@vb/otel/instrumentation';
import { RedisModule } from '@vb/redis';
import { UsersApi } from '@viewbid/ts-node-client';
import { ConfigModule } from 'libs/config-service/src/config.module';
import { SharedServicesModule } from 'libs/shared_services/shared.services.module';
import * as rt from 'route-trie';
import { ApplicationPreferencesModule } from './application-preferences/app-preferences.module';
import { AuctionHousesModule } from './auction-houses/auction-houses.module';
import { AuctionLotsModule } from './auction-lots/auction-lots.module';
import { AuctionLotsDbService } from './auction-lots/data/auction-lots-db.service';
import { AuctionLotsHandler } from './auction-lots/handlers/auction-lots.handler';
import { ExtendAuctionLotClosingTimeSubscriber } from './auction-lots/subscribers/extend-auction-lot-closing-time.subscriber';
import { UpdateCurrentBidSubscriber } from './auction-lots/subscribers/update-current-bid.subscriber';
import { AuctionModule } from './auction/auction.module';
import { AuctionsDbService } from './auction/data/auctions-db.service';
import { AuctionsHandler } from './auction/handlers/auctions.handler';
import { DeleteAllAuctionGroupsSubscriber } from './auction/subscribers/delete-all-auction-groups.subscriber';
import { UpdateAuctionLotCountSubscriber } from './auction/subscribers/update-auction-lot-count.subscriber';
import { UpdateAuctionLotTimesSubscriber } from './auction/subscribers/update-auction-lot-times.subscriber';
import { S3Module } from './aws/s3/s3.module';
import { BasicListsModule } from './basic-lists/basic-lists.module';
import { BidIncrementsModule } from './bid-increments/bid-increments.module';
import { GroupsDbService } from './groups/data/groups-db.service';
import { GroupsModule } from './groups/groups.module';
import { GroupsHandler } from './groups/handlers/groups.handler';
import { AuctionCatalogCreateGraphInterceptor } from './interceptors/auction-catalog-create-graph.interceptor';
import { InvoiceTemplateModule } from './invoice-template/invoice-template.module';
import { InvoiceBuyersDbService } from './invoicing/data/invoice-buyers-db.service';
import { InvoiceStatusesDbService } from './invoicing/data/invoice-statuses-db.service';
import { InvoicesDbService } from './invoicing/data/invoices-db.service';
import { InvoiceStatusFactory } from './invoicing/factories/invoice-status.factory';
import { InvoiceBuyersHandler } from './invoicing/handlers/invoice-buyers.handler';
import { InvoiceStatusesHandler } from './invoicing/handlers/invoice-statuses.handler';
import { InvoicesHandler } from './invoicing/handlers/invoices.handler';
import { InvoicesModule } from './invoicing/invoices.module';
import { NotifyAdminBuyerShippingQuoteApprovedSubscriber } from './invoicing/subscribers/notify-admin-buyer-shipping-quote-approved.subscriber';
import { NotifyAdminShippingRequestedSubscriber } from './invoicing/subscribers/notify-admin-shipping-requested.subscriber';
import { NotifyBuyerShippingQuoteApprovedSubscriber } from './invoicing/subscribers/notify-buyer-shipping-quote-approved.subscriber';
import { NotifyBuyerTimeslotCancelledSubscriber } from './invoicing/subscribers/notify-buyer-timeslot-cancelled.subscriber';
import { NotifyInvoiceStatusUpdatedSubscriber } from './invoicing/subscribers/notify-invoice-status-updated.subscriber';
import { PaymentCompleteNotificationSubscriber } from './invoicing/subscribers/payment-complete-notification.subscriber';
import { PaymentConfirmationNotificationSubscriber } from './invoicing/subscribers/payment-confirmation-notification.subscriber';
import { SendAllInvoicesSubscriber } from './invoicing/subscribers/send-all-invoices.subscriber';
import { SetInvoiceStatusSubscriber } from './invoicing/subscribers/set-invoice-status.subscriber';
import { UserNotFoundSubscriber } from './invoicing/subscribers/user-not-found.subscriber';
import { ItemMappingsModule } from './item-mappings/item-mappings.module';
import { LotAttributesModule } from './lot-attributes/lot-attributes.module';
import { LotItemUserNotesModule } from './lot-item-user-notes/lot-item-user-notes.module';
import { TemplatesDbService } from './lot-templates/data/templates-db.service';
import { TemplatesHandler } from './lot-templates/handlers/templates.handler';
import { TemplatesModule } from './lot-templates/templates.module';
import { LotEditHistoryDbService } from './lots/data/lot-edit-history-db.service';
import { LotsDbService } from './lots/data/lots-db.service';
import { LotEditHistoryHandler } from './lots/handlers/lot-edit-history.handler';
import { LotsHandler } from './lots/handlers/lots.handler';
import { LotsModule } from './lots/lots.module';
import { NotifyWatcherLotUpdatedSubscriber } from './lots/subscribers/notify-watcher-lot-updated.subscriber';
import { SaveLotEditHistorySubscriber } from './lots/subscribers/save-lot-edit-history.subscriber';
import { SetThumbnailSubscriber } from './lots/subscribers/set-thumbnail.subscriber';
import { PaymentProcessingModule } from './payment-processing/payment-processing.module';
import { PrismaService } from './prisma/prisma.service';
import { ProductsModule } from './products/products.module';
import { PurchaseOffersModule } from './purchase-offers/purchase-offers.module';
import { PurchaseOrderItemsModule } from './purchase-order-items/purchase-order-items.module';
import { PurchaseOrdersModule } from './purchase-orders/purchase-orders.module';
import { ResourcesModule } from './resources/resources.module';
import { SharedModule } from './shared/shared.module';
import { SharedService } from './shared/shared.service';
import { SubscriptionService } from './shared/subscription.service';
import { UploadItemsModule } from './upload-items/upload-items.module';
import { UploadsModule } from './uploads/uploads.module';
import { VendorsModule } from './vendors/vendors.module';
import { CloseLotWithAcceptedOfferSubscriber } from './purchase-offers/subscribers/close-lot-with-accepted-offer.subscriber';
import { MultiCacheInvalidationSubscriber } from './shared/multi-cache-invalidation.subscriber';
import { NotifyBuyerPurchaseOfferCounteredSubscriber } from './purchase-offers/subscribers/notify-buyer-purchase-offer-countered.subscriber';
import { NotifyBuyerPurchaseOfferRejectedSubscriber } from './purchase-offers/subscribers/notify-buyer-purchase-offer-rejected.subscriber';
import { NotifyBuyerPurchaseOfferAcceptedSubscriber } from './purchase-offers/subscribers/notify-buyer-purchase-offer-accepted.subscriber';
import { NotifyAdminPurchaseOfferAcceptedSubscriber } from './purchase-offers/subscribers/notify-admin-purchase-offer-accepted.subscriber';
import { NotifyAdminPurchaseOfferCounteredSubscriber } from './purchase-offers/subscribers/notify-admin-purchase-offer-countered.subscriber';
import { NotifyAdminPurchaseOfferRejectedSubscriber } from './purchase-offers/subscribers/notify-admin-purchase-offer-rejected.subscriber';
import { ServiceToServiceGuard } from '@http/authentication/guards/service-to-service.gaurd';
@Module({
  imports: [
    SentryModule.forRoot(),
    ApplicationPreferencesModule,
    AuctionHousesModule,
    AuctionModule,
    AuctionLotsModule,
    AuthorizationModule,
    BasicListsModule,
    BidIncrementsModule,
    CachingModule,
    ConfigModule.forRoot({
      provide: 'PRISMA_SERVICE',
      useExisting: PrismaService,
    }),
    getLogger(),
    GroupsModule,
    HealthModule,
    InvoicesModule,
    InvoiceTemplateModule,
    ItemMappingsModule,
    LotAttributesModule,
    LotItemUserNotesModule,
    LotsModule,
    NestJsConfigModule.forRoot({
      envFilePath: ['./apps/auction-catalogs/.auction-catalogs.env'],
    }),
    PaymentProcessingModule,
    ProductsModule,
    PurchaseOffersModule,
    PurchaseOrderItemsModule,
    PurchaseOrdersModule,
    RedisModule,
    ResourcesModule,
    SharedModule,
    SharedServicesModule,
    S3Module,
    TemplatesModule,
    UploadItemsModule,
    UploadsModule,
    VendorsModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: AccessTokenInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AccessTokenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: CasbinGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ServiceToServiceGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: (prismaService: PrismaService): any => createRequestAuditInterceptor(prismaService),
      inject: [PrismaService],
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorLoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: async (
        prismaService: PrismaService,
        usersApi: UsersApi,
        routeTree: [rt.Trie, CasbinInitConfig],
        moduleRef: ModuleRef,
      ): Promise<AuctionCatalogCreateGraphInterceptor> => {
        return new AuctionCatalogCreateGraphInterceptor(moduleRef, usersApi, routeTree, prismaService);
      },
      inject: [PrismaService, UsersApi, 'ROUTE_TREE', ModuleRef],
    },
    AuctionsDbService,
    AuctionsHandler,
    AuctionLotsDbService,
    AuctionLotsHandler,
    AwsSqsService,
    CloseLotWithAcceptedOfferSubscriber,
    GroupsDbService,
    GroupsHandler,
    DeleteAllAuctionGroupsSubscriber,
    EventManager,
    ExtendAuctionLotClosingTimeSubscriber,
    InvoiceBuyersDbService,
    InvoiceBuyersHandler,
    InvoicesDbService,
    InvoicesHandler,
    InvoiceStatusesDbService,
    InvoiceStatusesHandler,
    Logger,
    LoggingSubscriber,
    LotEditHistoryDbService,
    LotEditHistoryHandler,
    LotsDbService,
    LotsHandler,
    MultiCacheInvalidationSubscriber,
    NotifyAdminShippingRequestedSubscriber,
    NotifyBuyerPurchaseOfferCounteredSubscriber,
    NotifyBuyerPurchaseOfferRejectedSubscriber,
    NotifyBuyerPurchaseOfferCounteredSubscriber,
    NotifyBuyerPurchaseOfferAcceptedSubscriber,
    NotifyBuyerPurchaseOfferRejectedSubscriber,
    NotifyAdminPurchaseOfferAcceptedSubscriber,
    NotifyAdminPurchaseOfferCounteredSubscriber,
    NotifyAdminPurchaseOfferRejectedSubscriber,
    NotifyBuyerTimeslotCancelledSubscriber,
    NotifyWatcherLotUpdatedSubscriber,
    NotifyInvoiceStatusUpdatedSubscriber,
    PaymentCompleteNotificationSubscriber,
    PaymentConfirmationNotificationSubscriber,
    SaveLotEditHistorySubscriber,
    SendAllInvoicesSubscriber,
    SetInvoiceStatusSubscriber,
    SetThumbnailSubscriber,
    NotifyBuyerShippingQuoteApprovedSubscriber,
    NotifyAdminBuyerShippingQuoteApprovedSubscriber,
    SubscriptionService,
    TemplatesDbService,
    TemplatesHandler,
    UserNotFoundSubscriber,
    UpdateCurrentBidSubscriber,
    UpdateAuctionLotCountSubscriber,
    UpdateAuctionLotTimesSubscriber,
    InvoiceStatusFactory,
    AwsSqsService,
    SharedService,
  ],
  exports: [AwsSqsService],
})
export class AuctionCatalogsModule implements OnApplicationShutdown, OnModuleInit {
  private readonly logger = new Logger(AuctionCatalogsModule.name);

  constructor(private readonly awsSQSService: AwsSqsService) {}
  onModuleInit(): void {
    this.awsSQSService.receiveMessageClient('deleteLotItemUserNotes');
    this.awsSQSService.receiveMessageClient('isLastLotClosed');
  }

  async onApplicationShutdown(signal?: string): Promise<void> {
    this.logger.log({ signal }, 'Shutting down...');
    try {
      await openTelemetry.shutdown();
    } catch (e) {
      this.logger.error('Error shutting down OpenTelemetry', e);
    }
    Logger.flush();
  }
}
