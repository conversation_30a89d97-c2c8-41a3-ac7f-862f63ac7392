import { Injectable } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { CachingService } from '@vb/caching';
import { AuctionLotsHandler } from '../auction-lots/handlers/auction-lots.handler';
import { AuctionLotDto } from '../auction-lots/dto/auction-lot.dto';
import { AuctionLotNotFoundError } from '../auction-lots/exceptions/auction-lot-not-found.error';

@Injectable()
export class AuctionLotsCachingService extends CachingService<AuctionLotDto> {
  constructor(
    redisService: RedisService,
    private readonly auctionLotsHandler: AuctionLotsHandler,
  ) {
    super(redisService, 3600);
  }

  async getAuctionLot(auctionLotId: string): Promise<AuctionLotDto> {
    return this.getFromCache(auctionLotId, this._getAuctionLotFromDb.bind(this));
  }

  private async _getAuctionLotFromDb(auctionLotId: string): Promise<AuctionLotDto> {
    try {
      const auctionLot: AuctionLotDto = await this.auctionLotsHandler.get(auctionLotId);
      return auctionLot;
    } catch (error) {
      this.logger.error('Error occurred during call getAuctionLot: ' + error);
      throw new AuctionLotNotFoundError(auctionLotId);
    }
  }

  static getKeyPrefix(): string {
    return 'auctionLot_';
  }
}
