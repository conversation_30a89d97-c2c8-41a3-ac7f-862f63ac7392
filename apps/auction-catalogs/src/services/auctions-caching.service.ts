import { Injectable } from '@nestjs/common';
import { CachingService } from '@vb/caching';
import { RedisService } from '@vb/redis';
import { AuctionCatalogsApi, GetAuctionById200Response } from '@viewbid/ts-node-client';
import * as axios from 'axios';
import { AuctionDto } from '../auction/dtos/auction.dto';
import { AuctionNotFoundError } from '../auction/exceptions/auction-not-found.error';

@Injectable()
export class AuctionsCachingService extends CachingService<AuctionDto> {
  constructor(
    redisService: RedisService,
    private readonly auctionsApi: AuctionCatalogsApi,
  ) {
    super(redisService, 3600);
  }

  async getAuction(auctionId: string): Promise<AuctionDto> {
    return this.getFromCache(auctionId, this._getAuctionFromDb.bind(this));
  }

  private async _getAuctionFromDb(auctionId: string): Promise<AuctionDto> {
    const auctionResponse: axios.AxiosResponse<GetAuctionById200Response> = await this.auctionsApi.getAuctionById(auctionId).catch((error: any) => {
      this.logger.error('Error occurred during call getAuctionById: ' + error);
      throw new AuctionNotFoundError(auctionId);
    });

    if (!auctionResponse || !auctionResponse.data) {
      throw new AuctionNotFoundError(auctionId);
    }

    return new AuctionDto(auctionResponse.data.data);
  }
  static getKeyPrefix(): string {
    return 'auctions_';
  }
}
