import { Injectable } from '@nestjs/common';
import { RedisService } from '@vb/redis';
import { CachingService } from '@vb/caching';
import { LotsHandler } from '../lots/handlers/lots.handler';
import { LotDto } from '../lots/dtos/lot.dto';
import { LotNotFoundError } from '../lots/exceptions/lot-not-found.error';

@Injectable()
export class LotsCachingService extends CachingService<LotDto> {
  constructor(
    redisService: RedisService,
    private readonly lotsHandler: LotsHandler,
  ) {
    super(redisService, 3600);
  }

  async getLot(lotId: string): Promise<LotDto> {
    return this.getFromCache(lotId, this._getLotFromDb.bind(this));
  }

  private async _getLotFromDb(lotId: string): Promise<LotDto> {
    try {
      const lot: LotDto = await this.lotsHandler.get(lotId);
      return lot;
    } catch (error) {
      this.logger.error('Error occurred during call getLot: ' + error);
      throw new LotNotFoundError(lotId);
    }
  }
  static getKeyPrefix(): string {
    return 'lot_';
  }
}
