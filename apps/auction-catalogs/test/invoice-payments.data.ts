// Mock data for invoice payments tests
export const invoicePaymentsData: any = {
  mockPayments: [
    {
      id: '3e46c551-3d38-4f51-b0fe-874c3a6d5b9c',
      invoicesId: '86b8fc0f-1489-4d14-867c-3bb86ec299a8',
      amountPaid: 100.0,
      paymentDate: '2025-01-15',
      createdOn: '2025-01-15',
      paymentTypes: 'OnlineCard',
      isPartial: false,
      currentOwing: 0.0,
      transactionCode: '',
      createdBy: '6b1cb3c3-9d56-42ef-8105-1e66bdc7410d', // <PERSON>
      location: 'Online',
      itemsSubtotal: 90.0,
      shippingSubtotal: 10.0,
      taxesSubtotal: 0.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 100.0,
    },
    {
      id: 'f2c7480b-e7c4-4b9a-a5aa-b6c152a4e647',
      invoicesId: '569d2134-245b-406d-afb8-94cdb5887fbb',
      amountPaid: 200.0,
      paymentDate: '2025-02-20',
      createdOn: '2025-02-20',
      paymentTypes: 'Cash',
      isPartial: true,
      currentOwing: 100.0,
      transactionCode: '',
      createdBy: 'e4e306ab-db62-4f6f-9e11-86780e6c199e', // Stuart Braidwood
      location: 'Dartmouth',
      itemsSubtotal: 250.0,
      shippingSubtotal: 30.0,
      taxesSubtotal: 20.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 300.0,
    },
    {
      id: '92a8f197-13e4-4a76-b723-fd5b5a106da9',
      invoicesId: '38580204-6096-4257-958e-7a1162999824',
      amountPaid: 150.0,
      paymentDate: '2025-03-10',
      createdOn: '2025-03-10',
      paymentTypes: 'e-transfer',
      isPartial: false,
      currentOwing: 0.0,
      transactionCode: '',
      createdBy: '77d296f7-7cb7-41c4-9c0b-32232ad33caf', // Elkeno Jones
      location: 'Online',
      itemsSubtotal: 120.0,
      shippingSubtotal: 15.0,
      taxesSubtotal: 15.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 150.0,
    },
    {
      id: 'c5d579c8-5673-4d4f-9963-4b14a3fc6092',
      invoicesId: 'ac0a281b-3f0c-494e-a4fe-48b078e4609f',
      amountPaid: 75.5,
      paymentDate: '2025-04-05',
      createdOn: '2025-04-05',
      paymentTypes: 'OnlineCard',
      isPartial: true,
      currentOwing: 25.5,
      transactionCode: '',
      createdBy: 'a095b800-ed4f-422c-8b1f-2a34d9320ed4', // Allan Dawson
      location: 'Online',
      itemsSubtotal: 80.0,
      shippingSubtotal: 10.0,
      taxesSubtotal: 11.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 101.0,
    },
    {
      id: '836a87d9-8fb0-4cef-a829-5a31256dde5b',
      invoicesId: '86b8fc0f-1489-4d14-867c-3bb86ec299a8',
      amountPaid: 50.0,
      paymentDate: '2025-05-01',
      createdOn: '2025-05-01',
      paymentTypes: 'paypal',
      isPartial: false,
      currentOwing: 0.0,
      transactionCode: '',
      createdBy: 'e4a3551a-d323-4d95-aea0-8be300d4fa35', // John Doe
      location: 'Online',
      itemsSubtotal: 40.0,
      shippingSubtotal: 5.0,
      taxesSubtotal: 5.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 50.0,
    },
  ],
  // Mock data for export tests
  mockPaymentsExport: [
    {
      id: '3e46c551-3d38-4f51-b0fe-874c3a6d5b9c',
      invoicesId: '86b8fc0f-1489-4d14-867c-3bb86ec299a8',
      amountPaid: 100.0,
      paymentDate: '2025-01-15',
      createdOn: '2025-01-15',
      paymentTypes: 'OnlineCard',
      isPartial: false,
      currentOwing: 0.0,
      transactionCode: '',
      createdBy: '6b1cb3c3-9d56-42ef-8105-1e66bdc7410d', // Dave Meikle
      location: 'Online',
      itemsSubtotal: 90.0,
      shippingSubtotal: 10.0,
      taxesSubtotal: 0.0,
      buyerPremiumTotal: 0.0,
      totalAmount: 100.0,
    },
  ],
  // Sample queries for different testing scenarios
  queries: {
    default: {
      page: 1,
      size: 10,
    },
    pagination: {
      page: 2,
      size: 5,
    },
    dateRange: {
      startDate: '2025-01-01',
      endDate: '2025-03-31',
    },
  },
  // Database filters
  filters: {
    dateRange: [{ paymentDate: { gte: new Date('2025-01-01') } }, { paymentDate: { lte: new Date('2025-03-31') } }],
  },
};
