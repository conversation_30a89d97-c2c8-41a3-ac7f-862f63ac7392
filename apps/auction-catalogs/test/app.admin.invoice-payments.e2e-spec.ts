import { Test, TestingModule } from '@nestjs/testing';
import { InvoicePaymentsController } from '../src/invoicing/controllers/admin/invoice-payments.controller';
import { InvoicePaymentsHandler } from '../src/invoicing/handlers/invoice-payments.handler';
import { DateFilterService } from '@vb/nest/services/date-filter.service';
import { InvoicePaymentQueryDto } from '../src/invoicing/dtos/invoice-payment-query.dto';
import { ListResultset } from '@mvc/data/list-resultset';
import { InvoicePaymentDto } from '../src/invoicing/dtos/invoice-payment.dto';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { RedisService } from '@vb/redis';
import { InvoicesHandler } from '../src/invoicing/handlers/invoices.handler';
import { InvoiceItemsHandler } from '../src/invoicing/handlers/invoice-items.handler';
import { InvoiceShippingHandler } from '../src/invoicing/handlers/invoice-shipping.handler';
import { TransactionRequestsController } from '../src/payment-processing/controller/transaction-requests.controller';
import { PaymentProfilesController } from '../src/payment-processing/controller/payment-profiles.controller';
import { UsersApi } from '@viewbid/ts-node-client';
import { invoicePaymentsData } from './invoice-payments.data';
import { InvoicePaymentExportDto } from '../src/invoicing/dtos/invoice-payment-export.dto';
import { InvoicePaymentsExportListResponse } from '../src/invoicing/http/responses/invoice-payments-export-list.response';

/**
 * End-to-end test suite for the InvoicePaymentsController admin endpoints
 */
describe('InvoicePaymentsController - getAllInvoicePayments', () => {
  let controller: InvoicePaymentsController;
  let handler: InvoicePaymentsHandler;
  let dateFilterService: DateFilterService;
  let usersApi: UsersApi;

  // Mock data from invoice-payments.data.ts
  const mockInvoicePaymentExportDtos: InvoicePaymentExportDto[] = invoicePaymentsData.mockPaymentsExport.map((p: any) => new InvoicePaymentExportDto(p));

  const mockPaymentsExportList: ListResultset<InvoicePaymentExportDto> = new ListResultset<InvoicePaymentExportDto>(
    mockInvoicePaymentExportDtos,
    1, // page
    10, // pageSize
    mockInvoicePaymentExportDtos.length, // totalCount
    1, // totalPages
  );

  beforeEach(async () => {
    // Setup test module with mocked dependencies
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InvoicePaymentsController],
      providers: [
        {
          provide: InvoicePaymentsHandler,
          useValue: {
            getAllCustom: jest.fn().mockResolvedValue(mockPaymentsExportList),
            getAllPaymentsForExport: jest.fn().mockResolvedValue(mockPaymentsExportList),
          },
        },
        {
          provide: DateFilterService,
          useValue: {
            createDateRangeFilter: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: RedisService,
          useValue: { publish: jest.fn() },
        },
        {
          provide: InvoicesHandler,
          useValue: {},
        },
        {
          provide: InvoiceItemsHandler,
          useValue: {},
        },
        {
          provide: InvoiceShippingHandler,
          useValue: {},
        },
        {
          provide: TransactionRequestsController,
          useValue: {},
        },
        {
          provide: PaymentProfilesController,
          useValue: {},
        },
        {
          provide: UsersApi,
          useValue: {
            getAllByAccountMappingsIds: jest.fn().mockResolvedValue({
              data: {
                data: [{ accountMappingsId: 'mock-account-id', username: 'mockuser' }],
              },
            }),
          },
        },
      ],
    }).compile();

    controller = module.get<InvoicePaymentsController>(InvoicePaymentsController);
    handler = module.get<InvoicePaymentsHandler>(InvoicePaymentsHandler);
    dateFilterService = module.get<DateFilterService>(DateFilterService);
    usersApi = module.get<UsersApi>(UsersApi);
  });

  /**
   * Tests the basic functionality of the invoice payments endpoint
   * including default pagination and response format
   */
  describe('Basic Functionality Tests', () => {
    /**
     * Verifies that the endpoint retrieves invoice payments with default pagination settings
     */
    it('should retrieve invoice payments with default pagination', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;

      const result: InvoicePaymentsExportListResponse | ErrorResponse = await controller.getAllInvoicePayments(query);

      expect(dateFilterService.createDateRangeFilter).toHaveBeenCalledWith('paymentDate', undefined, undefined);
      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(1, 10, []);
      expect(result).toBeInstanceOf(InvoicePaymentsExportListResponse);
      expect(result).toHaveProperty('data');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBe(mockInvoicePaymentExportDtos.length);
    });

    /**
     * Verifies that the endpoint handles custom pagination parameters properly
     */
    it('should return payments with custom page and size', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 2;
      query.size = 5;

      await controller.getAllInvoicePayments(query);

      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(2, 5, []);
    });

    /**
     * Verifies that the export DTO contains all the required fields
     */
    it('should return export DTO with all required fields', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;

      const result: InvoicePaymentsExportListResponse | ErrorResponse = await controller.getAllInvoicePayments(query);

      // Check that we're working with the right response type
      if (result instanceof InvoicePaymentsExportListResponse && result.data.length > 0) {
        const payment: any = result.data[0];
        // Verify all export fields are present
        expect(payment).toHaveProperty('id');
        expect(payment).toHaveProperty('invoicesId');
        expect(payment).toHaveProperty('paymentTypes');
        expect(payment).toHaveProperty('isPartial');
        expect(payment).toHaveProperty('amountPaid');
        expect(payment).toHaveProperty('currentOwing');
        expect(payment).toHaveProperty('transactionCode');
        expect(payment).toHaveProperty('createdOn');
        expect(payment).toHaveProperty('createdBy');
        expect(payment).toHaveProperty('paymentDate');
        expect(payment).toHaveProperty('location');
        expect(payment).toHaveProperty('itemsSubtotal');
        expect(payment).toHaveProperty('shippingSubtotal');
        expect(payment).toHaveProperty('taxesSubtotal');
        expect(payment).toHaveProperty('buyerPremiumTotal');
        expect(payment).toHaveProperty('totalAmount');
      }
    });
  });

  /**
   * Tests that validate input parameters are handled correctly
   */
  describe('Input Validation Tests', () => {
    /**
     * Verifies that the page parameter is validated correctly
     */
    it('should validate page parameter correctly', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;

      await controller.getAllInvoicePayments(query);

      // but in the unit test we can only verify the controller accepts valid inputs
      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(1, 10, expect.any(Array));
    });

    /**
     * Verifies that date format validation works correctly
     */
    it('should validate date formats correctly', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;
      query.startDate = '2025-01-01';
      query.endDate = '2025-01-31';

      await controller.getAllInvoicePayments(query);

      expect(dateFilterService.createDateRangeFilter).toHaveBeenCalledWith('paymentDate', '2025-01-01', '2025-01-31');
    });
  });

  /**
   * Tests for date filtering functionality
   * These tests verify that the date range filters are correctly applied
   * when querying invoice payments
   */
  describe('Date Filtering Tests', () => {
    /**
     * Verifies that both start and end date filters are applied when provided
     */
    it('should filter by date range when both startDate and endDate are provided', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.startDate = invoicePaymentsData.queries.dateRange.startDate;
      query.endDate = invoicePaymentsData.queries.dateRange.endDate;

      const mockDateFilters: any[] = invoicePaymentsData.filters.dateRange;

      jest.spyOn(dateFilterService, 'createDateRangeFilter').mockReturnValue(mockDateFilters);

      await controller.getAllInvoicePayments(query);

      expect(dateFilterService.createDateRangeFilter).toHaveBeenCalledWith('paymentDate', query.startDate, query.endDate);
      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(expect.any(Number), expect.any(Number), mockDateFilters);
    });

    /**
     * Verifies that only start date filter is applied when only start date is provided
     */
    it('should filter by only startDate when endDate is not provided', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.startDate = '2025-01-01';

      const mockDateFilters: any[] = [{ paymentDate: { gte: new Date('2025-01-01') } }];

      jest.spyOn(dateFilterService, 'createDateRangeFilter').mockReturnValue(mockDateFilters);

      await controller.getAllInvoicePayments(query);

      expect(dateFilterService.createDateRangeFilter).toHaveBeenCalledWith('paymentDate', '2025-01-01', undefined);
      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(expect.any(Number), expect.any(Number), mockDateFilters);
    });

    /**
     * Verifies that only end date filter is applied when only end date is provided
     */
    it('should filter by only endDate when startDate is not provided', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.endDate = '2025-01-31';

      const mockDateFilters: any[] = [{ paymentDate: { lte: new Date('2025-01-31') } }];

      jest.spyOn(dateFilterService, 'createDateRangeFilter').mockReturnValue(mockDateFilters);

      await controller.getAllInvoicePayments(query);

      expect(dateFilterService.createDateRangeFilter).toHaveBeenCalledWith('paymentDate', undefined, '2025-01-31');
      expect(handler.getAllPaymentsForExport).toHaveBeenCalledWith(expect.any(Number), expect.any(Number), mockDateFilters);
    });
  });

  /**
   * Tests for additional filters and mapping logic
   */
  describe('Additional Filters and Mapping Logic Tests', () => {
    /**
     * Verifies that invoiceId is added to filters if provided
     */
    it('should add invoiceId to filters if provided', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;
      query.invoiceId = 'test-invoice-id';
      jest.spyOn(dateFilterService, 'createDateRangeFilter').mockReturnValue([]);
      const spy: jest.SpyInstance = jest.spyOn(handler, 'getAllPaymentsForExport');
      await controller.getAllInvoicePayments(query);
      expect(spy).toHaveBeenCalledWith(1, 10, [{ invoiceId: 'test-invoice-id' }]);
    });

    /**
     * Verifies that usernames are mapped to payments if accountMappingsId is present
     */
    it('should map usernames to payments if accountMappingsId is present', async () => {
      const query: InvoicePaymentQueryDto = new InvoicePaymentQueryDto();
      query.page = 1;
      query.size = 10;

      const paymentWithAccountMapping: any = { ...invoicePaymentsData.mockPaymentsExport[0], accountMappingsId: 'mock-account-id' };
      const mockList: ListResultset<InvoicePaymentExportDto> = new ListResultset<InvoicePaymentExportDto>([new InvoicePaymentExportDto(paymentWithAccountMapping)], 1, 10, 1, 1);
      jest.spyOn(handler, 'getAllPaymentsForExport').mockResolvedValue(mockList);
      const usersApiSpy: jest.SpyInstance = jest.spyOn(usersApi, 'getAllByAccountMappingsIds');

      const result: InvoicePaymentsExportListResponse | ErrorResponse = await controller.getAllInvoicePayments(query);

      expect(usersApiSpy).toHaveBeenCalledWith('mock-account-id', 1, -1);
      if (result instanceof InvoicePaymentsExportListResponse) {
        expect(result.data[0].username).toBe('mockuser');
      }
    });
  });
});
