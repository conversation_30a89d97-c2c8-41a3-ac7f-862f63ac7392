import * as moment from 'moment';

const now: moment.Moment = moment();

export const MOCK_DATES: any = {
  fromDatetime: now.clone().add(6, 'minutes').toISOString(), // 5 mins ahead
  toDatetime: now.clone().add(1, 'day').toISOString(), // 1 day ahead
};

export const auctionAdminData: any = {
  CREATE: [
    {
      request: {
        name: 'Fifth Auction',
        description: 'Fifth Auction',
        fromDatetime: MOCK_DATES.fromDatetime,
        toDatetime: MOCK_DATES.toDatetime,
        status: 'DRAFT',
        isShip: false,
        isProxyBid: false,
        isReservable: false,
        isBuyNow: false,
        isMakeOffer: false,
        isQuickBid: true,
        isInlineBidding: false,
        isLargeBidConfirm: false,
        isLiveAuction: false,
        tags: 'first tag',
        code: 'AB',
        invoiceTemplatesId: '12',
        buyerPremium: 20,
        featureDisplayOrder: 2,
        paymentTypes: ['cash', 'credit_card'],
        pickupLocation: 'Dartmouth',
        taxTypesId: 1,
        termsConditions:
          '<ol>\n' +
          '<li>A buyers Fee of BID_INCREMENT% will be added to all bids.</li>\n' +
          '<li>Buyer will have to Register before bidding. By registering and bidding, the bidders acknowledge they understand and agree to the terms and conditions of the auction. Furthermore bidders acknowledge that by bidding on an item they are entering into a legal contract with Auction Advantage. Buyers are responsible for inspecting items before bidding. No returns are allowed. </li>\n' +
          '<li>Payment: Master card and Visa only, E Transfer or Bank Transfer. Payments required within 3 days of auction closing. No equipment will be removed from the sale site until payment is received by Auction Advantage. No Exceptions. </li>\n' +
          '<li>All Equipment is sold "AS-IS-WHERE-IS" with all faults  no warranties, representation or guarantees, expressed or implied  whatsoever of any kind.</li>\n' +
          '<li>Removal: Pickup by Appointment - We are allowing removal of equipment up to 2 weeks from auction closing, we can assist in making special arrangements. Pickups Must be done with Covid 19 safety procedures in place, mask and gloves.</li>\n' +
          '<li> Purchaser does hereby indemnify and hold harmless seller from any and all damages, claims, or liabilities from injuries to persons or property of any type whatsoever caused by the removal of items purchased.</li>\n' +
          '<li>While descriptions are believed to be correct, the seller makes no warranties or guarantees expressed or implied, as to the genuineness, hours or kilometers, authenticity of, or defect in any lot and will not be held responsible for advertising discrepancies or inaccuracies. No allowance will be made or no sale of any correcting of imperfection not otherwise noted. No allowance will be made for damaged items as all items are sold as is and without recourse. No warranties are made as to the merchantability of any items or their fitness for any purpose. </li>\n' +
          '<li>The seller reserves the right to withdraw any items from the sale, group items into lots, sub-divide lots into separate items and sell items in bulk. The seller also reserves the right to accept or reject any and all bids</li>\n' +
          '<li>Due the nature of web, apps and technology, Viewbid Auctions / Auction Advantage reserve the right to pause, cancel or restart the bidding of any auction if our system is experiencing errors.</li>\n' +
          '</ol>',
        invoiceText: '',
        isViewable: true,
        numLots: 1,
        isReserveMinBid: false,
        isCharity: false,
        isDisplayFinalPrice: true,
        isDisplaySoldItems: true,
        auctionType: 'test type',
        snipingExtension: 0,
        bidSnipeWindow: 0,
      },
      response: {
        name: 'Fifth Auction',
        description: 'Fifth Auction',
        fromDatetime: MOCK_DATES.fromDatetime,
        toDatetime: MOCK_DATES.toDatetime,
        status: 'DRAFT',
        isShip: 0,
        defaultTaxPercentage: 0.14,
        isProxyBid: 0,
        isReservable: 0,
        isBuyNow: 0,
        isMakeOffer: 0,
        isQuickBid: 1,
        isInlineBidding: 0,
        isLargeBidConfirm: 0,
        isLiveAuction: false,
        invoicesApproved: false,
        tags: 'first tag',
        code: 'AB',
        invoiceTemplatesId: '12',
        buyerPremium: 20,
        featureDisplayOrder: 2,
        paymentTypes: ['cash', 'credit_card'],
        pickupLocation: 'Dartmouth',
        termsConditions:
          '<ol>\n' +
          '<li>A buyers Fee of BID_INCREMENT% will be added to all bids.</li>\n' +
          '<li>Buyer will have to Register before bidding. By registering and bidding, the bidders acknowledge they understand and agree to the terms and conditions of the auction. Furthermore bidders acknowledge that by bidding on an item they are entering into a legal contract with Auction Advantage. Buyers are responsible for inspecting items before bidding. No returns are allowed. </li>\n' +
          '<li>Payment: Master card and Visa only, E Transfer or Bank Transfer. Payments required within 3 days of auction closing. No equipment will be removed from the sale site until payment is received by Auction Advantage. No Exceptions. </li>\n' +
          '<li>All Equipment is sold "AS-IS-WHERE-IS" with all faults  no warranties, representation or guarantees, expressed or implied  whatsoever of any kind.</li>\n' +
          '<li>Removal: Pickup by Appointment - We are allowing removal of equipment up to 2 weeks from auction closing, we can assist in making special arrangements. Pickups Must be done with Covid 19 safety procedures in place, mask and gloves.</li>\n' +
          '<li> Purchaser does hereby indemnify and hold harmless seller from any and all damages, claims, or liabilities from injuries to persons or property of any type whatsoever caused by the removal of items purchased.</li>\n' +
          '<li>While descriptions are believed to be correct, the seller makes no warranties or guarantees expressed or implied, as to the genuineness, hours or kilometers, authenticity of, or defect in any lot and will not be held responsible for advertising discrepancies or inaccuracies. No allowance will be made or no sale of any correcting of imperfection not otherwise noted. No allowance will be made for damaged items as all items are sold as is and without recourse. No warranties are made as to the merchantability of any items or their fitness for any purpose. </li>\n' +
          '<li>The seller reserves the right to withdraw any items from the sale, group items into lots, sub-divide lots into separate items and sell items in bulk. The seller also reserves the right to accept or reject any and all bids</li>\n' +
          '<li>Due the nature of web, apps and technology, Viewbid Auctions / Auction Advantage reserve the right to pause, cancel or restart the bidding of any auction if our system is experiencing errors.</li>\n' +
          '</ol>',
        invoiceText: '',
        isViewable: 1,
        numLots: 1,
        isReserveMinBid: 0,
        isCharity: 0,
        isDisplayFinalPrice: true,
        isDisplaySoldItems: true,
        auctionType: 'test type',
        thumbnailId: null,
        isPreviewLots: false,
        isSchedulingRequired: false,
        snipingExtension: 0,
        bidSnipeWindow: 0,
      },
    },
  ],
  READ: [
    {
      id: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
      createdOn: '2024-02-13T16:44:43.095Z',
      createdBy: 'b00c7a4d-f588-4767-b144-301225ece040',
      updatedOn: '2024-02-13T16:49:19.474Z',
      updatedBy: null,
      deletedOn: null,
      deletedBy: null,
      name: 'Viewbid IWK Foundation Charity Auction',
      description:
        'We have been commissioned to sell at online auction the following items. Items are located at 11 Mount Hope Ave Dartmouth Nova Scotia. Bid sniper protection - any bid in the last 60 seconds will extend the bidding on that item 60 seconds. Viewing By Appointment Only - Please call 902 292 0490 or 902 580 1418. Tie Bids go to the Bidder who placed the earliest bid - User name displayed on High Bidder is the High Bidder. Please make sure to Refresh your Page often during closing hitting the forward and back button on your browser may not show actual correct bid prices and winning bidder. Items will be added during the auction.',
      fromDatetime: '2024-02-13T14:00:00.000Z',
      toDatetime: '2026-03-01T13:00:00.000Z',
      isViewable: 1,
      status: 'COMPLETE',
      isShip: 1,
      isProxyBid: false,
      isReservable: false,
      isBuyNow: false,
      isMakeOffer: false,
      isQuickBid: true,
      isLiveAuction: false,
      isInlineBidding: false,
      isLargeBidConfirm: false,
      code: 'CA',
      buyerPremium: 0.0,
      paymentTypes: ['cash', 'credit_card', 'e-transfer', 'paypal'],
      thumbnailId: null,
      numLots: 5,
      pickupLocation: '',
      termsConditions: '',
      isReserveMinBid: false,
      isCharity: false,
      isDisplayFinalPrice: false,
      isDisplaySoldItems: false,
      auctionType: '',
      tags: 'tags',
      featureDisplayOrder: 0,
      invoiceTemplatesId: '********-65fb-4403-a07b-ec5ecff971cb',
      invoiceText: '',
      isPreviewLots: false,
      isSchedulingRequired: false,
      defaultTaxPercentage: 0.14,
      snipingExtension: 0,
      bidSnipeWindow: 0,
      images: [
        {
          url: 'https://d3myu8ay48rd2g.cloudfront.net/auction_images/8ebfbf13-f153-457d-ac0d-3797f8bc5029',
          name: 'Auction_test_3.png',
          type: 'image/png',
          id: '8ebfbf13-f153-457d-ac0d-3797f8bc5029',
        },
      ],
      documents: [],
    },
  ],
  UPDATE: [
    {
      id: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
      request: {
        id: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
        name: 'updated',
        description:
          'We have been commissioned to sell at online auction the following items. Items are located at 11 Mount Hope Ave Dartmouth Nova Scotia. Bid sniper protection - any bid in the last 60 seconds will extend the bidding on that item 60 seconds. Viewing By Appointment Only - Please call 902 292 0490 or 902 580 1418. Tie Bids go to the Bidder who placed the earliest bid - User name displayed on High Bidder is the High Bidder. Please make sure to Refresh your Page often during closing hitting the forward and back button on your browser may not show actual correct bid prices and winning bidder. Items will be added during the auction.',
        fromDatetime: MOCK_DATES.fromDatetime,
        toDatetime: MOCK_DATES.toDatetime,
        taxTypesId: 1,
        isViewable: 1,
        status: 'COMPLETE',
        isShip: 1,
        isProxyBid: true,
        isReservable: true,
        isBuyNow: true,
        isMakeOffer: true,
        isQuickBid: false,
        isInlineBidding: true,
        isLargeBidConfirm: true,
        isLiveAuction: false,
        code: 'CA',
        buyerPremium: 0.0,
        paymentTypes: ['cash', 'credit_card', 'e-transfer', 'paypal'],
        thumbnailId: null,
        numLots: 5,
        pickupLocation: '',
        termsConditions: '',
        isReserveMinBid: false,
        isCharity: false,
        isDisplayFinalPrice: false,
        isDisplaySoldItems: false,
        auctionType: '',
        snipingExtension: 0,
        bidSnipeWindow: 0,
        images: [
          {
            url: 'https://d3myu8ay48rd2g.cloudfront.net/auction_images/8ebfbf13-f153-457d-ac0d-3797f8bc5029',
            name: 'cars.png',
            type: 'images/png',
          },
        ],
      },
      response: {
        id: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
        name: 'updated',
        description:
          'We have been commissioned to sell at online auction the following items. Items are located at 11 Mount Hope Ave Dartmouth Nova Scotia. Bid sniper protection - any bid in the last 60 seconds will extend the bidding on that item 60 seconds. Viewing By Appointment Only - Please call 902 292 0490 or 902 580 1418. Tie Bids go to the Bidder who placed the earliest bid - User name displayed on High Bidder is the High Bidder. Please make sure to Refresh your Page often during closing hitting the forward and back button on your browser may not show actual correct bid prices and winning bidder. Items will be added during the auction.',
        fromDatetime: MOCK_DATES.fromDatetime,
        toDatetime: MOCK_DATES.toDatetime,
        isViewable: 1,
        status: 'COMPLETE',
        isShip: 1,
        isProxyBid: 1,
        isReservable: 1,
        isBuyNow: 1,
        isMakeOffer: 1,
        isQuickBid: 0,
        isInlineBidding: 1,
        isLargeBidConfirm: 1,
        isLiveAuction: false,
        invoicesApproved: false,
        code: 'CA',
        buyerPremium: 0.0,
        paymentTypes: ['cash', 'credit_card', 'e-transfer', 'paypal'],
        thumbnailId: null,
        numLots: 5,
        pickupLocation: '',
        termsConditions: '',
        isReserveMinBid: 0,
        isCharity: 0,
        isDisplayFinalPrice: false,
        isDisplaySoldItems: false,
        auctionType: '',
        tags: 'tags',
        featureDisplayOrder: 0,
        invoiceTemplatesId: '********-65fb-4403-a07b-ec5ecff971cb',
        invoiceText: '',
        isPreviewLots: false,
        isSchedulingRequired: false,
        snipingExtension: 0,
        bidSnipeWindow: 0,
        defaultTaxPercentage: 0.14,
      },
    },
  ],
  DELETE: [
    {
      id: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
    },
  ],
  EXTERNAL_SERVICES: {
    BIDDING_SERVICE: {},
    USER_SERVICE: {
      getAccountMappingsById: [
        {
          id: 'b00c7a4d-f588-4767-b144-301225ece040',
          ssoId: 'auth0|65cb90b0affd51e1baed5dde',
          user: {
            firstname: 'Kulwinder',
            lastname: 'Billen',
          },
          email: [
            {
              accountMappingId: 'ccd6fcee-e3b7-4a51-9f78-300870c43cbc',
              email: '<EMAIL>',
              id: '3ce510b4-ea92-410e-a4a3-592b677f5fe1',
            },
          ],
          accountTelephone: [
            {
              id: '45767b3c-b1e5-4aab-b363-b735ad183ff8',
              countryCode: '905',
              name: 'telephone',
              extension: '',
              number: '**********',
            },
          ],
        },
      ],
    },
  },
};
