import { Test, TestingModule } from '@nestjs/testing';
import { LotsController } from '../src/lots/controllers/admin/lots.controller';
import { LotsHandler } from '../src/lots/handlers/lots.handler';
import { AuctionLotsHandler } from '../src/auction-lots/handlers/auction-lots.handler';
import { AuctionsHandler } from '../src/auction/handlers/auctions.handler';
import { GroupsHandler } from '../src/groups/handlers/groups.handler';
import { SharedService } from '../src/shared/shared.service';
import { LotEditHistoryHandler } from '../src/lots/handlers/lot-edit-history.handler';
import { LotAttributesHandler } from '../src/lot-attributes/handler/lot-attributes.handler';
import { RedisService } from '@vb/redis';
import { ProfilesApi } from '@viewbid/ts-node-client';
import { LotDto } from '../src/lots/dtos/lot.dto';
import { AuctionLotDto } from '../src/auction-lots/dto/auction-lot.dto';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { LotStatus, AuctionLotsStatus } from '@viewbid/ts-node-client';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { InvalidAuctionLotError } from '../src/auction-lots/exceptions/invalid-auction-lot.error';

import { lotsData } from './lots.data';
import { auctionLotsData } from './auction-lots.data';
import { AuctionTaxesHandler } from '../src/auction/handlers/auction-taxes.handler';
import { LotTaxesHandler } from '../src/lots/handlers/lot-taxes.handler';

describe('LotsController - Tests', () => {
  let controller: LotsController;
  let lotsHandler: jest.Mocked<LotsHandler>;
  let auctionLotsHandler: jest.Mocked<AuctionLotsHandler>;

  const mockAccountMappingsId: string = 'mock-account-id';

  const mockLotData: any = lotsData.CREATE.response;
  const mockLot: LotDto = new LotDto({
    id: 'test-lot-id',
    title: mockLotData.title,
    status: LotStatus.Draft,
    startAmount: mockLotData.startAmount || 100,
    reserveAmount: mockLotData.reserveAmount || 500,
  });

  const mockAuctionLotData: any = auctionLotsData.READ[0];
  const mockAuctionLot: AuctionLotDto = new AuctionLotDto({
    id: mockAuctionLotData.id,
    auctionId: mockAuctionLotData.auctionId,
    lotId: mockLot.id,
    number: mockAuctionLotData.number,
    suffix: mockAuctionLotData.suffix,
    status: AuctionLotsStatus.Draft,
  });

  const mockRequest: VBInterceptorRequest = {
    accountMappingsId: mockAccountMappingsId,
  } as VBInterceptorRequest;

  beforeEach(async () => {
    const mockLotsHandler: any = {
      delete: jest.fn(),
      updateLotStatus: jest.fn(),
    };

    const mockAuctionLotsHandler: any = {
      getByLotId: jest.fn(),
      delete: jest.fn(),
      updateStatus: jest.fn(),
    };

    const mockRedisService: any = {
      publish: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [LotsController],
      providers: [
        { provide: LotsHandler, useValue: mockLotsHandler },
        { provide: AuctionLotsHandler, useValue: mockAuctionLotsHandler },
        { provide: AuctionsHandler, useValue: {} },
        { provide: GroupsHandler, useValue: {} },
        { provide: SharedService, useValue: {} },
        { provide: LotEditHistoryHandler, useValue: {} },
        { provide: LotAttributesHandler, useValue: {} },
        { provide: RedisService, useValue: mockRedisService },
        { provide: ProfilesApi, useValue: {} },
        { provide: AuctionTaxesHandler, useValue: {} },
        { provide: LotTaxesHandler, useValue: {} },
      ],
    }).compile();

    controller = module.get<LotsController>(LotsController);
    lotsHandler = module.get(LotsHandler);
    auctionLotsHandler = module.get(AuctionLotsHandler);
  });

  describe('delete', () => {
    it('should successfully delete a lot', async () => {
      const testAuctionLot: AuctionLotDto = new AuctionLotDto(auctionLotsData.READ[0]);
      auctionLotsHandler.getByLotId.mockResolvedValue(testAuctionLot);
      lotsHandler.delete.mockResolvedValue(undefined);
      auctionLotsHandler.delete.mockResolvedValue(undefined);

      const result: void | ErrorResponse = await controller.delete(testAuctionLot.lotId, mockRequest);

      expect(auctionLotsHandler.getByLotId).toHaveBeenCalledWith(testAuctionLot.lotId);
      expect(lotsHandler.delete).toHaveBeenCalledWith(mockAccountMappingsId, testAuctionLot.lotId, testAuctionLot.auctionId);
      expect(auctionLotsHandler.delete).toHaveBeenCalledWith(mockAccountMappingsId, testAuctionLot.id);
      expect(result).toBeUndefined();
    });

    it('should handle auction lot not found error during delete', async () => {
      const testAuctionLot: AuctionLotDto = new AuctionLotDto(auctionLotsData.READ[0]);
      const mockError: InvalidAuctionLotError = new InvalidAuctionLotError('test-id');

      jest.spyOn(controller as any, 'executeAndNotify').mockImplementation((_action: string, callback: () => Promise<any>) => {
        return callback().catch((error: any) => {
          if (error instanceof InvalidAuctionLotError) {
            return undefined;
          }
          return new ErrorResponse(error, 'deleteLotById');
        });
      });

      auctionLotsHandler.getByLotId.mockResolvedValue(testAuctionLot);
      lotsHandler.delete.mockResolvedValue(undefined);
      auctionLotsHandler.delete.mockRejectedValue(mockError);

      const result: void | ErrorResponse = await controller.delete(testAuctionLot.lotId, mockRequest);

      expect(result).toBeUndefined();
    });

    it('should return ErrorResponse when an error occurs during deletion', async () => {
      const mockError: InvalidAuctionLotError = new InvalidAuctionLotError('Failed to delete');
      auctionLotsHandler.getByLotId.mockRejectedValue(mockError);

      const result: void | ErrorResponse = await controller.delete(mockLot.id, mockRequest);

      expect(result).toBeInstanceOf(ErrorResponse);
    });
  });

  describe('archiveLot', () => {
    it('should archive a lot successfully', async () => {
      const archivedLot: LotDto = new LotDto({
        ...mockLotData,
        id: mockLot.id,
        status: LotStatus.Archived,
      });

      lotsHandler.updateLotStatus.mockResolvedValue(archivedLot);
      auctionLotsHandler.getByLotId.mockResolvedValue(mockAuctionLot);
      auctionLotsHandler.updateStatus.mockResolvedValue(1);

      jest.spyOn(controller as any, 'executeAndNotify').mockImplementation((_action: string, callback: () => Promise<any>) => callback());

      const result: LotDto | ErrorResponse = await controller.archiveLot(mockLot.id, mockRequest);

      expect(lotsHandler.updateLotStatus).toHaveBeenCalledWith(mockAccountMappingsId, mockLot.id, LotStatus.Archived, 'Lot archived by admin');
      expect(auctionLotsHandler.updateStatus).toHaveBeenCalledWith(mockAuctionLot.id, AuctionLotsStatus.Archived);
      expect(result).toEqual(archivedLot);
    });

    it('should return ErrorResponse when archiving fails', async () => {
      const mockError: Error = new Error('Failed to archive lot');
      lotsHandler.updateLotStatus.mockRejectedValue(mockError);

      jest.spyOn(controller as any, 'executeAndNotify').mockImplementation((_action: string, callback: () => Promise<any>) => callback());

      const result: LotDto | ErrorResponse = await controller.archiveLot(mockLot.id, mockRequest);

      expect(result).toBeInstanceOf(ErrorResponse);
    });
  });

  describe('unarchiveLot', () => {
    it('should unarchive a lot successfully', async () => {
      const unarchivedLot: LotDto = new LotDto({
        ...mockLotData,
        id: mockLot.id,
        status: LotStatus.Draft,
      });

      lotsHandler.updateLotStatus.mockResolvedValue(unarchivedLot);
      auctionLotsHandler.getByLotId.mockResolvedValue(mockAuctionLot);
      auctionLotsHandler.updateStatus.mockResolvedValue(1);

      jest.spyOn(controller as any, 'executeAndNotify').mockImplementation((_action: string, callback: () => Promise<any>) => callback());

      const result: LotDto | ErrorResponse = await controller.unarchiveLot(mockLot.id, mockRequest);

      expect(lotsHandler.updateLotStatus).toHaveBeenCalledWith(mockAccountMappingsId, mockLot.id, LotStatus.Draft, 'Lot unarchived by admin');
      expect(auctionLotsHandler.updateStatus).toHaveBeenCalledWith(mockAuctionLot.id, AuctionLotsStatus.Draft);
      expect(result).toEqual(unarchivedLot);
    });

    it('should return ErrorResponse when unarchiving fails', async () => {
      const mockError: Error = new Error('Failed to unarchive lot');
      lotsHandler.updateLotStatus.mockRejectedValue(mockError);

      jest.spyOn(controller as any, 'executeAndNotify').mockImplementation((_action: string, callback: () => Promise<any>) => callback());

      const result: LotDto | ErrorResponse = await controller.unarchiveLot(mockLot.id, mockRequest);

      expect(result).toBeInstanceOf(ErrorResponse);
    });
  });
});
