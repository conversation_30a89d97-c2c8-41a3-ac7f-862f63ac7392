export const lotsAdminCRUDData: any = {
  CREATE: [
    {
      request: {
        title: 'Second Lot Title',
        description: 'Second Lot Description',
        status: 'Published',
        code: 'NA',
        reserveAmount: 0.0,
        tags: 'NA',
        taxTypesId: 1,
        condition: 'Lightly used',
        category: 'General Items',
        region: 'Nova Scotia',
        pickupLocation: '',
        subtitle: '',
        consignorNumber: '',
        excludedRegions: [''],
        isRegionSpecific: 0,
        inputDevice: 'Web',
        thumbnailId: null,
        inputMethod: 'Barcode',
      },
      response: {
        id: '00000000-0000-0000-0000-000000000000',
        category: 'General Items',
        code: 'NA',
        condition: 'Lightly used',
        consignorNumber: '',
        description: 'Second Lot Description',
        excludedRegions: [''],
        inputDevice: 'Web',
        inputMethod: 'Barcode',
        isRegionSpecific: 0,
        taxPercentage: 0.14,
        pickupLocation: '',
        region: 'Nova Scotia',
        reserveAmount: 0,
        status: 'Published',
        subtitle: '',
        tags: 'NA',
        thumbnailId: null,
        title: 'Second Lot Title',
        attributes: [],
      },
    },
  ],
  READ: [
    {
      id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      createdOn: '2024-02-13T20:10:32.488Z',
      createdBy: 'b00c7a4d-f588-4767-b144-301225ece040',
      updatedOn: '2024-02-13T21:00:00.098Z',
      updatedBy: null,
      auctionsId: '7e80a25b-e04e-4fa8-8ec7-3bd902a5e1f4',
      title: 'Panasonic ES-SL83-S, Arc3 Electric Shaver 3-Blade Cordless Razor with Wet Dry Co',
      description: 'Panasonic ES-SL83-S, Arc3 Electric Shaver 3-Blade Cordless Razor with Wet Dry Convenience for Men',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0,
      tags: 'NA',
      suffix: 'NA',
      taxPercentage: 0.14,
      condition: 'New / Sealed box',
      consignorNumber: '',
      category: 'General Items',
      region: 'Nova Scotia',
      number: 3,
      displayOrder: 0,
      images: [{ url: 'https://d3myu8ay48rd2g.cloudfront.net/lot_item_images/36391c9b-1b32-40f2-80cc-26f6278d92d8' }],
      documents: [],
      toDatetime: '2026-02-23T13:00:00.000Z',
      subtitle: '',
      pickupLocation: '',
      excludedRegions: [''],
      inputDevice: 'Web',
      inputMethod: 'Barcode',
      isRegionSpecific: 0,
      thumbnailId: null,
      attributes: [],
    },
  ],
  UPDATE: [
    {
      id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      request: {
        updatedOn: '2024-03-12, 8:23:26 p.m.',
        updatedBy: 'auth0|7f0e791660196c28eeb2cc9d',
        title: 'Car Play 9" touch smart screen',
        description: 'Car Play 9" touch smart screen Retail $99.99',
        status: 'Published',
        code: 'NA',
        reserveAmount: 0,
        tags: 'NA',
        taxTypesId: 1,
        condition: 'New / Damaged packaging',
        category: 'General Items',
        region: 'Nova Scotia',
        pickupLocation: 'Dartmouth',
        consignorNumber: '',
        excludedRegions: [''],
        inputDevice: 'Web',
        inputMethod: 'Barcode',
        isRegionSpecific: 0,
        subtitle: '',
        thumbnailId: null,
        reason: 'automated testing',
      },
      response: {
        title: 'Car Play 9" touch smart screen',
        description: 'Car Play 9" touch smart screen Retail $99.99',
        status: 'Published',
        code: 'NA',
        reserveAmount: 0,
        tags: 'NA',
        taxPercentage: 0.14,
        condition: 'New / Damaged packaging',
        category: 'General Items',
        region: 'Nova Scotia',
        pickupLocation: 'Dartmouth',
        consignorNumber: '',
        excludedRegions: [''],
        id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
        inputDevice: 'Web',
        inputMethod: 'Barcode',
        isRegionSpecific: 0,
        subtitle: '',
        thumbnailId: null,
        attributes: [],
      },
    },
  ],
  DELETE: [
    {
      id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
    },
  ],
  EXTERNAL_SERVICES: {
    BIDDING_SERVICE: {
      getLotBiddingDataByAuctionLotIds: [
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 400,
          biddingData: [
            {
              bidCount: 100,
              currentPrice: 400,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 200,
          biddingData: [
            {
              bidCount: 50,
              currentPrice: 200,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
      ],
    },
    USER_SERVICE: {
      getAccountMappingsById: [
        {
          id: 'b00c7a4d-f588-4767-b144-301225ece040',
          ssoId: 'auth0|65cb90b0affd51e1baed5dde',
          user: {
            firstname: 'Kulwinder',
            lastname: 'Billen',
          },
          email: [
            {
              accountMappingId: 'ccd6fcee-e3b7-4a51-9f78-300870c43cbc',
              email: '<EMAIL>',
              id: '3ce510b4-ea92-410e-a4a3-592b677f5fe1',
            },
          ],
          accountTelephone: [
            {
              id: '45767b3c-b1e5-4aab-b363-b735ad183ff8',
              countryCode: '905',
              name: 'telephone',
              extension: '',
              number: '**********',
            },
          ],
        },
      ],
    },
  },
};
