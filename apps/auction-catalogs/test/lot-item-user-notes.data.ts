export const lotItemUserNotesData: any = {
  CREATE: [
    {
      note: 'This is a test note',
      lotsId: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
    },
  ],
  READ: [
    {
      id: '800f0e28-1ec4-4028-96c2-5b5f6cbef55c',
      note: 'This is a test note',
      lotsId: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
    },
  ],
  UPDATE: [
    {
      id: '800f0e28-1ec4-4028-96c2-5b5f6cbef55c',
      note: 'This is not a test note',
      lotsId: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
    },
  ],
  DELETE: [
    {
      id: '800f0e28-1ec4-4028-96c2-5b5f6cbef55c',
    },
  ],
};
