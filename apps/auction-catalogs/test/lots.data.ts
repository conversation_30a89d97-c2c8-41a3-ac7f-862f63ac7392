export const lotsData: any = {
  CREATE: {
    request: {
      title: 'Second Lot Title',
      description: 'Second Lot Description',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0.0,
      tags: 'NA',
      taxTypesId: 1,
      condition: 'Lightly used',
      category: 'General Items',
      region: 'Nova Scotia',
      pickupLocation: '',
      subtitle: '',
      consignorNumber: '',
      excludedRegions: [''],
      isRegionSpecific: false,
      inputDevice: 'Web',
      thumbnailId: null,
      inputMethod: 'Barcode',
      buyNowPrice: 1,
      reason: '',
      number: 1,
      displayOrder: 1,
      startAmount: 0.0,
      isAcceptOffers: false,
      nextBidMeetsReserve: false,
    },
    response: {
      title: 'Second Lot Title',
      description: 'Second Lot Description',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0.0,
      tags: 'NA',
      condition: 'Lightly used',
      category: 'General Items',
      region: 'Nova Scotia',
      pickupLocation: '',
      subtitle: '',
      consignorNumber: '',
      excludedRegions: [''],
      isRegionSpecific: false,
      inputDevice: 'Web',
      thumbnailId: null,
      inputMethod: 'Barcode',
      attributes: [],
      toDatetime: '',
      startAmount: '0',
      isAcceptOffers: false,
      nextBidMeetsReserve: false,
      taxPercentage: 0.14,
    },
  },
  READ: [
    {
      id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
      createdOn: '2024-02-13T20:10:32.488Z',
      createdBy: 'b00c7a4d-f588-4767-b144-301225ece040',
      updatedOn: '2024-02-13T21:00:00.098Z',
      updatedBy: null,
      auctionsId: '7e80a25b-e04e-4fa8-8ec7-3bd902a5e1f4',
      auctionLotsId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
      title: 'Panasonic ES-SL83-S, Arc3 Electric Shaver 3-Blade Cordless Razor with Wet Dry Co',
      description: 'Panasonic ES-SL83-S, Arc3 Electric Shaver 3-Blade Cordless Razor with Wet Dry Convenience for Men',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0,
      tags: 'NA',
      suffix: 'NA',
      condition: 'New / Sealed box',
      consignorNumber: '',
      category: 'General Items',
      region: 'Nova Scotia',
      minBidAmount: 1,
      number: 3,
      displayOrder: 0,
      bidSnipeWindow: 0,
      images: [
        {
          url: 'https://d3myu8ay48rd2g.cloudfront.net/lot_item_images/36391c9b-1b32-40f2-80cc-26f6278d92d8',
          name: 'Panasonic.jpg',
          type: 'image/jpeg',
          id: '36391c9b-1b32-40f2-80cc-26f6278d92d8',
        },
      ],
      toDatetime: '2026-02-23T13:00:00.000Z',
      subtitle: '',
      excludedRegions: [''],
      inputDevice: 'Web',
      inputMethod: 'Barcode',
      isRegionSpecific: false,
      thumbnailId: null,
      documents: [],
      pickupLocation: '',
      buyNowPrice: 0,
      currentPrice: '0',
      watcherId: 'd3309e3e-529d-4559-9cf6-f768f04bc631',
      editHistory: [],
      bidCount: 0,
      numberOfWatchers: 0,
      highBidder: '',
      highBidderId: '',
      isHighestBidAProxy: 0,
      startAmount: '0',
      isAcceptOffers: false,
      nextBidMeetsReserve: false,
      taxPercentage: 0.14,
    },
  ],
  UPDATE: {
    id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
    request: {
      updatedOn: '2024-03-12, 8:23:26 p.m.',
      updatedBy: 'auth0|7f0e791660196c28eeb2cc9d',
      title: 'Car Play 9" touch smart screen',
      description: 'Car Play 9" touch smart screen Retail $99.99',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0,
      tags: 'NA',
      taxTypesId: 1,
      condition: 'New / Damaged packaging',
      category: 'General Items',
      region: 'Nova Scotia',
      pickupLocation: 'Dartmouth',
      consignorNumber: '',
      excludedRegions: [''],
      inputDevice: 'Web',
      inputMethod: 'Barcode',
      isRegionSpecific: false,
      subtitle: '',
      thumbnailId: null,
      reason: '',
      startAmount: 0,
      isAcceptOffers: false,
      nextBidMeetsReserve: false,
    },
    response: {
      updatedOn: '2024-03-12, 8:23:26 p.m.',
      updatedBy: 'auth0|7f0e791660196c28eeb2cc9d',
      title: 'Car Play 9" touch smart screen',
      description: 'Car Play 9" touch smart screen Retail $99.99',
      status: 'Published',
      code: 'NA',
      reserveAmount: 0,
      tags: 'NA',
      condition: 'New / Damaged packaging',
      category: 'General Items',
      region: 'Nova Scotia',
      pickupLocation: 'Dartmouth',
      consignorNumber: '',
      excludedRegions: [''],
      inputDevice: 'Web',
      inputMethod: 'Barcode',
      isRegionSpecific: false,
      subtitle: '',
      thumbnailId: null,
      attributes: [],
      toDatetime: '',
      startAmount: '0',
      isAcceptOffers: false,
      nextBidMeetsReserve: false,
      taxPercentage: 0.14,
    },
  },
  DELETE: [
    {
      id: '11e3f24b-38f3-49db-9c78-b620cd8f6afc',
    },
  ],

  EXTERNAL_SERVICES: {
    BIDDING_SERVICE: {
      getLotBiddingDataByAuctionLotIds: [
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 400,
          biddingData: [
            {
              bidCount: 100,
              currentPrice: 400,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 200,
          biddingData: [
            {
              bidCount: 50,
              currentPrice: 200,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
      ],
      watchedItemsByUser: [
        {
          id: 'd3309e3e-529d-4559-9cf6-f768f04bc631',
          auctionsid: '7e80a25b-e04e-4fa8-8ec7-3bd902a5e1f4',
          auctionLotsid: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
          watcherType: 'Bidder',
          createdOn: '2024-02-14T18:39:19.943Z',
          createdBy: 'b00c7a4d-f588-4767-b144-301225ece040',
          deletedOn: null,
          deletedBy: null,
          watcherId: 'd3309e3e-529d-4559-9cf6-f768f04bc631',
        },
      ],
    },
    USER_SERVICE: {
      getAccountMappingsById: [
        {
          id: 'b00c7a4d-f588-4767-b144-301225ece040',
          ssoId: 'auth0|65cb90b0affd51e1baed5dde',
          user: {
            firstname: 'Kulwinder',
            lastname: 'Billen',
          },
          email: [
            {
              accountMappingId: 'ccd6fcee-e3b7-4a51-9f78-300870c43cbc',
              email: '<EMAIL>',
              id: '3ce510b4-ea92-410e-a4a3-592b677f5fe1',
            },
          ],
          accountTelephone: [
            {
              id: '45767b3c-b1e5-4aab-b363-b735ad183ff8',
              countryCode: '905',
              name: 'telephone',
              extension: '',
              number: '**********',
            },
          ],
        },
      ],
    },
  },
};
