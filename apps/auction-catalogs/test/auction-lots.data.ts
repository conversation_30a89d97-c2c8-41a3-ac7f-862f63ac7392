export const auctionLotsData: any = {
  CREATE: [
    {
      request: {
        auctionId: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
        lotId: '05958241-9f13-4d3c-15ec-146c0c5cc786',
        nextBidMeetsReserve: false,
        number: 1,
        displayOrder: 1,
        groupsId: null,
        status: 'PUBLISHED',
        toDatetime: '2024-06-26T04:44:50.196Z',
        currentBid: 0,
        currentHighBidderId: '',
        suffix: '',
        buyNowPrice: 0,
      },
      response: {
        auctionId: 'c45d2da5-04c5-45a4-8dc9-b9416120362a',
        lotId: '05958241-9f13-4d3c-15ec-146c0c5cc786',
        nextBidMeetsReserve: false,
        number: 1,
        displayOrder: 1,
        groupsId: null,
        status: 'PUBLISHED',
        toDatetime: '2024-06-26T04:44:50.196Z',
        currentBid: 0,
        currentHighBidderId: '',
        suffix: '',
        buyNowPrice: 0,
        taxPercentage: 0.14,
      },
    },
  ],
  READ: [
    {
      id: 'cf839992-2394-41a7-b99a-75cd6a6944de',
      auctionId: '387d8c57-78c3-49f3-9952-d9c5121c4db1',
      currentBid: 0,
      currentHighBidderId: '',
      displayOrder: 0,
      lotId: '76989cc2-cfee-4907-832a-1f9fc3e858c9',
      nextBidMeetsReserve: false,
      number: 1,
      status: 'PUBLISHED',
      groupsId: null,
      toDatetime: '2026-03-29T21:00:00.000Z',
      suffix: 'NA',
      buyNowPrice: 0,
      taxPercentage: 0.14,
    },
  ],
  UPDATE: [
    {
      id: 'cf839992-2394-41a7-b99a-75cd6a6944de',
      request: {
        id: 'cf839992-2394-41a7-b99a-75cd6a6944de',
        auctionId: '387d8c57-78c3-49f3-9952-d9c5121c4db1',
        currentBid: 1,
        currentHighBidderId: 'test',
        displayOrder: 3,
        lotId: '76989cc2-cfee-4907-832a-1f9fc3e858c9',
        nextBidMeetsReserve: false,
        number: 2,
        groupsId: null,
        status: 'PUBLISHED',
        toDatetime: '2024-02-29T21:00:00.000Z',
        suffix: '',
        buyNowPrice: 0,
      },
      response: {
        id: 'cf839992-2394-41a7-b99a-75cd6a6944de',
        auctionId: '387d8c57-78c3-49f3-9952-d9c5121c4db1',
        currentBid: 1,
        currentHighBidderId: 'test',
        displayOrder: 3,
        lotId: '76989cc2-cfee-4907-832a-1f9fc3e858c9',
        nextBidMeetsReserve: false,
        number: 2,
        groupsId: null,
        status: 'PUBLISHED',
        toDatetime: '2024-02-29T21:00:00.000Z',
        suffix: '',
        buyNowPrice: 0,
        taxPercentage: 0.14,
      },
    },
  ],
  DELETE: [
    {
      id: 'cf839992-2394-41a7-b99a-75cd6a6944de',
    },
  ],
  LIST_AUCTION_LOTS_FLATTENED_BY_IDS_BASE_GET: [
    {
      id: ['cf839992-2394-41a7-b99a-75cd6a6944de', 'ad33ed04-2745-43a2-835f-98e474b4b4d8', '893e6629-ba7c-43d4-82f2-7fe9494409f5', '9f1f2033-fe2c-4bed-989f-a368fa9e18cf'],
      page: 1,
      size: 10,
    },
  ],
  EXTERNAL_SERVICES: {
    BIDDING_SERVICE: {
      getLotBiddingDataByAuctionLotIds: [
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 400,
          biddingData: [
            {
              bidCount: 100,
              currentPrice: 400,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
        {
          auctionLotId: 'ee79098e-aec9-4bf5-a1e3-29cb152f1a31',
          currentPrice: 200,
          biddingData: [
            {
              bidCount: 50,
              currentPrice: 200,
              highBidder: 'b00c7a4d-f588-4767-b144-301225ece040',
              numberOfWatchers: 100,
            },
          ],
        },
      ],
    },
    USER_SERVICE: {
      getAccountMappingsById: [
        {
          id: 'b00c7a4d-f588-4767-b144-301225ece040',
          ssoId: 'auth0|65cb90b0affd51e1baed5dde',
          user: {
            firstname: 'Kulwinder',
            lastname: 'Billen',
          },
          email: [
            {
              accountMappingId: 'ccd6fcee-e3b7-4a51-9f78-300870c43cbc',
              email: '<EMAIL>',
              id: '3ce510b4-ea92-410e-a4a3-592b677f5fe1',
            },
          ],
          accountTelephone: [
            {
              id: '45767b3c-b1e5-4aab-b363-b735ad183ff8',
              countryCode: '905',
              name: 'telephone',
              extension: '',
              number: '**********',
            },
          ],
        },
      ],
    },
  },
};
