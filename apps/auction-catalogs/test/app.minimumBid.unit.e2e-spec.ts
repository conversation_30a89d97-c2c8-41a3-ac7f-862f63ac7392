import { ListResultset } from '@mvc/data/list-resultset';
import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { Test, TestingModule } from '@nestjs/testing';
import { AuctionCatalogsModule } from '../src/auction-catalogs.module';
import { AuctionLotsModule } from '../src/auction-lots/auction-lots.module';
import { AuctionLotFlattenedWatchlistDto } from '../src/auction-lots/dto/auction-lot-flattened-watchlist.dto';
import { AuctionLotListHighlightedDto } from '../src/auction-lots/dto/auction-lot-list-highlighted.dto';
import { AuctionLotsHandler } from '../src/auction-lots/handlers/auction-lots.handler';
import { AuctionDto } from '../src/auction/dtos/auction.dto';
import { LotsController } from '../src/lots/controllers/admin/lots.controller';
import { LotDto } from '../src/lots/dtos/lot.dto';
import { LotsModule } from '../src/lots/lots.module';
import { SharedService } from '../src/shared/shared.service';
import { INestApplication } from '@nestjs/common';

describe('MinimumBid Calculation', () => {
  let lotController: LotsController;
  let auctionLotsHandler: AuctionLotsHandler;
  let sharedService: SharedService;
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [AuctionCatalogsModule, LotsModule, AuctionLotsModule],
    }).compile();

    app = moduleRef.createNestApplication();
    await app.init();

    lotController = moduleRef.get(LotsController);
    auctionLotsHandler = moduleRef.get(AuctionLotsHandler);
    sharedService = moduleRef.get(SharedService);
  }, 30000);

  it('setStartAmount - isReserveMinBid - False', async () => {
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(0, 50);
    const startAmount: number = await lotController.setStartAmount(auction, lot);
    expect(startAmount).toBe(0);
  });

  it('setStartAmount - isReserveMinBid - True', async () => {
    const auction: AuctionDto = getAuction(true);
    const lot: LotDto = getLot(0, 50);
    const startAmount: number = await lotController.setStartAmount(auction, lot);
    expect(startAmount).toBe(50);
  });

  it('setStartAmount - start > reserve', async () => {
    const auction: AuctionDto = getAuction(true);
    const lot: LotDto = getLot(40, 20);
    try {
      await lotController.setStartAmount(auction, lot);
    } catch (error) {
      expect(error).toBeInstanceOf(ViewbidError);
    }
  });

  it('setStartAmount - isReserveMinBid - False - startAmount = 0', async () => {
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(0, 20);
    const startAmount: number = await lotController.setStartAmount(auction, lot);
    expect(startAmount).toBe(0);
  });

  it('setStartAmount - isReserveMinBid - False - startAmount > 0', async () => {
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(10, 20);
    const startAmount: number = await lotController.setStartAmount(auction, lot);
    expect(startAmount).toBe(10);
  });

  it('listAuctionLotsFlattenedByIds', async () => {
    const ids: string[] = ['cf839992-2394-41a7-b99a-75cd6a6944de', 'ad33ed04-2745-43a2-835f-98e474b4b4d8', '893e6629-ba7c-43d4-82f2-7fe9494409f5'];
    const page: number = 1;
    const size: number = 10;
    const result: ListResultset<AuctionLotFlattenedWatchlistDto> = await auctionLotsHandler.listAuctionLotsFlattenedByIds(page, size, ids);

    const auctionLotcf8: AuctionLotFlattenedWatchlistDto | undefined = result.list.find((auctionLot: any) => auctionLot.auctionLotId == 'cf839992-2394-41a7-b99a-75cd6a6944de');
    const auctionLot893: AuctionLotFlattenedWatchlistDto | undefined = result.list.find((auctionLot: any) => auctionLot.auctionLotId == '893e6629-ba7c-43d4-82f2-7fe9494409f5');

    expect(auctionLot893!.minimumBidAmount).toBe(1);
    expect(auctionLot893!.currentBid.toString()).toEqual('0');
    expect(auctionLot893!.isReserveMinBid).toBe(0);
    expect(auctionLot893!.reserveAmount.toString()).toBe('0');

    expect(auctionLotcf8!.minimumBidAmount).toBe(1);
    expect(auctionLotcf8!.currentBid.toString()).toEqual('0');
    expect(auctionLotcf8!.isReserveMinBid).toBe(0);
    expect(auctionLotcf8!.reserveAmount.toString()).toBe('0');
  });

  it('calculateMinimumBidAmount - SharedService - isReserveMinBid - False', async () => {
    // Get the next bid increment
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(0, 50);
    const result: number = await sharedService.calculateMinimumBidAmount(0, 1, lot, auction);
    expect(result).toBe(1);
  });

  it('calculateMinimumBidAmount - SharedService - isReserveMinBid - True', async () => {
    // Get the minReserveBid
    const auction: AuctionDto = getAuction(true);
    const lot: LotDto = getLot(5, 50);
    const result: number = await sharedService.calculateMinimumBidAmount(0, 1, lot, auction);
    expect(result).toBe(50);
  });

  it('calculateMinimumBidAmount - SharedService - isReserveMinBid - False - Start Amount - 5', async () => {
    // Get the startAmount
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(5, 50);
    const result: number = await sharedService.calculateMinimumBidAmount(0, 1, lot, auction);
    expect(result).toBe(5);
  });

  it('calculateMinimumBidAmount - SharedService - isReserveMinBid - False - Start Amount - 5 - currentBid > startAmount', async () => {
    // Get the currentBid + increment, because currentBid higher than startAmount
    const auction: AuctionDto = getAuction(false);
    const lot: LotDto = getLot(5, 50);
    const result: number = await sharedService.calculateMinimumBidAmount(10, 1, lot, auction);
    expect(result).toBe(11);
  });

  it('calculateHighlightedMinimumBidAmount - SharedService - isReserveMinBid - False', async () => {
    // Get the next bid increment - start amount = 0
    const auction: AuctionLotListHighlightedDto = {
      isReserveMinBid: false,
      reserveAmount: 50,
      startAmount: 0,
    } as AuctionLotListHighlightedDto;

    const result: number = await sharedService.calculateHighlightedMinimumBidAmount(0, 1, auction);
    expect(result).toBe(1);
  });

  it('calculateHighlightedMinimumBidAmount - SharedService - isReserveMinBid - True', async () => {
    // Get the next bid increment - start amount = 0
    const auction: AuctionLotListHighlightedDto = {
      isReserveMinBid: true,
      reserveAmount: 50,
      startAmount: 5,
    } as AuctionLotListHighlightedDto;

    const result: number = await sharedService.calculateHighlightedMinimumBidAmount(0, 1, auction);
    expect(result).toBe(50);
  });

  it('calculateHighlightedMinimumBidAmount - SharedService - isReserveMinBid - False - Start Amount = 5', async () => {
    // Get the startAmount
    const auction: AuctionLotListHighlightedDto = {
      isReserveMinBid: false,
      reserveAmount: 50,
      startAmount: 5,
    } as AuctionLotListHighlightedDto;

    const result: number = await sharedService.calculateHighlightedMinimumBidAmount(0, 1, auction);
    expect(result).toBe(5);
  });

  it('calculateHighlightedMinimumBidAmount - SharedService - isReserveMinBid - False - Start Amount - 5 - currentBid > startAmount', async () => {
    // Get the startAmount
    const auction: AuctionLotListHighlightedDto = {
      isReserveMinBid: false,
      reserveAmount: 50,
      startAmount: 5,
    } as AuctionLotListHighlightedDto;

    const result: number = await sharedService.calculateHighlightedMinimumBidAmount(10, 1, auction);
    expect(result).toBe(11);
  });

  it('calculateHighlightedMinimumBidAmount - SharedService - isReserveMinBid - False - Start Amount - 5 - currentBid > startAmount', async () => {
    // Get the startAmount
    const auction: AuctionLotListHighlightedDto = {
      isReserveMinBid: false,
      reserveAmount: 50,
      startAmount: 2,
    } as AuctionLotListHighlightedDto;

    const result: number = await sharedService.calculateHighlightedMinimumBidAmount(10, 1, auction);
    expect(result).toBe(11);
  });

  afterAll(async () => {
    await app.close();
  });
});

function getAuction(isReserveMinBid: boolean): AuctionDto {
  return {
    isReserveMinBid: isReserveMinBid,
  } as unknown as AuctionDto;
}

function getLot(startAmount: number, reserveAmount: number): LotDto {
  return {
    startAmount: startAmount,
    reserveAmount: reserveAmount,
  } as unknown as LotDto;
}
