import { Test, TestingModule } from '@nestjs/testing';
import { AuctionCatalogsModule } from '../src/auction-catalogs.module';
import { INestApplication } from '@nestjs/common';
import { PurchaseOffersController } from '../src/purchase-offers/controllers/admin/purchase-offers.controller';
import { PurchaseOffersHandler } from '../src/purchase-offers/handlers/purchase-offers.handler';
import { PurchaseOffersModule } from '../src/purchase-offers/purchase-offers.module';
import { ListResultset } from '@mvc/data/list-resultset';
import { PurchaseOfferListDto } from '../src/purchase-offers/dtos/purchase-offer-list.dto';
import { PurchaseOfferDto } from '../src/purchase-offers/dtos/purchase-offer.dto';
import { PurchaseOfferStatus } from '../src/prisma/generated/auction-catalogs-db-client';
import { AuctionLotDto } from '../src/auction-lots/dto/auction-lot.dto';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { PurchaseOffersService } from '../src/purchase-offers/services/purchase-offers.service';
import { BidResponse } from 'apps/bidding/src/bids/http/responses/bid.response';
import { LotsHandler } from '../src/lots/handlers/lots.handler';
import { AuctionLotsHandler } from '../src/auction-lots/handlers/auction-lots.handler';
import { BiddingApi } from '@viewbid/ts-node-client';
import { BidDto } from 'apps/bidding/src/bids/dtos/bid.dto';

describe('Purchase offers', () => {
  let app: INestApplication;
  let purchaseOffersController: PurchaseOffersController;
  let purchaseOffersHandler: PurchaseOffersHandler;
  let purchaseOfferService: PurchaseOffersService;

  const mockItemMappingId: string = 'mapping-id';
  const mockPurchaseOfferDto: PurchaseOfferDto = {
    id: 'some-id',
    accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
    status: PurchaseOfferStatus.Pending,
    message: '',
    price: 50,
    quantity: 1,
    itemMappingsId: mockItemMappingId,
  } as PurchaseOfferDto;

  const mockAuctionLot: AuctionLotDto = {} as AuctionLotDto;
  const mockUser: VBInterceptorRequest = {
    accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
  } as VBInterceptorRequest;

  const mockPurchaseOffersHandler: PurchaseOffersHandler = {
    create: jest.fn().mockResolvedValue(mockPurchaseOfferDto),
    update: jest.fn().mockImplementation(async (_accountMappingId: string, purchaseOffer: PurchaseOfferDto, _poId: string) => purchaseOffer),
  } as unknown as PurchaseOffersHandler;

  const mockAuctionLotsHandler: AuctionLotsHandler = {
    update: jest.fn().mockImplementation(async (_accountMappingsId: string, auctionLot: AuctionLotDto, _id: string) => auctionLot),
  } as unknown as AuctionLotsHandler;

  const mockLotsHandler: LotsHandler = {
    updateStatuses: jest.fn().mockImplementation(async (_ids: string[], _status: string) => {}),
  } as unknown as LotsHandler;

  const mockBiddingApi: BiddingApi = {
    removeHigherBids: jest.fn().mockResolvedValue({
      data: {
        amount: mockPurchaseOfferDto.price,
        accountMappingsId: mockPurchaseOfferDto.accountMappingsId,
      } as BidDto,
    } as BidResponse),
  } as unknown as BiddingApi;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [AuctionCatalogsModule, PurchaseOffersModule],
    }).compile();

    app = moduleRef.createNestApplication();
    await app.init();

    purchaseOffersController = moduleRef.get(PurchaseOffersController);
    purchaseOffersHandler = moduleRef.get(PurchaseOffersHandler);
    purchaseOfferService = new PurchaseOffersService(mockLotsHandler, mockAuctionLotsHandler, mockBiddingApi, mockPurchaseOffersHandler);
  }, 30000);

  describe('Fetch and filter purchase offers', () => {
    it('gets list of offers', async () => {
      const page: number = 1;
      const size: number = 10;
      const params: any[] = [];
      const offers: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page, size, params);

      expect(offers.list.length).toBeGreaterThan(0);
    });

    it('gets list of offers by username', async () => {
      const page: number = 1;
      const size: number = 10;
      const params: any[] = [{ OR: [{ 'po."accountMappingsId"': { in: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2' } }] }];
      jest.spyOn(purchaseOffersController, 'addAccountMapping').mockImplementationOnce(async (query: string, params: Record<string, any>[]) => params);

      const offers: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page, size, params);

      expect(offers.list.length).toBeGreaterThan(0);
    });

    it('gets list of offers by auctionId', async () => {
      const page: number = 1;
      const size: number = 10;
      const params: any[] = [{ 'a."id"': 'c1130055-e0ab-4109-a6b8-c64a90a534fc' }];

      const offersList: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page, size, []);
      const offersListFiltered: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page, size, params);

      expect(offersList.list.length).toBeGreaterThan(0);
      expect(offersListFiltered.list.length).toBeLessThan(offersList.list.length);
    });

    it('gets list of offers page 2', async () => {
      const page1: number = 1;
      const page2: number = 2;
      const size: number = 1;
      const params: any[] = [{ OR: [{ 'po."accountMappingsId"': { in: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2' } }] }];
      jest.spyOn(purchaseOffersController, 'addAccountMapping').mockImplementationOnce(async (query: string, params: Record<string, any>[]) => params);

      const offers1: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page1, size, params);
      const offers2: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page2, size, params);

      expect(offers1.list.length).toBeGreaterThan(0);
      expect(offers2.list.length).toBeGreaterThan(0);
      expect(offers1).not.toEqual(offers2);
    });

    it('gets list of offers - no results', async () => {
      const page: number = 1;
      const size: number = 10;
      const params: any[] = [{ OR: [{ 'po."accountMappingsId"': { in: '4d679c43-9851-4acd-a664-e6572a84b0da' } }] }];
      jest.spyOn(purchaseOffersController, 'addAccountMapping').mockImplementationOnce(async (query: string, params: Record<string, any>[]) => params);
      const offers: ListResultset<PurchaseOfferListDto> = await purchaseOffersHandler.getAllOffers(page, size, params);

      expect(offers.list.length).toBe(0);
    });
  });

  describe('Updating purchase offers', () => {
    it('sets offer to rejected', async () => {
      const result: PurchaseOfferDto = await purchaseOfferService.setOfferStatus(mockPurchaseOfferDto, mockUser.accountMappingsId, 'Rejected');

      expect(result.status).toBe(PurchaseOfferStatus.Rejected);
      expect(mockPurchaseOffersHandler.update).toHaveBeenCalled();
    });

    it('set item to sold', async () => {
      const result: PurchaseOfferDto = await purchaseOfferService.setItemSold(mockPurchaseOfferDto, mockAuctionLot, mockUser.accountMappingsId, 'Accepted');

      expect(mockPurchaseOffersHandler.update).toHaveBeenCalled();
      expect(mockAuctionLotsHandler.update).toHaveBeenCalled();
      expect(mockLotsHandler.updateStatuses).toHaveBeenCalled();
      expect(mockBiddingApi.removeHigherBids).toHaveBeenCalled();
      expect(result.status).toBe(PurchaseOfferStatus.Accepted);
    });
  });

  afterAll(async () => {
    await app.close();
  });
});
