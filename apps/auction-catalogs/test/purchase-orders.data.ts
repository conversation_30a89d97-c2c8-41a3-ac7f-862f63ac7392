export const purchaseOrdersData: any = {
  CREATE: {
    request: {
      shippingMethod: 'Standard',
      shippingTerms: 'TBA',
      deliveryDate: new Date('2024-04-30 09:00:00.000').toISOString(),
      subTotal: 1000,
      tax: 0,
      other: 0,
      total: 1000,
      comments: 'Tax-free for reasons',
      vendorsId: 'a81d68b8-9605-4e3e-b88f-e181bcd83b6d',
      auctionIds: ['387d8c57-78c3-49f3-9952-d9c5121c4db1'],
    },
    response: {
      shippingMethod: 'Standard',
      shippingTerms: 'TBA',
      deliveryDate: new Date('2024-04-30 09:00:00.000').toISOString(),
      subTotal: 1000,
      tax: 0,
      other: 0,
      total: 1000,
      comments: 'Tax-free for reasons',
      vendorsId: 'a81d68b8-9605-4e3e-b88f-e181bcd83b6d',
      auctionIds: ['387d8c57-78c3-49f3-9952-d9c5121c4db1'],
    },
  },
  READ: {
    id: '4d6bb92b-8f71-4a69-98cd-daa7051e9e39',
    response: {
      id: '4d6bb92b-8f71-4a69-98cd-daa7051e9e39',
      shippingMethod: 'Standard',
      shippingTerms: 'TBA',
      deliveryDate: '2024-04-30T09:00:00.000Z',
      subTotal: 100,
      tax: 15,
      other: 0,
      total: 115,
      comments: '100 dollars plus HST from vendor guy',
      vendorsId: 'a81d68b8-9605-4e3e-b88f-e181bcd83b6d',
      auctionIds: [],
    },
  },
  UPDATE: {
    id: '4d6bb92b-8f71-4a69-98cd-daa7051e9e39',
    request: {
      shippingMethod: 'Standard',
      shippingTerms: 'TBA',
      deliveryDate: new Date('2024-04-30 09:00:00.000').toISOString(),
      subTotal: 100,
      tax: 15,
      other: 0,
      total: 115,
      comments: '100 dollars plus HST from vendor guy. Added an auction ID.',
      vendorsId: 'a81d68b8-9605-4e3e-b88f-e181bcd83b6d',
      auctionIds: ['387d8c57-78c3-49f3-9952-d9c5121c4db1'],
    },
    response: {
      id: '4d6bb92b-8f71-4a69-98cd-daa7051e9e39',
      shippingMethod: 'Standard',
      shippingTerms: 'TBA',
      deliveryDate: new Date('2024-04-30 09:00:00.000').toISOString(),
      subTotal: 100,
      tax: 15,
      other: 0,
      total: 115,
      comments: '100 dollars plus HST from vendor guy. Added an auction ID.',
      vendorsId: 'a81d68b8-9605-4e3e-b88f-e181bcd83b6d',
      auctionIds: ['387d8c57-78c3-49f3-9952-d9c5121c4db1'],
    },
  },
  DELETE: {
    id: '4d6bb92b-8f71-4a69-98cd-daa7051e9e39',
  },
};
