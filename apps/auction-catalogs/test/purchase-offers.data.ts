const resolvedOn: string = new Date().toISOString();

export const purchaseOffersData: any = {
  CREATE: {
    request: {
      itemMappingsId: '7a989311-9a09-4ae5-a8b6-4a2432a7a1dd',
      quantity: 1,
      status: 'Rejected',
      message: '',
      price: '10',
      isAdmin: false,
      resolvedOn: resolvedOn,
      accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
    },
    response: {
      itemMappingsId: '7a989311-9a09-4ae5-a8b6-4a2432a7a1dd',
      quantity: 1,
      status: 'Rejected',
      message: '',
      price: '10',
      isAdmin: false,
      resolvedOn: resolvedOn,
      accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
    },
  },
  READ: {
    id: '0293f436-6539-49e9-8570-7c23e97f2b5e',
    response: {
      id: '0293f436-6539-49e9-8570-7c23e97f2b5e',
      itemMappingsId: '21f178a3-5b85-4d94-a3aa-3846f8e3976d',
      quantity: 1,
      status: 'Accepted',
      message: 'offer up',
      price: 15.0,
      isAdmin: true,
      resolvedOn: resolvedOn,
      accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
    },
  },
  UPDATE: {
    id: '22b25694-c768-46ab-9b52-53e6cc041adb',
    request: {
      itemMappingsId: '7a989311-9a09-4ae5-a8b6-4a2432a7a1dd',
      quantity: 1,
      status: 'Accepted',
      message: 'Higher counter from admin',
      price: '30',
      isAdmin: true,
      resolvedOn: resolvedOn,
    },
    response: {
      itemMappingsId: '7a989311-9a09-4ae5-a8b6-4a2432a7a1dd',
      quantity: 1,
      status: 'Accepted',
      message: 'Higher counter from admin',
      price: '30',
      isAdmin: true,
      resolvedOn: resolvedOn,
      accountMappingsId: 'ef4ad208-28db-4cd0-a398-842a7f11f9f2',
    },
  },
  DELETE: [{ id: 'c3600192-f407-437b-87d1-bf2feaaafc93' }],
};
