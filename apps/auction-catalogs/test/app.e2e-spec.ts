import { INestApplication } from '@nestjs/common';
import { Test, TestingModule, TestingModuleBuilder } from '@nestjs/testing';
import { baseTestCases } from '@vb/nest/test-utils/test-cases/base-test-cases';
import { BASE_CRUD_TESTS } from '@vb/nest/test-utils/test-cases/test-cases-enums';
import { BaseResourceUrls, ResourceUrls, TestResources } from '@vb/nest/test-utils/utils/test-resources';
import { initializeTestingModule } from '@vb/nest/test-utils/utils/test-utils';
import { RedisService } from '@vb/redis';
import * as _ from 'lodash';
import { RedisClientType } from 'redis';
import { AuctionCatalogsModule } from '../src/auction-catalogs.module';
import { ExtensionArgs } from '../src/prisma/generated/auction-catalogs-db-client/runtime/library';
import { PrismaService } from '../src/prisma/prisma.service';
import { auctionAdminData } from './auction-admin.data';
import { auctionLotsData } from './auction-lots.data';
import { auctionPublicData } from './auction-public.data';
import { bidIncrementsData } from './bid-increments.data';
import { invoiceTemplatesData } from './invoice-templates.data';
import { lotItemUserNotesData } from './lot-item-user-notes.data';
import { lotsData } from './lots.data';
import { purchaseOrdersData } from './purchase-orders.data';
import { resourceData } from './resources.data';
import { vendorsData } from './vendors.data';
import { purchaseOffersData } from './purchase-offers.data';

let models: { [key: string]: ExtensionArgs } = {};
let app: INestApplication;
let testResources: TestResources;
let prisma: PrismaService;
let redisClient: RedisClientType | null;
let redisService: RedisService;
const dataFiles: any = {
  AUCTIONS_PUBLIC: auctionPublicData,
  AUCTIONS_ADMIN: auctionAdminData,
  LOTS: lotsData,
  AUCTION_LOTS: auctionLotsData,
  PURCHASE_ORDERS_ADMIN: purchaseOrdersData,
  INVOICE_TEMPLATES: invoiceTemplatesData,
  VENDORS_ADMIN: vendorsData,
  BID_INCREMENTS: bidIncrementsData,
  RESOURCES: resourceData,
  LOT_ITEM_USER_NOTES: lotItemUserNotesData,
  PURCHASE_OFFERS_ADMIN: purchaseOffersData,
};

const urls: BaseResourceUrls = {
  AUCTIONS_PUBLIC: {
    [BASE_CRUD_TESTS.READ]: 'auctions',
  },
  AUCTIONS_ADMIN: {
    [BASE_CRUD_TESTS.CREATE]: 'admin/auctions',
    [BASE_CRUD_TESTS.READ]: 'admin/auctions',
    [BASE_CRUD_TESTS.UPDATE]: 'admin/auctions',
    [BASE_CRUD_TESTS.DELETE]: 'admin/auctions',
  },
  LOTS: {
    [BASE_CRUD_TESTS.CREATE]: 'admin/lots',
    [BASE_CRUD_TESTS.READ]: 'lots',
    [BASE_CRUD_TESTS.UPDATE]: 'admin/lots',
    [BASE_CRUD_TESTS.DELETE]: 'admin/lots',
  },
  AUCTION_LOTS: {
    [BASE_CRUD_TESTS.CREATE]: 'auction-lots',
    [BASE_CRUD_TESTS.READ]: 'auction-lots',
    [BASE_CRUD_TESTS.UPDATE]: 'auction-lots',
    [BASE_CRUD_TESTS.DELETE]: 'auction-lots',
  },
  RESOURCES: {
    [BASE_CRUD_TESTS.CREATE]: 'resources',
    [BASE_CRUD_TESTS.READ]: 'resources',
    [BASE_CRUD_TESTS.UPDATE]: 'resources',
    [BASE_CRUD_TESTS.DELETE]: 'resources',
  },
  INVOICE_TEMPLATES: {
    [BASE_CRUD_TESTS.CREATE]: 'invoiceTemplate',
    [BASE_CRUD_TESTS.READ]: 'invoiceTemplate',
    [BASE_CRUD_TESTS.UPDATE]: 'invoiceTemplate',
    [BASE_CRUD_TESTS.DELETE]: 'invoiceTemplate',
  },
  BID_INCREMENTS: {
    [BASE_CRUD_TESTS.CREATE]: 'bid-increments',
    [BASE_CRUD_TESTS.UPDATE]: 'bid-increments',
  },
  VENDORS_ADMIN: {
    [BASE_CRUD_TESTS.CREATE]: 'admin/vendors',
    [BASE_CRUD_TESTS.READ]: 'admin/vendors',
    [BASE_CRUD_TESTS.UPDATE]: 'admin/vendors',
    [BASE_CRUD_TESTS.DELETE]: 'admin/vendors',
  },
  PURCHASE_ORDERS_ADMIN: {
    [BASE_CRUD_TESTS.CREATE]: 'admin/purchase-orders',
    [BASE_CRUD_TESTS.READ]: 'admin/purchase-orders',
    [BASE_CRUD_TESTS.UPDATE]: 'admin/purchase-orders',
    [BASE_CRUD_TESTS.DELETE]: 'admin/purchase-orders',
  },
  LOT_ITEM_USER_NOTES: {
    CREATE: 'lotItemUserNotes',
    READ: 'lotItemUserNotes',
    UPDATE: 'lotItemUserNotes',
    DELETE: 'lotItemUserNotes',
  },
  PURCHASE_OFFERS_ADMIN: {
    [BASE_CRUD_TESTS.CREATE]: 'admin/purchase-offers',
    [BASE_CRUD_TESTS.UPDATE]: 'admin/purchase-offers',
    [BASE_CRUD_TESTS.DELETE]: 'admin/purchase-offers',
  },
};

describe('AuctionCatalogsController (e2e)', () => {
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    const moduleFixtureBuilder: TestingModuleBuilder = Test.createTestingModule({
      imports: [AuctionCatalogsModule],
    });

    initializeTestingModule(moduleFixtureBuilder, dataFiles);
    moduleFixture = await moduleFixtureBuilder.compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    prisma = await app.get<PrismaService>(PrismaService, { strict: false });

    redisService = await app.get<RedisService>(RedisService, { strict: false });
    redisClient = redisService.getClient();
    await redisClient?.flushAll();

    models = {
      AUCTIONS_PUBLIC: prisma.auctions,
      AUCTIONS_ADMIN: prisma.auctions,
      AUCTION_LOTS: prisma.auctionLots,
      RESOURCES: prisma.resources,
      LOTS: prisma.lots,
      BID_INCREMENTS: prisma.bidIncrements,
      INVOICE_TEMPLATES: prisma.invoiceTemplates,
      VENDORS_ADMIN: prisma.vendors,
      PURCHASE_ORDERS_ADMIN: prisma.purchaseOrders,
      PURCHASE_OFFERS_ADMIN: prisma.purchaseOffers,
      LOT_ITEM_USER_NOTES: prisma.lotItemUserNotes,
    };
  }, 30000);

  testResources = new TestResources(urls, dataFiles, prisma);
  const testCases: any = baseTestCases<ExtensionArgs>;
  _.forEach(testResources.urls, (resourceUrls: ResourceUrls, modelName: string) => {
    _.forEach(resourceUrls, (url: string, method: string) => {
      it(
        'BaseTestCases - ' + modelName + ' - ' + method,
        async () => {
          await testCases(app, testResources)[method](models[modelName], modelName);
        },
        30000,
      );
    });
  });

  afterAll(async () => {
    await app.close();
    if (prisma) {
      await prisma.$disconnect();
    }
    if (redisService) {
      const client: RedisClientType | null = redisService.getClient();
      if (client) {
        await client.disconnect();
      }
    }
    if (moduleFixture) {
      await moduleFixture.close();
    }
  });
});
