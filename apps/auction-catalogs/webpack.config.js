const { sentryWebpackPlugin } = require("@sentry/webpack-plugin");

module.exports = {
  // ... other options
  devtool: "source-map", // Source map generation must be turned on
  output: {
    filename: "./apps/auction-catalogs/[name].js",
    sourceMapFilename: "./apps/auction-catalogs/[name].js.map"
  },
  plugins: [
    // Put the Sentry Webpack plugin after all other plugins
    sentryWebpackPlugin({
      disable: process.env.WEBPACK_SENTRY_MODE !== 'on',
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: "viewbid",
      project: "auction-catalogs",
    }),
  ],
}
