-- Update ApplicationPreferences table <NAME_EMAIL> with <EMAIL>
UPDATE "ApplicationPreferences"
SET "value" = CASE
    WHEN "preferenceKey" = 'ADMIN_EMAIL' THEN '<EMAIL>'
    WHEN "preferenceKey" = 'DEFAULT_ADMIN_INFO' THEN REPLACE("value", '"administratorEmail": "<EMAIL>"', '"administratorEmail": "<EMAIL>"')
    ELSE "value"
END
WHERE ("preferenceKey" = 'ADMIN_EMAIL' AND "value" = '<EMAIL>')
   OR ("preferenceKey" = 'DEFAULT_ADMIN_INFO' AND "value" LIKE '%"administratorEmail": "<EMAIL>"%');
