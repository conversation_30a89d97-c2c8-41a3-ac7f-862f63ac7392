import { IsDeployable } from '../../imports/data/contracts/is-deployable';
import { IsBuildable } from '../../imports/data/contracts/is-buildable';
import { ImportHeaderDto } from '../../imports/dtos/import-header.dto';
import { ImportModel } from '../../imports/data/contracts/import-model';

export class AccountPreferencesImportModel implements ImportModel, IsBuildable, IsDeployable {
  getDeploymentTablename(): string {
    return AccountPreferencesImportModel.DEPLOYMENT_TABLE;
  }

  static DEPLOYMENT_TABLE = 'public."AccountPreferencesDeploy"';
  getTablename(): string {
    return AccountPreferencesImportModel.DEPLOYMENT_TABLE;
  }
  toSqlClearPreviousData(): string {
    return `DELETE FROM ${AccountPreferencesImportModel.DEPLOYMENT_TABLE}`;
  }
  toSqlCopyToDeploymentTable(importHeader: ImportHeaderDto): string {
    importHeader;
    return `
        INSERT INTO ${AccountPreferencesImportModel.DEPLOYMENT_TABLE} (
            id,
            preferences,
            "registrationStatus",
            "spendingCap",
            "accountMappingsId",
            "createdOn",
            "updatedOn",
            "createdBy",
            "updatedBy"
        )
        SELECT uuid_generate_v4(),
               jsonb_build_object(
                       'defaultEmail', jsonb_agg(ued.email::text), -- Explicit cast to text
                       'defaultPhone', jsonb_agg(atd."number"::text), -- Explicit cast to text
                       'defaultLanguage', jsonb_agg('EN'::text), -- Explicit cast to text
                       'defaultAddressId', aad.id::text, -- Explicit cast to text
                       'notificationPreferences', (
                           SELECT jsonb_agg(
                               jsonb_build_object(
                                   'preferenceKey', np.preference_key,
                                   'channels', jsonb_build_object(
                                       'SMS', true,
                                       'email', true
                                   )
                               )
                           )
                           FROM (
                               SELECT unnest(ARRAY[
                                   'INVOICE_STATUS_UPDATED_NOTIFICATION',
                                   'USER_ACCOUNT_APPROVED',
                                   'NEW_HIGH_BID_NOTIFICATION',
                                   'PROXY_BID_EXCEEDED',
                                   'NEW_USER_REGISTRATION',
                                   'NEW_USER_REGISTRATION_ADMIN',
                                   'AUCTION_OPEN_NOTIFICATION',
                                   'WINNING_BID_NOTIFICATION',
                                   'AUCTION_CLOSING_SOON_PRICE',
                                   'NEW_USER_REGISTRATION_EMAIL_CONFIRM',
                                   'USER_NOT_FOUND',
                                   'NOTIFY_ADMIN_PURCHASE_OFFER_REJECTED',
                                   'LOT_UPDATE_NOTIFICATION',
                                   'NOTIFY_ADMIN_PURCHASE_OFFER_COUNTERED',
                                   'NOTIFY_BUYER_PURCHASE_OFFER_COUNTERED',
                                   'AUCTION_CLOSING_SOON',
                                   'NOTIFY_BUYER_PURCHASE_OFFER_ACCEPTED',
                                   'SHIPPING_QUOTE_NOTIFICATION',
                                   'TIMESLOT_CANCELLED_NOTIFICATION',
                                   'ADMIN_SHIPPING_REQUEST_NOTIFICATION',
                                   'AUCTION_OPENING_SOON',
                                   'NEW_USER_MESSAGE_ADMIN_NOTIFICATION',
                                   'NOTIFY_BUYER_PURCHASE_OFFER_REJECTED',
                                   'NOTIFY_ADMIN_PURCHASE_OFFER_ACCEPTED',
                                   'SHIPPING_QUOTE_APPROVED_ADMIN_NOTIFICATION',
                                   'NEW_USER_MESSAGE_NOTIFICATION',
                                   'PAYMENT_CONFIRMATION_NOTIFICATION',
                                   'LOT_CLOSED_NOTIFICATION',
                                   'NOTIFY_ADMIN_NEW_PURCHASE_OFFER',
                                   'LOT_CLOSING_SOON_NOTIFICATION',
                                   'BUYER_INVOICE_ARCHIVED',
                                   'AUCTION_REMINDER_NOTIFICATION',
                                   'RAISE_ALARM',
                                   'ACCOUNT_MIGRATION'
                               ]) AS preference_key
                           ) np
                       ),
                       'roles',
                       COALESCE(
                           CASE
                               WHEN urf.roles IS NOT NULL AND jsonb_array_length(urf.roles) > 0
                               THEN (
                                   SELECT jsonb_agg(DISTINCT role_element)
                                   FROM jsonb_array_elements_text(urf.roles || '["IS_USER", "IS_ANONYMOUS"]'::jsonb) AS role_element
                               )
                               ELSE '["IS_USER", "IS_ANONYMOUS", "IS_BIDDER"]'::jsonb  -- Default roles if none imported
                           END,
                           '["IS_USER", "IS_ANONYMOUS", "IS_BIDDER"]'::jsonb  -- Fallback default
                       )
               )                 AS preferences,
               'CONFIRM'         AS "registrationStatus", -- Explicit cast to text
               500                 AS "spendingCap",
               uim."id"          AS "accountMappingsId",
               CURRENT_TIMESTAMP AS "createdOn",
               CURRENT_TIMESTAMP AS "updatedOn",
               '********-0000-0000-0000-************'::uuid AS "createdBy",
               '********-0000-0000-0000-************'::uuid AS "updatedBy"
        FROM
            public."AccountEmailsDeploy" ued
            LEFT JOIN
            public."UsersIdMapping" uim
        ON
            ued.email = uim.email
            LEFT JOIN
            public."AccountTelephonesDeploy" atd
            ON
            uim."id":: uuid = atd."accountMappingsId"
            LEFT JOIN
            public."AccountAddressesDeploy" aad
            ON
            uim."id":: uuid = aad."accountMappingsId"
            LEFT JOIN
            public."UserRawFiles" urf
            ON
            uim.email = urf."emailAddress"
        GROUP BY
            ued.id, uim."id", aad.id, urf.roles;
        `;
  }

  toSqlCopyToProductionTable(): string[] {
    return [
      `INSERT INTO public."AccountPreferences" (
                id,
                preferences,
                "registrationStatus",
                "spendingCap",
                "accountMappingsId",
                "createdOn",
                "updatedOn",
                "createdBy",
                "updatedBy"
            )
             SELECT
                 id,
                 preferences,
                 "registrationStatus",
                 "spendingCap",
                 "accountMappingsId",
                 "createdOn",
                 "updatedOn",
                 "createdBy",
                 "updatedBy"
             FROM ${AccountPreferencesImportModel.DEPLOYMENT_TABLE}
             ON CONFLICT ("accountMappingsId")
            DO UPDATE SET
                preferences = EXCLUDED.preferences,
                "registrationStatus" = EXCLUDED."registrationStatus",
                "spendingCap" = EXCLUDED."spendingCap",
                "updatedOn" = EXCLUDED."updatedOn",
                "updatedBy" = EXCLUDED."updatedBy";
            `,
    ];
  }
}
