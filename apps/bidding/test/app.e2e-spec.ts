import { Test, TestingModule, TestingModuleBuilder } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { BiddingModule } from '../src/bidding.module';
import { ExtensionArgs } from '../src/prisma/generated/bidding-db-client/runtime/library';
import { PrismaService } from '../src/prisma/prisma.service';
import { biddingData } from './bidding-data';
import { watchersData } from './watchers-data';
import { RedisService } from '@vb/redis';
import { RedisClientType } from 'redis';
import { baseTestCases } from '@vb/nest/test-utils/test-cases/base-test-cases';
import { BASE_CRUD_TESTS, BIDDING_TESTS } from '@vb/nest/test-utils/test-cases/test-cases-enums';
import { TestResources, BaseResourceUrls } from '@vb/nest/test-utils/utils/test-resources';
import { initializeTestingModule, isEnumValue } from '@vb/nest/test-utils/utils/test-utils';
import { biddingTestCases } from '@vb/nest/test-utils/test-cases/bidding-test-cases';
import * as _ from 'lodash';

let models: { [key: string]: ExtensionArgs } = {};
let app: INestApplication;
let testResources: TestResources;
let prisma: PrismaService;
let redisClient: RedisClientType | null;
let redisService: RedisService;
const dataFiles: any = {
  BIDDING: biddingData,
  WATCHERS: watchersData,
};

const urls: BaseResourceUrls = {
  BIDDING: {
    [BASE_CRUD_TESTS.READ]: 'bidding',
    [BASE_CRUD_TESTS.UPDATE]: 'bidding',
    [BASE_CRUD_TESTS.DELETE]: 'bidding',
    [BIDDING_TESTS.HAPPY_PATH]: 'bidding/postBid',
    [BIDDING_TESTS.POST_CHARITY_BID]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_PROXY_BID]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_CHARITY_PROXY_BID]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_BID_AGAINST_SELF]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_BELOW_MIN_BIT_AMOUNT]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_QUICK_BID_AGAINST_PROXY]: 'bidding/postBid',
    // [BIDDING_TESTS.POST_CHARITY_QUICK_BID_AGAINST_PROXY]: 'bidding/postBid',
  },
  WATCHERS: {
    [BASE_CRUD_TESTS.CREATE]: 'watchers',
    [BASE_CRUD_TESTS.READ]: 'watchers',
    [BASE_CRUD_TESTS.UPDATE]: 'watchers',
    [BASE_CRUD_TESTS.DELETE]: 'watchers',
  },
};

describe('BiddingController (e2e)', () => {
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    const moduleFixtureBuilder: TestingModuleBuilder = Test.createTestingModule({
      imports: [BiddingModule],
    });

    initializeTestingModule(moduleFixtureBuilder, dataFiles);
    moduleFixture = await moduleFixtureBuilder.compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    prisma = await app.get<PrismaService>(PrismaService, { strict: false });

    redisService = await app.get<RedisService>(RedisService, { strict: false });
    redisClient = redisService.getClient();
    await redisClient?.flushAll();

    models = {
      BIDDING: prisma.bids,
      WATCHERS: prisma.watchers,
    };
  }, 30000);

  testResources = new TestResources(urls, dataFiles, prisma);

  _.forEach(urls, (resourceUrls: any, modelName: string) => {
    if (resourceUrls) {
      _.forEach(resourceUrls, (url: string, method: string) => {
        if (url) {
          it(`TestCases - ${modelName} - ${method}`, async () => {
            if (isEnumValue(BASE_CRUD_TESTS, method)) {
              await baseTestCases<ExtensionArgs>(app, testResources)[method](models[modelName], modelName);
            } else if (isEnumValue(BIDDING_TESTS, method)) {
              await biddingTestCases<ExtensionArgs>(app, testResources)[method](models[modelName], modelName);
            } else {
              throw new Error('Test type is not supported: ' + method);
            }
          }, 30000);
        }
      });
    }
  });

  afterAll(async () => {
    await app.close();

    if (prisma) {
      await prisma.$disconnect();
    }

    if (redisService) {
      const client: RedisClientType | null = redisService.getClient();
      if (client) {
        await client.disconnect();
      }
    }

    if (moduleFixture) {
      await moduleFixture.close();
    }
  });
});
