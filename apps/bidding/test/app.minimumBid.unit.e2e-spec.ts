import { Test, TestingModule } from '@nestjs/testing';
import { AuctionLotFlattenedDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';
import { AuctionDto } from 'apps/auction-catalogs/src/auction/dtos/auction.dto';
import { BidIncrementDto } from 'apps/auction-catalogs/src/bid-increments/dtos/bid-increment.dto';
import { BiddingModule } from '../src/bidding.module';
import { BidDto } from '../src/bids/dtos/bid.dto';
import { MinimumBidNotMetError } from '../src/bids/exceptions/minimum-bid-not-met.error';
import { ProxyBidService } from '../src/bids/services/proxy-bid.service';

describe('MinimumBid Calculation', () => {
  let proxyBidService: any;

  beforeAll(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [BiddingModule],
    }).compile();

    proxyBidService = moduleRef.get(ProxyBidService) as any;
  }, 30000);

  it('verifyBidAmount - minReserveBid - False - currentHighBid > receivedBid', async () => {
    const data: any = generateVerifyBidAmountData(15, false, 1, 10, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - False - currentHighBid <= receivedBid', async () => {
    const data: any = generateVerifyBidAmountData(0, false, 1, 3, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - False - startAmount - 5 - currentBid < startAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, false, 1, 1, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - False - startAmount - 5 - currentBid => startAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, false, 1, 6, 50, 5);
    const result: any = proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    expect(result).toBeUndefined();
  });

  it('verifyBidAmount - minReserveBid - True - receivedBid <= reserveAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, true, 0, 5, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - True - receivedBid = reserveAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, true, 1, 50, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - True - receivedBid < reserveAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, true, 1, 45, 50, 5);
    try {
      proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    } catch (error) {
      expect(error).toBeInstanceOf(MinimumBidNotMetError);
    }
  });

  it('verifyBidAmount - minReserveBid - True - receivedBid > reserveAmount', async () => {
    const data: any = generateVerifyBidAmountData(0, true, 1, 51, 50, 5);
    const result: any = proxyBidService['verifyBidAmount'](data['auctionLot'], data['auction'], data['receivedBid'], data['nextIncrement'], data['currentHighBid']);
    expect(result).toBeUndefined();
  });

  afterAll(async () => {});
});

function generateVerifyBidAmountData(
  currentHighBidAmount: number,
  isReserveMinBid: boolean,
  increment: number,
  receivedBidAmount: number,
  reserveAmount: number,
  startAmount: number,
): any {
  const auctionLot: AuctionLotFlattenedDto = {
    reserveAmount: reserveAmount,
    startAmount: startAmount,
  } as unknown as AuctionLotFlattenedDto;

  const auction: AuctionDto = {
    isReserveMinBid: isReserveMinBid,
  } as unknown as AuctionDto;

  const receivedBid: BidDto = {
    amount: receivedBidAmount,
  } as unknown as BidDto;

  const nextIncrement: BidIncrementDto = {
    increment: increment,
  } as unknown as BidIncrementDto;

  const currentHighBid: BidDto = {
    amount: currentHighBidAmount,
  } as unknown as BidDto;

  return {
    auctionLot,
    auction,
    receivedBid,
    nextIncrement,
    currentHighBid,
  };
}
