export const watchersData: any = {
  CREATE: [
    {
      auctionsid: 'b646cc7c-7c56-40ac-b92e-e43504e733e8',
      auctionLotsid: 'c1c78d9e-e917-47ae-9e0e-c984b30f1d79',
      accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
      watcherType: 'Bidder',
    },
  ],
  READ: [
    {
      id: '6cf0266a-146d-4133-8042-5f72339faaf3',
      accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
      auctionsid: 'c1bf3753-1e3d-4d7b-a98c-a61566ec4cd9',
      auctionLotsid: '6bd2540f-d548-4312-ba05-70ad89b14bb3',
      watcherType: 'Bidder',
    },
  ],
  UPDATE: [
    {
      id: '6cf0266a-146d-4133-8042-5f72339faaf3',
      auctionsid: 'c1bf3753-1e3d-4d7b-a98c-a61566ec4cd9',
      auctionLotsid: '6bd2540f-d548-4312-ba05-70ad89b14bb3',
      watcherType: 'Bidder',
      accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
    },
  ],
  DELETE: [
    {
      id: '6cf0266a-146d-4133-8042-5f72339faaf3',
    },
  ],
  EXTERNAL_SERVICES: {
    USER_SERVICE: {
      getAccountMappingsById: [
        {
          id: 'b00c7a4d-f588-4767-b144-301225ece040',
          ssoId: 'auth0|65cb90b0affd51e1baed5dde',
          accountType: 'USER',
          accountTelephones: [
            {
              id: '5cf36c47-34e4-4a19-a5a1-6d090f68232f',
              name: 'default',
              countryCode: '1',
              number: '(*************',
              extension: '',
              accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
              createdOn: '2024-02-14T14:11:42.384Z',
              updatedOn: '2024-02-14T14:11:42.384Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
            },
          ],
          accountEmails: [
            {
              id: 'ee047c17-d896-4865-b4f2-a8547f7d1dc1',
              email: '<EMAIL>',
              accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
              createdOn: '2024-02-14T14:11:42.384Z',
              updatedOn: '2024-02-14T14:11:42.384Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
              isVerified: false,
            },
          ],
          accountAddresses: [
            {
              id: '15b37ca8-b0af-48ca-9149-7888fb803ebb',
              address1: '123 Long Street',
              address2: '',
              country: 'CA',
              province: 'Alberta',
              city: 'Calgary',
              postalCode: 'T3M1K6',
              name: 'default',
              accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
              createdOn: '2024-02-14T14:11:42.384Z',
              updatedOn: '2024-02-14T14:11:42.384Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
              buzzer: '',
            },
          ],
          accountPreferences: [
            {
              id: '567407c7-811d-42c4-b312-9e71b49d21be',
              preferences: {
                defaultEmail: ['<EMAIL>'],
                defaultPhone: ['+***********'],
                defaultLanguage: ['EN'],
                defaultAddressId: '567407c7-811d-42c4-b312-9e71b49d21be',
                notificationPreferences: {
                  SMS: true,
                  email: true,
                },
              },
              registrationStatus: 'UNCONFIRM',
              spendingCap: '0',
              accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
              createdOn: '2024-02-14T14:11:42.384Z',
              updatedOn: '2024-02-14T14:11:42.384Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
            },
          ],
          user: {
            id: 'e4e306ab-db62-4f6f-9e11-86780e6c199e',
            firstname: 'Kulwinder',
            lastname: 'Billen',
            accountMappingsId: 'b00c7a4d-f588-4767-b144-301225ece040',
            createdOn: '2024-02-14T14:11:42.384Z',
            updatedOn: '2024-02-14T14:11:42.384Z',
            deletedOn: null,
            createdBy: 'SYSTEM',
            updatedBy: 'SYSTEM',
            deletedBy: null,
            status: 'PENDING',
            ipAddress: null,
          },
          organization: null,
        },
        {
          id: '774507a0-9b53-49c1-a8b3-e47b558984ea',
          ssoId: 'auth0|65ccf31faffd51e1baee7802',
          accountType: 'USER',
          accountTelephones: [
            {
              id: 'c84b2e0c-34de-433a-8e75-ea26a84c4e7b',
              name: 'default',
              countryCode: '1',
              number: '(*************',
              extension: '',
              accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
              createdOn: '2024-02-14T17:07:08.449Z',
              updatedOn: '2024-02-14T17:07:08.449Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
            },
          ],
          accountEmails: [
            {
              id: 'ca492a8f-721f-40bc-b8d1-69a4f8d045ed',
              email: '<EMAIL>',
              accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
              createdOn: '2024-02-14T17:07:08.449Z',
              updatedOn: '2024-02-14T17:07:08.449Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
              isVerified: false,
            },
          ],
          accountAddresses: [
            {
              id: 'd2e9ab70-d44d-4c4f-b3c5-bd6fb2bfe5b2',
              address1: '1504-55 Warrender Ave',
              address2: '',
              country: 'CA',
              province: 'Ontario',
              city: 'Etobicoke',
              postalCode: 'M9B0C7',
              name: 'default',
              accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
              createdOn: '2024-02-14T17:07:08.449Z',
              updatedOn: '2024-02-14T17:07:08.449Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
              buzzer: '',
            },
          ],
          accountPreferences: [
            {
              id: '2a5d416f-1a06-41d5-b533-74c78b0a0d74',
              preferences: {
                defaultEmail: ['<EMAIL>'],
                defaultPhone: ['+***********'],
                defaultLanguage: ['EN'],
                defaultAddressId: '567407c7-811d-42c4-b312-9e71b49d21be',
                notificationPreferences: {
                  SMS: true,
                  email: true,
                },
              },
              registrationStatus: 'UNCONFIRM',
              spendingCap: '0',
              accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
              createdOn: '2024-02-14T17:07:08.449Z',
              updatedOn: '2024-02-14T17:07:08.449Z',
              deletedOn: null,
              createdBy: 'SYSTEM',
              updatedBy: 'SYSTEM',
              deletedBy: null,
            },
          ],
          user: {
            id: 'cab4955f-36d0-4823-b3cc-3516e5fd6efb',
            firstname: 'Allan',
            lastname: 'Jeyakumar',
            accountMappingsId: '774507a0-9b53-49c1-a8b3-e47b558984ea',
            createdOn: '2024-02-14T17:07:08.449Z',
            updatedOn: '2024-02-14T17:07:08.449Z',
            deletedOn: null,
            createdBy: 'SYSTEM',
            updatedBy: 'SYSTEM',
            deletedBy: null,
            status: 'PENDING',
            ipAddress: null,
          },
          organization: null,
        },
      ],
    },
    AUCTION_SERVICE: {
      getAuctionLotsById: [
        {
          id: 'c1c78d9e-e917-47ae-9e0e-c984b30f1d78',
          createdBy: 'b00c7a4d-f588-4767-b144-301225ece040',
          createdOn: '2/13/2024, 10:06:17 PM',
          updatedOn: '2/13/2024, 10:06:17 PM',
          updatedBy: '',
          deletedBy: '',
          auctionsId: 'b646cc7c-7c56-40ac-b92e-e43504e733e8',
          auctions: {
            id: 'b646cc7c-7c56-40ac-b92e-e43504e733e8',
            name: 'VEHICLES, EQUIPMENT, INDUSTRIAL & TOOLS',
            description:
              'We have been commissioned to sell at online auction the following items Items are located at 11 Mount Hope Ave Dartmouth Nova Scotia Bid sniper protection - any bid in the last 60 seconds will extend the bidding on that item 60 seconds Viewing By Appointment Only - Please call 902 292 0490 or 902 580 1418 Tie Bids go to the Bidder who placed the earliest bid - User name displayed on High Bidder is the High Bidder Please make sure to Refresh your Page often during closing hitting the forward and back button on your browser may not show actual correct bid prices and winning bidder Items will be added during the auction',
            fromDatetime: '2024-02-11T13:00:00.000Z',
            toDatetime: '2099-12-29T13:00:00.000Z',
            status: 'PUBLIC',
            isShip: true,
            isProxyBid: false,
            isReservable: false,
            isBuyNow: false,
            isMakeOffer: false,
            isQuickBid: true,
            isInlineBidding: false,
            isLargeBidConfirm: false,
            tags: 'tags',
            code: 'CA',
            invoiceTemplatesId: '55499309-65fb-4403-a07b-ec5ecff971cb',
            buyerPremium: 10,
            featureDisplayOrder: 0,
            paymentTypes: ['cash', 'credit_card', 'e-transfer', 'paypal'],
          },
          lotsId: 'c82d5e6f-bd51-460c-b0a7-b9cee8cfc9cf',
          lots: {
            id: 'c82d5e6f-bd51-460c-b0a7-b9cee8cfc9cf',
            title: 'Extra Thick Dark Grey Black Concrete Wallpaper Peel and Stick 3D',
            description:
              'Material: Made of the premium vinyl material, extra thick and durable, waterproof;\n' +
              'removable;\n' +
              "self adhesive, sticky well on smooth surface Size: The size of faux concrete contact paper is 16×591 inch (W×L), Coverage: 64.6 sq ft, enough to match up your DIY remodel projects Matte Texture: Come with thicken matte texture design, touch well, look like realistic cement concrete, wallpaper surface scattered spots dark gray, with a sense of mess, easy to match up your garage's wall and basement's wall Self Adhesive: The back of matte texture wallpaper is attached to glue, just peel and stick, there are size lines on the back, easy to cut and adjust, meet to remodel your different projects needed Application: Suitable for decorative remodel old accent wall, kitchen island, cabinet, bedroom flooring, garage, basement, special coffee room, gym, kitchen, countertop, dorm, rental room, apartment, bathroom, drawer backsplash, office, vanity room, living room,can meet every smooth surface projects",
            status: 'Published',
            code: 'NA',
            reserveAmount: 0,
            tags: 'NA',
            taxPercentage: 0.14,
            condition: 'New / Open box',
            category: 'General Items',
            region: 'Viewbid Darmouth Location',
            number: 2,
          },
          number: 2,
          displayOrder: 0,
          status: 'PUBLISHED',
          toDateTime: '2099-12-29T13:00:00.000Z',
        },
      ],
    },
  },
};
