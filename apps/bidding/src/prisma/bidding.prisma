datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider        = "prisma-client-js"
  output          = "./generated/bidding-db-client"
  binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
  previewFeatures = ["tracing"]
}

model Bids {
  id                String    @id @default(uuid()) @db.VarChar(36)
  createdOn         DateTime  @default(now())
  createdBy         String    @db.VarChar(36)
  updatedOn         DateTime?
  updatedBy         String?   @db.VarChar(36)
  deletedOn         DateTime?
  deletedBy         String?   @db.VarChar(36)
  auctionsid        String    @db.VarChar(36)
  auctionLotsid     String    @db.VarChar(36)
  accountMappingsId String    @db.VarChar(36)
  amount            Decimal?  @db.Decimal(10, 2)
  isProxy           Int       @db.SmallInt
  isQuickBid        Int       @db.SmallInt
  isBuyNow          Int       @db.SmallInt
  metaData          String

  // @@unique([auctionLotsid, amount])
  @@index([auctionLotsid])
}

model Watchers {
  id                String    @id @default(uuid()) @db.VarChar(36)
  createdOn         DateTime  @default(now())
  createdBy         String    @db.VarChar(36)
  updatedOn         DateTime?
  updatedBy         String?   @db.VarChar(36)
  deletedOn         DateTime?
  deletedBy         String?   @db.VarChar(36)
  auctionsid        String    @db.VarChar(36)
  auctionLotsid     String    @db.VarChar(36)
  accountMappingsId String    @db.VarChar(36)
  watcherType       String

  @@unique([auctionsid, auctionLotsid, accountMappingsId])
}

model ApplicationPreferences {
  id            String    @id @default(uuid()) @db.Uuid
  createdOn     DateTime  @default(now())
  createdBy     String    @default(dbgenerated("'********-0000-0000-0000-************'::uuid")) @db.Uuid
  updatedOn     DateTime? @updatedAt
  updatedBy     String?   @db.Uuid
  deletedOn     DateTime?
  deletedBy     String?   @db.Uuid
  preferenceKey String    @unique
  value         String
  description   String

  @@map("ApplicationPreferences")
}

model ProxyBids {
  id                String    @id @default(uuid()) @db.Uuid
  createdOn         DateTime  @default(now())
  createdBy         String    @default(dbgenerated("'********-0000-0000-0000-************'::uuid")) @db.Uuid
  updatedOn         DateTime? @updatedAt
  updatedBy         String?   @db.Uuid
  deletedOn         DateTime?
  deletedBy         String?   @db.Uuid
  accountMappingsId String    @db.Uuid
  proxyBidAmount    Decimal   @db.Decimal(10, 2)
  auctionLotsId     String    @db.Uuid
}

model AuctionWatchers {
  id                String           @id @default(uuid()) @db.VarChar(36)
  createdOn         DateTime         @default(now())
  createdBy         String           @db.VarChar(36)
  updatedOn         DateTime?
  updatedBy         String?          @db.VarChar(36)
  deletedOn         DateTime?
  deletedBy         String?          @db.VarChar(36)
  auctionsId        String           @db.VarChar(36)
  accountMappingsId String           @db.VarChar(36)
  notificationType  NotificationType

  @@unique([auctionsId, accountMappingsId, notificationType])
}

enum NotificationType {
  AUCTION_BEGINS_SOON
  AUCTION_ENDS_SOON
}

model RequestAudit {
  id                String   @id @default(uuid()) @db.Uuid
  createdOn         DateTime @default(now())
  createdBy         String   @default(dbgenerated("'********-0000-0000-0000-************'::uuid")) @db.Uuid
  accountMappingsId String?  @db.VarChar(36)
  path              String   @db.Text
  method            String   @db.VarChar(10)
  body              Json?
}
