import { Controller } from '@nestjs/common';
import { ListResultset } from '@mvc/data/list-resultset';
import { BaseController } from '@mvc/controllers/base.controller';
import { AppPreferencesHandler } from '../handler/app-preferences.handler';
import { ApplicationPreferencesRequest } from 'libs/application-preferences/src/http/requests/app-preferences.request';
import { ApplicationPreferencesDto } from 'libs/application-preferences/src/dtos/app-preferences-dto';
import { ApplicationPreferencesResponse } from 'libs/application-preferences/src/http/responses/app-preferences.response';
import { ApplicationPreferencesListResponse } from 'libs/application-preferences/src/http/responses/app-preferences-list.response';

@Controller('applicationPreferences')
export class AppPreferencesController extends BaseController<
  AppPreferencesHandler,
  ApplicationPreferencesRequest,
  ApplicationPreferencesDto,
  ApplicationPreferencesResponse,
  ApplicationPreferencesListResponse
> {
  constructor(handler: AppPreferencesHandler) {
    super(handler);
  }

  createDtoFromRequest(request: ApplicationPreferencesRequest): ApplicationPreferencesDto {
    return new ApplicationPreferencesDto(request);
  }

  createResponseFromDto(dto: ApplicationPreferencesDto): ApplicationPreferencesResponse {
    return new ApplicationPreferencesResponse(dto, {});
  }
  createResponseList(list: ListResultset<ApplicationPreferencesDto>): ApplicationPreferencesListResponse {
    return new ApplicationPreferencesListResponse(list);
  }
}
