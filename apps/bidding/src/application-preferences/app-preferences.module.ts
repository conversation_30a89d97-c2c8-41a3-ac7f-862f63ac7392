import { Module } from '@nestjs/common';
import { AppPreferencesController } from './controller/app-preferences.controller';
import { AppPreferencesHandler } from './handler/app-preferences.handler';
import { ApplicationPreferencesDbService } from './data/app-preferences-db.service';

@Module({
  controllers: [AppPreferencesController],
  providers: [AppPreferencesHandler, ApplicationPreferencesDbService],
})
export class ApplicationPreferencesModule {}
