import { Logger } from '@nestjs/common';
import { Message } from '@vb-utils/messaging/dtos/message';
import { MessageFactory } from '@vb-utils/messaging/factories/message-factory';
import { AccountMappingCommonListDto } from '@vb/account-mappings/dtos/account-mapping-common-list.dto';
import { AccountMappingCommonDto } from '@vb/account-mappings/dtos/account-mapping-common.dto';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { AuctionLotFlattenedDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';
import { AuctionDto } from '../../../../auction-catalogs/src/auction/dtos/auction.dto';
import { BidIncrementDto } from '../../../../auction-catalogs/src/bid-increments/dtos/bid-increment.dto';
import { BidExceededParametersDto } from '../dtos/bid-exceeded-parameters.dto';
import { BidDto } from '../dtos/bid.dto';
import { ProxyBidDto } from '../dtos/proxy-bid.dto';
import { BidAmountExistsError } from '../exceptions/bid-amount-exists.error';
import { BidsHandler } from '../handler/bids.handler';
import { ProxyBidsHandler } from '../handler/proxy-bids.handler';
import { BidIncrementsCachingService } from '../services/bid-increments-caching.service';
import { ConfigService } from '@vb/config/src/config.service';

export abstract class BaseAuctionBidCommand {
  protected logger: Logger = new Logger(this.constructor.name);
  private baseUrl: string;
  private cdnURL: string;
  constructor(
    protected bidsHandler: BidsHandler,
    protected proxyBidsHandler: ProxyBidsHandler,
    private readonly sqsService: AwsSqsService,
    private readonly bidIncrementsCachingService: BidIncrementsCachingService,
    private readonly configService: ConfigService,
  ) {}

  abstract determineWinningBid(
    userId: string,
    bid: BidDto,
    nextIncrement: BidIncrementDto,
    auction: AuctionDto,
    auctionLot: AuctionLotFlattenedDto,
    previousHighBid: BidDto,
    bidReceiveTime: number,
    firstAttempt: boolean,
  ): Promise<BidDto>;

  /**
   * This will iterate the list of proxy bids on this item, and continue to loop until only 1 bidder remains
   * in the proxy bid list. As it loops and a proxy bidder's max bid amount is attained, they are removed
   * from the list. This loop continues until there is 1 bidder left.
   * It is designed to not max out the bids, but stop when the next increment amount is obtained that is still
   * within the range of the maximum proxy amount, and they are the last bidder within that range.
   *
   * @param auctionLotId
   * @param receivedBid
   * @param auctionLot
   * @param mustMeetReserve
   * @protected
   */
  protected async executeProxyBids(
    auctionLotId: string,
    receivedBid: BidDto,
    auctionLot: AuctionLotFlattenedDto,
    mustMeetReserve: boolean,
    bidReceiveTime: number,
    previousHighBid: BidDto,
  ): Promise<BidDto> {
    this.baseUrl = await this.configService.getSetting('APPLICATION_BASE_URL', '');
    this.cdnURL = await this.configService.getSetting('CDN_URL', '');

    const isCurrentBidZero: boolean = auctionLot.currentBid == 0;
    if (isCurrentBidZero && !receivedBid.isProxy) {
      return receivedBid;
    }
    this.logger.log(`executing proxy bids for bid received at ${bidReceiveTime}`);
    const [proxyBids, _nextIncrement] = await Promise.all([
      this.proxyBidsHandler.getAll(1, -1, [{ auctionLotsId: auctionLotId, proxyBidAmount: previousHighBid.amount }]),
      this.bidIncrementsCachingService.getNextIncrement(receivedBid.auctionsid, this.determineNextIncrementAmount(auctionLot, mustMeetReserve)),
    ]);

    const incrementAmount: number = Number(_nextIncrement.increment);
    if (receivedBid.isProxy && proxyBids.list.length === 1) {
      receivedBid.amount = this.determineNextCorrectBidAmount(auctionLot.currentBid, auctionLot, mustMeetReserve, isCurrentBidZero, incrementAmount);
      return receivedBid;
    } else if (!receivedBid.isProxy && proxyBids.list.length === 0) {
      return receivedBid;
    }

    this.logger.log('increment amount: ' + incrementAmount);
    let winningBidPlaceholder: ProxyBidDto = proxyBids.list[proxyBids.list.length - 1];
    const lastLoser: ProxyBidDto | undefined = proxyBids.list.length > 1 ? proxyBids.list[proxyBids.list.length - 2] : undefined;

    const winningBidAmount: number = this.determineNextCorrectBidAmount(
      auctionLot.currentBid,
      auctionLot,
      mustMeetReserve,
      isCurrentBidZero,
      incrementAmount,
      winningBidPlaceholder,
      lastLoser,
    );

    let tmpWinner: BidDto | undefined;
    if (!receivedBid.isProxy) {
      tmpWinner = this.executeNonProxyBid(receivedBid, winningBidPlaceholder, winningBidAmount, auctionLot, mustMeetReserve, isCurrentBidZero, _nextIncrement);
    }

    winningBidPlaceholder.proxyBidAmount = winningBidAmount;
    const losingBids: ProxyBidDto[] = proxyBids.list.filter((bid: ProxyBidDto) => bid !== winningBidPlaceholder);
    if (tmpWinner) {
      if (receivedBid.accountMappingsId === tmpWinner.accountMappingsId) {
        losingBids.push(winningBidPlaceholder);
      } else if (receivedBid.amount !== tmpWinner.amount) {
        losingBids.push(receivedBid.toProxyBid());
      }
      winningBidPlaceholder = tmpWinner.toProxyBid();
    }

    const proxiesToNotify: ProxyBidDto[] = losingBids.filter((b: ProxyBidDto) => b.isProxy === 1);
    await Promise.all([
      this.insertAllProxyBids(losingBids, receivedBid),
      ...proxiesToNotify.map((lostProxyBid: ProxyBidDto) => this.sendProxyBidExceededNotification(lostProxyBid, winningBidPlaceholder, receivedBid, auctionLot)),
    ]);

    return BidDto.buildFromProxyBid(winningBidPlaceholder, receivedBid);
  }

  private determineNextIncrementAmount(auctionLot: AuctionLotFlattenedDto, mustMeetReserve: boolean): number {
    if (mustMeetReserve) {
      return Math.max(Number(auctionLot.currentBid), Number(auctionLot.reserveAmount));
    } else if (auctionLot.startAmount && Number(auctionLot.startAmount)) {
      return Math.max(Number(auctionLot.currentBid), Number(auctionLot.startAmount));
    }
    return Number(auctionLot.currentBid);
  }

  private determineNextCorrectBidAmount(
    currentBidAmount: number,
    auctionLot: AuctionLotFlattenedDto,
    mustMeetReserve: boolean,
    isCurrentBidZero: boolean,
    incrementAmount: number,
    winningBidPlaceholder?: ProxyBidDto,
    lastLoser?: ProxyBidDto,
  ): number {
    const calculatedWinningBidAmount: number = lastLoser ? Number(lastLoser.proxyBidAmount) + incrementAmount : Number(currentBidAmount) + incrementAmount;

    const winningBidAmount: number =
      winningBidPlaceholder && Number(winningBidPlaceholder.proxyBidAmount) < calculatedWinningBidAmount
        ? Number(winningBidPlaceholder.proxyBidAmount)
        : calculatedWinningBidAmount;

    if (mustMeetReserve) {
      return Math.max(winningBidAmount, Number(auctionLot.reserveAmount));
    } else if (isCurrentBidZero) {
      return Math.max(winningBidAmount, Number(auctionLot.startAmount));
    } else {
      return winningBidAmount;
    }
  }

  /**
   *  If the received bid is a proxy bid, and it is less than the current high bid, then we need to determine the next
   *  correct bid amount. This will be the current high bid amount + the increment amount.
   *
   * @param receivedBid
   * @param winningBidPlaceholder
   * @param winningBidAmount
   * @param auctionLot
   * @param mustMeetReserve
   * @param isCurrentBidZero
   * @param incrementAmount
   * @private
   */
  private executeNonProxyBid(
    receivedBid: BidDto,
    winningBidPlaceholder: ProxyBidDto,
    winningBidAmount: number,
    auctionLot: AuctionLotFlattenedDto,
    mustMeetReserve: boolean,
    isCurrentBidZero: boolean,
    incrementAmount: BidIncrementDto,
  ): BidDto {
    //the 2 bids match so automatically award it to the proxy bidder
    if (Number(winningBidPlaceholder.proxyBidAmount) === Number(receivedBid.amount)) {
      winningBidPlaceholder.proxyBidAmount = winningBidAmount;
      return BidDto.buildFromProxyBid(winningBidPlaceholder, receivedBid);
      //the proxy bid is higher, so we'll be able to place the received bid and the the new proxy bid increment
    } else if (Number(winningBidPlaceholder.proxyBidAmount) > Number(receivedBid.amount)) {
      winningBidPlaceholder.proxyBidAmount = this.determineNextCorrectBidAmount(
        receivedBid.amount,
        auctionLot,
        mustMeetReserve,
        isCurrentBidZero,
        incrementAmount.increment,
        winningBidPlaceholder,
      );
      return BidDto.buildFromProxyBid(winningBidPlaceholder, receivedBid);
    }
    //the received bid won
    return receivedBid;
  }

  /**
   * Sends a notification to the losing bidder that their proxy bid has been exceeded.
   *
   * @param lostProxyBid
   * @param winningBid
   * @param receivedBid
   * @param auctionLot
   * @private
   */
  private async sendProxyBidExceededNotification(lostProxyBid: ProxyBidDto, winningBid: ProxyBidDto, receivedBid: BidDto, auctionLot: AuctionLotFlattenedDto): Promise<void> {
    const eventKey: string = 'PROXY_BID_EXCEEDED';

    const message: Message = MessageFactory.build(
      eventKey,
      new AccountMappingCommonListDto([new AccountMappingCommonDto(lostProxyBid.accountMappingsId, '', '', lostProxyBid.accountMappingsId, '', '')]),
      new BidExceededParametersDto(lostProxyBid, winningBid, receivedBid, auctionLot, this.baseUrl, this.cdnURL),
    );

    this.sqsService.sendMessage(JSON.stringify(message), 'notifications');
  }

  /**
   * Inserts all proxy bids into the bids table.
   * @param proxyBids
   * @param receivedBid
   * @protected
   */
  protected async insertAllProxyBids(proxyBids: ProxyBidDto[], receivedBid: BidDto): Promise<void> {
    await Promise.all(
      proxyBids.map(async (proxyBid: ProxyBidDto) => {
        try {
          await this.bidsHandler.createOptimisticBid(proxyBid.accountMappingsId, BidDto.buildFromProxyBid(proxyBid, receivedBid));
        } catch (error) {
          this.logger.error(`Error inserting proxy bid into bids table: ${error}`);
        }
      }),
    );
  }

  protected async verifyAmount(bid: BidDto, previousBid: BidDto): Promise<void> {
    if (previousBid.amount >= bid.amount) {
      throw new BidAmountExistsError(bid.amount);
    }
  }

  async saveProxyBid(userId: string, bid: BidDto): Promise<void> {
    await this.proxyBidsHandler.createOrUpdate(userId, ProxyBidDto.build(userId, bid));
  }
}
