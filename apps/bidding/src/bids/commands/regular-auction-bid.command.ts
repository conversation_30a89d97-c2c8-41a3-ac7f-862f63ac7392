import { AuctionLotFlattenedDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';
import { AuctionDto } from '../../../../auction-catalogs/src/auction/dtos/auction.dto';
import { BidIncrementDto } from '../../../../auction-catalogs/src/bid-increments/dtos/bid-increment.dto';
import { BidDto } from '../dtos/bid.dto';
import { CurrentHighBidderError } from '../exceptions/current-high-bidder.error';
import { ProxyBidNotAvailableError } from '../exceptions/proxy-bid-not-available.error';
import { BaseAuctionBidCommand } from './base-auction-bid.command';

export class RegularAuctionBidCommand extends BaseAuctionBidCommand {
  async determineWinningBid(
    userId: string,
    bid: BidDto,
    nextIncrement: BidIncrementDto,
    auction: AuctionDto,
    auctionLot: AuctionLotFlattenedDto,
    previousHighBid: BidDto,
    bidReceiveTime: number,
    firstAttempt: boolean,
  ): Promise<BidDto> {
    this.verifyAmount(bid, previousHighBid);
    if (bid.isProxy) {
      await this.handleProxyBid(userId, bid, auction, firstAttempt);
    } else {
      this.validateNonProxyBid(userId, previousHighBid);
    }

    return auction.isProxyBid ? this.executeProxyBids(bid.auctionLotsid, bid, auctionLot, Boolean(auction.isReserveMinBid), bidReceiveTime, previousHighBid) : bid;
  }

  private async handleProxyBid(userId: string, receivedBid: BidDto, auction: AuctionDto, firstAttempt: boolean): Promise<void> {
    if (auction.isProxyBid) {
      //only try to save the proxy on a first attempt
      if (firstAttempt) {
        await this.saveProxyBid(userId, receivedBid);
      }
    } else {
      throw new ProxyBidNotAvailableError();
    }
  }

  private validateNonProxyBid(userId: string, previousBid: BidDto): void {
    if (previousBid.accountMappingsId === userId) {
      throw new CurrentHighBidderError(previousBid.accountMappingsId);
    }
  }
}
