import { AuctionLotFlattenedDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';
import { AuctionDto } from '../../../../auction-catalogs/src/auction/dtos/auction.dto';
import { BidIncrementDto } from '../../../../auction-catalogs/src/bid-increments/dtos/bid-increment.dto';
import { BidDto } from '../dtos/bid.dto';
import { BaseAuctionBidCommand } from './base-auction-bid.command';

export class CharityAuctionBidCommand extends BaseAuctionBidCommand {
  async determineWinningBid(
    userId: string,
    bid: BidDto,
    nextIncrement: BidIncrementDto,
    auction: AuctionDto,
    auctionLot: AuctionLotFlattenedDto,
    previousHighBid: BidDto,
    bidReceiveTime: number,
    firstAttempt: boolean,
  ): Promise<BidDto> {
    this.verifyAmount(bid, previousHighBid);
    if (bid.isProxy) {
      await this.saveProxyBid(userId, bid);
    }
    return await this.executeProxyBids(bid.auctionLotsid, bid, auctionLot, Boolean(auction.isReserveMinBid), bidReceiveTime, previousHighBid);
  }
}
