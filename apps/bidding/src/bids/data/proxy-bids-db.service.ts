import { BaseDbService } from '@mvc/data/base-db.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '../../prisma/generated/bidding-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { Constants } from '../config/constants';
import { ProxyBidDto } from '../dtos/proxy-bid.dto';
import { ProxyBidNotFoundError } from '../exceptions/proxy-bid-not-found.error';
import { ReducedProxyBidAmountError } from '../exceptions/reduced-proxy-bid-amount.error';
import { MaxProxyDto } from '../dtos/max-proxy.dto';

@Injectable()
export class ProxyBidsDbService extends BaseDbService<Prisma.ProxyBidsDelegate, ProxyBidDto> {
  constructor(prismaService: PrismaService) {
    super(prismaService.proxyBids, true, false, false);
  }
  protected getExcludedFields(): (keyof ProxyBidDto)[] {
    return ['isProxy'];
  }
  createFilter(params: any[]): any {
    const filter: Record<string, any> = {
      deletedOn: null, // Always include this condition
      auctionLotsId: params[0].auctionLotsId, // UUID for the auction lot
      proxyBidAmount: {
        gte: params[0].proxyBidAmount, // Greater than condition for proxy bid amount
      },
    };

    return filter;
  }

  getOrderBy(): any {
    return [
      { proxyBidAmount: 'asc' }, // First order by amount in ascending order
      { createdOn: 'asc' }, // Then order by createdOn in ascending order if amounts are equal
    ];
  }

  mapToDto(model: any): ProxyBidDto {
    return new ProxyBidDto(model);
  }

  throw404(id: string): Error {
    id;
    throw new ProxyBidNotFoundError('', '');
  }

  async getMaxProxiesForAuctionLots(userId: string, auctionLotsIds: string[]): Promise<MaxProxyDto[]> {
    const groupBy: any = await this.table.groupBy({
      by: ['auctionLotsId'],
      where: {
        auctionLotsId: {
          in: auctionLotsIds,
        },
        accountMappingsId: userId,
        deletedOn: null,
      },
      _max: {
        proxyBidAmount: true,
      },
    });

    const maxProxies: MaxProxyDto[] = [];

    groupBy.forEach((group: any) => {
      if (group._max.proxyBidAmount !== null) {
        maxProxies.push({
          accountMappingsId: userId,
          auctionLotsId: group.auctionLotsId,
          proxyAmount: group._max.proxyBidAmount.toNumber(),
        });
      }
    });

    return maxProxies;
  }

  /**
   * checks to ensure whether the user already has an existing proxy bid. if they do
   * and this bid is lower, we throw an exception. if they do and this is higher,
   * we update the new proxy bid amount. if they don't, we create the new proxy bid.
   *
   * @param userId
   * @param proxyBidDto
   */
  async createOrUpdate(userId: string, proxyBidDto: ProxyBidDto): Promise<ProxyBidDto> {
    const paramsArray: any = [
      {
        accountMappingsId: userId,
        auctionLotsId: proxyBidDto.auctionLotsId,
      },
    ];
    try {
      const existingProxyBid: ProxyBidDto = await this.getCustom(paramsArray, ProxyBidDto);

      if (existingProxyBid.proxyBidAmount >= proxyBidDto.proxyBidAmount) {
        throw new ReducedProxyBidAmountError(proxyBidDto.proxyBidAmount, existingProxyBid.proxyBidAmount);
      }

      const updatedProxyBid: ProxyBidDto = await this.update(userId, proxyBidDto, existingProxyBid.id);
      return updatedProxyBid;
    } catch (error) {
      if (error.errorCode === Constants.BID_NOT_FOUND) {
        // Our check for existing will throw a 404 - now we create a new instance
        return await this.create(userId, proxyBidDto);
      } else {
        throw error; // Re-throw other errors to be handled elsewhere
      }
    }
  }
}
