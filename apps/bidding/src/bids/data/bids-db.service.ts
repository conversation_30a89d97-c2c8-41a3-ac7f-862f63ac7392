import { BaseDbService } from '@mvc/data/base-db.service';
import { ListResultset } from '@mvc/data/list-resultset';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Bids, Prisma } from '../../prisma/generated/bidding-db-client';
import { PrismaService } from '../../prisma/prisma.service';
import { AuctionLotBidPriceDto } from '../dtos/auction-lot-bid-price.dto';
import { BidWinnerDto } from '../dtos/bid-winner.dto';
import { BidDto } from '../dtos/bid.dto';
import { BiddingDataDto } from '../dtos/bidding-data.dto';
import { BidCollisionOccurredError } from '../exceptions/bid-collision-occured.error';
import { BidNotFoundError } from '../exceptions/bid-not-found.error';
import { BiddingData } from './bids.service.interfaces';
import { AuctionBiddingSummaryDto } from '../dtos/auction-bidding-summary.dto';

@Injectable()
export class BidsDbService extends BaseDbService<Prisma.BidsDelegate, BidDto> {
  mapToDto(model: any): BidDto {
    return new BidDto(model);
  }

  getOrderBy(): any {
    return { createdOn: 'desc' };
  }

  protected getExcludedFields(): (keyof BidDto)[] {
    return ['isProxyTopUp', 'proxyAmount'];
  }

  createFilter(params: any[]): any {
    const queryParam: any = params.find((param: any) => param.query);

    if (queryParam) {
      return { auctionLotsid: queryParam.query };
    }

    return super.createFilter(params);
  }

  throw404(id: string): Error {
    return new BidNotFoundError(id);
  }

  constructor(private readonly prisma: PrismaService) {
    super(prisma.bids);
  }

  async getAuctionBiddingSummary(auctionsId: string): Promise<AuctionBiddingSummaryDto> {
    const numBids: number = await this.table.count({ where: { auctionsid: auctionsId, deletedOn: null } });

    const numBidders: number = await this.prisma.$queryRaw`
      SELECT COUNT(DISTINCT b."accountMappingsId") as "count"
      FROM public."Bids" b
      WHERE b."deletedOn" IS NULL
      AND b."auctionsid" = ${auctionsId}
    `.then((rows: any[]) => Number(rows[0].count));

    const maxProxyBidsTotal: number = await this.prisma.$queryRaw<any[]>`
      SELECT COALESCE(SUM("maxProxyBid"), 0) as "total"
      FROM (
        SELECT MAX(distinct_pb."proxyBidAmount") as "maxProxyBid"
        FROM (
          SELECT DISTINCT ON (pb.id) pb.id, pb."proxyBidAmount", pb."auctionLotsId"
          FROM public."ProxyBids" pb
          INNER JOIN public."Bids" b ON b."auctionLotsid"::uuid = pb."auctionLotsId"::uuid
          WHERE b."auctionsid" = ${auctionsId}
          AND pb."deletedOn" IS NULL
        ) as distinct_pb
        GROUP BY distinct_pb."auctionLotsId"
      ) as max_proxy_bids
    `.then((rows: any[]) => Number(rows[0].total));

    return { auctionsId, numBids, numBidders, maxProxyBidsTotal };
  }

  async getBiddingData(auctionLotsid: string): Promise<BiddingData> {
    const result: BiddingData[] = await this.prisma.$queryRaw<BiddingData[]>`SELECT
      COUNT(*) AS "bidCount",
      MAX(amount) AS "currentPrice",
      (SELECT DISTINCT "accountMappingsId" FROM public."Bids"
      WHERE amount = (SELECT MAX(amount) FROM public."Bids" WHERE "auctionLotsid"=${auctionLotsid} AND "deletedOn" IS NULL) AND "auctionLotsid"=${auctionLotsid}) AS highBidder,
      (SELECT COUNT(*) FROM public."Watchers" WHERE "auctionLotsid"=${auctionLotsid} AND "deletedOn" IS NULL) as "numberOfWatchers"
      FROM public."Bids" WHERE "auctionLotsid"=${auctionLotsid} AND "deletedOn" IS NULL;`;
    if (result.length < 1) {
      throw new NotFoundException('Bid data not found.');
    }
    return result[0];
  }

  async getPreviousBidData(auctionLotsid: string): Promise<BidDto[]> {
    const previousBidData: Bids[] = await this.prisma.bids.findMany({
      orderBy: [{ amount: 'desc' }],
      where: { AND: [{ auctionLotsid: auctionLotsid, deletedOn: null }] },
      take: 1,
    });
    const bidHistory: BidDto[] = previousBidData.map((dbResult: any) => {
      return new BidDto(dbResult);
    });
    return bidHistory;
  }

  async getAuctionLotBidPrice(auctionLotsIds: string[]): Promise<AuctionLotBidPriceDto[]> {
    const auctionLotBidPrices: any = await this.prisma.bids.groupBy({ by: ['auctionLotsid'], _max: { amount: true }, where: { auctionLotsid: { in: auctionLotsIds } } });

    const lotBiddingData: BiddingDataDto[] = await this.getLotBiddingData(auctionLotsIds);
    const lotBiddingDataStringfy: string = JSON.stringify(lotBiddingData, (key: string, value: any) => (typeof value === 'bigint' ? value.toString() : value));
    return auctionLotBidPrices.map((dbResult: any) => {
      return new AuctionLotBidPriceDto(
        dbResult.auctionLotsid,
        Number(dbResult._max.amount),
        JSON.parse(lotBiddingDataStringfy).find((biddingData: any) => biddingData.auctionLotsId === dbResult.auctionLotsid),
      );
    });
  }

  async getHighestBid(auctionLotsId: string): Promise<BidDto> {
    const row: any = await this.table.findFirst({
      where: { AND: [{ auctionLotsid: auctionLotsId, deletedOn: null }, { amount: { not: null } }] },
      orderBy: [{ amount: 'desc' }, { createdOn: 'asc' }],
    });

    if (!row) {
      throw new BidNotFoundError(auctionLotsId);
    }

    return new BidDto(row);
  }

  async getLotBiddingDataByAuctionLotIds(auctionLotsIds: string): Promise<AuctionLotBidPriceDto[]> {
    return await this.getAuctionLotBidPrice(auctionLotsIds.split(','));
  }

  async getLotBiddingData(auctionLotsIds: string[]): Promise<BiddingDataDto[]> {
    const result: BiddingDataDto[] = await this.prisma.$queryRaw<BiddingDataDto[]>`WITH LatestBids AS (
      SELECT DISTINCT ON ("auctionLotsid") "auctionLotsid", "accountMappingsId", "isProxy", amount
      FROM public."Bids"
      WHERE "deletedOn" IS NULL
      ORDER BY "auctionLotsid", amount DESC
      )
      SELECT
          b."auctionLotsid" AS "auctionLotsId",
          COUNT(*) AS "bidCount",
          MAX(b.amount) AS "currentPrice",
          lb."accountMappingsId" AS "highBidder",
          lb."isProxy" AS "isProxy",
          COALESCE(w."numberOfWatchers", 0) AS "numberOfWatchers"
      FROM
          public."Bids" b
          LEFT JOIN LatestBids lb ON lb."auctionLotsid" = b."auctionLotsid"
          LEFT JOIN (
              SELECT "auctionLotsid", COUNT(*) AS "numberOfWatchers"
              FROM public."Watchers"
              WHERE "deletedOn" IS NULL
              GROUP BY "auctionLotsid"
          ) w ON w."auctionLotsid" = b."auctionLotsid"
      WHERE
          b."auctionLotsid" IN (${Prisma.join(auctionLotsIds)})
          AND b."deletedOn" IS NULL
      GROUP BY
          b."auctionLotsid", lb."accountMappingsId", lb."isProxy", w."numberOfWatchers";
      `;
    if (result.length < 1) {
      return [];
    }
    return result;
  }

  async getAllBidWinners(auctionLotsIds: string): Promise<BidWinnerDto[]> {
    const bidWinners: { auctionLotsid: string; createdBy: string; amount: number }[] = await this.prisma
      .$queryRaw`SELECT "auctionLotsid", "accountMappingsId", "amount"FROM (SELECT "auctionLotsid", "accountMappingsId", "amount", ROW_NUMBER()
        OVER (PARTITION BY "auctionLotsid" ORDER BY "amount" DESC) AS row_num FROM public."Bids" WHERE "auctionLotsid" IN (${Prisma.join(auctionLotsIds.split(','))})
        AND "deletedOn" IS NULL) AS ranked_bids WHERE row_num = 1`;

    const winnersList: BidWinnerDto[] = bidWinners.map((dbResult: any) => {
      return new BidWinnerDto(dbResult);
    });
    return winnersList;
  }

  async createUnique(accountMappingsId: string, bid: BidDto): Promise<BidDto | void> {
    const existingBid: any = await this.table.findFirst({ where: { auctionLotsid: bid.auctionLotsid, amount: bid.amount } });
    if (!existingBid) {
      return await this.create(accountMappingsId, bid);
    }
  }

  async getUserBidHistory(page: number, size: number, params: any[]): Promise<ListResultset<BidDto>> {
    const filter: any = params ? this.createFilter(params) : {};
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    const [items, totalItems] = await Promise.all([
      this.prisma.$queryRaw<BidDto[]>`SELECT b."auctionLotsid", b."isProxy", b."amount"
      FROM public."Bids" b
      WHERE b."accountMappingsId" = ${filter.accountMappingsId}
      ORDER BY b."createdOn" DESC
      LIMIT ${take} OFFSET ${skip};`,
      this.table.findMany({ where: filter, distinct: ['auctionLotsid'] }),
    ]);

    return new ListResultset(items.map(this.mapToDto), page, size, totalItems.length, Math.ceil(totalItems.length / size));
  }

  async getAllWinningBids(auctionId: string, page: number, size: number): Promise<any> {
    // Create filter from params or use an empty object if no params are provided
    const filter: any = { auctionId: auctionId };
    const skip: number = size > 0 ? (page - 1) * size : 0; // Calculate offset for pagination
    const take: number | undefined = size < 0 ? undefined : size; // Determine limit for pagination

    // Execute the query to fetch winning bids with pagination and return JSON
    const items: any = await this.prisma.$queryRaw<any>`
    WITH "RankedBids" AS (
      SELECT
        b."id",
        b."createdOn",
        b."createdBy",
        b."auctionsid",
        b."auctionLotsid",
        b."accountMappingsId",
        b."amount",
        ROW_NUMBER() OVER (PARTITION BY b."auctionLotsid" ORDER BY b."amount" DESC, b."createdOn" ASC) AS "rank"
      FROM
        "Bids" b
      WHERE
        b."auctionsid" = ${filter.auctionId} AND
        b."deletedOn" IS NULL -- Exclude deleted bids
    ),
    "WinningBids" AS (
      SELECT
        r."accountMappingsId",
        r."auctionsid" AS "auctionId",
        jsonb_build_object(
          'auctionLotsid', r."auctionLotsid",
          'amount', r."amount"
        ) AS "bid"
      FROM
        "RankedBids" r
      WHERE
        r."rank" = 1  -- Get only the highest bid for each lot
    )
    SELECT
      "auctionId",
      "accountMappingsId" AS "accountMappingId",
      jsonb_agg("bid") AS "bids"
    FROM
      "WinningBids"
    GROUP BY
      "auctionId", "accountMappingsId"
    ORDER BY
      "accountMappingId"
    LIMIT ${take} OFFSET ${skip};
  `;

    // Return the JSON response directly
    return { page, size, totalItems: items.length, totalPages: size > 0 ? Math.ceil(items.length / size) : 1, list: items };
  }

  async createOptimisticBid(accountMappingsId: string, newBid: BidDto): Promise<BidDto> {
    const filteredData: any = this.removeExcludedFields(newBid);
    return this.prisma.$transaction(async (prisma: Prisma.TransactionClient) => {
      // Lock the rows for the auction lot to prevent other transactions from modifying them
      await prisma.$executeRaw`SELECT 1 FROM "Bids" WHERE "auctionLotsid" = ${newBid.auctionLotsid} AND "deletedOn" IS NULL FOR UPDATE`;

      // Check the latest bid for the auction lot
      const latestBid: any = await prisma.bids.findFirst({ where: { auctionLotsid: newBid.auctionLotsid, deletedOn: null }, orderBy: { amount: 'desc' } });

      // If the latest bid is newer than the received bid, throw an error
      //scenario: 2 bids arrive at same time, the first one is higher than the second. the first bid gets saved.
      //                before we insert the 2nd bid we make sure there is not a more recent write with a higher (or equal) amount
      if (latestBid && latestBid.amount >= newBid.amount) {
        throw new BidCollisionOccurredError();
      }
      filteredData.createdBy = accountMappingsId;
      filteredData.createdOn = new Date().toISOString();
      // Insert the new bid
      let createdBid: any;
      try {
        createdBid = await prisma.bids.create({ data: { ...filteredData, accountMappingsId } });
      } catch (error) {
        this.logger.log(error.message);
        throw new BidCollisionOccurredError();
      }
      return new BidDto(createdBid);
    });
  }

  async removeHigherBids(bid: BidDto): Promise<BidDto> {
    return this.prisma.$transaction(async (prisma: Prisma.TransactionClient) => {
      // Lock the rows for the auction lot to prevent other transactions from modifying them
      await prisma.$executeRaw`
        SELECT *
        FROM "Bids"
        WHERE "auctionLotsid" = ${bid.auctionLotsid}
          FOR UPDATE`;

      // Remove all higher bids for the auction lot
      await prisma.bids.updateMany({
        where: { auctionLotsid: bid.auctionLotsid, deletedOn: null, amount: { gte: bid.amount } },
        data: { deletedBy: 'SYSTEM', deletedOn: new Date().toISOString() },
      });

      // Now add final bid to the auction lot of the amount that was passed in
      const filteredData: any = this.removeExcludedFields(bid);

      // Set the createdBy and updatedBy fields
      filteredData.createdBy = bid.accountMappingsId;
      filteredData.createdOn = new Date().toISOString();
      filteredData.updatedBy = bid.accountMappingsId;
      filteredData.updatedOn = new Date().toISOString();

      // Insert the new bid
      const createdBid: any = await prisma.bids.create({ data: filteredData });
      return this.mapToDto(createdBid);
    });
  }
}
