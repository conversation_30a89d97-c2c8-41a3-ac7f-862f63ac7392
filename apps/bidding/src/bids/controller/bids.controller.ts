import { ListResultset } from '@mvc/data/list-resultset';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Body, Controller, Get, Param, Post, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { BidDto } from '../dtos/bid.dto';
import { BidsHandler } from '../handler/bids.handler';
import { BidRequest } from '../http/requests/bid.request';
import { BidListResponse } from '../http/responses/bid-list.response';
import { BidResponse } from '../http/responses/bid.response';

import { ViewbidError } from '@mvc/exceptions/viewbid-error';
import { AccountMappingCachingService } from '@vb/account-mappings';
import { SystemOnly } from '@vb/authorization/decorators/systemOnly.decorator';
import { AccountMappingDto } from '@vb/nest/common-dtos/users/account-mapping.dto';
import { AccountPreferenceDto } from '@vb/nest/common-dtos/users/account-preference.dto';
import { UserDto } from '@vb/nest/common-dtos/users/user.dto';
import { RedisService } from '@vb/redis';
import { PubSubController } from '@vb/redis/controllers/pub-sub.controller';
import { EventDetailDto } from '@vb/redis/dtos/event-detail.dto';
import { AuctionCatalogsApi, GetAllByAccountMappingsIds200Response, ProfilesApi, UsersApi, UserStatus } from '@viewbid/ts-node-client';
import { AuctionLotFlattenedDto } from 'apps/auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';
import * as axios from 'axios';
import { Record } from 'prisma/prisma-client/runtime/library';
import { AuctionLotFlattenedWatchlistDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened-watchlist.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { AuctionLotBidPriceDto } from '../dtos/auction-lot-bid-price.dto';
import { BidHistoryDto } from '../dtos/bid-history.dto';
import { BidWinnerDto } from '../dtos/bid-winner.dto';
import { AccountMappingsNotFoundInBiddingData } from '../exceptions/account-mappings-not-found-in-bidding-data';
import { BidCollisionOccurredError } from '../exceptions/bid-collision-occured.error';
import { CouldNotProcessBidError } from '../exceptions/could-not-process-bid.error';
import { LowBidAttemptError } from '../exceptions/low-bid-attempt.error';
import { MaxPermittedBidAmountExceededError } from '../exceptions/max-permitted-bid-amount-exceeded.error';
import { UserIsPending } from '../exceptions/user-is-pending';
import { UserIsRestricted } from '../exceptions/user-is-restricted';
import { UserIsSuspended } from '../exceptions/user-is-suspended';
import { ValidProfileNotFound } from '../exceptions/valid-profile-not-found';
import { RemoveHigherBidsRequest } from '../http/requests/remove-higher-bids.request';
import { BidAuctionLotHistoryListResponse } from '../http/responses/bid-auction-lot-history-list.response';
import { BidHistoryListResponse } from '../http/responses/bid-history-list.response';
import { BidWinnersResponse } from '../http/responses/bid-winners.response';
import { LotBiddingDataResponse } from '../http/responses/lot-bidding-data.response';
import { ProxyBidService } from '../services/proxy-bid.service';
import { AuctionBiddingSummaryDto } from '../dtos/auction-bidding-summary.dto';
import { AuctionBiddingSummaryResponse } from '../http/responses/auction-bidding-summary.response';
import { ProxyBidsHandler } from '../handler/proxy-bids.handler';
import { MaxProxyDto } from '../dtos/max-proxy.dto';

@Controller('bidding')
export class BidsController extends PubSubController<BidsHandler, BidRequest, BidDto, BidResponse, BidListResponse> {
  constructor(
    private readonly bidsHandler: BidsHandler,
    private readonly proxyBidService: ProxyBidService,
    private readonly proxyBidsHandler: ProxyBidsHandler,
    private readonly usersApi: UsersApi,
    private readonly profilesApi: ProfilesApi,
    private readonly auctionsApi: AuctionCatalogsApi,
    redisService: RedisService,
    private readonly prisma: PrismaService,
    private readonly accountMappingCachingService: AccountMappingCachingService,
  ) {
    super(bidsHandler, redisService, 'bids');
  }
  createDtoFromRequest(request: BidRequest): BidDto {
    return new BidDto(request);
  }
  createResponseFromDto(dto: BidDto): BidResponse {
    return new BidResponse(dto);
  }
  createResponseList(list: ListResultset<BidDto>): BidListResponse {
    return new BidListResponse(list);
  }

  @Post('postBid')
  async createBid(@Body() body: BidRequest, @Req() req: VBInterceptorRequest): Promise<BidResponse | ErrorResponse> {
    const maxRetries: number = 3;
    let attempt: number = 0;
    let bidReceiveTime: number = Date.now();
    try {
      this.checkValidProfile(req);
      const bidRequest: BidDto = this.createDtoFromRequest(body);
      bidRequest.accountMappingsId = req.accountMappingsId;
      await this.checkBidLimits(req.accountMappingsId, bidRequest);

      while (attempt < maxRetries) {
        //this is necessary if a bid collision occurs, reset the value to the original proxy to start again
        if (bidRequest.proxyAmount > 0 && bidRequest.amount !== bidRequest.proxyAmount) {
          bidRequest.amount = bidRequest.proxyAmount;
        }
        bidReceiveTime = Date.now(); // Current time in milliseconds
        try {
          const result: BidDto = await this.prisma.$transaction(
            async () => {
              const bidValues: { bid: BidDto; previousHighBid: BidDto; auctionLot: AuctionLotFlattenedDto } = await this.proxyBidService.createBid(
                req.accountMappingsId,
                bidRequest,
                bidReceiveTime,
                attempt === 0,
              );
              const bid: BidDto = bidValues.bid;

              this.proxyBidService.pushOutBidEvent(bid, bidValues.previousHighBid, bidValues.auctionLot).catch(() => {
                this.logger.error('Could not push out events for the following lot: ', bid.auctionLotsid, ' and currentBidder: ', bid.createdBy);
              });

              return bid;
            },
            // { timeout: 300000 }, // Set transaction timeout to 5 minutes while debugging. do not commit this line);
          );
          const bidCompletionTime: number = Date.now();
          this.logger.log('elapsed bid processing time: ' + (bidCompletionTime - bidReceiveTime));
          return this.createResponseFromDto(result);
        } catch (error) {
          //because the Transaction() returns a generic error we get the actual error from the service
          const serviceError: any = this.proxyBidService.getException(req.accountMappingsId);
          if (serviceError) {
            error = serviceError;
          }
          this.logger.error(error.message);
          if (error instanceof BidCollisionOccurredError) {
            attempt++;
            if (attempt >= maxRetries) {
              const bidCompletionTime: number = Date.now();
              this.logger.log('elapsed bid processing time: ' + (bidCompletionTime - bidReceiveTime));

              return new ErrorResponse(new LowBidAttemptError(), 'createBid');
            }
          } else {
            const bidCompletionTime: number = Date.now();
            this.logger.log('elapsed bid processing time: ' + (bidCompletionTime - bidReceiveTime));
            this.logger.error(error);
            this.notify('error', 'create', new EventDetailDto({ newBid: body, accountMappingsId: req.accountMappingsId }));
            this.logger.log(error.constructor.name);
            if (error instanceof ViewbidError) {
              return new ErrorResponse(error, 'createBid');
            }
            return new ErrorResponse(new CouldNotProcessBidError(bidRequest.auctionLotsid), 'createBid');
          }
        }
      }
    } catch (error) {
      const bidCompletionTime: number = Date.now();
      this.logger.log('elapsed bid processing time: ' + (bidCompletionTime - bidReceiveTime));
      this.notify('error', 'create', new EventDetailDto({ newBid: body, accountMappingsId: req.accountMappingsId }));
      return new ErrorResponse(error, 'createBid');
    }
    return new ErrorResponse(new LowBidAttemptError(), 'createBid');
  }

  private checkValidProfile(req: VBInterceptorRequest): boolean {
    if (req.accountMappingsId == undefined || req.accountMappingsId == null || req.accountMappingsId.length <= 0) {
      throw new ValidProfileNotFound();
    }
    return true;
  }

  private async checkBidLimits(accountMappingsId: string, bidRequest: BidDto): Promise<void> {
    const accountMapping: AccountMappingDto = await this.accountMappingCachingService.getAccountMapping(accountMappingsId);
    const user: UserDto = accountMapping.user;

    switch (user.status) {
      case UserStatus.Deactivated:
      case UserStatus.Denied:
        throw new UserIsSuspended(user.id);
      case UserStatus.Restricted:
        throw new UserIsRestricted(user.id);
      case UserStatus.Pending:
        throw new UserIsPending(user.id);
      case UserStatus.Active:
      default:
      //Do nothing and move forward. Included here to be verbose.
    }

    const accountPreference: AccountPreferenceDto = new AccountPreferenceDto(accountMapping.accountPreferences[0]);
    if (accountPreference.spendingCap > 0 && bidRequest.amount > accountPreference.spendingCap) {
      throw new MaxPermittedBidAmountExceededError(bidRequest.amount, accountPreference.spendingCap);
    }
  }

  @Get('getLotBiddingDataByAuctionLotIds')
  async getLotBiddingDataByAuctionLotIds(@Query('auctionLotsIds') auctionLotsIds: string): Promise<LotBiddingDataResponse | ErrorResponse> {
    try {
      const auctionLotBidPrices: AuctionLotBidPriceDto[] = await this.bidsHandler.getLotBiddingDataByAuctionLotIds(auctionLotsIds);

      return new LotBiddingDataResponse(auctionLotBidPrices);
    } catch (error) {
      this.logger.error(error.message);

      return new ErrorResponse(error, 'getLotBiddingData');
    }
  }

  @Get('getAllBidWinners')
  async getAllBidWinners(@Query('auctionLotsIds') auctionLotsIds: string): Promise<BidWinnersResponse | ErrorResponse> {
    try {
      const bidWinners: BidWinnerDto[] = await this.bidsHandler.getAllBidWinners(auctionLotsIds);

      return new BidWinnersResponse(bidWinners);
    } catch (error) {
      this.logger.error(error);

      return new ErrorResponse(error, 'getAllBidWinners');
    }
  }

  @Get('history/:auctionLotId')
  async getAuctionLotBidHistory(
    @Param('auctionLotId') auctionLotsId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') _query: string,
    @Req() _req: VBInterceptorRequest,
  ): Promise<BidAuctionLotHistoryListResponse | ErrorResponse> {
    try {
      const params: Record<string, any>[] = [{ auctionLotsid: auctionLotsId }];
      const bidDtos: ListResultset<BidDto> = await this.bidsHandler.getAll(page, size, params);
      const accountMappingsIds: string[] = bidDtos.list.map((bid: BidDto) => bid.accountMappingsId);
      if (accountMappingsIds.length <= 0) {
        throw new AccountMappingsNotFoundInBiddingData();
      }

      const userDictionary: { [key: string]: any } = await this.getUserDictionary(accountMappingsIds);
      const bidHistory: BidHistoryDto[] = bidDtos.list
        .filter((bid: BidDto) => userDictionary[bid.accountMappingsId])
        .map((bid: BidDto) => new BidHistoryDto(bid, userDictionary[bid.accountMappingsId]));

      return new BidAuctionLotHistoryListResponse(new ListResultset(bidHistory, page, size, bidHistory.length, Math.ceil(bidHistory.length / size)));
    } catch (error) {
      this.logger.error(error);

      return new ErrorResponse(error, 'getBidHistory');
    }
  }

  @Get('myBidHistory')
  async getMyBidHistory(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Query('orderBy') orderBy: string | undefined,
    @Query('filter') _filter: string | undefined,
    @Req() req: VBInterceptorRequest,
  ): Promise<BidHistoryListResponse | ErrorResponse> {
    try {
      const params: any[] = [{ accountMappingsId: req.accountMappingsId }];
      const bids: ListResultset<BidDto> = await this.handler.getUserBidHistory(1, -1, params);
      if (bids.list.length > 0) {
        const auctionLotsids: string[] = bids.list.map((dto: BidDto) => dto.auctionLotsid);
        const itemsResponse: any = await this.auctionsApi.getAuctionLotsByIds(page, size, query, { auctionLotIds: auctionLotsids }, '', orderBy);
        const maxProxies: MaxProxyDto[] = await this.proxyBidsHandler.getMaxProxiesForAuctionLots(req.accountMappingsId, auctionLotsids);

        itemsResponse.data.data.forEach((item: AuctionLotFlattenedWatchlistDto) => {
          const maxProxyAmount: number | undefined = maxProxies.find((proxy: MaxProxyDto) => proxy.auctionLotsId === item.auctionLotId)?.proxyAmount;
          const recentBid: BidDto | undefined = bids.list.find((bid: BidDto) => bid.auctionLotsid === item.auctionLotId);
          // display the proxy if it is higher than the most recent bid
          item.userBid = Math.max(maxProxyAmount ?? 0, recentBid?.amount ?? 0);
          item.isProxy = recentBid?.isProxy === 1 || item.userBid === maxProxyAmount ? 1 : 0;
        });

        return new BidHistoryListResponse(
          new ListResultset<AuctionLotFlattenedWatchlistDto>(
            itemsResponse.data.data,
            itemsResponse.data.page,
            itemsResponse.data.pageSize,
            itemsResponse.data.totalCount,
            itemsResponse.totalPages,
          ),
        );
      }
      return new BidHistoryListResponse(new ListResultset<BidHistoryListResponse>([], 1, 0, 0, 1));
    } catch (error) {
      return new ErrorResponse(error, 'getAll');
    }
  }

  async getUserDictionary(accountMappingsIds: string[]): Promise<any> {
    const userResponse: axios.AxiosResponse<GetAllByAccountMappingsIds200Response> = await this.usersApi.getAllByAccountMappingsIds(accountMappingsIds.join(','), 1, -1);

    return userResponse.data.data.reduce(
      (acc: any, user: any) => {
        acc[user.accountMappingsId] = user;
        return acc;
      },
      {} as { [key: string]: any },
    );
  }

  @SystemOnly()
  @Post('removeHigherBids')
  async removeHigherBids(@Body() body: RemoveHigherBidsRequest): Promise<BidResponse | ErrorResponse> {
    try {
      const newBid: BidDto = new BidDto({ ...body, accountMappingsId: body.userId, isBuyNow: 1, auctionsid: body.auctionId, auctionLotsid: body.auctionLotId });
      const bid: BidDto = await this.bidsHandler.removeHigherBids(newBid);
      return new BidResponse(bid);
    } catch (error) {
      return new ErrorResponse(error, 'removeHigherBids');
    }
  }

  @SystemOnly()
  @Get('getAuctionBiddingSummary/:auctionId')
  async getAuctionBiddingSummary(@Param('auctionId') auctionId: string): Promise<AuctionBiddingSummaryResponse | ErrorResponse> {
    try {
      await this.auctionsApi.getAuctionById(auctionId);

      const summary: AuctionBiddingSummaryDto = await this.handler.getAuctionBiddingSummary(auctionId);

      return new AuctionBiddingSummaryResponse(summary);
    } catch (error) {
      return new ErrorResponse(error, 'getAuctionBiddingSummary');
    }
  }
}
