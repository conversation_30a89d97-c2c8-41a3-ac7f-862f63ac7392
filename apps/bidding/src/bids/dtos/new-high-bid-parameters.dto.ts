export class NewHighBidParameters {
  readonly highBidder: string;
  readonly currentHighBid: number;
  readonly auctionLotsId: string;
  readonly lotItemName: string;
  readonly previousHighBid: number;
  readonly lotNumber: string;
  readonly imageURL: string;
  readonly link: string;

  constructor(parameters: { [key: string]: string | number }) {
    this.highBidder = parameters['previousHighBidder'] as string;
    this.currentHighBid = parameters['currentHighBid'] as number;
    this.auctionLotsId = parameters['auctionLotsId'] as string;
    this.lotItemName = parameters['lotItemName'] as string;
    this.previousHighBid = parameters['previousHighBid'] as number;
    this.lotNumber = parameters['lotNumber'] as string;
    this.imageURL = parameters['imageURL'] as string;
    this.link = parameters['link'] as string;
  }
}
