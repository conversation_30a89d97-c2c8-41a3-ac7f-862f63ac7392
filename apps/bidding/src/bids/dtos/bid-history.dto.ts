import { BidDto } from './bid.dto';

export class BidHistoryDto {
  bidderName: string;
  dateTimePlaced: string;
  amount: number;
  isProxy: boolean;
  constructor(bidDto: BidDto, userData: any) {
    this.bidderName = this.formatBidderName(userData.firstname, userData.lastname);
    this.dateTimePlaced = bidDto.createdOn;
    this.amount = bidDto.amount;
    this.isProxy = Boolean(bidDto.isProxy);
  }

  formatBidderName(firstName: string, lastName: string): string {
    const firstInitial: string = firstName[0];
    const lastInitial: string = lastName[lastName.length - 1];

    const hiddenPartLength: number = firstName.length + lastName.length - 1;
    const hiddenPart: string = '*'.repeat(hiddenPartLength);

    return `${firstInitial}${hiddenPart}${lastInitial}`;
  }
}
