export class BiddingDataDto {
  bidCount: number;
  currentPrice: number;
  numberOfWatchers: number;
  highBidder: string;
  minimumBid: number;
  remainingTime: string | null;
  endDate: string | undefined;
  serverTimezone: string;
  auctionLotsId: string;
  isProxy: number;

  constructor(row: any) {
    this.bidCount = row.bidCount;
    this.currentPrice = row.currentPrice;
    this.numberOfWatchers = row.numberOfWatchers;
    this.highBidder = row.highBidder;
    this.minimumBid = row.minimumBid;
    this.remainingTime = row.remainingTime;
    this.endDate = row.endDate;
    this.serverTimezone = row.serverTimezone;
    this.auctionLotsId = row.auctionLotsId;
    this.isProxy = row.isProxy;
  }
}
