import { BidDto } from './bid.dto';

export class ProxyBidDto {
  readonly id: string;
  readonly accountMappingsId: string;
  proxyBidAmount: number;
  readonly auctionLotsId: string;
  isProxy: number;
  currentMax: number;

  constructor(row: any) {
    this.id = row.id;
    this.accountMappingsId = row.accountMappingsId;
    this.proxyBidAmount = row.proxyBidAmount;
    this.auctionLotsId = row.auctionLotsId;
    //if row holds isProxy value, it's from a build() command.
    //if undefined it's from the ProxyBids table so set to true
    this.isProxy = row.isProxy ?? 1;
  }

  static build(userId: string, bid: BidDto): ProxyBidDto {
    return new ProxyBidDto({
      accountMappingsId: userId,
      proxyBidAmount: bid.amount,
      auctionLotsId: bid.auctionLotsid,
    });
  }
}
