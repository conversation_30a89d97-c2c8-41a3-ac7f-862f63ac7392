import { ProxyBidDto } from './proxy-bid.dto';
import { GetAuctionById200Response } from '@viewbid/ts-node-client';

export class BidDto {
  id: string;
  createdOn: string;
  createdBy: string;
  auctionsid: string;
  auctionLotsid: string;
  accountMappingsId: string;
  amount: number;
  isProxy: number;
  isQuickBid: number;
  isBuyNow: number;
  metaData: string;
  isProxyTopUp: boolean;
  proxyAmount: number;

  constructor(row: any) {
    this.id = row.id;
    this.createdOn = row.createdOn;
    this.createdBy = row.createdBy;
    this.auctionsid = row.auctionsid;
    this.auctionLotsid = row.auctionLotsid;
    this.accountMappingsId = row.accountMappingsId;
    this.amount = row.amount;
    this.isProxy = row.isProxy;
    this.isQuickBid = row.isQuickBid;
    this.isBuyNow = row.isBuyNow;
    this.metaData = row.metaData;
    this.proxyAmount = Number(row.amount);
  }

  static createNewBid(userId: string, bid: BidDto, auction: GetAuctionById200Response): BidDto {
    return new BidDto({
      auctionsid: auction.id,
      createdOn: bid.createdOn,
      auctionLotsid: bid.auctionLotsid,
      accountMappingsId: userId,
      amount: bid.amount,
      isProxy: bid.isProxy,
      isQuickBid: bid.isQuickBid,
      isBuyNow: bid.isBuyNow,
      metaData: '',
      proxyAmount: bid.amount,
    });
  }

  static buildFromProxyBid(winningBid: ProxyBidDto, receivedBid: BidDto): BidDto {
    return new BidDto({
      auctionsid: receivedBid.auctionsid,
      auctionLotsid: winningBid.auctionLotsId,
      accountMappingsId: winningBid.accountMappingsId,
      amount: winningBid.currentMax ?? winningBid.proxyBidAmount,
      isProxy: winningBid.isProxy,
      isQuickBid: 0,
      isBuyNow: 0,
      metaData: '',
      proxyAmount: winningBid.proxyBidAmount,
    });
  }

  static emptyObject(): BidDto {
    return new BidDto({
      auctionsid: '',
      auctionLotsid: '',
      accountMappingsId: '',
      amount: 0,
      isProxy: 0,
      isQuickBid: 0,
      isBuyNow: 0,
      metaData: '',
    });
  }

  toProxyBid(): ProxyBidDto {
    return new ProxyBidDto({
      id: this.id,
      accountMappingsId: this.accountMappingsId,
      proxyBidAmount: this.amount,
      auctionLotsId: this.auctionLotsid,
      currentMax: this.amount,
      isProxy: this.isProxy,
    });
  }
}
