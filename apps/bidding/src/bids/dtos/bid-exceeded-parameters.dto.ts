import { ProxyBidDto } from './proxy-bid.dto';
import { BidDto } from './bid.dto';
import { AuctionLotFlattenedDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened.dto';

export class BidExceededParametersDto {
  readonly lotItemName: string;
  readonly auctionLotsId: string;
  readonly previousHighBid: number;
  readonly recipientName: string;
  readonly currentHighBid: number;
  readonly lotNumber: number;
  readonly link: string;
  readonly imageURL: string;

  constructor(lostProxyBid: ProxyBidDto, winningBid: ProxyBidDto, receivedBid: BidDto, auctionLot: AuctionLotFlattenedDto, baseURL: string, cdnURL: string) {
    //profile: ProfileDto,
    this.auctionLotsId = receivedBid.auctionLotsid;
    this.previousHighBid = lostProxyBid.proxyBidAmount;
    this.recipientName = '';
    this.currentHighBid = winningBid.proxyBidAmount;
    this.lotItemName = auctionLot.title;
    this.lotNumber = auctionLot.number;
    this.link = `${baseURL}/auctions/${auctionLot.auctionId}/lot/${auctionLot.lotId}`;
    this.imageURL = auctionLot.images.length > 0 ? (auctionLot.images[0]?.url ?? '') : `${cdnURL}/no-image-lot.svg`;
  }
}
