import { Injectable } from '@nestjs/common';
import { AccountMappingCachingService } from '@vb/account-mappings';
import { CachingServiceNotFoundError } from '@vb/caching/exceptions/caching-service-not-found.error';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { RedisService } from '@vb/redis';
import { BaseInvalidateCacheSubscriber } from '../../../../libs/caching/src/subscribers/base-invalidate-cache-subscriber';
import { AuctionsCachingService } from '../../../auction-catalogs/src/services/auctions-caching.service';
import { AccountPreferencesCachingService } from '../../../users/src/services/account-preferences-caching.service';
import { BidIncrementsCachingService } from '../bids/services/bid-increments-caching.service';

@Injectable()
export class MultiCacheInvalidationSubscriber extends BaseInvalidateCacheSubscriber {
  constructor(protected readonly redisService: RedisService) {
    super(redisService);
  }

  protected async deleteCacheByEventType(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    // Determine the cache key prefix based on the event type
    if (eventType.startsWith('USERS_ROLES') || eventType.startsWith('ACCOUNTPREFERENCE')) {
      this.logger.log('deleting account preferences cache in bidding');
      await AccountPreferencesCachingService.deleteCache(this.redisService, eventPayload.items.id);
      return;
    } else if (eventType.startsWith('ACCOUNTMAPPING')) {
      this.logger.log('deleting account mapping cache in bidding');
      await AccountMappingCachingService.deleteCache(this.redisService, eventPayload.items.id);
      return;
    } else if (eventType.startsWith('BIDINCREMENTS')) {
      this.logger.log('deleting bid increments cache in bidding');
      await BidIncrementsCachingService.deleteCache(this.redisService, eventPayload.items.data[0].auctionId);
      return;
    } else if (eventType.startsWith('AUCTIONS_STATUS')) {
      await AuctionsCachingService.deleteCache(this.redisService, eventPayload.items.data.id);
      return;
    } else if (eventType.startsWith('AUCTIONS')) {
      this.logger.log('deleting auctions cache in bidding');
      await BidIncrementsCachingService.deleteCache(this.redisService, eventPayload.items.id);
      return;
    } else if (eventType.startsWith('BIDS')) {
      this.logger.log('deleting bids cache in bidding');
      await BidIncrementsCachingService.deleteCache(this.redisService, eventPayload.items.id);
      return;
    }
    throw new CachingServiceNotFoundError(eventType);
  }
}
