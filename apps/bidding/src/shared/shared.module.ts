import { LoadEntitiesInterceptor } from '@mvc/interceptors/load-entities.interceptor';
import { Global, Module } from '@nestjs/common';
import { prismaTransactional } from '@transactional/prisma';
import { AccountMappingCachingService } from '@vb/account-mappings/services/account-mapping-caching.service';
import { AwsSqsService } from '@vb/nest/services/aws.sqs.service';
import { RedisService } from '@vb/redis/redis.service';
import * as api from '@viewbid/ts-node-client';
import { ConfigService } from 'libs/config-service/src/config.service';
import { axiosSecureInstance } from 'libs/ssl/src';
import { BidsDbService } from '../bids/data/bids-db.service';
import { BidsHandler } from '../bids/handler/bids.handler';
import { PrismaService } from '../prisma/prisma.service';
import { MultiCacheInvalidationSubscriber } from './multi-cache-invalidation.subscriber';
import { CachingModule } from '@vb/caching/caching.module';

@Global()
@Module({
  imports: [CachingModule],
  providers: [
    BidsDbService,
    BidsHandler,
    MultiCacheInvalidationSubscriber,
    {
      provide: AccountMappingCachingService,
      useFactory: async (redisService: RedisService, profilesApi: api.ProfilesApi): Promise<AccountMappingCachingService> => {
        return new AccountMappingCachingService(redisService, profilesApi);
      },
      inject: [RedisService, api.ProfilesApi],
    },
    {
      provide: PrismaService,
      useValue: new PrismaService().$extends(prismaTransactional),
    },
    {
      provide: api.AuctionCatalogsApi,
      useFactory: async (configService: ConfigService): Promise<api.AuctionCatalogsApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('AUCTIONS_SERVICE_URL');

        return new api.AuctionCatalogsApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.UsersApi,
      useFactory: async (configService: ConfigService): Promise<api.UsersApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('USERS_SERVICE_URL');

        return new api.UsersApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.ProfilesApi,
      useFactory: async (configService: ConfigService): Promise<api.ProfilesApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('USERS_SERVICE_URL');

        return new api.ProfilesApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: api.SocketsApi,
      useFactory: async (configService: ConfigService): Promise<api.SocketsApi> => {
        const accessToken: string | undefined = await configService.getSetting('SERVICE_TO_SERVICE_SECRET');
        const serviceUrl: string | undefined = await configService.getSetting('SOCKETS_SERVICE_URL');

        return new api.SocketsApi(
          {
            isJsonMime: () => true,
            accessToken: accessToken,
          },
          serviceUrl,
          axiosSecureInstance,
        );
      },
      inject: [ConfigService],
    },
    {
      provide: 'queueUrls',
      useFactory: async (configService: ConfigService): Promise<any> => {
        const notificationsUrl: string | undefined = await configService.getSetting('MESSAGES_QUEUE_URL');
        const endingSoonUrl: string | undefined = await configService.getSetting('LOT_ENDING_SOON_QUEUE_URL');
        const auctionPublishUrl: string | undefined = await configService.getSetting('AUCTION_PUBLISHED_QUEUE_URL');
        const auctionCloseUrl: string | undefined = await configService.getSetting('AUCTION_CLOSED_QUEUE_URL');
        const auctionOpenUrl: string | undefined = await configService.getSetting('AUCTION_OPEN_QUEUE_URL');
        const auctionEndingSoonUrl: string | undefined = await configService.getSetting('AUCTION_REMINDER_QUEUE_URL');
        const lotItemClosedUrl: string | undefined = await configService.getSetting('LOT_ITEM_CLOSED_QUEUE_URL');
        const deleteLotItemUserNotesUrl: string | undefined = await configService.getSetting('LOT_ITEM_USER_NOTES_TO_DELETE_QUEUE_URL');
        return {
          notifications: notificationsUrl,
          endingSoon: endingSoonUrl,
          auctionPublish: auctionPublishUrl,
          auctionClose: auctionCloseUrl,
          auctionOpen: auctionOpenUrl,
          auctionEndingSoon: auctionEndingSoonUrl,
          lotItemClosed: lotItemClosedUrl,
          deleteLotItemUserNotes: deleteLotItemUserNotesUrl,
        };
      },
      inject: [ConfigService],
    },
    AwsSqsService,
    {
      provide: 'SERVICES_MAP',
      useFactory: (bidsHandler: BidsHandler): Record<string, any> => {
        return {
          _bidId: bidsHandler,
        };
      },
      inject: [BidsHandler],
    },
    {
      provide: LoadEntitiesInterceptor,
      useFactory: (serviceMap: Record<string, any>): LoadEntitiesInterceptor => {
        return new LoadEntitiesInterceptor(serviceMap);
      },
      inject: ['SERVICES_MAP'],
    },
  ],
  exports: [PrismaService, api.AuctionCatalogsApi, api.ProfilesApi, api.SocketsApi, api.UsersApi, AwsSqsService, AccountMappingCachingService, MultiCacheInvalidationSubscriber],
})
export class SharedModule {}
