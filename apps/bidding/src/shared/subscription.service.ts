import { Injectable } from '@nestjs/common';
import { EventManager } from '@vb/nest/events/event.manager';
import { LoggingSubscriber } from '@vb/nest/events/logging.subscriber';
import { EventType } from '@vb/nest/events/event-type';
import { Subscription, SubscriptionBase } from '@mvc/events/subscription-base';
import { AddWatcherToAuctionLotSubscriber } from '../watchers/subscribers/add-watcher-to-auction-lot.subscriber';
import { GenerateInvoicesOnAuctionCloseSubscriber } from '../bids/subscribers/generate-invoices-on-auction-close.subscriber';
import { CreateBidForAcceptedOfferSubscriber } from '../bids/subscribers/create-bid-for-accepted-offer.subscriber';
import { MultiCacheInvalidationSubscriber } from './multi-cache-invalidation.subscriber';

@Injectable()
export class SubscriptionService extends SubscriptionBase {
  constructor(
    readonly eventManager: EventManager,
    private readonly loggingSubscriber: LoggingSubscriber,
    private readonly addWatcherToAuctionLotSubscriber: AddWatcherToAuctionLotSubscriber,
    private readonly generateInvoicesOnAuctionCloseSubscriber: GenerateInvoicesOnAuctionCloseSubscriber,
    private readonly createBidForAcceptedOfferSubscriber: CreateBidForAcceptedOfferSubscriber,
    private readonly multiCacheInvalidationSubscriber: MultiCacheInvalidationSubscriber,
  ) {
    super(eventManager);
  }

  getSubscriptions(): Subscription[] {
    return this.subscriptions;
  }

  private readonly subscriptions: Subscription[] = [
    { type: EventType.CreateBidSuccess, subscriber: this.loggingSubscriber },
    { type: EventType.CreateBidSuccess, subscriber: this.addWatcherToAuctionLotSubscriber },
    { type: EventType.CreateBidSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.CreateBidFail, subscriber: this.loggingSubscriber },
    { type: EventType.AllAuctionLotsClosedSuccess, subscriber: this.generateInvoicesOnAuctionCloseSubscriber },
    { type: EventType.PurchaseOffersAcceptedSuccess, subscriber: this.createBidForAcceptedOfferSubscriber },
    { type: EventType.UpdateAuctionSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.BidIncrementsSaveSuccess, subscriber: this.multiCacheInvalidationSubscriber },
    { type: EventType.UpdateAuctionStatusSuccess, subscriber: this.multiCacheInvalidationSubscriber },
  ];
}
