import { AccessTokenGuard } from '@http/authentication/guards/access-token.guard';
import { AccessTokenInterceptor } from '@http/authentication/interceptors/access-token.interceptor';
import { createRequestAuditInterceptor } from '@mvc/interceptors/request-audit.interceptor';
import { Logger, Module, OnApplicationShutdown, OnModuleInit } from '@nestjs/common';
import { ConfigModule as NestJsConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, ModuleRef } from '@nestjs/core';
import { PassportModule } from '@nestjs/passport';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { AuthorizationModule } from '@vb/authorization';
import { CasbinInitConfig } from '@vb/authorization/config/init-auth';
import { HealthModule } from '@vb/health/health.module';
import { getLogger } from '@vb/nest';
import { EventManager } from '@vb/nest/events/event.manager';
import { LoggingSubscriber } from '@vb/nest/events/logging.subscriber';
import { ErrorLoggingInterceptor } from '@vb/nest/intercepters/error-logging.interceptor';
import { openTelemetry } from '@vb/otel/instrumentation';
import { RedisModule } from '@vb/redis';
import { UsersApi } from '@viewbid/ts-node-client';
import { ConfigModule } from 'libs/config-service/src/config.module';
import * as rt from 'route-trie';
import { AuctionsCachingService } from '../../auction-catalogs/src/services/auctions-caching.service';
import { ApplicationPreferencesModule } from './application-preferences/app-preferences.module';
import { BidsModule } from './bids/bids.module';
import { BidsDbService } from './bids/data/bids-db.service';
import { BidsHandler } from './bids/handler/bids.handler';
import { GenerateInvoicesOnAuctionCloseSubscriber } from './bids/subscribers/generate-invoices-on-auction-close.subscriber';
import { CreateBidForAcceptedOfferSubscriber } from './bids/subscribers/create-bid-for-accepted-offer.subscriber';
import { BiddingCreateGraphInterceptor } from './interceptors/bidding-create-graph.interceptor';
import { PrismaService } from './prisma/prisma.service';
import { SharedModule } from './shared/shared.module';
import { SubscriptionService } from './shared/subscription.service';
import { WatchersDbService } from './watchers/data/watchers-db.service';
import { WatchersHandler } from './watchers/handler/watchers.handler';
import { AddWatcherToAuctionLotSubscriber } from './watchers/subscribers/add-watcher-to-auction-lot.subscriber';
import { WatchersModule } from './watchers/watchers.module';
import { ServiceToServiceGuard } from '@http/authentication/guards/service-to-service.gaurd';

@Module({
  imports: [
    SentryModule.forRoot(),
    getLogger(),
    ApplicationPreferencesModule,
    AuthorizationModule,
    BidsModule,
    ConfigModule.forRoot({
      provide: 'PRISMA_SERVICE',
      useExisting: PrismaService,
    }),
    HealthModule,
    NestJsConfigModule.forRoot({
      envFilePath: ['./apps/bidding/.bidding.env'],
    }),
    PassportModule,
    RedisModule,
    SharedModule,
    WatchersModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: AccessTokenInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AccessTokenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ServiceToServiceGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: (prismaService: PrismaService): any => createRequestAuditInterceptor(prismaService),
      inject: [PrismaService],
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorLoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: async (
        prismaService: PrismaService,
        usersApi: UsersApi,
        routeTree: [rt.Trie, CasbinInitConfig],
        moduleRef: ModuleRef,
      ): Promise<BiddingCreateGraphInterceptor> => {
        return new BiddingCreateGraphInterceptor(moduleRef, usersApi, routeTree, prismaService);
      },
      inject: [PrismaService, UsersApi, 'ROUTE_TREE', ModuleRef],
    },
    AddWatcherToAuctionLotSubscriber,
    AuctionsCachingService,
    BidsDbService,
    BidsHandler,
    EventManager,
    GenerateInvoicesOnAuctionCloseSubscriber,
    CreateBidForAcceptedOfferSubscriber,
    LoggingSubscriber,
    SubscriptionService,
    WatchersDbService,
    WatchersHandler,
  ],
  exports: [],
})
export class BiddingModule implements OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(BiddingModule.name);

  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing OpenTelemetry...');
    try {
      await openTelemetry.start();
    } catch (e) {
      this.logger.error('Failed to initialize OpenTelemetry', e);
    }
  }

  async onApplicationShutdown(signal?: string): Promise<void> {
    this.logger.log({ signal }, 'Shutting down...');
    try {
      await openTelemetry.shutdown();
    } catch (e) {
      this.logger.error('Failed to shut down OpenTelemetry', e);
    }
    Logger.flush();
  }
}
