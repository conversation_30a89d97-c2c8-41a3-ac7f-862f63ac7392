import { LotItemUserNoteDto } from '../../../../auction-catalogs/src/lot-item-user-notes/dtos/lot-item-user-note.dto';

export class WatchedListItemDto {
  readonly watcherId: string;
  readonly auctionLotId: string;
  readonly auctionId: string;
  readonly auctionStatus: string;
  readonly auctionToDatetime: string;
  readonly buyerPremium: number;
  readonly condition: string;
  readonly currentBid: number;
  readonly currentHighBidderId: string;
  readonly fromDatetime: string;
  readonly imageURL: string | null;
  readonly isDisplayFinalPrice: boolean;
  readonly isDisplaySoldItems: boolean;
  readonly lotId: string;
  readonly lotNumber: string;
  readonly lotPickupLocation: string;
  readonly lotStatus: string;
  readonly lotToDatetime: string;
  readonly minimumBidAmount: number;
  readonly pickupLocation: string;
  readonly reserveAmount: number;
  readonly title: string;
  readonly suffix: string;
  readonly lotItemUserNotes: LotItemUserNoteDto[] | null;
  readonly isProxyBid: boolean;
  readonly bidSnipeWindow: number;

  constructor(watcherId: string, row: any) {
    this.watcherId = watcherId;
    this.auctionLotId = row.auctionLotId;
    this.auctionId = row.auctionId;
    this.auctionStatus = row.auctionStatus;
    this.auctionToDatetime = row.auctionToDatetime;
    this.buyerPremium = row.buyerPremium;
    this.condition = row.condition;
    this.currentBid = row.currentBid;
    this.currentHighBidderId = row.currentHighBidderId;
    this.fromDatetime = row.fromDatetime;
    this.imageURL = row.imageURL;
    this.isDisplayFinalPrice = row.isDisplayFinalPrice;
    this.isDisplaySoldItems = row.isDisplaySoldItems;
    this.lotId = row.lotId;
    this.lotNumber = row.lotNumber;
    this.lotPickupLocation = row.lotPickupLocation;
    this.lotStatus = row.lotStatus;
    this.lotToDatetime = row.lotToDatetime;
    this.minimumBidAmount = row.minimumBidAmount;
    this.pickupLocation = row.pickupLocation;
    this.reserveAmount = row.reserveAmount;
    this.title = row.title;
    this.suffix = row.suffic;
    this.lotItemUserNotes = row.lotItemUserNotes;
    this.isProxyBid = row.isProxyBid;
    this.bidSnipeWindow = row.bidSnipeWindow;
  }
}
