import { WatcherType } from '@viewbid/ts-node-client';

export class WatcherDto {
  id: string;
  createdOn: string;
  createdBy: string;
  deletedOn: string;
  deletedBy: string;
  auctionsid: string;
  auctionLotsid: string;
  accountMappingsId: string;
  watcherType: WatcherType;

  constructor(row: any) {
    this.id = row.id;
    this.auctionsid = row.auctionsid;
    this.auctionLotsid = row.auctionLotsid;
    this.accountMappingsId = row.accountMappingsId;
    this.watcherType = row.watcherType;
    this.createdOn = row.createdOn;
    this.createdBy = row.createdBy;
    this.deletedOn = row.deletedOn;
    this.deletedBy = row.deletedBy;
  }
}
