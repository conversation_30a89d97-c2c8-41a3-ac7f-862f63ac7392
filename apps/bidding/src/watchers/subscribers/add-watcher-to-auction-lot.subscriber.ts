import { SubscriberInterface } from '@vb/redis/subscribers/subscriber.interface';
import { EventPayload, EventType } from '@vb/nest/events/event-type';
import { WatcherDto } from '../dtos/watcher.dto';
import { SocketsApi, WatcherType } from '@viewbid/ts-node-client';
import { PrismaClientKnownRequestError } from 'prisma/prisma-client/runtime/library';
import { WatchersHandler } from '../handler/watchers.handler';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class AddWatcherToAuctionLotSubscriber implements SubscriberInterface {
  private watchersHandler: WatchersHandler;
  private logger = new Logger(AddWatcherToAuctionLotSubscriber.name);

  constructor(
    watchersHandler: WatchersHandler,
    private socketService: SocketsApi,
  ) {
    this.watchersHandler = watchersHandler;
  }

  async handleEvent(eventType: EventType, eventPayload: EventPayload): Promise<void> {
    const { auctionLot, newBid, previousBid, accountMappingsId } = eventPayload.items.details;
    const watcherDto: WatcherDto = new WatcherDto(newBid);
    let result: WatcherDto | void = watcherDto;
    watcherDto.watcherType = WatcherType.Bidder;
    try {
      const params: Record<string, any>[] = [
        { auctionsid: watcherDto.auctionsid, auctionLotsid: watcherDto.auctionLotsid, accountMappingsId: accountMappingsId, deletedOn: { not: null } },
      ];
      const watcher: WatcherDto = await this.watchersHandler.getCustom(params, WatcherDto);
      if (watcher && watcher.deletedBy) {
        result = await this.watchersHandler.restore(accountMappingsId, watcher.id).catch((error: PrismaClientKnownRequestError) => {
          this.logger.log(`Watcher restore has failed for the user ${accountMappingsId} and watcher id: ${watcher.id}`, error);
        });
      }

      this.logger.log(`Watcher already exists for watcher id: ${watcher.id}`);
    } catch (error) {
      result = await this.watchersHandler.create(newBid.createdBy, watcherDto).catch((error: PrismaClientKnownRequestError) => {
        // Just means they've already bid on this before
        this.logger.log(`Watcher already exists for the following lot: ${newBid.auctionLotsid} and user: ${newBid.createdBy}`, error);
      });
    }

    await this.sendSocketMessage('NEW_WATCHER_FROM_BID', newBid.auctionLotsid, result);
    auctionLot;
    previousBid;
  }

  private async sendSocketMessage(messageName: string, auctionLotsid: string, watcher: WatcherDto | void): Promise<void> {
    try {
      if (watcher) {
        const pageLoadStringfy: string = JSON.stringify({ action: messageName, data: watcher }, (key: string, value: any) =>
          typeof value === 'bigint' ? value.toString() : value,
        );
        const socketMessageNewWatcher: any = { message: { message: pageLoadStringfy }, messageType: messageName, resourceId: auctionLotsid };

        await this.socketService.postSocketMessage({
          message: JSON.stringify(socketMessageNewWatcher),
        });
      }
    } catch (error) {
      this.logger.log(`Failed to send socket message for new watcher: ${watcher?.id}`, error);
    }
  }
}
