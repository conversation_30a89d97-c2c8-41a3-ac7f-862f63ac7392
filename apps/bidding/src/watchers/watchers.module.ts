import { Module } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { WatchersController } from './controller/watchers.controller';
import { WatchersDbService } from './data/watchers-db.service';
import { WatchersHandler } from './handler/watchers.handler';
import { RedisService } from '@vb/redis';
import { SharedServicesModule } from 'libs/shared_services/shared.services.module';
import { AddWatcherToAuctionLotSubscriber } from './subscribers/add-watcher-to-auction-lot.subscriber';

@Module({
  controllers: [WatchersController],
  providers: [WatchersDbService, WatchersHandler, RedisService, AddWatcherToAuctionLotSubscriber],
  imports: [SharedModule, SharedServicesModule],
})
export class WatchersModule {}
