import { Injectable } from '@nestjs/common';
import { WatchersDbService } from '../data/watchers-db.service';
import { BaseHandler } from '@mvc/handlers/base.handler';
import { WatcherDto } from '../dtos/watcher.dto';
import { ListResultset } from '@mvc/data/list-resultset';

@Injectable()
export class WatchersHandler extends BaseHandler<WatchersDbService, WatcherDto> {
  constructor(watchersService: WatchersDbService) {
    super(watchersService);
  }

  async getMyWatchers(page: number, size: number, query: string, userId: string): Promise<ListResultset<WatcherDto>> {
    return await this.dbService.getMyWatchers(page, size, query, userId);
  }

  async getCurrentBidPrice(auctionLotsid: string): Promise<number> {
    return await this.dbService.getCurrentBidPrice(auctionLotsid);
  }

  async getWatchedItems(page: number, size: number, params: Record<string, any>[]): Promise<ListResultset<WatcherDto>> {
    const result: ListResultset<typeof WatcherDto> = await this.getAllCustom(page, size, params, WatcherDto);
    return result as unknown as ListResultset<WatcherDto>;
  }

  /**
   * instead of relying on the db to throw an exception, let's check before we try
   * to create a new watcher
   *
   * @param userId
   * @param request
   */
  async create(userId: string, request: WatcherDto): Promise<WatcherDto> {
    const params: Record<string, any>[] = [
      {
        accountMappingsId: request.accountMappingsId,
        auctionLotsid: request.auctionLotsid,
        auctionsid: request.auctionsid,
      },
    ];
    try {
      const existingWatcher: WatcherDto = await this.getCustom(params);
      request.id = existingWatcher.id;
      const updatedWatcher: WatcherDto = await this.dbService.update(userId, request, existingWatcher.id);
      return updatedWatcher;
    } catch (error) {}

    return await this.dbService.create(userId, request);
  }

  async getAllWatchersByAuctionLotsId(pageNum: number, sizeNum: number, params: Record<string, any>[]): Promise<ListResultset<WatcherDto>> {
    return await this.dbService.getAllCustom(pageNum, sizeNum, params, WatcherDto);
  }
}
