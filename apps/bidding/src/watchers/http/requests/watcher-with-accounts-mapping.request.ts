import { IsDef<PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { WatcherRequest } from './watcher.request';

export class WatcherRequestWithAccountsMapping extends WatcherRequest {
  @IsDefined()
  @IsNotEmpty()
  @MaxLength(36)
  @IsString()
  accountMappingsId: string;

  constructor(request: WatcherRequest, accountMappingsId: string) {
    super();
    this.auctionLotsid = request.auctionLotsid;
    this.auctionsid = request.auctionsid;
    this.watcherType = request.watcherType;
    this.accountMappingsId = accountMappingsId;
  }
}
