import { PostWatcherRequest, WatcherType } from '@viewbid/ts-node-client';
import { IsDefined, IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class WatcherRequest implements PostWatcherRequest {
  @IsDefined()
  @IsNotEmpty()
  @MaxLength(36)
  @IsString()
  auctionsid: string;
  @IsDefined()
  @IsNotEmpty()
  @MaxLength(36)
  @IsString()
  auctionLotsid: string;
  @IsDefined()
  @IsNotEmpty()
  @MaxLength(10)
  @IsEnum(WatcherType)
  watcherType: WatcherType;
}
