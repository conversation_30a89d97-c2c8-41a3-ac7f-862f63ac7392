import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '../../prisma/generated/bidding-db-client';
import { BaseDbService } from '@mvc/data/base-db.service';
import { WatcherDto } from '../dtos/watcher.dto';
import { WatcherNotFoundError } from '../exceptions/watcher-not-found.error';
import { ListResultset } from '@mvc/data/list-resultset';
@Injectable()
export class WatchersDbService extends BaseDbService<Prisma.WatchersDelegate, WatcherDto> {
  constructor(private prisma: PrismaService) {
    super(prisma.watchers);
  }

  mapToDto(model: any): WatcherDto {
    return new WatcherDto(model);
  }

  createFilter(params: any[]): any {
    if (params.length === 0) {
      return {};
    }
    const queryParam: any = params.find((param: any) => param.query);

    if (queryParam) {
      return {
        auctionLotsid: queryParam.query,
      };
    }

    return params.reduce((acc: Record<string, any>, param: any) => {
      Object.keys(param).forEach((key: string) => {
        const value: any = param[key];

        if (key === 'AND' || key === 'OR') {
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key] = acc[key].concat(value);
        } else if (key.includes('.')) {
          const keys: string[] = key.split('.');
          let current: Record<string, any> = acc;

          keys.forEach((k: string, index: number) => {
            if (index === keys.length - 1) {
              current[k] = value;
            } else {
              current[k] = current[k] || {};
              current = current[k];
            }
          });
        } else {
          acc[key] = value;
        }
      });

      return acc;
    }, {});
  }

  throw404(id: string): Error {
    throw new WatcherNotFoundError(id);
  }

  async getMyWatchers(page: number, size: number, query: string, userId: string): Promise<ListResultset<WatcherDto>> {
    const skip: number = size > 0 ? (page - 1) * size : 0;
    const take: number | undefined = size < 0 ? undefined : size;
    const [watchers, totalCount] = await Promise.all([
      this.prisma.watchers.findMany({
        where: {
          AND: [
            {
              accountMappingsId: userId,
              deletedOn: null,
            },
          ],
        },
        take: take,
        skip: skip,
      }),
      this.prisma.watchers.count({
        where: {
          AND: [
            {
              accountMappingsId: userId,
              deletedOn: null,
            },
          ],
        },
      }),
    ]);
    return new ListResultset<WatcherDto>(
      watchers.map((dbResult: any) => {
        return new WatcherDto(dbResult);
      }),
      page,
      size,
      totalCount,
      Math.ceil(totalCount / size),
    );
  }

  async getCurrentBidPrice(auctionLotsid: string): Promise<number> {
    const result: any = await this.prisma.bids.aggregate({
      _max: {
        amount: true,
      },
      where: {
        auctionLotsid: auctionLotsid,
      },
    });
    return Number(result._max.amount);
  }
}
