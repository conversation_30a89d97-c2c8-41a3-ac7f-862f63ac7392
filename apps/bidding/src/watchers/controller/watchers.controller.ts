import { BaseController } from '@mvc/controllers/base.controller';
import { ListResultset } from '@mvc/data/list-resultset';
import { Body, Controller, Get, Param, Post, Query, Req } from '@nestjs/common';
import { VBInterceptorRequest } from '@vb/nest/intercepters/interceptor-data-interface';
import { AuctionCatalogsApi, WatcherType } from '@viewbid/ts-node-client';
import { WatcherDto } from '../dtos/watcher.dto';
import { WatchersHandler } from '../handler/watchers.handler';
import { WatcherRequest } from '../http/requests/watcher.request';
import { WatcherListResponse } from '../http/responses/watcher-list.response';
import { WatcherResponse } from '../http/responses/watcher.response';

import { EmptyListResponse } from '@mvc/http/responses/empty-list.response';
import { ErrorResponse } from '@mvc/http/responses/error-response';
import { Anonymous } from '@vb/authorization/decorators/anonymous.decorator';
import { RedisService } from '@vb/redis';
import { AxiosResponse } from 'axios';
import { Record } from 'prisma/prisma-client/runtime/library';
import { AuctionLotFlattenedWatchlistDto } from '../../../../auction-catalogs/src/auction-lots/dto/auction-lot-flattened-watchlist.dto';
import { WatchedListItemDto } from '../dtos/watched-list-item.dto';
import { WatcherRequestWithAccountsMapping } from '../http/requests/watcher-with-accounts-mapping.request';
import { WatchedItemsFlattenedListResponse } from '../http/responses/watched-items-flattened-list.response';
import { X_SERVICE_TO_SERVICE } from '@http/constants';

@Controller('watchers')
export class WatchersController extends BaseController<WatchersHandler, WatcherRequest, WatcherDto, WatcherResponse, WatcherListResponse> {
  constructor(
    watchersHandler: WatchersHandler,
    private auctionCatalogsApi: AuctionCatalogsApi,
    private redisService: RedisService,
  ) {
    super(watchersHandler);
  }

  createDtoFromRequest(request: WatcherRequest): WatcherDto {
    return new WatcherDto(request);
  }

  createResponseFromDto(dto: WatcherDto): WatcherResponse {
    return new WatcherResponse(dto);
  }

  createResponseList(list: ListResultset<WatcherDto>): WatcherListResponse {
    return new WatcherListResponse(list);
  }

  @Post('/')
  async create(@Body() body: WatcherRequest, @Req() req: VBInterceptorRequest): Promise<ErrorResponse | WatcherResponse> {
    try {
      const userId: string = req.accountMappingsId;
      const data: WatcherRequestWithAccountsMapping = new WatcherRequestWithAccountsMapping(body, userId);

      try {
        const existingWatcher: WatcherDto = await this.handler.getCustom([{ ...data, deletedOn: { not: null }, watcherType: { in: [WatcherType.Bidder, WatcherType.Watcher] } }]);
        return super.restore(existingWatcher.id, req);
      } catch (error) {
        return super.create(data, req);
      }
    } catch (error) {
      return new ErrorResponse(error, 'create');
    }
  }

  @Get('getMyWatchers')
  async getMyWatchers(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
    @Req() req: VBInterceptorRequest | any,
  ): Promise<WatcherListResponse | ErrorResponse> {
    try {
      const watchers: ListResultset<WatcherDto> = await this.handler.getMyWatchers(page, size, query, req.accountMappingsId);

      return this.createResponseList(watchers);
    } catch (error) {
      return new ErrorResponse(error, 'getAllBidWinners');
    }
  }

  @Get('watchedItemsByUser/:id')
  async getWatchedItemsByUser(
    @Param('id') id: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('tags') query: string[],
    @Query('watcherType') watcherType: WatcherType,
  ): Promise<WatcherListResponse | ErrorResponse> {
    try {
      const params: Record<string, any>[] = this.buildFilterParams(id, query, watcherType);
      const watchers: ListResultset<WatcherDto> = await this.handler.getWatchedItems(page, size, params);

      return this.createResponseList(watchers);
    } catch (error) {
      return new ErrorResponse(error, 'getWatchedItemsByUser');
    }
  }

  buildFilterParams(id: string, tags: string[], watcherType?: WatcherType): Record<string, any>[] {
    const params: Record<string, any>[] = [];

    // Add accountMappingsId to the params list
    params.push({ accountMappingsId: id });
    params.push({ deletedOn: null });

    if (watcherType) {
      params.push({ watcherType: watcherType });
    }

    if (tags && tags.length > 0) {
      // Add each tag to the params list
      tags.forEach((tag: string) => {
        params.push({ tag: tag });
      });
    }
    return params;
  }

  @Anonymous()
  @Get('allWatchersByAuctionLotsId/:id')
  async getWatchersByAuctionLotsId(@Param('id') id: string, @Query('page') page: string, @Query('size') size: string): Promise<WatcherListResponse | ErrorResponse> {
    const pageNum: number = parseInt(page, 10) || 0;
    const sizeNum: number = parseInt(size, 10) || 10;

    //fail-fast check to ensure it's a valid auction lot item
    await this.auctionCatalogsApi.getAuctionLotById(id);

    const params: Record<string, any>[] = [{ auctionLotsid: id }];

    try {
      const watchers: ListResultset<WatcherDto> = await this.handler.getAllWatchersByAuctionLotsId(pageNum, sizeNum, params);

      return new WatcherListResponse(watchers);
    } catch (error) {
      return new ErrorResponse(error, 'allWatchersByAuctionLotsId');
    }
  }

  @Get('watchedAuctionLots/:auctionId')
  async getWatchedAuctionLots(
    @Param('auctionId') auctionId: string,
    @Query('accountMappingId') accountMappingId: string,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('query') query: string,
  ): Promise<WatcherListResponse | ErrorResponse> {
    try {
      query;
      const params: Record<string, any>[] = [{ auctionsid: auctionId }, { accountMappingsId: accountMappingId }];
      const watchedAuctionItems: ListResultset<WatcherDto> = (await this.handler.getAllCustom(page, size, params, WatcherDto)) as unknown as ListResultset<WatcherDto>;

      if (watchedAuctionItems.totalCount > 0) {
        const auctionLotIds: string[] = watchedAuctionItems.list.map((item: WatcherDto) => item.auctionLotsid);
        const additionalHeaders: any = {
          accountMappingsId: accountMappingId,
        };
        const auctionLotsFlattenedList: AxiosResponse<any> = await this.auctionCatalogsApi.listAuctionLotsFlattenedByIds(page, size, auctionLotIds, undefined, additionalHeaders);
        const auctionLotsList: Array<AuctionLotFlattenedWatchlistDto> = auctionLotsFlattenedList.data.data;
        const mergedList: WatchedListItemDto[] = await this.constructWatchedListItemDto(auctionLotsList, watchedAuctionItems);

        return new WatchedItemsFlattenedListResponse(
          new ListResultset<WatchedListItemDto>(mergedList, watchedAuctionItems.page, watchedAuctionItems.pageSize, watchedAuctionItems.totalCount, watchedAuctionItems.totalPages),
        );
      }
      return new EmptyListResponse('getMyWatchedItems');
    } catch (error) {
      return new ErrorResponse(error, 'getMyWatchedItems');
    }
  }

  @Anonymous()
  @Get('allWatchersByAuctionsId/:id')
  async getWatchersByAuctionsId(@Param('id') id: string, @Query('page') page: string, @Query('size') size: string): Promise<WatcherListResponse | ErrorResponse> {
    const pageNum: number = parseInt(page, 10) || 0;
    const sizeNum: number = parseInt(size, 10) || 10;

    //fail-fast check to ensure it's a valid auctions
    await this.auctionCatalogsApi.getAdminAuctionById(id);

    const params: Record<string, any>[] = [{ auctionsid: id }];

    try {
      const watchers: ListResultset<WatcherDto> = (await this.handler.getAllCustom(pageNum, sizeNum, params, WatcherDto)) as unknown as ListResultset<WatcherDto>;

      return new WatcherListResponse(watchers);
    } catch (error) {
      return new ErrorResponse(error, 'allWatchersByAuctionsId');
    }
  }

  @Get('getMyWatchedItems')
  async getMyWatchedItems(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('filters') filters: string,
    @Req() req: VBInterceptorRequest | any,
  ): Promise<WatchedItemsFlattenedListResponse | ErrorResponse> {
    try {
      const params: any[] = [{ accountMappingsId: req.accountMappingsId }, { deletedOn: null }];
      const watchedAuctionItems: ListResultset<WatcherDto> = await this.handler.getAll(1, -1, params);
      if (watchedAuctionItems.totalCount > 0) {
        const auctionLotIds: string[] = watchedAuctionItems.list.map((item: WatcherDto) => item.auctionLotsid);
        const additionalHeaders: any = {
          accountMappingsId: req.accountMappingsId,
          [X_SERVICE_TO_SERVICE]: true,
        };
        const auctionLotsFlattenedList: AxiosResponse<any> = await this.auctionCatalogsApi.listAuctionLotsFlattenedByIds(page, size, auctionLotIds, filters, {
          headers: additionalHeaders,
        });
        const auctionLotsList: Array<AuctionLotFlattenedWatchlistDto> = auctionLotsFlattenedList.data.data;
        const mergedList: WatchedListItemDto[] = await this.constructWatchedListItemDto(auctionLotsList, watchedAuctionItems);

        return new WatchedItemsFlattenedListResponse(
          new ListResultset<WatchedListItemDto>(
            mergedList,
            auctionLotsFlattenedList.data.page,
            auctionLotsFlattenedList.data.pageSize,
            auctionLotsFlattenedList.data.totalCount,
            auctionLotsFlattenedList.data.totalPages,
          ),
        );
      }
      return new EmptyListResponse('getMyWatchedItems');
    } catch (error) {
      return new ErrorResponse(error, 'getMyWatchedItems');
    }
  }

  constructWatchedListItemDto(auctionLotsList: Array<AuctionLotFlattenedWatchlistDto>, watchedAuctionItems: ListResultset<WatcherDto>): WatchedListItemDto[] {
    const mergedList: WatchedListItemDto[] = watchedAuctionItems.list
      .map((watchedItem: WatcherDto) => {
        const auctionLot: AuctionLotFlattenedWatchlistDto | undefined = auctionLotsList.find(
          (lot: AuctionLotFlattenedWatchlistDto) => lot.auctionLotId === watchedItem.auctionLotsid,
        );

        if (auctionLot) {
          return new WatchedListItemDto(watchedItem.id, auctionLot);
        } else {
          return null;
        }
      })
      .filter((item: WatchedListItemDto | null) => item !== null) as WatchedListItemDto[];
    return mergedList;
  }
}
